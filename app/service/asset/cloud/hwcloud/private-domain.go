package hwcloud

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/asset/cloud"
	"cmdb/pkg/utils"
	"errors"
	"time"

	"github.com/huaweicloud/huaweicloud-sdk-go-v3/core/auth/basic"
	dns "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/dns/v2"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/dns/v2/model"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/dns/v2/region"
	"gorm.io/gorm"
)

// 同步账号的私有域名
func (a Account) syncPrivateDomains() (result cloud.SyncResults, err error) {
	// 创建一个同步结果对象
	sr := cloud.SyncResult{
		Key: "账号：" + a.Name,
	}
	// 同步私有域名
	r, err := a.syncRegionPrivateDomains(0)
	// 如果同步成功
	if err == nil {
		sr.Result = "同步成功"
		// 回收过期内网域名
		err = a.ExpiredPrivateDomains(time.Now().Add(-10 * time.Hour))
		// 如果回收失败
		if err != nil {
			sr.Result = "同步成功，但是回收内网域名失败" + err.Error()
		}
	} else {
		// 如果同步失败
		sr.Result = r + " " + err.Error()
	}
	// 将同步结果添加到结果列表中
	result = append(result, sr)
	app.Log().Info("完成同步", "err", err)
	return
}

// 同步区域私有域名
func (a Account) syncRegionPrivateDomains(offset int32) (result string, err error) {
	// 获取AccessKey
	accesesKeyID := a.AccessKey
	// 解码AccessSecret
	accessSecret, _ := utils.Base64Decode(a.AccessSecret)
	// 创建认证
	auth, err := basic.NewCredentialsBuilder().WithAk(accesesKeyID).WithSk(accessSecret).SafeBuild()
	if err != nil {
		// 认证失败
		result = "认证失败"
		return
	}
	// 获取区域编码
	r, err := region.SafeValueOf("cn-north-4")
	if err != nil {
		// 区域编码错误
		result = "区域编码错误"
		return
	}
	// 创建DNS客户端
	b, err := dns.DnsClientBuilder().WithRegion(r).WithCredential(auth).SafeBuild()
	if err != nil {
		// 创建客户端失败
		result = "创建客户端失败"
		return
	}
	// 创建请求
	request := &model.ListPrivateZonesRequest{}
	// 设置请求参数
	limitRequest := int32(500)
	request.Type = "private"
	request.Limit = &limitRequest
	request.Offset = &offset
	// 发送请求
	response, err := dns.NewDnsClient(b).ListPrivateZones(request)
	if err != nil {
		// 获取内网域名失败
		result = "获取内网域名失败" + err.Error()
		return
	}
	// 处理响应
	if response.Zones != nil {
		now := time.Now()
		for _, zone := range *response.Zones {
			exist := asset.PrivateDomain{}
			// 解析创建时间
			createTime, timeErr := time.Parse("2006-01-02T15:04:05", *zone.CreatedAt)
			if timeErr != nil {
				createTime = now
			}
			// 解析更新时间
			UpdateTime, timeErr := time.Parse("2006-01-02T15:04:05", *zone.UpdatedAt)
			if timeErr != nil {
				createTime = now
			}
			// 获取备注
			remark := ""
			if zone.Description != nil {
				remark = *zone.Description
			}
			// 获取资源组ID
			resourceGroupID := ""
			if zone.EnterpriseProjectId != nil {
				resourceGroupID = *zone.EnterpriseProjectId
			}
			// 获取域名
			name := ""
			if zone.Name != nil {
				name = *zone.Name
			}
			// 获取域名ID
			domainID := ""
			if zone.Id != nil {
				domainID = *zone.Id
			}
			// 查询数据库中是否存在该域名
			err1 := app.DB().Where("domain_id = ? AND account_id = ? ", domainID, a.ID).Take(&exist).Error
			if errors.Is(err1, gorm.ErrRecordNotFound) {
				// 如果不存在，则创建
				err = app.DB().Create(&asset.PrivateDomain{
					AccountID:       a.ID,
					Name:            name,
					RecordCount:     int64(*zone.RecordNum),
					ResourceGroupID: resourceGroupID,
					CloudType:       a.CloudType,
					DomainID:        domainID,
					CreatedAt:       createTime,
					UpdatedAt:       UpdateTime,
					SyncTime:        now,
					Remark:          remark,
				}).Error
			} else if err1 == nil {
				// 如果存在，则更新
				updateMap := map[string]any{
					"sync_time": now,
				}
				if exist.Name != name {
					updateMap["name"] = name
				}
				if exist.RecordCount != int64(*zone.RecordNum) {
					updateMap["record_count"] = int64(*zone.RecordNum)
				}
				if exist.Remark != remark {
					updateMap["remark"] = remark
				}
				if exist.ResourceGroupID != resourceGroupID {
					updateMap["resource_group_id"] = resourceGroupID
				}
				// if exist.CreatedAt != createTime {
				// 	updateMap["created_at"] = createTime
				// }
				if exist.UpdatedAt != UpdateTime {
					updateMap["updated_at"] = UpdateTime
				}
				err = app.DB().Model(&exist).Updates(updateMap).Error
			} else {
				err = err1
			}
			if err != nil {
				return
			}
		}
	}
	if len(*response.Zones) == 0 {
		return
	}
	// 如果总数小于偏移量+限制，则继续同步
	if len(*response.Zones) > 0 && *response.Metadata.TotalCount > offset+limitRequest {
		result, err = a.syncRegionPrivateDomains(offset + limitRequest)
	}
	return
}
