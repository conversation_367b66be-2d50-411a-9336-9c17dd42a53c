package dingtalk

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"time"
)

type Message interface {
	ToJSON() ([]byte, error)
}

// TextMessage 文本消息
type TextMessage struct {
	MsgType string `json:"msgtype"`
	Text    struct {
		Content string `json:"content"`
	} `json:"text"`
	At struct {
		AtMobiles []string `json:"atMobiles,omitempty"`
		AtUserIds []string `json:"atUserIds,omitempty"`
		IsAtAll   bool     `json:"isAtAll"`
	} `json:"at"`
}

// MarkdownMessage Markdown消息
type MarkdownMessage struct {
	MsgType  string `json:"msgtype"`
	Markdown struct {
		Title string `json:"title"`
		Text  string `json:"text"`
	} `json:"markdown"`
	At struct {
		AtMobiles []string `json:"atMobiles,omitempty"`
		AtUserIds []string `json:"atUserIds,omitempty"`
		IsAtAll   bool     `json:"isAtAll"`
	} `json:"at"`
}

// NewTextMessage 创建文本消息
func NewTextMessage(content string) *TextMessage {
	msg := &TextMessage{
		MsgType: "text",
	}
	msg.Text.Content = content
	return msg
}

// NewMarkdownMessage 创建Markdown消息
func NewMarkdownMessage(title, text string) *MarkdownMessage {
	msg := &MarkdownMessage{
		MsgType: "markdown",
	}
	msg.Markdown.Title = title
	msg.Markdown.Text = text
	return msg
}

// AtMobiles 添加@手机号
func (m *TextMessage) AtMobiles(mobiles ...string) *TextMessage {
	m.At.AtMobiles = append(m.At.AtMobiles, mobiles...)
	return m
}

// AtUserIds 添加@用户ID
func (m *TextMessage) AtUserIds(userIds ...string) *TextMessage {
	m.At.AtUserIds = append(m.At.AtUserIds, userIds...)
	return m
}

// AtAll @所有人
func (m *TextMessage) AtAll() *TextMessage {
	m.At.IsAtAll = true
	return m
}

// ToJSON 实现Message接口
func (m *TextMessage) ToJSON() ([]byte, error) {
	return json.Marshal(m)
}

// AtMobiles 添加@手机号
func (m *MarkdownMessage) AtMobiles(mobiles ...string) *MarkdownMessage {
	m.At.AtMobiles = append(m.At.AtMobiles, mobiles...)
	return m
}

// AtUserIds 添加@用户ID
func (m *MarkdownMessage) AtUserIds(userIds ...string) *MarkdownMessage {
	m.At.AtUserIds = append(m.At.AtUserIds, userIds...)
	return m
}

// AtAll @所有人
func (m *MarkdownMessage) AtAll() *MarkdownMessage {
	m.At.IsAtAll = true
	return m
}

// ToJSON 实现Message接口
func (m *MarkdownMessage) ToJSON() ([]byte, error) {
	return json.Marshal(m)
}

// 生成签名
func generateSignature(secret string, timestamp int64) string {
	stringToSign := fmt.Sprintf("%d\n%s", timestamp, secret)
	h := hmac.New(sha256.New, []byte(secret))
	h.Write([]byte(stringToSign))
	return base64.StdEncoding.EncodeToString(h.Sum(nil))
}

// Send 发送消息
func Send(msg Message, webhook string, secret string) error {
	if webhook == "" {
		return fmt.Errorf("dingtalk webhook not configured")
	}

	// 生成签名
	timestamp := time.Now().UnixMilli()

	// 构建URL
	u, err := url.Parse(webhook)
	if err != nil {
		return err
	}
	q := u.Query()
	q.Set("timestamp", fmt.Sprintf("%d", timestamp))
	if secret != "" {
		sign := generateSignature(secret, timestamp)
		q.Set("sign", sign)
	}
	u.RawQuery = q.Encode()

	// 构建请求体
	jsonBody, err := msg.ToJSON()
	if err != nil {
		return err
	}

	// 发送请求
	req, err := http.NewRequest("POST", u.String(), bytes.NewBuffer(jsonBody))
	if err != nil {
		return err
	}
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{
		Timeout: 5 * time.Second,
	}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("dingtalk api returned status code: %d", resp.StatusCode)
	}

	// 解析响应
	var result struct {
		ErrCode int    `json:"errcode"`
		ErrMsg  string `json:"errmsg"`
	}
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return err
	}

	if result.ErrCode != 0 {
		return fmt.Errorf("dingtalk api returned error: %s", result.ErrMsg)
	}

	return nil
}
