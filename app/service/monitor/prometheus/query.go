package prometheus

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/prometheus/client_golang/api"
	v1 "github.com/prometheus/client_golang/api/prometheus/v1"
	"github.com/prometheus/common/model"
)

// 定义查询相关的错误
var (
	ErrInstanceNotFound = errors.New("Prometheus实例未找到")
	ErrQueryFailed      = errors.New("查询失败")
	ErrInvalidResponse  = errors.New("无效的响应")
)

// QueryType 定义查询类型
type QueryType string

const (
	// 即时查询
	QueryTypeQuery QueryType = "query"
	// 范围查询
	QueryTypeQueryRange QueryType = "query_range"
	// 元数据查询
	QueryTypeMetadata QueryType = "metadata"
	// 标签查询
	QueryTypeLabels QueryType = "labels"
	// 序列查询
	QueryTypeSeries QueryType = "series"
)

// QueryResult 查询结果结构
type QueryResult struct {
	// 原始响应数据
	Data interface{} `json:"data"`
	// 警告信息
	Warnings []string `json:"warnings,omitempty"`
}

// GetType 获取查询结果的类型
func (r *QueryResult) GetType() model.ValueType {
	if r == nil || r.Data == nil {
		return model.ValNone
	}

	switch r.Data.(type) {
	case model.Vector:
		return model.ValVector
	case model.Matrix:
		return model.ValMatrix
	case model.Scalar:
		return model.ValScalar
	case model.String:
		return model.ValString
	default:
		return model.ValNone
	}
}

// AsVector 将结果转换为Vector类型（即时查询结果）
func (r *QueryResult) AsVector() (model.Vector, error) {
	if r == nil || r.Data == nil {
		return nil, fmt.Errorf("结果为空")
	}

	if vec, ok := r.Data.(model.Vector); ok {
		return vec, nil
	}
	return nil, fmt.Errorf("结果类型不是Vector，实际类型: %T", r.Data)
}

// AsMatrix 将结果转换为Matrix类型（范围查询结果）
func (r *QueryResult) AsMatrix() (model.Matrix, error) {
	if r == nil || r.Data == nil {
		return nil, fmt.Errorf("结果为空")
	}

	if matrix, ok := r.Data.(model.Matrix); ok {
		return matrix, nil
	}
	return nil, fmt.Errorf("结果类型不是Matrix，实际类型: %T", r.Data)
}

// AsScalar 将结果转换为Scalar类型（标量结果）
func (r *QueryResult) AsScalar() (*model.Scalar, error) {
	if r == nil || r.Data == nil {
		return nil, fmt.Errorf("结果为空")
	}

	if scalar, ok := r.Data.(model.Scalar); ok {
		return &scalar, nil
	}
	return nil, fmt.Errorf("结果类型不是Scalar，实际类型: %T", r.Data)
}

// AsString 将结果转换为String类型（字符串结果）
func (r *QueryResult) AsString() (*model.String, error) {
	if r == nil || r.Data == nil {
		return nil, fmt.Errorf("结果为空")
	}

	if str, ok := r.Data.(model.String); ok {
		return &str, nil
	}
	return nil, fmt.Errorf("结果类型不是String，实际类型: %T", r.Data)
}

// GetVectorValue 获取Vector中的第一个值
func (r *QueryResult) GetVectorValue() (float64, error) {
	vec, err := r.AsVector()
	if err != nil {
		return 0, err
	}

	if len(vec) == 0 {
		return 0, fmt.Errorf("Vector结果为空")
	}

	return float64(vec[0].Value), nil
}

// GetVectorValues 获取Vector中的所有值
func (r *QueryResult) GetVectorValues(labelName string) (map[string]float64, error) {
	vec, err := r.AsVector()
	if err != nil {
		return nil, err
	}

	result := make(map[string]float64)
	for _, sample := range vec {
		var key string
		if labelName != "" {
			if labelValue, ok := sample.Metric[model.LabelName(labelName)]; ok {
				key = string(labelValue)
			} else {
				key = sample.Metric.String()
			}
		} else {
			key = sample.Metric.String()
		}
		result[key] = float64(sample.Value)
	}

	return result, nil
}

// GetMatrixValues 获取Matrix中的时间序列数据
// 返回一个map，键为指标名称（使用指定的标签），值为时间-值对的列表
func (r *QueryResult) GetMatrixValues(labelName string) (map[string][]model.SamplePair, error) {
	matrix, err := r.AsMatrix()
	if err != nil {
		return nil, err
	}

	result := make(map[string][]model.SamplePair)
	for _, series := range matrix {
		var key string
		if labelName != "" {
			if labelValue, ok := series.Metric[model.LabelName(labelName)]; ok {
				key = string(labelValue)
			} else {
				key = series.String()
			}
		} else {
			key = series.String()
		}
		result[key] = series.Values
	}

	return result, nil
}

// GetScalarValue 获取标量值
func (r *QueryResult) GetScalarValue() (float64, error) {
	scalar, err := r.AsScalar()
	if err != nil {
		return 0, err
	}

	return float64(scalar.Value), nil
}

// 获取Prometheus客户端API
func getPrometheusAPI(datasource string) (v1.API, error) {
	// 创建客户端配置
	config := api.Config{
		Address: datasource,
	}

	// 创建客户端
	client, err := api.NewClient(config)
	if err != nil {
		return nil, fmt.Errorf("创建Prometheus客户端失败: %v", err)
	}

	// 返回API接口
	return v1.NewAPI(client), nil
}

// QueryInstant 执行即时查询
// datasource: Prometheus数据源
// query: 查询表达式
// timestamp: 查询时间点，如果为nil则使用当前时间
// timeout: 查询超时时间，如果为nil则使用默认值
func QueryInstant(datasource string, query string, timestamp *time.Time, timeout *time.Duration) (*QueryResult, error) {
	// 获取API客户端
	api, err := getPrometheusAPI(datasource)
	if err != nil {
		return nil, err
	}

	// 设置上下文
	ctx := context.Background()
	if timeout != nil {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, *timeout)
		defer cancel()
	}

	// 设置查询时间
	var ts time.Time
	if timestamp != nil {
		ts = *timestamp
	} else {
		ts = time.Now()
	}

	// 执行查询
	result, warnings, err := api.Query(ctx, query, ts)
	if err != nil {
		return nil, fmt.Errorf("%w: %v", ErrQueryFailed, err)
	}

	// 返回结果
	return &QueryResult{
		Data:     result,
		Warnings: warnings,
	}, nil
}

// QueryRange 执行范围查询
// datasource: Prometheus数据源
// query: 查询表达式
// start: 开始时间
// end: 结束时间
// step: 步长
// timeout: 查询超时时间，如果为nil则使用默认值
func QueryRange(datasource string, query string, start, end time.Time, step time.Duration, timeout *time.Duration) (*QueryResult, error) {
	// 获取API客户端
	api, err := getPrometheusAPI(datasource)
	if err != nil {
		return nil, err
	}

	// 设置上下文
	ctx := context.Background()
	if timeout != nil {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, *timeout)
		defer cancel()
	}

	// 设置查询范围
	r := v1.Range{
		Start: start,
		End:   end,
		Step:  step,
	}

	// 执行查询
	result, warnings, err := api.QueryRange(ctx, query, r)
	if err != nil {
		return nil, fmt.Errorf("%w: %v", ErrQueryFailed, err)
	}

	// 返回结果
	return &QueryResult{
		Data:     result,
		Warnings: warnings,
	}, nil
}

// QuerySeries 查询时间序列
// datasource: Prometheus数据源
// matches: 标签匹配器
// start: 开始时间，可以为nil
// end: 结束时间，可以为nil
// timeout: 查询超时时间，如果为nil则使用默认值
func QuerySeries(datasource string, matches []string, start, end *time.Time, timeout *time.Duration) (*QueryResult, error) {
	// 获取API客户端
	api, err := getPrometheusAPI(datasource)
	if err != nil {
		return nil, err
	}

	// 设置上下文
	ctx := context.Background()
	if timeout != nil {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, *timeout)
		defer cancel()
	}

	// 设置查询时间范围
	var startTime, endTime time.Time
	if start != nil {
		startTime = *start
	} else {
		startTime = time.Now().Add(-1 * time.Hour)
	}
	if end != nil {
		endTime = *end
	} else {
		endTime = time.Now()
	}

	// 执行查询
	result, warnings, err := api.Series(ctx, matches, startTime, endTime)
	if err != nil {
		return nil, fmt.Errorf("%w: %v", ErrQueryFailed, err)
	}

	// 返回结果
	return &QueryResult{
		Data:     result,
		Warnings: warnings,
	}, nil
}

// QueryLabels 查询标签名称
// datasource: Prometheus数据源
// matches: 标签匹配器，可以为nil
// start: 开始时间，可以为nil
// end: 结束时间，可以为nil
// timeout: 查询超时时间，如果为nil则使用默认值
func QueryLabels(datasource string, matches []string, start, end *time.Time, timeout *time.Duration) (*QueryResult, error) {
	// 获取API客户端
	api, err := getPrometheusAPI(datasource)
	if err != nil {
		return nil, err
	}

	// 设置上下文
	ctx := context.Background()
	if timeout != nil {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, *timeout)
		defer cancel()
	}

	// 设置查询时间范围
	var startTime, endTime time.Time
	if start != nil {
		startTime = *start
	} else {
		startTime = time.Now().Add(-1 * time.Hour)
	}
	if end != nil {
		endTime = *end
	} else {
		endTime = time.Now()
	}

	// 执行查询
	result, warnings, err := api.LabelNames(ctx, matches, startTime, endTime)
	if err != nil {
		return nil, fmt.Errorf("%w: %v", ErrQueryFailed, err)
	}

	// 返回结果
	return &QueryResult{
		Data:     result,
		Warnings: warnings,
	}, nil
}

// QueryLabelValues 查询标签值
// instanceID: Prometheus实例ID
// label: 标签名称
// matches: 标签匹配器，可以为nil
// start: 开始时间，可以为nil
// end: 结束时间，可以为nil
// timeout: 查询超时时间，如果为nil则使用默认值
func QueryLabelValues(datasource string, label string, matches []string, start, end *time.Time, timeout *time.Duration) (*QueryResult, error) {
	// 获取API客户端
	api, err := getPrometheusAPI(datasource)
	if err != nil {
		return nil, err
	}

	// 设置上下文
	ctx := context.Background()
	if timeout != nil {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, *timeout)
		defer cancel()
	}

	// 设置查询时间范围
	var startTime, endTime time.Time
	if start != nil {
		startTime = *start
	} else {
		startTime = time.Now().Add(-1 * time.Hour)
	}
	if end != nil {
		endTime = *end
	} else {
		endTime = time.Now()
	}

	// 执行查询
	var result model.LabelValues
	var warnings v1.Warnings
	err = nil

	if len(matches) > 0 {
		result, warnings, err = api.LabelValues(ctx, label, matches, startTime, endTime)
	} else {
		result, warnings, err = api.LabelValues(ctx, label, nil, startTime, endTime)
	}

	if err != nil {
		return nil, fmt.Errorf("%w: %v", ErrQueryFailed, err)
	}

	// 返回结果
	return &QueryResult{
		Data:     result,
		Warnings: warnings,
	}, nil
}

// QueryTargets 查询Prometheus目标
// datasource: Prometheus数据源
// state: 目标状态，可以为"active", "dropped", 或 ""（所有状态）
// timeout: 查询超时时间，如果为nil则使用默认值
func QueryTargets(datasource string, state string, timeout *time.Duration) (*QueryResult, error) {
	// 获取API客户端
	api, err := getPrometheusAPI(datasource)
	if err != nil {
		return nil, err
	}

	// 设置上下文
	ctx := context.Background()
	if timeout != nil {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, *timeout)
		defer cancel()
	}

	// 执行查询
	result, err := api.Targets(ctx)
	if err != nil {
		return nil, fmt.Errorf("%w: %v", ErrQueryFailed, err)
	}

	// 根据状态过滤
	var data interface{}
	switch state {
	case "active":
		data = result.Active
	case "dropped":
		data = result.Dropped
	default:
		data = result
	}

	// 返回结果
	return &QueryResult{
		Data: data,
	}, nil
}

// QueryRules 查询Prometheus规则
// datasource: Prometheus数据源
// timeout: 查询超时时间，如果为nil则使用默认值
func QueryRules(datasource string, timeout *time.Duration) (*QueryResult, error) {
	// 获取API客户端
	api, err := getPrometheusAPI(datasource)
	if err != nil {
		return nil, err
	}

	// 设置上下文
	ctx := context.Background()
	if timeout != nil {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, *timeout)
		defer cancel()
	}

	// 执行查询
	result, err := api.Rules(ctx)
	if err != nil {
		return nil, fmt.Errorf("%w: %v", ErrQueryFailed, err)
	}

	// 返回结果
	return &QueryResult{
		Data: result,
	}, nil
}

// QueryAlerts 查询Prometheus告警
// datasource: Prometheus数据源
// timeout: 查询超时时间，如果为nil则使用默认值
func QueryAlerts(datasource string, timeout *time.Duration) (*QueryResult, error) {
	// 获取API客户端
	api, err := getPrometheusAPI(datasource)
	if err != nil {
		return nil, err
	}

	// 设置上下文
	ctx := context.Background()
	if timeout != nil {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, *timeout)
		defer cancel()
	}

	// 执行查询
	result, err := api.Alerts(ctx)
	if err != nil {
		return nil, fmt.Errorf("%w: %v", ErrQueryFailed, err)
	}

	// 返回结果
	return &QueryResult{
		Data: result,
	}, nil
}

// ToJSON 将查询结果转换为JSON
func (r *QueryResult) ToJSON() ([]byte, error) {
	return json.Marshal(r.Data)
}
