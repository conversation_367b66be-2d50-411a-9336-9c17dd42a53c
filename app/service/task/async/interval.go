package async

import (
	"cmdb/app"
	"context"
	"time"
)

type IntervalTask struct {
	Name     string
	Func     func() (err error)
	Interval time.Duration
	Timeout  time.Duration
	CtxCacel context.CancelFunc
}

type IntervalTasks []*IntervalTask

func (tasks IntervalTasks) Run() {
	for _, task := range tasks {
		go func(t *IntervalTask) {
			var ctx context.Context
			ctx, t.CtxCacel = context.WithCancel(context.Background())
			for {
				select {
				case <-ctx.Done():
					return
				default:
					now := time.Now()
					app.Log().Info("开始任务 " + t.Name)
					err := t.Func()
					if err != nil {
						app.Log().Error("完成任务 "+t.Name, "cost", time.Since(now).String(), "result", "失败", "error", err)
					} else {
						app.Log().Info("完成任务 "+t.Name, "cost", time.Since(now).String(), "result", "成功")
					}
					time.Sleep(t.Interval)
				}
			}
		}(task)
	}
}

func (tasks IntervalTasks) Stop() {
	for i := range tasks {
		if tasks[i].CtxCacel != nil {
			app.Log().Info("关闭任务 " + tasks[i].Name)
			tasks[i].CtxCacel()
		}
	}
}
