package asset

import (
	"cmdb/app"
	"cmdb/pkg/db"
	"fmt"
	"time"

	"gorm.io/gorm"
)

type PublicIP struct {
	ID           uint           `gorm:"column:id;primaryKey;autoIncrement;comment:主键ID" json:"id" `
	Name         string         `gorm:"column:name;type:varchar(255);index;comment:名称" json:"name"`
	CloudType    CloudType      `gorm:"column:cloud_type;index;comment:账户类型" json:"cloud_type"`
	SN           string         `gorm:"column:sn;type:varchar(255);index;comment:序列号" json:"sn" `
	IP           string         `gorm:"column:ip;type:varchar(255);index;comment:IP" json:"ip" `
	PrivateIP    string         `gorm:"column:private_ip;type:varchar(255);index;comment:内网IP" json:"private_ip" `
	Status       string         `gorm:"column:status;index;comment:状态" json:"status"`
	ExpiredTime  *time.Time     `gorm:"column:expired_time;index;comment:过期时间" json:"expired_time"`
	DatacenterID uint           `gorm:"column:datacenter_id;index;comment:关联dc id" json:"datacenter_id"`
	AccountID    uint           `gorm:"column:account_id;index;comment:关联云账户id" json:"account_id"`
	SyncTime     *time.Time     `gorm:"column:sync_time;index;comment:同步时间" json:"sync_time"`
	CreatedAt    time.Time      `gorm:"column:created_at;index;comment:创建时间" json:"created_at"`
	DeletedAt    gorm.DeletedAt `gorm:"column:deleted_at;comment:软删除" json:"-"`
}

func (PublicIP) TableName() string {
	return "asset_public_ips"
}

func (p PublicIP) Delete() error {
	return app.DB().Delete(&p).Error
}

type GetPublicIPsParmas struct {
	Offset       int
	Limit        int
	IP           *string
	Keyword      *string
	CloudType    *int
	AccountID    *int
	DatacenterID *int
}

func GetPublicIPs(params GetPublicIPsParmas) (count int64, data []PublicIP, err error) {
	dbop := app.DB()
	if params.Keyword != nil {
		dbop = db.MLike(dbop, *params.Keyword, "sn", "name")
	}
	if params.CloudType != nil {
		dbop = dbop.Where("cloud_type = ?", *params.CloudType)
	}
	if params.AccountID != nil {
		dbop = dbop.Where("account_id = ?", *params.AccountID)
	}
	if params.DatacenterID != nil {
		dbop = dbop.Where("datacenter_id = ?", *params.DatacenterID)
	}
	if params.IP != nil {
		dbop = dbop.Where("ip = ? OR private_ip = ?", *params.IP, *params.IP)
	}
	err = dbop.Model(&PublicIP{}).Count(&count).Order("created_at DESC").Offset(params.Offset).Limit(params.Limit).Find(&data).Error
	return
}

func (eip PublicIP) GetDatacenterCode() (code string) {
	var datacenter Datacenter
	err := app.DB().Where("id = ?", eip.DatacenterID).Take(&datacenter).Error
	if err != nil {
		return ""
	}
	return datacenter.Code
}

func (eip PublicIP) GetCloudURL() (url string) {
	switch eip.CloudType {
	case HuaweiCloudType:
		return fmt.Sprintf("https://console.huaweicloud.com/vpc/?region=%s&locale=zh-cn#/eip/eips/detail/basicInfo?eipId=%s", eip.GetDatacenterCode(), eip.SN)
	case AliyunCloudType:
		return fmt.Sprintf("https://vpc.console.aliyun.com/eip/%s/eips/%s", eip.GetDatacenterCode(), eip.SN)
	}
	return
}
