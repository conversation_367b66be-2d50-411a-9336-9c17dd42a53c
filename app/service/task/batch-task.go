package task

import (
	"cmdb/app"
	"cmdb/pkg/db"
	"errors"
	"time"

	"gorm.io/gorm"
)

// 定义批量任务执行中错误
var (
	ErrBatchTaskRuning = errors.New("任务执行中")
	// 定义批量任务已执行错误
	ErrBatchTaskRan = errors.New("任务已执行")
	// 定义批量任务已成功执行错误
	ErrBatchTaskRanSuccess = errors.New("任务已成功执行")
	// 定义任务状态更新失败错误
	ErrTaskUpdateRunningStatFail = errors.New("任务状态更新失败")
)

// 定义批量任务结构体
type BatchTask struct {
	ID           uint           `gorm:"primaryKey;column:id;comment:ID" json:"id"`
	Name         string         `gorm:"type:varchar(255);column:name;index;comment:名称" json:"name"`
	TaskType     TaskType       `gorm:"type:varchar(255);index;column:task_type;comment:任务类型" json:"task_type"`
	FilePath     string         `gorm:"type:varchar(255);column:file_path;comment:文件路径" json:"file_path"`
	IP           string         `gorm:"type:varchar(128);column:ip;index;comment:主机IP" json:"ip"`
	Parameter    string         `gorm:"type:varchar(255);column:parameter;comment:参数，一般是json" json:"parameter"`
	Result       string         `gorm:"type:longtext;column:result;comment:执行结果" json:"result"`
	CreatedBy    string         `gorm:"type:varchar(255);column:created_by;创建者" json:"created_by"`
	OPUser       string         `gorm:"type:varchar(255);column:op_user;comment:执行者" json:"op_user"`
	Status       StatusTask     `gorm:"type:int;index;column:status;comment:任务状态" json:"status"`
	RunTime      *time.Time     `gorm:"type:datetime;index;column:run_time;comment:执行时间" json:"run_time"`
	FinishedTime *time.Time     `gorm:"type:datetime;index;column:finished_time;comment:完成时间" json:"finished_time"`
	CreatedAt    time.Time      `gorm:"column:created_at;index;comment:创建时间" json:"created_at"`
	UpdatedAt    time.Time      `gorm:"column:updated_at;comment:更新时间" json:"updated_at"`
	DeletedAt    gorm.DeletedAt `gorm:"column:deleted_at;index;comment:删除时间" json:"-"`
}

// 返回批量任务表名
func (BatchTask) TableName() string {
	return "task_batch_tasks"
}

// 根据条件获取批量任务列表
func GetBatchTasks(offset, limit int, taskType, ip, keyword *string, status *int) (count int64, tasks []BatchTask, err error) {
	dbop := app.DB()
	if taskType != nil {
		dbop = dbop.Where("task_type = ?", taskType)
	}
	if status != nil {
		dbop = dbop.Where("status = ?", status)
	}
	if ip != nil {
		dbop = dbop.Where("ip = ?", ip)
	}
	if keyword != nil {
		dbop = db.MLike(dbop, *keyword, "name")
	}
	err = dbop.Model(&BatchTask{}).Count(&count).
		Order("updated_at DESC,finished_time DESC,run_time DESC").Offset(offset).Limit(limit).Find(&tasks).Error
	return
}

// 根据ID获取批量任务
func GetBatchTaskByID(id int) (task BatchTask, err error) {
	err = app.DB().Where("id = ?", id).Take(&task).Error
	return
}

// 删除批量任务
func (task BatchTask) Delete() (err error) {
	if task.Status == StatusTaskInit {
		err = app.DB().Delete(&task).Error
		return
	}
	err = ErrBatchTaskRan
	return
}

// 执行批量任务
func (task BatchTask) Run(opuser string) (err error) {
	if task.Status != StatusTaskInit {
		err = ErrBatchTaskRuning
		return
	}
	opResult := app.DB().Model(&task).Where("status = ?", StatusTaskInit).Update("status", StatusTaskReady)
	if opResult.Error != nil {
		err = opResult.Error
		return
	}
	if opResult.RowsAffected == 0 {
		err = ErrTaskUpdateRunningStatFail
		return
	}
	go app.DB().Model(&task).Update("op_user", opuser)
	return
}

// 执行批量任务
func (task BatchTask) ReRun(opuser string) (err error) {
	if task.Status == StatusTaskRunning {
		err = ErrBatchTaskRuning
		return
	}
	opResult := app.DB().Model(&task).Where("status = ?", StatusTaskFailed).Update("status", StatusTaskReady)
	if opResult.Error != nil {
		err = opResult.Error
		return
	}
	if opResult.RowsAffected == 0 {
		err = ErrTaskUpdateRunningStatFail
		return
	}
	go app.DB().Model(&task).Update("op_user", opuser)
	return
}

type BatchTaskForm struct {
	IPs         []string `json:"ips" binding:"required"`
	TemplateIDs []int    `json:"template_ids" binding:"required"`
	Parameter   string   `json:"parameter"`
}

func (form BatchTaskForm) CreateTasks(createdBy string) (err error) {
	templates := []BatchTemplate{}
	err = app.DB().Where("id IN (?)", form.TemplateIDs).Find(&templates, form.TemplateIDs).Error
	if err != nil {
		return
	}
	if len(templates) == 0 {
		return
	}
	tx := app.DB().Begin()
	for _, template := range templates {
		for _, ip := range form.IPs {
			err = tx.Create(&BatchTask{
				Name:      template.Name,
				TaskType:  template.TaskType,
				FilePath:  template.FilePath,
				Parameter: form.Parameter,
				IP:        ip,
				Status:    StatusTaskInit,
				CreatedBy: createdBy,
			}).Error
			if err != nil {
				tx.Rollback()
				return
			}
		}
	}
	err = tx.Commit().Error
	if err != nil {
		tx.Rollback()
		return
	}
	return
}
