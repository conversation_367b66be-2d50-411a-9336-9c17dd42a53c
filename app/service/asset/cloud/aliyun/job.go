package aliyun

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/task"
	"cmdb/pkg/utils/mytime"
	"errors"
	"time"

	"github.com/jinzhu/now"
)

type jobFunc func(a Account) (err error)

var jobTypes = map[string]jobFunc{
	"region": func(a Account) (err error) {
		return a.SyncRegions()
	},
	"ecs": func(a Account) (err error) {
		return a.SyncECSs()
	},
	"eip": func(a Account) (err error) {
		return a.SyncEIPs()
	},
	"loadbalancer": func(a Account) (err error) {
		return a.SyncLoadbanlancers()
	},
	"domain": func(a Account) (err error) {
		return a.SyncDomains()
	},
	"resource-group": func(a Account) (err error) {
		return a.SyncResourceGroups()
	},
	"ddos": func(a Account) (err error) {
		return a.SyncDDoSSet()
	},
	"subnet": func(a Account) (err error) {
		return a.SyncSubnets()
	},
}

func SyncAllAccounts() (err error) {
	accounts := []Account{}
	err = app.DB().Where("cloud_type = ?", asset.AliyunCloudType).Find(&accounts).Error
	if err != nil {
		return
	}
	for _, account := range accounts {
		for jobName, job := range jobTypes {
			err1 := job(account)
			if err1 != nil {
				app.Log().Error("阿里云同步资产异常", "account", account.Name, "job_type", jobName, "err", err1.Error())
			}
		}
	}
	return
}

func SyncAllAccountsMonthlyBills() (err error) {
	accounts := []Account{}
	err = app.DB().Where("cloud_type = ?", asset.AliyunCloudType).Find(&accounts).Error
	if err != nil {
		return
	}
	for _, account := range accounts {
		err = account.SyncMonthlyBills(time.Now().Format("2006-01"))
		if err != nil {
			app.Log().Error("阿里云同步账单异常", "account", account.Name, "err", err.Error())
		}
	}
	return
}

// 同步所有账户的上月账单
func SyncAllAccountsLastMonthMonthlyBills() (err error) {
	accounts := []Account{}
	err = app.DB().Where("cloud_type = ?", asset.AliyunCloudType).Find(&accounts).Error
	if err != nil {
		return
	}
	for _, account := range accounts {
		err = account.SyncMonthlyBills(mytime.GetLastMonth(time.Now()).Format("2006-01"))
		if err != nil {
			app.Log().Error("阿里云同步账单异常", "account", account.Name, "err", err.Error())
		}
	}
	return
}

func (a Account) Sync(assetType string) (err error) {
	if job, ok := jobTypes[assetType]; ok {
		return job(a)
	} else {
		return errors.New("不支持同步")
	}
}

func (a Account) SyncRegions() (err error) {
	err = task.RunJob("sync-aliyun-regions", "同步阿里云regions", func() (result string, err error) {
		rs, err := a.syncRegions()
		result = rs.String()
		return
	})
	return
}

func (a Account) SyncECSs() (err error) {
	err = task.RunJob("sync-aliyun-ecs", "同步阿里云ecs", func() (result string, err error) {
		rs, err := a.syncECSs()
		result = rs.String()
		return
	})
	return
}

func (a Account) SyncEIPs() (err error) {
	err = task.RunJob("sync-aliyun-eip", "同步阿里云eip", func() (result string, err error) {
		rs, err := a.syncEIPs()
		result = rs.String()
		return
	})
	return
}

func (a Account) SyncLoadbanlancers() (err error) {
	err = task.RunJob("sync-aliyun-loadbancers", "同步阿里云负载均衡", func() (result string, err error) {
		rs, err := a.syncLoadbanlancers()
		result = rs.String()
		return
	})
	return
}

func (a Account) SyncDomains() (err error) {
	err = task.RunJob("sync-aliyun-domains", "同步阿里云DNS", func() (result string, err error) {
		rs, err := a.syncPrivateDomains()
		result = "内网域名同步：" + rs.String() + "\n"
		if err != nil {
			return
		}
		rs, err = a.syncPrivateDomainsRecords()
		result += "内网域名解析同步：" + rs.String() + "\n"
		if err != nil {
			return
		}
		rs, err = a.syncPublicDomains()
		result = "外网域名同步：" + rs.String() + "\n"
		if err != nil {
			return
		}
		rs, err = a.syncPublicDomainRecords()
		result = "外网域名解析同步：" + rs.String() + "\n"
		return
	})
	return
}

func (a Account) SyncResourceGroups() (err error) {
	err = task.RunJob("sync-aliyun-resource-groups", "同步阿里云资源组", func() (result string, err error) {
		rs, err := a.syncResourceGroups()
		result = rs
		return
	})
	return
}

func (a Account) SyncSubnets() (err error) {
	err = task.RunJob("sync-aliyun-subnets", "同步阿里云子网", func() (result string, err error) {
		rs, err := a.syncVSwitches()
		result = rs.String()
		return
	})
	return
}

func (a Account) SyncMonthlyBills(billCycle string) (err error) {
	err = task.RunJob("sync-aliyun-monthly-bills", "同步阿里云账单", func() (result string, err error) {
		rs, err := a.syncMonthlyBills(billCycle)
		result = rs
		return
	})
	return
}

func (a Account) SyncDDoSSet() (err error) {
	err = task.RunJob("sync-aliyun-ddos", "同步阿里云DDoS防护", func() (result string, err error) {
		rs, err := a.syncDDoSSet()
		result = rs.String()
		return
	})
	return
}

func SyncAllAccountsDDoSDomainBps() (err error) {
	accounts := []Account{}
	err = app.DB().Where("cloud_type = ?", asset.AliyunCloudType).Find(&accounts).Error
	if err != nil {
		return
	}
	yesterday := now.With(time.Now().AddDate(0, 0, -1)).BeginningOfDay()
	for _, account := range accounts {
		rs, err := account.syncDDoSDomainBps(yesterday)
		if err != nil {
			app.Log().Error("阿里云同步DDoS域名带宽异常", "account", account.Name, "err", err.Error())
		} else {
			app.Log().Info("阿里云同步DDoS域名带宽", "account", account.Name, "rs", rs)
		}
	}
	return
}
