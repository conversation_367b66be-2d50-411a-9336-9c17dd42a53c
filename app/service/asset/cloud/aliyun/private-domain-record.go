package aliyun

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/asset/cloud"
	"cmdb/pkg/utils"
	"errors"
	"strconv"
	"time"

	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/requests"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/pvtz"
	"gorm.io/gorm"
)

func (a Account) syncPrivateDomainsRecords() (result cloud.SyncResults, err error) {
	zones := []asset.PrivateDomain{}
	err = app.DB().Where("account_id = ? ", a.ID).Find(&zones).Error
	if err != nil {
		return
	}
	for _, zone := range zones {
		r, err := a.syncPrivateDomainRecords(zone.DomainID, 1)
		sr := cloud.SyncResult{
			Key: zone.Name + "(" + zone.DomainID + ")",
		}
		if err == nil {
			sr.Result = "同步成功"
			// 回收过期主机
			err = asset.ExpiredPrivateDomainRecords(zone.DomainID, time.Now().Add(-10*time.Minute))
			if err != nil {
				sr.Result = "同步成功，但是回收Domain失败" + err.Error()
			}
		} else {
			sr.Result = r + " " + err.Error()
		}
		result = append(result, sr)
	}

	return
}

func (a Account) syncPrivateDomainRecords(zoneID string, page int) (result string, err error) {
	accessSecret, _ := utils.Base64Decode(a.AccessSecret)
	client, err := pvtz.NewClientWithAccessKey("cn-beijing", a.AccessKey, accessSecret)
	if err != nil {
		result = "创建客户端失败"
		return
	}
	request := pvtz.CreateDescribeZoneRecordsRequest()
	request.Scheme = "https"
	request.PageSize = requests.NewInteger(100)
	if page < 1 {
		page = 1
	}
	request.PageNumber = requests.NewInteger(page)
	request.ZoneId = zoneID
	response, err := client.DescribeZoneRecords(request)
	if err != nil {
		return
	}
	now := time.Now()
	for _, record := range response.Records.Record {
		exist := asset.PrivateDomainRecord{}
		err = app.DB().Where("domain_id = ? AND account_id = ? AND record_id = ?", zoneID, a.ID, strconv.FormatInt(record.RecordId, 10)).Take(&exist).Error
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = app.DB().Create(&asset.PrivateDomainRecord{
				AccountID: a.ID,
				DomainID:  zoneID,
				RecordID:  strconv.FormatInt(record.RecordId, 10),
				Rr:        record.Rr,
				Value:     record.Value,
				Remark:    record.Remark,
				TTL:       record.Ttl,
				Type:      record.Type,
				Line:      record.Line,
				Status:    record.Status,
				CreatedAt: time.UnixMilli(record.CreateTimestamp),
				UpdatedAt: time.UnixMilli(record.UpdateTimestamp),
				SyncTime:  now,
			}).Error
		} else if err == nil {
			updateMap := map[string]any{
				"sync_time": now,
			}
			if exist.CreatedAt != time.UnixMilli(record.CreateTimestamp) {
				updateMap["created_at"] = time.UnixMilli(record.CreateTimestamp)
			}
			if exist.UpdatedAt != time.UnixMilli(record.UpdateTimestamp) {
				updateMap["updated_at"] = time.UnixMilli(record.UpdateTimestamp)
			}
			if exist.Rr != record.Rr {
				updateMap["rr"] = record.Rr
			}
			if exist.Value != record.Value {
				updateMap["value"] = record.Value
			}
			if exist.Remark != record.Remark {
				updateMap["remark"] = record.Remark
			}
			if exist.TTL != record.Ttl {
				updateMap["ttl"] = record.Ttl
			}
			if exist.Type != record.Type {
				updateMap["type"] = record.Type
			}
			if exist.Status != record.Status {
				updateMap["status"] = record.Status
			}
			err = app.DB().Model(&exist).Updates(updateMap).Error
		}
		if err != nil {
			return
		}
	}
	if response.TotalItems > page*100 {
		result, err = a.syncPrivateDomainRecords(zoneID, page+1)
	}
	return
}
