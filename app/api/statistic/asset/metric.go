package asset

import (
	"cmdb/app"
	"cmdb/app/service/statistic/asset"
	"strconv"

	"github.com/gin-gonic/gin"
)

// GetResourceMetrics 获取资源指标历史趋势
// @Summary 获取资源指标历史趋势
// @Description 获取指定资源类型、指标类型、资源名称和日期范围的指标历史趋势
// @Tags 资源指标
// @Accept json
// @Produce json
// @Param resource_type query string true "资源类型，可选值为 computer, cloud, region, data, network, optimizable"
// @Param metric_type query string true "指标类型，根据资源类型不同而不同"
// @Param name query string false "资源名称，当resource_type为cloud时表示云类型名称，当resource_type为region时表示区域名称，当resource_type为network时表示域名"
// @Param start_date query string true "开始日期，格式为 YYYY-MM-DD"
// @Param end_date query string true "结束日期，格式为 YYYY-MM-DD"
// @Success 200 {object} app.Response
// @Router /api/statistic/asset/metrics [get]
func GetResourceMetrics(c *gin.Context) {
	// 获取参数
	resourceType := c.Query("resource_type")
	metricType := c.Query("metric_type")
	name := c.Query("name")
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")

	// 验证参数
	if resourceType == "" {
		app.FailedResponseMsg(c, "资源类型不能为空")
		return
	}

	if metricType == "" {
		app.FailedResponseMsg(c, "指标类型不能为空")
		return
	}

	if startDate == "" {
		app.FailedResponseMsg(c, "开始日期不能为空")
		return
	}

	if endDate == "" {
		app.FailedResponseMsg(c, "结束日期不能为空")
		return
	}

	// 当资源类型为cloud或region时，name不能为空
	if (resourceType == "cloud" || resourceType == "region") && name == "" {
		app.FailedResponseMsg(c, "资源名称不能为空")
		return
	}

	// 调用服务层函数
	metrics, err := asset.GetResourceMetrics(asset.ResourceType(resourceType), asset.MetricType(metricType), name, startDate, endDate)
	if err != nil {
		app.FailedResponseMsg(c, err.Error())
		return
	}

	// 返回结果
	app.SuccessResponseData(c, metrics)
}

// GetLatestResourceMetric 获取最新的资源指标值
// @Summary 获取最新的资源指标值
// @Description 获取指定资源类型、指标类型和资源名称的最新指标值
// @Tags 资源指标
// @Accept json
// @Produce json
// @Param resource_type query string true "资源类型，可选值为 computer, cloud, region, data, network, optimizable"
// @Param metric_type query string true "指标类型，根据资源类型不同而不同"
// @Param name query string false "资源名称，当resource_type为cloud时表示云类型名称，当resource_type为region时表示区域名称，当resource_type为network时表示域名"
// @Success 200 {object} app.Response
// @Router /api/statistic/asset/metrics/latest [get]
func GetLatestResourceMetric(c *gin.Context) {
	// 获取参数
	resourceType := c.Query("resource_type")
	metricType := c.Query("metric_type")
	name := c.Query("name")

	// 验证参数
	if resourceType == "" {
		app.FailedResponseMsg(c, "资源类型不能为空")
		return
	}

	if metricType == "" {
		app.FailedResponseMsg(c, "指标类型不能为空")
		return
	}

	// 当资源类型为cloud或region时，name不能为空
	if (resourceType == "cloud" || resourceType == "region") && name == "" {
		app.FailedResponseMsg(c, "资源名称不能为空")
		return
	}

	// 调用服务层函数
	value, err := asset.GetLatestResourceMetric(asset.ResourceType(resourceType), asset.MetricType(metricType), name)
	if err != nil {
		app.FailedResponseMsg(c, err.Error())
		return
	}

	// 返回结果
	app.SuccessResponseData(c, value)
}

// GetResourceMetricsByDateRange 获取指定日期范围内的资源指标历史趋势
// @Summary 获取指定日期范围内的资源指标历史趋势
// @Description 获取指定资源类型、指标类型、资源名称和天数的指标历史趋势
// @Tags 资源指标
// @Accept json
// @Produce json
// @Param resource_type query string true "资源类型，可选值为 computer, cloud, region, data, network, optimizable"
// @Param metric_type query string true "指标类型，根据资源类型不同而不同"
// @Param name query string false "资源名称，当resource_type为cloud时表示云类型名称，当resource_type为region时表示区域名称，当resource_type为network时表示域名"
// @Param days query int true "天数，表示获取最近多少天的数据"
// @Success 200 {object} app.Response
// @Router /api/statistic/asset/metrics/range [get]
func GetResourceMetricsByDateRange(c *gin.Context) {
	// 获取参数
	resourceType := c.Query("resource_type")
	metricType := c.Query("metric_type")
	name := c.Query("name")
	daysStr := c.Query("days")

	// 验证参数
	if resourceType == "" {
		app.FailedResponseMsg(c, "资源类型不能为空")
		return
	}

	if metricType == "" {
		app.FailedResponseMsg(c, "指标类型不能为空")
		return
	}

	if daysStr == "" {
		app.FailedResponseMsg(c, "天数不能为空")
		return
	}

	// 当资源类型为cloud或region时，name不能为空
	if (resourceType == "cloud" || resourceType == "region") && name == "" {
		app.FailedResponseMsg(c, "资源名称不能为空")
		return
	}

	// 将天数转换为整数
	days, err := strconv.Atoi(daysStr)
	if err != nil {
		app.FailedResponseMsg(c, "天数必须为整数")
		return
	}

	// 调用服务层函数
	metrics, err := asset.GetResourceMetricsByDateRange(asset.ResourceType(resourceType), asset.MetricType(metricType), name, days)
	if err != nil {
		app.FailedResponseMsg(c, err.Error())
		return
	}

	// 返回结果
	app.SuccessResponseData(c, metrics)
}

// GetAllCloudTypes 获取所有云类型
// @Summary 获取所有云类型
// @Description 获取所有云类型
// @Tags 资源指标
// @Accept json
// @Produce json
// @Success 200 {object} app.Response
// @Router /api/statistic/asset/metrics/cloud-types [get]
func GetAllCloudTypes(c *gin.Context) {
	// 调用服务层函数
	cloudTypes, err := asset.GetAllCloudTypes()
	if err != nil {
		app.FailedResponseMsg(c, err.Error())
		return
	}

	// 返回结果
	app.SuccessResponseData(c, cloudTypes)
}

// GetAllRegionNames 获取所有区域名称
// @Summary 获取所有区域名称
// @Description 获取所有区域名称
// @Tags 资源指标
// @Accept json
// @Produce json
// @Success 200 {object} app.Response
// @Router /api/statistic/asset/metrics/region-names [get]
func GetAllRegionNames(c *gin.Context) {
	// 调用服务层函数
	regionNames, err := asset.GetAllRegionNames()
	if err != nil {
		app.FailedResponseMsg(c, err.Error())
		return
	}

	// 返回结果
	app.SuccessResponseData(c, regionNames)
}

// GetAllDomainNames 获取所有域名
// @Summary 获取所有域名
// @Description 获取所有域名
// @Tags 资源指标
// @Accept json
// @Produce json
// @Success 200 {object} app.Response
// @Router /api/statistic/asset/metrics/domain-names [get]
func GetAllDomainNames(c *gin.Context) {
	// 调用服务层函数
	domainNames, err := asset.GetAllDomainNames()
	if err != nil {
		app.FailedResponseMsg(c, err.Error())
		return
	}

	// 返回结果
	app.SuccessResponseData(c, domainNames)
}

// GetAllDataResourceTypes 获取所有数据资源类型
// @Summary 获取所有数据资源类型
// @Description 获取所有数据资源类型
// @Tags 资源指标
// @Accept json
// @Produce json
// @Success 200 {object} app.Response
// @Router /api/statistic/asset/metrics/data-resource-types [get]
func GetAllDataResourceTypes(c *gin.Context) {
	// 调用服务层函数
	dataResourceTypes, err := asset.GetAllDataResourceTypes()
	if err != nil {
		app.FailedResponseMsg(c, err.Error())
		return
	}

	// 将 MetricType 转换为字符串
	result := make([]string, len(dataResourceTypes))
	for i, t := range dataResourceTypes {
		result[i] = string(t)
	}

	// 返回结果
	app.SuccessResponseData(c, result)
}

// GetAllOptimizableResourceTypes 获取所有可优化资源类型
// @Summary 获取所有可优化资源类型
// @Description 获取所有可优化资源类型
// @Tags 资源指标
// @Accept json
// @Produce json
// @Success 200 {object} app.Response
// @Router /api/statistic/asset/metrics/optimizable-resource-types [get]
func GetAllOptimizableResourceTypes(c *gin.Context) {
	// 调用服务层函数
	optimizableResourceTypes, err := asset.GetAllOptimizableResourceTypes()
	if err != nil {
		app.FailedResponseMsg(c, err.Error())
		return
	}

	// 将 MetricType 转换为字符串
	result := make([]string, len(optimizableResourceTypes))
	for i, t := range optimizableResourceTypes {
		result[i] = string(t)
	}

	// 返回结果
	app.SuccessResponseData(c, result)
}
