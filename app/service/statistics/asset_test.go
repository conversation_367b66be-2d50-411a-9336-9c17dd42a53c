package statistics

import (
	"cmdb/app"
	"testing"
)

func TestStataAssets(t *testing.T) {
	err := app.NewApp("../../../app.ini")
	if err != nil {
		t.Fatal(err)
	}
	err = app.ConnectDB()
	if err != nil {
		t.<PERSON><PERSON>(err)
	}
	defer func() {
		dbop, err := app.DB().DB()
		if err != nil {
			t.<PERSON>al(err)
		}
		err = dbop.Close()
		if err != nil {
			t.<PERSON>al(err)
		}
	}()
	// err = StataAssets(now.BeginningOfMonth().Format("2006-01"))
	storageTotal, err := statDliStorage()
	if err != nil {
		t.<PERSON>al(err)
	}
	app.Log().Info("统计柚先森DLI存储容量", "容量", storageTotal)
}
