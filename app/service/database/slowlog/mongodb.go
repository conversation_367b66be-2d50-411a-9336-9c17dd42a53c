package slowlog

import "time"

// MongoDBSlowLog 生产mongodb慢日志表结构
type MongoDBSlowLog struct {
	ID                int64     `gorm:"column:id;primaryKey;autoIncrement;comment:'自增字段'" json:"id"`
	DBInstanceID      string    `gorm:"column:db_instance_id;not null;default:'';comment:'数据库实例id'" json:"db_instance_id"`
	DBPrivateIP       string    `gorm:"column:db_private_ip;not null;default:'';comment:'数据库内网ip'" json:"db_private_ip"`
	DBTitle           string    `gorm:"column:db_title;not null;default:'';comment:'数据库名称'" json:"db_title"`
	DBPort            int       `gorm:"column:db_port;not null;default:27017;comment:'数据库端口'" json:"db_port"`
	DBProjectName     string    `gorm:"column:db_project_name;not null;default:'';comment:'数据库实例企业项目名称'" json:"db_project_name"`
	CloudType         string    `gorm:"column:cloud_type;not null;default:'';comment:'所属cloud,如huaweiyun,aliyun'" json:"cloud_type"`
	QueryType         string    `gorm:"column:query_type;not null;default:'';comment:'语句类型'" json:"query_type"`
	QueryTime         int       `gorm:"column:query_time;not null;default:0;comment:'语句执行耗时'" json:"query_time"`
	QueryDocsExamined int       `gorm:"column:query_docs_examined;not null;default:0;comment:'扫描文档数'" json:"query_docs_examined"`
	QueryDatabase     string    `gorm:"column:query_database;not null;default:'';comment:'所属数据库'" json:"query_database"`
	QueryCollection   string    `gorm:"column:query_collection;not null;default:'';comment:'所属数据库表'" json:"query_collection"`
	QueryCommand      string    `gorm:"column:query_command;type:text;not null;comment:'执行语句'" json:"query_command"`
	QueryStartTime    string    `gorm:"column:query_start_time;not null;default:'';comment:'语句执行时间'" json:"query_start_time"`
	CreateTime        time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP;comment:'当前时间'" json:"create_time"`
}

// TableName 设置表名
func (m *MongoDBSlowLog) TableName() string {
	return "t_prod_mongodb_slowlog"
}

// MongoDBSlowLogStatistics 生产环境mongodb慢查询统计表结构
type MongoDBSlowLogStatistics struct {
	ID            int64     `gorm:"column:id;primaryKey;autoIncrement;comment:'自增字段'" json:"id"`
	DBInstanceID  string    `gorm:"column:db_instance_id;not null;default:'';comment:'数据库实例id'" json:"db_instance_id"`
	DBPrivateIP   string    `gorm:"column:db_private_ip;not null;default:'';comment:'数据库内网ip'" json:"db_private_ip"`
	DBPort        int       `gorm:"column:db_port;not null;default:27017;comment:'数据库端口'" json:"db_port"`
	DBTitle       string    `gorm:"column:db_title;not null;default:'';comment:'数据库标签'" json:"db_title"`
	DBProjectName string    `gorm:"column:db_project_name;not null;default:'';comment:'数据库实例企业项目名称'" json:"db_project_name"`
	TotalCount    int       `gorm:"column:total_count;not null;default:0;comment:'总慢查询数'" json:"total_count"`
	CreateTime    time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP;comment:'当前时间'" json:"create_time"`
}

// TableName 设置表名
func (m *MongoDBSlowLogStatistics) TableName() string {
	return "t_prod_mongodbslowlog_statistics"
}
