package statistics

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/asset/cloud/hwcloud"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"
)

type CloudComputerAsset struct {
	ID                uint   `gorm:"column:id;primaryKey;comment:主键id" json:"id"`
	Month             string `gorm:"column:month;index;comment:统计月份" json:"month"`
	Name              string `gorm:"column:name;comment:名称" json:"name"`
	HostTotal         int64  `gorm:"column:host_total;comment:主机总数" json:"host_total"`
	HostTotalChange   int64  `gorm:"column:host_total_change;comment:主机变化数" json:"host_total_change"`
	MemoryTotal       int64  `gorm:"column:memory_total;comment:内存总数" json:"memory_total"`
	MemoryTotalChange int64  `gorm:"column:memory_total_change;comment:内存变化数" json:"memory_total_change"`
	CPUTotal          int64  `gorm:"column:cpu_total;comment:CPU总数" json:"cpu_total"`
	CPUTotalChange    int64  `gorm:"column:cpu_total_change;comment:CPU变化数" json:"cpu_total_change"`
	GPUTotal          int64  `gorm:"column:gpu_total;comment:GPU总数" json:"gpu_total"`
	GPUTotalChange    int64  `gorm:"column:gpu_total_change;comment:GPU变化数" json:"gpu_total_change"`
}

func (CloudComputerAsset) TableName() string {
	return "statistics_cloud_computer_assets"
}

type MetricsItem struct {
	Metric float64 `gorm:"column:metric" json:"metric"`
	Month  string  `gorm:"column:month" json:"month"`
}

func GetCloudComputerMetrics(start, end, computerType, cloudType string) (data []MetricsItem, err error) {
	if computerType != "host_total" && computerType != "memory_total" && computerType != "cpu_total" && computerType != "gpu_total" {
		err = errors.New("不支持的资产类型")
		return
	}
	sql := fmt.Sprintf("select %s AS metric, month from statistics_cloud_computer_assets where month >= ? and month <= ? AND name = ?", computerType)
	err = app.DB().Raw(sql, start, end, cloudType).Scan(&data).Error
	return
}

func (s *CloudComputerAsset) Compute() {
	monthDate, err := time.Parse("2006-01", s.Month)
	if err != nil {
		return
	}
	lastMonthDate := monthDate.AddDate(0, -1, 0).Format("2006-01")
	var exist CloudComputerAsset
	err = app.DB().Model(&CloudComputerAsset{}).Where("month = ? AND name = ?", lastMonthDate, s.Name).Take(&exist).Error
	if err != nil {
		return
	}
	s.HostTotalChange = s.HostTotal - exist.HostTotal
	s.MemoryTotalChange = s.MemoryTotal - exist.MemoryTotal
	s.CPUTotalChange = s.CPUTotal - exist.CPUTotal
	s.GPUTotalChange = s.GPUTotal - exist.GPUTotal
}

type RegionComputerAsset struct {
	ID                uint   `gorm:"column:id;primaryKey;comment:主键id" json:"id"`
	Month             string `gorm:"column:month;index;comment:统计月份" json:"month"`
	Name              string `gorm:"column:name;comment:名称" json:"name"`
	HostTotal         int64  `gorm:"column:host_total;comment:主机总数" json:"host_total"`
	HostTotalChange   int64  `gorm:"column:host_total_change;comment:主机变化数" json:"host_total_change"`
	MemoryTotal       int64  `gorm:"column:memory_total;comment:内存总数" json:"memory_total"`
	MemoryTotalChange int64  `gorm:"column:memory_total_change;comment:内存变化数" json:"memory_total_change"`
	CPUTotal          int64  `gorm:"column:cpu_total;comment:CPU总数" json:"cpu_total"`
	CPUTotalChange    int64  `gorm:"column:cpu_total_change;comment:CPU变化数" json:"cpu_total_change"`
	GPUTotal          int64  `gorm:"column:gpu_total;comment:GPU总数" json:"gpu_total"`
	GPUTotalChange    int64  `gorm:"column:gpu_total_change;comment:GPU变化数" json:"gpu_total_change"`
}

func (RegionComputerAsset) TableName() string {
	return "statistics_region_computer_assets"
}

func GetRegionComputerMetrics(start, end string, region, computerType string) (data []MetricsItem, err error) {
	if computerType != "host_total" && computerType != "memory_total" && computerType != "cpu_total" && computerType != "gpu_total" {
		err = errors.New("不支持的数据类型")
		return
	}
	sql := fmt.Sprintf("select %s AS metric, month from statistics_region_computer_assets where name = ? and month >= ? and month <= ?", computerType)
	err = app.DB().Raw(sql, region, start, end).Scan(&data).Error
	return
}

func (s *RegionComputerAsset) Compute() {
	monthDate, err := time.Parse("2006-01", s.Month)
	if err != nil {
		return
	}
	lastMonthDate := monthDate.AddDate(0, -1, 0).Format("2006-01")
	var exist RegionComputerAsset
	err = app.DB().Model(&RegionComputerAsset{}).Where("month = ? AND name = ?", lastMonthDate, s.Name).Take(&exist).Error
	if err != nil {
		return
	}
	s.HostTotalChange = s.HostTotal - exist.HostTotal
	s.MemoryTotalChange = s.MemoryTotal - exist.MemoryTotal
	s.CPUTotalChange = s.CPUTotal - exist.CPUTotal
	s.GPUTotalChange = s.GPUTotal - exist.GPUTotal
}

type ComputerAsset struct {
	ID                uint                  `gorm:"column:id;primaryKey;comment:主键id" json:"id"`
	Month             string                `gorm:"column:month;index;comment:统计月份" json:"month"`
	HostTotal         int64                 `gorm:"column:host_total;comment:主机总数" json:"host_total"`
	HostTotalChange   int64                 `gorm:"column:host_total_change;comment:主机变化数" json:"host_total_change"`
	MemoryTotal       int64                 `gorm:"column:memory_total;comment:内存总数" json:"memory_total"`
	MemoryTotalChange int64                 `gorm:"column:memory_total_change;comment:内存变化数" json:"memory_total_change"`
	CPUTotal          int64                 `gorm:"column:cpu_total;comment:CPU总数" json:"cpu_total"`
	CPUTotalChange    int64                 `gorm:"column:cpu_total_change;comment:CPU变化数" json:"cpu_total_change"`
	GPUTotal          int64                 `gorm:"column:gpu_total;comment:GPU总数" json:"gpu_total"`
	GPUTotalChange    int64                 `gorm:"column:gpu_total_change;comment:GPU变化数" json:"gpu_total_change"`
	DliCPUTotal       int64                 `gorm:"column:dli_cpu_total;comment:带宽总数" json:"dli_cpu_total"`
	DliCPUTotalChange int64                 `gorm:"column:dli_cpu_total_change;comment:带宽变化数" json:"dli_cpu_total_change"`
	CloudStats        []CloudComputerAsset  `gorm:"-" json:"cloud_stats"`
	RegionStats       []RegionComputerAsset `gorm:"-" json:"region_stats"`
}

func (ComputerAsset) TableName() string {
	return "statistics_computer_assets"
}

func GetComputerMetrics(start, end string, computerType string) (data []MetricsItem, err error) {
	if computerType != "host_total" && computerType != "memory_total" && computerType != "cpu_total" && computerType != "gpu_total" && computerType != "dli_cpu_total" {
		err = errors.New("不支持的数据类型")
		return
	}
	sql := fmt.Sprintf("select %s AS metric, month from statistics_computer_assets where month >= ? and month <= ?", computerType)
	err = app.DB().Raw(sql, start, end).Scan(&data).Error
	return
}

func (s *ComputerAsset) Compute() {
	monthDate, err := time.Parse("2006-01", s.Month)
	if err != nil {
		return
	}
	lastMonthDate := monthDate.AddDate(0, -1, 0).Format("2006-01")
	var exist ComputerAsset
	err = app.DB().Model(&ComputerAsset{}).Where("month = ? ", lastMonthDate).Take(&exist).Error
	if err != nil {
		return
	}
	s.HostTotalChange = s.HostTotal - exist.HostTotal
	s.MemoryTotalChange = s.MemoryTotal - exist.MemoryTotal
	s.CPUTotalChange = s.CPUTotal - exist.CPUTotal
	s.GPUTotalChange = s.GPUTotal - exist.GPUTotal
	s.DliCPUTotalChange = s.DliCPUTotal - exist.DliCPUTotal
	for i := range s.CloudStats {
		s.CloudStats[i].Compute()
	}
	for i := range s.RegionStats {
		s.RegionStats[i].Compute()
	}
}

type HostStat struct {
	Count   int64 `gorm:"column:count" json:"count"`
	CPUCore int64 `gorm:"column:cpu_core" json:"cpu_core"`
	Memory  int64 `gorm:"column:memory" json:"memory"`
	GPU     int64 `gorm:"column:gpu" json:"gpu"`
}

type HostCloudStat struct {
	HostType asset.CloudType `gorm:"column:host_type" json:"host_type"`
	Count    int64           `gorm:"column:count" json:"count"`
	CPUCore  int64           `gorm:"column:cpu_core" json:"cpu_core"`
	Memory   int64           `gorm:"column:memory" json:"memory"`
	GPU      int64           `gorm:"column:gpu" json:"gpu"`
}

func statComputerAssets(month string) (err error) {
	data := ComputerAsset{
		Month:       month,
		HostTotal:   0,
		MemoryTotal: 0,
		CPUTotal:    0,
		GPUTotal:    0,
	}
	// 主机统计
	var hostStat HostStat
	err = app.DB().Model(&asset.Host{}).Select("count(id) AS count , sum(cpu_thread) AS cpu_core , sum(memory) AS memory , sum(gpu_amount) AS gpu").Where("host_type > ? AND charge_type = ?", asset.PhysicalHostType, asset.PrepaidChargeType).Take(&hostStat).Error
	if err != nil {
		return
	}
	data.CPUTotal = hostStat.CPUCore
	data.MemoryTotal = hostStat.Memory
	data.HostTotal = hostStat.Count
	data.GPUTotal = hostStat.GPU
	// 云主机分组统计
	var cloudStat []HostCloudStat
	err = app.DB().Model(&asset.Host{}).Select("host_type, count(id) AS count , sum(cpu_thread) AS cpu_core , sum(memory) AS memory , sum(gpu_amount) AS gpu").Where("host_type > ? AND charge_type = ?", asset.PhysicalHostType, asset.PrepaidChargeType).Group("host_type").Find(&cloudStat).Error
	if err != nil {
		return
	}

	data.CloudStats = make([]CloudComputerAsset, len(cloudStat))
	for i := range cloudStat {
		data.CloudStats[i] = CloudComputerAsset{
			Month:       month,
			Name:        cloudStat[i].HostType.String(),
			HostTotal:   cloudStat[i].Count,
			MemoryTotal: cloudStat[i].Memory,
			CPUTotal:    cloudStat[i].CPUCore,
			GPUTotal:    cloudStat[i].GPU,
		}
	}
	cnDatacenterID := []uint{}
	err = app.DB().Model(&asset.Datacenter{}).Where("code like ?", "cn-%").Select("id").Scan(&cnDatacenterID).Error
	if err != nil {
		return
	}
	var cnHostStat, intlHostStat HostStat
	err = app.DB().Model(&asset.Host{}).Select("count(id) AS count , sum(cpu_thread) AS cpu_core , sum(memory) AS memory , sum(gpu_amount) AS gpu").Where("host_type > ? AND datacenter_id in (?) AND charge_type = ?", asset.PhysicalHostType, cnDatacenterID, asset.PrepaidChargeType).Take(&cnHostStat).Error
	if err != nil {
		return
	}
	data.RegionStats = []RegionComputerAsset{}
	data.RegionStats = append(data.RegionStats, RegionComputerAsset{
		Month:       month,
		Name:        "国内",
		HostTotal:   cnHostStat.Count,
		MemoryTotal: cnHostStat.Memory,
		CPUTotal:    cnHostStat.CPUCore,
		GPUTotal:    cnHostStat.GPU,
	})
	err = app.DB().Model(&asset.Host{}).Select("count(id) AS count , sum(cpu_thread) AS cpu_core , sum(memory) AS memory , sum(gpu_amount) AS gpu").Where("host_type > ? AND datacenter_id not in (?) AND charge_type = ?", asset.PhysicalHostType, cnDatacenterID, asset.PrepaidChargeType).Take(&intlHostStat).Error
	if err != nil {
		return
	}
	data.RegionStats = append(data.RegionStats, RegionComputerAsset{
		Month:       month,
		Name:        "国际",
		HostTotal:   intlHostStat.Count,
		MemoryTotal: intlHostStat.Memory,
		CPUTotal:    intlHostStat.CPUCore,
		GPUTotal:    intlHostStat.GPU,
	})
	cpuTotal, err := hwcloud.GetAllDILCPUCount()
	if err != nil {
		return
	}
	data.DliCPUTotal = int64(cpuTotal)
	data.Compute()
	dbop := app.DB().Begin()
	defer func() {
		if err != nil {
			err = dbop.Rollback().Error
		} else {
			err = dbop.Commit().Error
		}
	}()
	var exist ComputerAsset
	err = dbop.Where("month = ? ", month).Take(&exist).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = dbop.Create(&data).Error
		if err != nil {
			return
		}
	} else {
		updateMap := map[string]any{}
		if exist.CPUTotal != data.CPUTotal {
			updateMap["cpu_total"] = data.CPUTotal
		}
		if exist.CPUTotalChange != data.CPUTotalChange {
			updateMap["cpu_total_change"] = data.CPUTotalChange
		}
		if exist.MemoryTotal != data.MemoryTotal {
			updateMap["memory_total"] = data.MemoryTotal
		}
		if exist.MemoryTotalChange != data.MemoryTotalChange {
			updateMap["memory_total_change"] = data.MemoryTotalChange
		}
		if exist.GPUTotal != data.GPUTotal {
			updateMap["gpu_total"] = data.GPUTotal
		}
		if exist.GPUTotalChange != data.GPUTotalChange {
			updateMap["gpu_total_change"] = data.GPUTotalChange
		}
		if exist.HostTotal != data.HostTotal {
			updateMap["host_total"] = data.HostTotal
		}
		if exist.HostTotalChange != data.HostTotalChange {
			updateMap["host_total_change"] = data.HostTotalChange
		}
		if exist.DliCPUTotal != data.DliCPUTotal {
			updateMap["dli_cpu_total"] = data.DliCPUTotal
		}
		if exist.DliCPUTotalChange != data.DliCPUTotalChange {
			updateMap["dli_cpu_total_change"] = data.DliCPUTotalChange
		}
		if len(updateMap) > 0 {
			err = dbop.Model(&exist).Updates(updateMap).Error
			if err != nil {
				return
			}
		}
	}
	for i := range data.CloudStats {
		exist := CloudComputerAsset{}
		err = dbop.Where("month = ? AND name = ?", month, data.CloudStats[i].Name).Take(&exist).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = dbop.Create(&CloudComputerAsset{
				Month:             month,
				Name:              data.CloudStats[i].Name,
				HostTotal:         data.CloudStats[i].HostTotal,
				HostTotalChange:   data.CloudStats[i].HostTotalChange,
				MemoryTotal:       data.CloudStats[i].MemoryTotal,
				MemoryTotalChange: data.CloudStats[i].MemoryTotalChange,
				CPUTotal:          data.CloudStats[i].CPUTotal,
				CPUTotalChange:    data.CloudStats[i].CPUTotalChange,
				GPUTotal:          data.CloudStats[i].GPUTotal,
				GPUTotalChange:    data.CloudStats[i].GPUTotalChange,
			}).Error
			if err != nil {
				return
			}
		} else {
			updateMap := map[string]any{}
			if exist.HostTotal != data.CloudStats[i].HostTotal {
				updateMap["host_total"] = data.CloudStats[i].HostTotal
			}
			if exist.HostTotalChange != data.CloudStats[i].HostTotalChange {
				updateMap["host_total_change"] = data.CloudStats[i].HostTotalChange
			}
			if exist.MemoryTotal != data.CloudStats[i].MemoryTotal {
				updateMap["memory_total"] = data.CloudStats[i].MemoryTotal
			}
			if exist.MemoryTotalChange != data.CloudStats[i].MemoryTotalChange {
				updateMap["memory_total_change"] = data.CloudStats[i].MemoryTotalChange
			}
			if exist.CPUTotal != data.CloudStats[i].CPUTotal {
				updateMap["cpu_total"] = data.CloudStats[i].CPUTotal
			}
			if exist.CPUTotalChange != data.CloudStats[i].CPUTotalChange {
				updateMap["cpu_total_change"] = data.CloudStats[i].CPUTotalChange
			}
			if exist.GPUTotal != data.CloudStats[i].GPUTotal {
				updateMap["gpu_total"] = data.CloudStats[i].GPUTotal
			}
			if exist.GPUTotalChange != data.CloudStats[i].GPUTotalChange {
				updateMap["gpu_total_change"] = data.CloudStats[i].GPUTotalChange
			}
			if len(updateMap) > 0 {
				err = dbop.Model(&exist).Updates(updateMap).Error
				if err != nil {
					return
				}
			}
		}
	}

	for i := range data.RegionStats {
		exist := RegionComputerAsset{}
		err = dbop.Where("month = ? AND name = ?", month, data.RegionStats[i].Name).Take(&exist).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = dbop.Create(&RegionComputerAsset{
				Month:             month,
				Name:              data.RegionStats[i].Name,
				HostTotal:         data.RegionStats[i].HostTotal,
				HostTotalChange:   data.RegionStats[i].HostTotalChange,
				MemoryTotal:       data.RegionStats[i].MemoryTotal,
				MemoryTotalChange: data.RegionStats[i].MemoryTotalChange,
				CPUTotal:          data.RegionStats[i].CPUTotal,
				CPUTotalChange:    data.RegionStats[i].CPUTotalChange,
				GPUTotal:          data.RegionStats[i].GPUTotal,
				GPUTotalChange:    data.RegionStats[i].GPUTotalChange,
			}).Error
			if err != nil {
				return
			}
		} else {
			updateMap := map[string]any{}
			if exist.HostTotal != data.RegionStats[i].HostTotal {
				updateMap["host_total"] = data.RegionStats[i].HostTotal
			}
			if exist.HostTotalChange != data.RegionStats[i].HostTotalChange {
				updateMap["host_total_change"] = data.RegionStats[i].HostTotalChange
			}
			if exist.MemoryTotal != data.RegionStats[i].MemoryTotal {
				updateMap["memory_total"] = data.RegionStats[i].MemoryTotal
			}
			if exist.MemoryTotalChange != data.RegionStats[i].MemoryTotalChange {
				updateMap["memory_total_change"] = data.RegionStats[i].MemoryTotalChange
			}
			if exist.CPUTotal != data.RegionStats[i].CPUTotal {
				updateMap["cpu_total"] = data.RegionStats[i].CPUTotal
			}
			if exist.CPUTotalChange != data.RegionStats[i].CPUTotalChange {
				updateMap["cpu_total_change"] = data.RegionStats[i].CPUTotalChange
			}
			if exist.GPUTotal != data.RegionStats[i].GPUTotal {
				updateMap["gpu_total"] = data.RegionStats[i].GPUTotal
			}
			if exist.GPUTotalChange != data.RegionStats[i].GPUTotalChange {
				updateMap["gpu_total_change"] = data.RegionStats[i].GPUTotalChange
			}
			if len(updateMap) > 0 {
				err = dbop.Model(&exist).Updates(updateMap).Error
				if err != nil {
					return
				}
			}
		}
	}

	return
}
