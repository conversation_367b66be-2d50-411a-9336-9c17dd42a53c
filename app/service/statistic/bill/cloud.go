package bill

import (
	"cmdb/app"
	"database/sql"
	"errors"
	"time"
)

// CloudBill 表示单个月份的云服务商账单
type CloudBill struct {
	Month  string  `json:"month"`  // 账单周期，格式为 2021-01
	Name   string  `json:"name"`   // 云服务商名称
	Amount float64 `json:"amount"` // 账单金额
}

// CloudBillRange 表示时间范围内的云服务商账单
type CloudBillRange struct {
	Bills []CloudBill `json:"bills"` // 账单列表
}

// GetCloudBills 获取指定账单周期内所有云服务商的账单统计
func GetCloudBills(billCycle string) (cbs []CloudBill, err error) {
	cbs = make([]CloudBill, 0)

	// 阿里云账单
	var nullAmount sql.NullFloat64
	err = app.DB().Table("asset_monthly_bills_aliyun").Select("sum(pretax_amount) as amount").Where("bill_cycle = ?", billCycle).Scan(&nullAmount).Error
	if err != nil {
		app.Log().Error("获取阿里云账单失败", "billCycle", billCycle, "err", err)
		return
	}

	// 如果值为NULL，则使用0.0作为默认值
	amount := 0.0
	if nullAmount.Valid {
		amount = nullAmount.Float64
	}

	cbs = append(cbs, CloudBill{
		Month:  billCycle, // 2021-01
		Name:   "阿里云",
		Amount: amount,
	})

	// 华为云账单
	nullAmount = sql.NullFloat64{}
	err = app.DB().Table("asset_monthly_bills_hwcloud").Select("sum(consume_amount) as amount").Where("cycle = ?", billCycle).Scan(&nullAmount).Error
	if err != nil {
		app.Log().Error("获取华为云账单失败", "billCycle", billCycle, "err", err)
		return
	}

	// 如果值为NULL，则使用0.0作为默认值
	amount = 0.0
	if nullAmount.Valid {
		amount = nullAmount.Float64
	}

	cbs = append(cbs, CloudBill{
		Month:  billCycle, // 2021-01
		Name:   "华为云",
		Amount: amount,
	})

	// 腾讯云账单
	nullAmount = sql.NullFloat64{}
	err = app.DB().Table("asset_monthly_bills_tencentcloud").Select("sum(real_total_cost) as amount").Where("bill_cycle = ?", billCycle).Scan(&nullAmount).Error
	if err != nil {
		app.Log().Error("获取腾讯云账单失败", "billCycle", billCycle, "err", err)
		return
	}

	// 如果值为NULL，则使用0.0作为默认值
	amount = 0.0
	if nullAmount.Valid {
		amount = nullAmount.Float64
	}

	cbs = append(cbs, CloudBill{
		Month:  billCycle, // 2021-01
		Name:   "腾讯云",
		Amount: amount,
	})

	return
}

// GetCloudBillsByDateRange 获取指定时间范围内所有云服务商的账单统计
func GetCloudBillsByDateRange(startTime, endTime string) (cbr CloudBillRange, err error) {
	// 初始化结果
	cbr = CloudBillRange{
		Bills: make([]CloudBill, 0),
	}

	// 计算时间范围内的所有月份
	billCycles := []string{}
	if startTime == endTime {
		billCycles = append(billCycles, startTime)
	} else {
		// 解析开始和结束时间
		start, err1 := time.Parse("2006-01", startTime)
		end, err2 := time.Parse("2006-01", endTime)
		if err1 != nil || err2 != nil {
			app.Log().Error("解析时间失败", "startTime", startTime, "endTime", endTime)
			return cbr, errors.New("解析时间失败")
		}

		// 确保开始时间不晚于结束时间
		if start.After(end) {
			start, end = end, start
		}

		// 生成时间范围内的所有月份
		for d := start; !d.After(end); d = d.AddDate(0, 1, 0) {
			billCycles = append(billCycles, d.Format("2006-01"))
		}
	}

	// 遍历所有月份，获取每个月份的账单数据
	for _, billCycle := range billCycles {
		bills, err1 := GetCloudBills(billCycle)
		if err1 != nil {
			app.Log().Error("获取云服务商账单失败", "billCycle", billCycle, "err", err1)
			continue // 如果获取某个月份的账单失败，继续处理下一个月份
		}

		// 添加到结果集
		cbr.Bills = append(cbr.Bills, bills...)
	}

	return
}

// CloudBillSummary 表示云服务商账单汇总
type CloudBillSummary struct {
	Name   string  `json:"name"`   // 云服务商名称
	Amount float64 `json:"amount"` // 账单总金额
}

// GetCloudBillsSummaryByDateRange 获取指定时间范围内按云服务商分组的账单汇总
func GetCloudBillsSummaryByDateRange(startTime, endTime string) (summaries []CloudBillSummary, err error) {
	// 获取时间范围内的所有账单
	cbr, err := GetCloudBillsByDateRange(startTime, endTime)
	if err != nil {
		return
	}

	// 按云服务商名称分组统计
	summaryMap := make(map[string]*CloudBillSummary)
	for _, bill := range cbr.Bills {
		if _, ok := summaryMap[bill.Name]; !ok {
			summaryMap[bill.Name] = &CloudBillSummary{
				Name:   bill.Name,
				Amount: 0,
			}
		}
		summaryMap[bill.Name].Amount += bill.Amount
	}

	// 转换为切片
	for _, summary := range summaryMap {
		summaries = append(summaries, *summary)
	}

	return
}
