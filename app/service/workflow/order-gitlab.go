package workflow

import (
	"bytes"
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/auth"
	"cmdb/app/service/setting"
	"cmdb/pkg/utils/gitlab"
	"cmdb/pkg/utils/mytime"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"gorm.io/datatypes"
)

var (
	ErrGitlabPorjectEmpty = errors.New("项目不能为空")
)

type OrderGitLab struct {
	ID         uint           `gorm:"column:id;primarykey;comment:id" json:"id"`
	SN         string         `gorm:"type:varchar(128);unique;column:sn;comment:工单单号" json:"sn"`
	Content    string         `gorm:"type:text;column:content;comment:工单内容" json:"content"`
	Email      string         `gorm:"type:varchar(120);column:email;comment:邮箱" json:"email"`
	Permission uint           `gorm:"column:permission" json:"permission"`
	Projects   datatypes.JSON `gorm:"column:projects" json:"projects"`
	ExpiredAt  *time.Time     `gorm:"expired_at"`
}

func (OrderGitLab) TableName() string {
	return "**********************"
}

type OrderGitLabDetail struct {
	Order      `gorm:"-"`
	Content    string         `gorm:"type:text;column:content;comment:工单内容" json:"content"`
	Email      string         `gorm:"type:varchar(120);column:email;comment:邮箱" json:"email"`
	Permission uint           `gorm:"column:permission" json:"permission"`
	Projects   datatypes.JSON `gorm:"column:projects" json:"projects"`
	ExpiredAt  *time.Time     `gorm:"expired_at"`
}

// 设置表名
func (OrderGitLabDetail) TableName() string {
	return "**********************"
}

func GetOrderGitlabDetailByID(id int) (detail OrderGitLabDetail, err error) {
	var o Order
	err = app.DB().Where("id = ?", id).Take(&o).Error
	if err != nil {
		return
	}
	detail, err = GetOrderGitLabDetail(o)
	return
}

func GetOrderGitLabDetail(o Order) (detail OrderGitLabDetail, err error) {
	detail = OrderGitLabDetail{}
	err = app.DB().Where("sn = ?", o.SN).Take(&detail).Error
	if err != nil {
		return
	}
	detail.Order = o
	return
}

type OrderGitlabForm struct {
	Content    string     `json:"content"`
	Permission uint       `json:"permission" binding:"required"`
	Projects   []string   `json:"projects"`
	ExpiredAt  *time.Time `json:"expired_at"`
	Orders     []string   `json:"orders"`
	Env        asset.ENV  `json:"env"`
	Critical   bool       `json:"critical"`
}

func (form OrderGitlabForm) GenProcesses(sn string, a Applicant, admin *auth.User) (processes []Process, err error) {
	var flow Flow
	err = app.DB().Where("order_type = ?", GitlabOrderType).Take(&flow).Error
	if err != nil {
		return
	}
	flow.Nodes, err = flow.GetFlowNodes()
	if err != nil {
		return
	}
	processes, err = flow.genProcesses(sn, a, admin)
	return
}
func (form *OrderGitlabForm) check() (err error) {
	if len(form.Projects) == 0 {
		return ErrGitlabPorjectEmpty
	}
	_, err = gitlab.GetAccessLevel(int(form.Permission))
	if err != nil {
		return
	}
	invalidProjects := []string{}
	okProjects := []string{}
	for i := range form.Projects {
		if strings.Contains(form.Projects[i], " ") {
			invalidProjects = append(invalidProjects, form.Projects[i])
			continue
		}
		p := gitlab.GetFileNameFromURL(form.Projects[i])
		if p == "" {
			invalidProjects = append(invalidProjects, form.Projects[i])
		} else {
			okProjects = append(okProjects, strings.TrimSpace(form.Projects[i]))
		}
	}
	form.Projects = okProjects
	if len(invalidProjects) > 0 {
		return errors.New("存在非法路径：" + strings.Join(invalidProjects, "、"))
	}

	return
}

func (form OrderGitlabForm) Create(a Applicant, flow Flow, admin *auth.User) (err error) {
	err = form.check()
	if err != nil {
		return
	}
	sn := genSN("gitlab")
	processes, err := flow.genProcesses(sn, a, admin)
	if err != nil {
		return
	}
	status := ApprovingStatus
	var index uint
	if len(processes) == 0 {
		err = errors.New("生成流程失败")
		return
	} else if len(processes) <= 1 {
		// 如果流程没有配置就直接结束流程
		status = CompletedStatus
	} else {
		// 如果有流程就获取下一个节点的索引
		index = processes[1].ProcessIndex
		processes[1].Status = ApprovingProcessStatus
	}
	planTime := mytime.NextXWorkingDays(time.Now(), 1)
	order := Order{
		Title:        a.Name + " 申请 gitlab 项目权限",
		OrderType:    GitlabOrderType.Name(),
		Env:          form.Env,
		ExtType:      GitlabExtType,
		SN:           sn,
		Critical:     form.Critical,
		ApplicantID:  a.ID,
		Status:       status,
		PlanTime:     &planTime,
		ProcessIndex: index,
	}
	byteProjects, _ := json.Marshal(&form.Projects)
	extOrder := OrderGitLab{
		SN:         sn,
		Content:    form.Content,
		Email:      a.Email,
		Permission: form.Permission,
		Projects:   byteProjects,
		ExpiredAt:  form.ExpiredAt,
	}
	dbop := app.DB().Begin()
	err = dbop.Create(&order).Error
	if err != nil {
		dbop.Rollback()
		return
	}
	err = dbop.Create(&extOrder).Error
	if err != nil {
		dbop.Rollback()
		return
	}
	err = dbop.Create(&processes).Error
	if err != nil {
		dbop.Rollback()
		return
	}
	err = dbop.Commit().Error
	if err != nil {
		dbop.Rollback()
	}
	go order.notifyNextProcess()
	return
}

func (form OrderGitlabForm) Update(o Order) (err error) {
	err = form.check()
	if err != nil {
		return
	}
	extOrder := OrderGitLab{}
	err = app.DB().Where("sn = ?", o.SN).Take(&extOrder).Error
	if err != nil {
		return
	}
	updateMap := map[string]any{}
	if form.Content != extOrder.Content {
		updateMap["content"] = form.Content
	}
	if form.Permission != extOrder.Permission {
		updateMap["permission"] = form.Permission
	}
	if form.ExpiredAt != extOrder.ExpiredAt {
		updateMap["expired_at"] = form.ExpiredAt
	}
	byteProjects, _ := json.Marshal(&form.Projects)
	if !bytes.Equal(byteProjects, extOrder.Projects) {
		updateMap["projects"] = byteProjects
	}
	if len(updateMap) > 0 {
		err = app.DB().Model(&extOrder).Updates(updateMap).Error
	}
	return
}

var ErrGitlabNotConfig = errors.New("gitlab没有配置")

func (o OrderGitLabDetail) AddProjectMember() (rs bytes.Buffer, err error) {
	projects := []string{}
	err = json.Unmarshal(o.Projects, &projects)
	if err != nil {
		return
	}
	permission, err := gitlab.GetAccessLevel(int(o.Permission))
	if err != nil {
		return
	}
	var expiredAt *string
	if o.ExpiredAt != nil {
		at := o.ExpiredAt.Format("2006-01-02 15:04:05")
		expiredAt = &at
	}
	gitlabSetting, _ := setting.GetGitlabSetting()
	if gitlabSetting.Token == "" || gitlabSetting.Url == "" {
		rs.WriteString(ErrGitlabNotConfig.Error())
		err = ErrGitlabNotConfig
		return
	}
	fmt.Println("开通gitlab", "token", gitlabSetting.Token, "user_id", "url", gitlabSetting.Url)
	c, err := gitlab.NewClient(gitlabSetting.Token, gitlabSetting.Url)
	if err != nil {
		rs.WriteString(err.Error())
		return
	}
	name := strings.Split(o.Email, "@")[0]
	u, err := c.GetUserByName(name)
	if err != nil {
		rs.WriteString(err.Error())
		return
	}
	for i := range projects {
		p, err1 := c.GetProjectByURL(projects[i])
		if err1 != nil {
			err = err1
			rs.WriteString(projects[i] + " 失败：" + err1.Error() + "；")
			fmt.Println("开通gitlab，获取项目对象失败", projects[i], "err", err1)
			continue
		}
		fmt.Println("开通gitlab", "project", p.HTTPURLToRepo, "user_id", u.ID)
		err1 = c.AddProjectMember(p, u.ID, &permission, expiredAt)
		if err1 != nil {
			rs.WriteString(projects[i] + " 失败：" + err1.Error() + "；")
			fmt.Println("开通gitlab失败", "project", p.HTTPURLToRepo, "user_id", u.ID, "err", err1)
		} else {
			rs.WriteString(projects[i] + " 成功：" + "；")
			fmt.Println("开通gitlab成功", "project", p.HTTPURLToRepo, "user_id", u.ID)
		}
	}
	return
}
