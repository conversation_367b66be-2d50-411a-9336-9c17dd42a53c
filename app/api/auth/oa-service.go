package auth

import (
	"cmdb/app"
	"cmdb/app/service/audit"
	"cmdb/app/service/auth"
	"encoding/json"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

type OALoginForm struct {
	Code string `form:"code" json:"code" binding:"required"`
}

// OA登录接口
func OAAuth(c *gin.Context) {
	form := OALoginForm{}
	err := c.ShouldBind(&form)
	if err != nil {
		c.JSON(http.StatusOK, LoginResponseData{
			Success: false,
			Msg:     "OA登录失败，参数异常",
		})
		return
	}
	if form.Code == "" {
		c.JSON(http.StatusOK, LoginResponseData{
			Success: false,
			Msg:     "OA登录失败，参数异常",
		})
		return
	}
	u, err := auth.OAAuth(form.Code)
	if err != nil {
		app.Log().Error("登陆失败", "err", err)
		msg := "登陆失败"
		if err == auth.ErrUserDisabled {
			audit.LogLogin(u.Name, u.Username, c.<PERSON>(), c.Request.UserAgent(), "登录失败，用户被禁用", "OA登录")
			msg = "用户被禁用"
		}
		c.JSON(http.StatusOK, LoginResponseData{
			Success: false,
			Msg:     msg,
		})
		return
	}
	token, err := u.GenerateToken(time.Hour * 7 * 24)
	if err != nil {
		app.Log().Error("登陆，生成token失败", "err", err)
		c.JSON(http.StatusOK, LoginResponseData{
			Success: false,
			Msg:     "登陆失败",
		})
		return
	}
	refreshToken, err := u.GenerateToken(time.Hour * 8 * 24)
	if err != nil {
		app.Log().Error("登陆，生成refresh token失败", "err", err)
		c.JSON(http.StatusOK, LoginResponseData{
			Success: false,
			Msg:     "登陆失败",
		})
		return
	}
	roles := []string{}
	json.Unmarshal(u.Roles, &roles)
	if u.IsAdmin {
		roles = append(roles, "admin")
	}
	roles = append(roles, "common")
	go u.UpdateLoginTime()
	clientIP := c.GetHeader("X-Forwarded-For")
	if clientIP == "" {
		clientIP = c.ClientIP()
	}
	audit.LogLogin(u.Name, u.Username, clientIP, c.Request.UserAgent(), "登陆成功", "OA登陆")
	c.JSON(http.StatusOK, LoginResponseData{
		Success: true,
		Data: UserData{
			AccessToken:  token,
			Username:     u.Username,
			Nickname:     u.Name,
			RefreshToken: refreshToken,
			Expires:      time.Now().Add(time.Hour * 24 * 7).Format("2006/01/02 15:04:05"),
			Roles:        roles,
		},
	})
}
