package assets

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/database/mysql"
	"cmdb/app/service/database/redis"
	"encoding/json"
	"errors"
	"fmt"
	"reflect"
	"time"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

var (
	ErrAssetNodeExist = errors.New("资产节点已经存在")
)

// 资产节点
type AssetNode struct {
	ID        uint           `gorm:"column:id;primaryKey;comment:主键" json:"id"`
	Name      string         `gorm:"column:name;type:varchar(255);comment:节点名称" json:"name"`
	SystemID  uint           `gorm:"column:system_id;index;comment:关联业务的ID" json:"system_id"`
	AssetType AssetType      `gorm:"column:asset_type;comment:资产类型" json:"asset_type"`
	NextNodes datatypes.JSON `gorm:"column:next_nodes;type:json;comment:后面节点的ID" json:"next_nodes"`
	Tags      []asset.Tag    `gorm:"many2many:assets_asset_node_tags;" json:"tags"`
	CreatedAt time.Time      `gorm:"column:created_at;comment:创建时间" json:"created_at"`
	UpdatedAt time.Time      `gorm:"column:updated_at;comment:更新时间" json:"updated_at"`
}

func (AssetNode) TableName() string {
	return "asset_asset_nodes"
}

type AssetNodeForm struct {
	Name      string    `json:"name" binding:"required,max=255"`
	SystemID  uint      `json:"system_id" binding:"required"`
	AssetType AssetType `json:"asset_type" binding:"required"`
	NextNodes []uint    `json:"next_nodes"`
	TagIDs    []uint    `json:"tag_ids"`
}

func (form AssetNodeForm) Create() (err error) {
	err = app.DB().Where("name = ? AND system_id = ?", form.Name, form.SystemID).Take(&AssetNode{}).Error
	if err == nil {
		err = ErrAssetNodeExist
	} else if errors.Is(err, gorm.ErrRecordNotFound) {
		nextNodesData, _ := json.Marshal(&form.NextNodes)
		err = app.DB().Create(&AssetNode{
			Name:      form.Name,
			AssetType: form.AssetType,
			SystemID:  form.SystemID,
			NextNodes: nextNodesData,
		}).Error
	}
	if err != nil {
		return
	}
	tags := []asset.Tag{}
	if len(form.TagIDs) > 0 {
		err = app.DB().Where("id IN ?", form.TagIDs).Find(&tags).Error
		if err != nil {
			return
		}
	}
	node := AssetNode{}
	err = app.DB().Where("name = ? AND system_id = ?", form.Name, form.SystemID).Take(&node).Error
	if err != nil {
		return
	}
	err = app.DB().Model(&node).Association("Tags").Replace(&tags)
	if err != nil {
		return
	}
	return
}

func (node AssetNode) Update(form AssetNodeForm) (err error) {
	updateMap := map[string]any{}
	if node.Name != form.Name {
		updateMap["name"] = form.Name
	}
	if node.SystemID != form.SystemID {
		updateMap["system_id"] = form.SystemID
	}
	if node.AssetType != form.AssetType {
		updateMap["asset_type"] = form.AssetType
	}
	nextNodesData, _ := json.Marshal(&form.NextNodes)
	if !reflect.DeepEqual(node.NextNodes, nextNodesData) {
		updateMap["next_nodes"] = nextNodesData
	}
	if len(updateMap) > 0 {
		// 判断是否存在
		exist := AssetNode{}
		err = app.DB().Select("id").Where("name = ? AND system_id = ?", form.Name, form.SystemID).Take(&exist).Error
		if err == nil && exist.ID != node.ID {
			err = ErrAssetNodeExist
			return
		}
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
	}
	// 开启会话，更新记录和关联标签
	session := app.DB().Begin()
	if len(updateMap) > 0 {
		err = session.Model(&node).Updates(updateMap).Error
		if err != nil {
			session.Rollback()
			return
		}
	}
	tags := []asset.Tag{}
	if len(form.TagIDs) > 0 {
		err = app.DB().Where("id IN ?", form.TagIDs).Find(&tags).Error
		if err != nil {
			return
		}
	}
	err = session.Model(&node).Association("Tags").Replace(&tags)
	if err == nil {
		err = session.Commit().Error
	} else {
		session.Rollback()
	}
	return
}

func (node AssetNode) Delete() (err error) {
	err = app.DB().Delete(&node).Error
	return
}

func GetAssetNodeByID(id uint) (node AssetNode, err error) {
	err = app.DB().Where("id = ?", id).Take(&node).Error
	return
}

func (system AssetSystem) GetNodes() (nodes []AssetNode, err error) {
	err = app.DB().Where("system_id = ?", system.ID).Order("name").Find(&nodes).Error
	for i := range nodes {
		err1 := app.DB().Model(&nodes[i]).Association("Tags").Find(&nodes[i].Tags)
		if err1 != nil {
			app.Log().Error("获取节点列表失败", "err", err1)
			return
		}
	}
	return
}

type AssetNodeAsset struct {
	Name string `json:"name"`
	Host string `json:"host"`
}

// 获取节点关联的资产
func (node AssetNode) GetNodeAssets() (assets []AssetNodeAsset, err error) {
	assets = []AssetNodeAsset{}
	tags := []asset.Tag{}
	err = app.DB().Model(&node).Association("Tags").Find(&tags)
	if err != nil {
		return
	}
	if len(tags) == 0 {
		return
	}
	ids := []uint{}
	for _, tag := range tags {
		ids = append(ids, tag.ID)
	}
	fmt.Println("asset_type", node.AssetType)
	switch node.AssetType {
	case HostAssetType:
		hosts := []asset.Host{}
		err = app.DB().Model(&asset.Host{}).Where("id IN (?)", app.DB().Table("asset_host_tags").Select("host_id").Where("tag_id IN ?", ids)).Find(&hosts).Error
		if err != nil {
			return
		}
		for _, host := range hosts {
			assets = append(assets, AssetNodeAsset{Name: host.Name, Host: host.IP + "_" + host.PublicIP})
		}
	case RedisAssetType:
		instances := []redis.Instance{}
		err = app.DB().Model(&redis.Instance{}).Where("id IN (?)", app.DB().Table("asset_db_redis_instance_tags").Select("instance_id").Where("tag_id IN ?", ids)).Find(&instances).Error
		if err != nil {
			return
		}
		for _, instance := range instances {
			assets = append(assets, AssetNodeAsset{Name: instance.Remark, Host: fmt.Sprintf("%s:%d", instance.Host, instance.Port)})
		}
	case MySQLAssetType:
		instances := []mysql.Instance{}
		err = app.DB().Model(&mysql.Instance{}).Where("id IN (?)", app.DB().Table("asset_db_mysql_instance_tags").Select("instance_id").Where("tag_id IN ?", ids)).Find(&instances).Error
		if err != nil {
			return
		}
		for _, instance := range instances {
			assets = append(assets, AssetNodeAsset{Name: instance.Name, Host: fmt.Sprintf("%s:%d", instance.Host, instance.Port)})
		}
	}
	return
}
