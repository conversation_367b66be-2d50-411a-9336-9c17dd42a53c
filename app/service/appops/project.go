package appops

import (
	"time"

	"gorm.io/gorm"
)

type Project struct {
	ID          uint           `gorm:"column:id;primaryKey;comment:ID" json:"id"`
	Name        string         `gorm:"type:varchar(255);column:name;index;comment:项目名称" json:"name"`
	<PERSON><PERSON>       string         `gorm:"type:varchar(255);column:alias;comment:项目别名" json:"alias"`
	Description string         `gorm:"type:varchar(255);column:description;comment:项目描述" json:"description"`
	CreateTime  time.Time      `gorm:"column:create_time;index;comment:创建时间" json:"create_time"`
	UpdateTime  time.Time      `gorm:"column:update_time;index;comment:更新时间" json:"update_time"`
	DeletedAt   gorm.DeletedAt `gorm:"column:deleted_at;index;comment:删除时间" json:"deleted_at"`
}

func (Project) TableName() string {
	return "appops_projects"
}
