package workflow

import (
	"bytes"
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/auth"
	"cmdb/pkg/db"
	"encoding/json"
	"errors"
	"strings"
	"time"

	"github.com/gofrs/uuid"
	"gorm.io/datatypes"
)

// ErrAlreadyApproved 已审批错误
var ErrAlreadyApproved = errors.New("已审批")

// Order 工单
type Order struct {
	ID                     uint            `gorm:"column:id;primaryKey;comment:id" json:"id"`
	Title                  string          `gorm:"column:title;type:varchar(255);comment:标题" json:"title"`
	OrderType              string          `gorm:"type:varchar(255);column:order_type;comment:工单类型" json:"order_type"`
	ExtType                OrderExtType    `gorm:"column:ext_type;index;comment:工单归类ID"  json:"ext_type"`
	SN                     string          `gorm:"type:varchar(255);unique;column:sn;comment:工单单号" json:"sn"`
	ApplicantID            uint            `gorm:"column:applicant_id;index;comment:申请人ID" json:"-"`
	Status                 OrderStatus     `gorm:"column:status;index;comment:工单状态" json:"status"`
	ProcessIndex           uint            `gorm:"column:process_index;index;comment:当前审批流程索引" json:"process_index"`
	CreatedAt              time.Time       `gorm:"column:created_at;comment:创建时间" json:"created_at"`
	PlanTime               *time.Time      `gorm:"column:plan_time;comment:计划执行时间"  json:"plan_time"`
	Orders                 datatypes.JSON  `gorm:"column:orders;comment:关联工单" json:"orders"`
	RelatedOrders          []RelatedOrder  `gorm:"-" json:"related_orders,omitempty"`
	Env                    asset.ENV       `gorm:"column:env;comment:环境：0开发 1测试 2预发 3正式" json:"env"`
	Critical               bool            `gorm:"column:critical" json:"critical"`
	Applicant              *Applicant      `gorm:"-" json:"applicant,omitempty"`
	Processes              []ProcessDetail `gorm:"-" json:"processes,omitempty"`
	CloudPlatforms         datatypes.JSON  `gorm:"column:cloud_platforms" json:"cloud_platforms"`
	ServiceDurationSeconds uint64          `gorm:"column:service_duration_seconds" json:"service_duration_seconds"`
}

// TableName 表名
func (Order) TableName() string {
	return "workflow_orders"
}

// Attachment 附件
type Attachment struct {
	Filename string `json:"filename"`
	URL      string `json:"url"`
}

type RelatedOrder struct {
	ID    uint   `gorm:"column:id;primaryKey;comment:id" json:"id"`
	SN    string `gorm:"column:sn;comment:工单单号" json:"sn"`
	Title string `gorm:"column:title;comment:工单标题" json:"title"`
}

func (RelatedOrder) TableName() string {
	return "workflow_orders"
}

func (o Order) GetRelatedOrders() (orders []RelatedOrder, err error) {
	sns := []string{}
	json.Unmarshal(o.Orders, &sns)
	if len(sns) == 0 {
		return
	}
	err = app.DB().Model(&Order{}).Where("sn IN (?)", sns).Find(&orders).Error
	return
}

// GetOrderByID 根据ID获取工单
func GetOrderByID(id int) (order Order, err error) {
	err = app.DB().Where("id = ?", id).Take(&order).Error
	return
}

// GetOrderBySN 根据SN获取工单
func GetOrderBySN(sn string) (order Order, err error) {
	err = app.DB().Where("sn = ?", sn).Take(&order).Error
	return
}

// 检测工单权限
func (o *Order) CheckOrderPrivilege(uid uint) (ok bool) {
	if o.ApplicantID == uid {
		ok = true
		return
	}
	processes := []Process{}
	err := app.DB().Select("id", "approvers_id").Where("sn = ?", o.SN).Find(&processes).Error
	if err != nil {
		return
	}
	for i := range processes {
		approversID := []uint{}
		json.Unmarshal(processes[i].ApproversIDs, &approversID)
		for j := range approversID {
			if approversID[j] == uid {
				ok = true
				return
			}
		}
		// 抄送也可以看到
		ccsID := []uint{}
		json.Unmarshal(processes[i].CCIDs, &ccsID)
		for j := range ccsID {
			if ccsID[j] == uid {
				ok = true
				return
			}
		}
	}
	return
}

// AlreadyApproved 工单是否已审批
func (o Order) AlreadyApproved() (yes bool, err error) {
	if o.Status != ApprovingStatus {
		yes = true
		return
	}
	var count int64
	err = app.DB().Model(&Process{}).Where("sn = ? AND status > ?", o.SN, ApprovingStatus).Count(&count).Error
	if count > 1 {
		yes = true
	}
	return
}

// CanUpdate 工单是否可以更新
func (o Order) CanUpdate() (yes bool, err error) {
	ok, err := o.AlreadyApproved()
	if err != nil {
		return
	}
	if !ok {
		yes = true
	}
	return
}

// Cancel 取消工单
func (o Order) Cancel() (err error) {
	if o.Status > ApprovingStatus {
		err = ErrAlreadyApproved
		return
	}
	yes, err := o.AlreadyApproved()
	if err != nil {
		return
	}
	if yes {
		err = ErrAlreadyApproved
		return
	}
	dbop := app.DB().Begin()
	err = dbop.Model(&o).Updates(map[string]any{"status": CancelStatus, "process_index": 0}).Error
	if err != nil {
		dbop.Rollback()
		return
	}
	err = dbop.Model(&Process{}).Where("sn = ? AND process_index = ?", o.SN, 0).Update("status", CancelStatus).Error
	if err != nil {
		dbop.Rollback()
		return
	}
	err = dbop.Model(&Process{}).Where("sn = ? AND process_index > ?", o.SN, 0).Update("status", 0).Error
	if err != nil {
		dbop.Rollback()
		return
	}
	err = dbop.Commit().Error
	if err != nil {
		dbop.Rollback()
		return
	}
	return
}

// GetApplicant 获取申请人
func (o Order) GetApplicant() (applicant Applicant, err error) {
	err = app.DB().Where("id = ?", o.ApplicantID).Take(&applicant).Error
	return
}

// CurrentProcess 当前流程
func (o Order) CurrentProcess() (process Process, err error) {
	err = app.DB().Where("sn = ? AND process_index = ? AND status = ?",
		o.SN, o.ProcessIndex, ApprovingProcessStatus).Take(&process).Error
	return
}

func (o Order) IsLastProcess() (yes bool, err error) {
	var count int64
	err = app.DB().Model(&Process{}).Where("sn = ? AND status <= ?", o.SN, ApprovingProcessStatus).Count(&count).Error
	yes = count == 1
	return
}

// CanApproveByApprover 是否可以审批
func (o Order) CanApproveByApprover(approver Approver) (yes bool, err error) {
	yes = false
	if o.Status != ApprovingStatus {
		return
	}
	ps := Process{}
	if o.ProcessIndex == 0 {
		return
	}
	err = app.DB().Model(&Process{}).Where("sn = ? AND status = ?",
		o.SN, ApprovingProcessStatus).Take(&ps).Error
	if err != nil {
		return
	}
	ids := []uint{}
	json.Unmarshal(ps.ApproversIDs, &ids)
	for i := range ids {
		if ids[i] == approver.ID {
			yes = true
			return
		}
	}
	return
}

// genSN 生成SN
func genSN(prefix string) string {
	var sn bytes.Buffer
	sn.WriteString(prefix + "_" + time.Now().Format("20060102150405"))
	uuidV4, err := uuid.NewV4()
	if err == nil {
		sn.WriteString("_" + strings.ReplaceAll(uuidV4.String(), "-", ""))
	}
	return sn.String()
}

// GetOrders 获取工单列表
func GetOrders(offset, limit int, keyword *string, extType *OrderExtType, status *OrderStatus, applicantID *int) (count int64, orders []Order, err error) {
	dbop := app.DB()
	if extType != nil {
		dbop = dbop.Where("ext_type = ?", *extType)
	}
	if applicantID != nil {
		dbop = dbop.Where("applicant_id = ?", *applicantID)
	}
	if status != nil {
		dbop = dbop.Where("status = ?", *status)
	}
	if keyword != nil {
		dbop = db.MLike(dbop, *keyword, "title")
	}
	err = dbop.Model(&Order{}).Count(&count).
		Order("created_at DESC").Offset(offset).Limit(limit).Find(&orders).Error
	return
}

// Detail 工单详情
func (o Order) Detail() (detail any, err error) {
	o.Processes, err = o.GetProcessDetails()
	if err != nil {
		return
	}
	switch o.ExtType {
	case ServerExtType:
		detail, err = GetOrderServerDetail(o)
	case PermissionExtType:
		detail, err = GetOrderPermissionDetail(o)
	case DomainExtType:
		detail, err = GetOrderDomainDetail(o)
	case DatabaseExtType:
		detail, err = GetOrderMySQLDetail(o)
	case ServiceNeedExtType:
		detail, err = GetOrderServiceNeedDetail(o)
	case MongoDBExtType:
		detail, err = GetOrderMongoDBDetail(o)
	case GitlabExtType:
		detail, err = GetOrderGitLabDetail(o)
	case CloudAccountExtType:
		detail, err = GetOrderPermissionDetail(o)
	case DesktopOPSExtType:
		detail, err = GetOrderDesktopOPSDetail(o)
	default:
		err = errors.New("unknown order type")
	}
	return
}

func (o Order) AddProcess(tos []Approver, nodeName string) (err error) {
	if nodeName == "" {
		nodeName = "加签节点"
	}
	ps, err := o.GetProcessDetails()
	if err != nil {
		return
	}
	pindex := o.ProcessIndex + 1
	cproc := false
	ids := []uint{}
	for _, to := range tos {
		ids = append(ids, to.ID)
	}
	aIDs, _ := json.Marshal(&ids)
	dbop := app.DB().Begin()
	err = dbop.Create(&Process{
		SN:           o.SN,
		NodeName:     nodeName,
		ApproversIDs: aIDs,
		Status:       UnapproveProcessStatus,
		ProcessIndex: pindex,
	}).Error
	if err != nil {
		dbop.Rollback()
		return
	}
	for i := range ps {
		if ps[i].ProcessIndex == o.ProcessIndex {
			// 找到当前审批流程并且设置标记
			cproc = true
			// 当前节点不执行操作
			continue
		}
		if cproc {
			pindex += 1
			err = dbop.Model(&ps[i].Process).Update("process_index", pindex).Error
			if err != nil {
				dbop.Rollback()
				return
			}
		}
	}
	err = dbop.Commit().Error
	if err != nil {
		dbop.Rollback()
	}
	return
}

// 转签
func (o Order) TransferProcess(from Approver, tos []Approver, comment string) (err error) {
	ps, err := o.GetProcessDetails()
	if err != nil {
		return
	}
	pindex := o.ProcessIndex + 1
	cproc := false
	ids := []uint{}
	for _, to := range tos {
		ids = append(ids, to.ID)
	}
	aIDs, _ := json.Marshal(&ids)
	dbop := app.DB().Begin()
	err = dbop.Create(&Process{
		SN:           o.SN,
		NodeName:     "转签节点",
		ApproversIDs: aIDs,
		Status:       ApprovingProcessStatus,
		ProcessIndex: pindex,
	}).Error
	if err != nil {
		dbop.Rollback()
		return
	}
	for i := range ps {
		if ps[i].ProcessIndex == o.ProcessIndex {
			// 找到当前审批流程并且设置标记
			cproc = true
			// 当前节点自动完成审批
			err = dbop.Model(&ps[i].Process).Updates(map[string]interface{}{
				"status":       PassProcessStatus,
				"comment":      "转签!" + comment,
				"approve_time": time.Now(),
				"approver_id":  from.ID,
			}).Error
			if err != nil {
				dbop.Rollback()
				return
			}
		} else if cproc {
			pindex += 1
			err = dbop.Model(&ps[i].Process).Update("process_index", pindex).Error
			if err != nil {
				dbop.Rollback()
				return
			}
		}
	}
	err = dbop.Model(&o).Update("process_index", o.ProcessIndex+1).Error
	if err != nil {
		dbop.Rollback()
		return
	}
	err = dbop.Commit().Error
	if err != nil {
		dbop.Rollback()
	} else {
		go o.notifyNextProcess() // 通知下一节点
	}
	return
}

// 自动完成工单流程，并发送通知
func (o Order) AutoCompleteOrder(admin auth.User) (err error) {
	var ps []Process
	notifyUserID := []uint{o.ApplicantID, admin.ID}
	err = app.DB().Where("sn = ? AND status <= ?", o.SN, ApprovingProcessStatus).Order("process_index").Find(&ps).Error
	if err != nil {
		return
	}

	dbop := app.DB().Begin()
	err = dbop.Model(&o).Updates(map[string]any{
		"status":        PassProcessStatus,
		"process_index": 0,
	}).Error
	if err != nil {
		return
	}
	for i := range ps {
		notifyUserID = append(notifyUserID, ps[i].ApproverID)
		ids := []uint{}
		json.Unmarshal(ps[i].ApproversIDs, &ids)
		notifyUserID = append(notifyUserID, ids...)
		ids = []uint{}
		json.Unmarshal(ps[i].CCIDs, &ids)
		notifyUserID = append(notifyUserID, ids...)
		err = dbop.Model(&ps[i]).Updates(map[string]any{
			"status":       PassProcessStatus,
			"comment":      "自动通过，无需审批",
			"approve_time": time.Now(),
			"approver_id":  admin.ID,
			"node_name":    ps[i].NodeName + "（自动通过）",
		}).Error
		if err != nil {
			return
		}
	}
	err = dbop.Commit().Error
	if err != nil {
		dbop.Rollback()
		return
	}
	go func() {
		err1 := o.notifyByApproverIDs("「自动通过，无需审批」 - "+o.Title, notifyUserID...)
		if err1 != nil {
			app.Log().Error("notifyByApproverIDs 失败", "err", err1)
		}
	}()
	return nil
}
