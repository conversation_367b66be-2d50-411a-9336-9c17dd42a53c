package hwcloud

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/pkg/utils"
	"errors"

	"github.com/huaweicloud/huaweicloud-sdk-go-v3/core/auth/global"
	eps "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/eps/v1"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/eps/v1/model"
	region "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/eps/v1/region"
	"gorm.io/gorm"
)

func (a Account) syncEnterpriseProjects() (result string, err error) {
	result, err = a.syncPageEnterpriseProjects(1)
	return
}

func (a Account) syncPageEnterpriseProjects(page int32) (result string, err error) {
	// 解码访问密钥
	secret, _ := utils.Base64Decode(a.AccessSecret)
	auth, err := global.NewCredentialsBuilder().WithAk(a.<PERSON>ey).WithSk(secret).SafeBuild()
	if err != nil {
		result = "认证失败"
		return
	}
	regionID, err := region.SafeValueOf("cn-north-4")
	if err != nil {
		result = "regionID fail"
		return
	}
	clientBuild, err := eps.EpsClientBuilder().WithRegion(regionID).WithCredential(auth).SafeBuild()
	if err != nil {
		result = "客户端创建失败"
		return
	}
	client := eps.NewEpsClient(clientBuild)
	limit := int32(1000)
	offset := (page - 1) * limit
	request := &model.ListEnterpriseProjectRequest{
		Limit:  &limit,
		Offset: &offset,
	}
	response, err := client.ListEnterpriseProject(request)
	if err != nil {
		result = "请求 ListEnterpriseProject fail"
		return
	}
	err = a.saveEnterpriseProjects(response.EnterpriseProjects)
	if err != nil {
		result = "saveEnterpriseProjects fail"
		return
	}
	if response.TotalCount != nil && *response.TotalCount > page*limit {
		r1, err1 := a.syncPageEnterpriseProjects(page + 1)
		if err1 != nil {
			result += r1 + ":" + err1.Error()
			err = err1
			return
		}
	}
	return
}
func (a Account) saveEnterpriseProjects(groups *[]model.EpDetail) (err error) {
	if groups == nil {
		return
	}
	// 遍历获取到的资源组
	for _, group := range *groups {
		exist := asset.ResourceGroup{}
		// 检查数据库中是否已存在该资源组
		err = app.DB().Where("group_id = ? AND account_id = ?", group.Id, a.ID).Take(&exist).Error
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 如果不存在，则创建新的资源组记录
			err = app.DB().Create(&asset.ResourceGroup{AccountID: a.ID, GroupID: group.Id, GroupName: group.Name, GroupDisplayName: group.Name}).Error
		} else if err == nil {
			updateMap := map[string]any{}
			if exist.GroupName != group.Name {
				updateMap["group_name"] = group.Name
			}
			if exist.GroupDisplayName != group.Name {
				updateMap["group_display_name"] = group.Name
			}
			if len(updateMap) > 0 {
				err = app.DB().Model(&exist).Updates(updateMap).Error
			}
		}
		if err != nil {
			return err
		}
	}
	return
}
