package app

import (
	"encoding/json"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

type CookiesAuth struct {
	AccessToken string `json:"AccessToken"`
}

func GetToken(c *gin.Context) (token string) {
	token = c.<PERSON>("Authorization")
	token = strings.TrimPrefix(token, "Bearer ")
	if token == "" {
		token = c.Query("token")
	}
	if token == "" {
		// 从cookies里面获取认证信息
		authStr, err := c<PERSON>("authorized-token")
		if err == nil {
			authJSON := CookiesAuth{}
			err = json.Unmarshal([]byte(authStr), &authJSON)
			if err == nil {
				token = authJSON.AccessToken
			}
		}
	}
	return
}

type ResponseData struct {
	Success bool    `json:"success"`
	Data    any     `json:"data,omitempty"`
	Msg     *string `json:"msg,omitempty"`
	Count   *int64  `json:"count,omitempty"`
	Code    *int    `json:"code,omitempty"`
}

func SuccessResponseDataCount(c *gin.Context, data any, count int64) {
	c.JSON(http.StatusOK, ResponseData{
		Success: true,
		Data:    data,
		Count:   &count,
	})
}

func SuccessResponseData(c *gin.Context, data any) {
	c.JSON(http.StatusOK, ResponseData{
		Success: true,
		Data:    data,
	})
}

func SuccessResponseMsg(c *gin.Context, msg string) {
	c.JSON(http.StatusOK, ResponseData{
		Success: true,
		Msg:     &msg,
	})
}
func SuccessResponseMsgData(c *gin.Context, msg string, data any) {
	c.JSON(http.StatusOK, ResponseData{
		Success: true,
		Msg:     &msg,
		Data:    data,
	})
}

func FailedResponseMsgData(c *gin.Context, msg string, data any) {
	c.JSON(http.StatusOK, ResponseData{
		Success: false,
		Msg:     &msg,
		Data:    data,
	})
}

func FailedResponseData(c *gin.Context, data any) {
	c.JSON(http.StatusOK, ResponseData{
		Success: false,
		Data:    data,
	})
}

func FailedResponseMsg(c *gin.Context, msg string) {
	c.JSON(http.StatusOK, ResponseData{
		Success: false,
		Msg:     &msg,
	})
}

func V2FailedResponseMsg(c *gin.Context, msg string) {
	var code int = 1
	c.JSON(http.StatusOK, ResponseData{
		Success: false,
		Code:    &code,
		Msg:     &msg,
	})
}

func V2SuccessResponseMsg(c *gin.Context, msg string) {
	var code int = 0
	c.JSON(http.StatusOK, ResponseData{
		Success: true,
		Code:    &code,
		Msg:     &msg,
	})
}

func V2SuccessResponseMsgData(c *gin.Context, msg string, data any) {
	var code int = 0
	c.JSON(http.StatusOK, ResponseData{
		Success: true,
		Code:    &code,
		Msg:     &msg,
		Data:    data,
	})
}
func V2SuccessResponseMsgCount(c *gin.Context, msg string, data any, count int64) {
	var code int = 0
	c.JSON(http.StatusOK, ResponseData{
		Success: true,
		Code:    &code,
		Msg:     &msg,
		Count:   &count,
		Data:    data,
	})
}

func V2SuccessResponseData(c *gin.Context, data any) {
	var code int = 0
	c.JSON(http.StatusOK, ResponseData{
		Success: true,
		Code:    &code,
		Data:    data,
	})
}
