package asset

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/audit"
	"errors"
	"strconv"

	"github.com/gin-gonic/gin"
)

type DatacenterRow struct {
	asset.Datacenter
	HostTotal int64 `json:"host_total"`
}

// 获取数据中心列表
func GetDatacenters(c *gin.Context) {
	// 获取查询参数
	keyword := c.Query("keyword")
	var cloudType *asset.CloudType
	// 将查询参数转换为int类型
	cloudTypeInt, err := strconv.Atoi(c.DefaultQuery("cloud_type", "0"))
	if err == nil && cloudTypeInt > 0 {
		// 将int类型转换为CloudType类型
		cloudType = asset.CloudType(cloudTypeInt).Point()
	}
	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	// 计算偏移量
	offset := (page - 1) * limit
	// 调用服务层获取数据中心列表
	data, count, err := asset.GetDatacenters(offset, limit, cloudType, keyword)
	if err != nil {
		// 返回错误信息
		app.FailedResponseMsg(c, "获取数据失败")
		return
	}
	newData := make([]DatacenterRow, len(data))
	if len(data) > 0 {
		for i, v := range data {
			count, _ := data[i].CountHost()
			newData[i] = DatacenterRow{v, count}
		}
	}
	// 返回成功信息
	app.SuccessResponseDataCount(c, newData, count)
}

// 添加数据中心
func AddDatacenter(c *gin.Context) {
	// 定义表单结构体
	var form asset.DatacenterForm
	// 绑定表单数据
	err := c.ShouldBind(&form)
	if err != nil {
		// 记录日志
		app.Log().Debug("添加数据中心，验证表单失败", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "非法字段")
		return
	}
	// 调用服务层添加数据中心
	err = form.Create()
	if err != nil {
		msg := "添加失败"
		// 判断错误类型
		if errors.Is(err, asset.ErrDatacenterExist) {
			msg = err.Error()
		} else {
			// 记录日志
			app.Log().Error("添加数据中心失败", "err", err)
		}
		// 返回错误信息
		app.FailedResponseMsg(c, msg)
		return
	}
	// 记录操作日志
	audit.LogAPIOP(c, "资产管理", "添加数据中心："+form.Name)
	// 返回成功信息
	app.SuccessResponseMsg(c, "添加成功")
}

// 更新数据中心
func UpdateDatacenter(c *gin.Context) {
	// 定义表单结构体
	var form asset.DatacenterForm
	// 绑定表单数据
	err := c.ShouldBind(&form)
	if err != nil {
		// 记录日志
		app.Log().Debug("更新数据中心，验证表单失败", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "非法字段")
		return
	}
	// 获取参数
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		// 记录日志
		app.Log().Debug("更新数据中心，存在非法参数:"+c.Param("id"), "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "非法参数")
		return
	}
	// 调用服务层获取数据中心对象
	d, err := asset.GetDatacenterByID(id)
	if err != nil {
		// 记录日志
		app.Log().Error("更新数据中心，获取数据中心对象失败", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "获取数据中心对象失败")
		return
	}
	// 调用服务层更新数据中心
	err = d.Update(form)
	if err != nil {
		msg := "更新失败"
		// 判断错误类型
		if errors.Is(err, asset.ErrDatacenterExist) {
			msg = err.Error()
		} else {
			// 记录日志
			app.Log().Error("更新数据中心失败", "err", err)
		}
		// 返回错误信息
		app.FailedResponseMsg(c, msg)
		return
	}
	// 记录操作日志
	audit.LogAPIOP(c, "资产管理", "更新数据中心："+form.Name)
	// 返回成功信息
	app.SuccessResponseMsg(c, "更新成功")
}

// 删除数据中心
func DeleteDatacenter(c *gin.Context) {
	// 获取参数
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		// 记录日志
		app.Log().Debug("删除数据中心，存在非法参数:"+c.Param("id"), "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "非法参数")
		return
	}
	// 调用服务层获取数据中心对象
	d, err := asset.GetDatacenterByID(id)
	if err != nil {
		// 记录日志
		app.Log().Error("删除数据中心，获取数据中心对象失败", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "获取数据中心对象失败")
		return
	}
	// 调用服务层删除数据中心
	err = d.Delete()
	if err != nil {
		msg := "删除失败"
		// 判断错误类型
		if errors.Is(err, asset.ErrDatacenterExistAssociation) {
			msg = err.Error()
		} else {
			// 记录日志
			app.Log().Error("删除数据中心失败", "err", err)
		}
		// 返回错误信息
		app.FailedResponseMsg(c, msg)
		return
	}
	// 记录操作日志
	audit.LogAPIOP(c, "资产管理", "删除数据中心："+d.Name)
	// 返回成功信息
	app.SuccessResponseMsg(c, "删除成功")
}

func GetAllDatacenters(c *gin.Context) {
	data, err := asset.GetAllDatacenters()
	if err != nil {
		// 记录日志
		app.Log().Error("获取所有数据中心失败", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "获取所有数据中心失败")
		return
	}
	// 返回成功信息
	app.SuccessResponseData(c, data)
}
