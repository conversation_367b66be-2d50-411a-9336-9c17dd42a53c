package https

import (
	"bytes"
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/notice"
	"cmdb/app/service/setting"
	"cmdb/pkg/db"
	"crypto/tls"
	"errors"
	"fmt"
	"net"
	"strconv"
	"time"

	"gorm.io/gorm"
)

var ErrDomainHttpsExist = errors.New("该域名已经存在")

// 1 表示安全 2 标识危险 3 无法连接 其他表示未知
type DomainHttpsSecure int

const (
	DomainHttpsSecureSafe        DomainHttpsSecure = 1
	DomainHttpsSecureDanger      DomainHttpsSecure = 2
	DomainHttpsSecureUnreachable DomainHttpsSecure = 3
)

// DomainHttps 域名证书
type DomainHttps struct {
	ID                     uint              `gorm:"column:id;primary_key;comment:id" json:"id"`
	Domain                 string            `gorm:"type:varchar(255);index;column:domain;comment:域名" json:"domain"`
	Port                   uint              `gorm:"column:port;comment:端口，针对非443端口的，如果留空表示用443" json:"port"`
	Remark                 string            `gorm:"type:varchar(300);column:remark;comment:备注" json:"remark"`
	EnableMonitor          bool              `gorm:"column:enable_monitor;index;comment:监控状态" json:"enable_monitor"`
	IsSecure               DomainHttpsSecure `gorm:"column:is_secure;index;comment:是否安全：1 表示安全 2 标识危险 3 无法连接 其他表示未知" json:"is_secure"`
	Info                   any               `gorm:"-" json:"info,omitempty"`
	AlarmDaysBeforeExpired uint              `gorm:"column:alarm_days_before_expired" json:"alarm_days_before_expired"`
	NotBefore              *time.Time        `gorm:"column:not_before;index;comment:颁发日期" json:"not_before"`
	NotAfter               *time.Time        `gorm:"column:not_after;index;comment:截止日期" json:"not_after"`
	UpdatedAt              time.Time         `gorm:"column:updated_at;comment:更新时间" json:"updated_at"`
}

// TableName 设置表名
func (DomainHttps) TableName() string {
	return "monitor_https_domains"
}

// DomainHttpsForm 域名证书表单
type DomainHttpsForm struct {
	Domain                 string `json:"domain" binding:"required,max=255"`
	Port                   uint   `json:"port" binding:"max=65536"`
	Remark                 string `json:"remark" binding:"max=300"`
	EnableMonitor          bool   `json:"enable_monitor"`
	AlarmDaysBeforeExpired uint   `json:"alarm_days_before_expired"`
}

// Create 创建域名证书监控
func (form *DomainHttpsForm) Create() (err error) {
	err = app.DB().Select("id").Where("domain = ?", form.Domain).Take(&DomainHttps{}).Error
	if err == nil {
		err = ErrDomainHttpsExist
		return
	}
	if err == gorm.ErrRecordNotFound {
		err = app.DB().Create(&DomainHttps{
			Domain:                 form.Domain,
			Remark:                 form.Remark,
			EnableMonitor:          form.EnableMonitor,
			Port:                   form.Port,
			AlarmDaysBeforeExpired: form.AlarmDaysBeforeExpired,
		}).Error
	}
	return
}

// 更新域名证书监控
func (https DomainHttps) Update(form DomainHttpsForm) (err error) {
	updateMap := map[string]any{}
	if https.Domain != form.Domain {
		exist := DomainHttps{}
		err = app.DB().Select("id").Where("domain = ?", form.Domain).Take(&exist).Error
		if err == nil && exist.ID != https.ID {
			err = ErrDomainHttpsExist
			return
		} else if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		updateMap["domain"] = form.Domain
	}
	if https.Remark != form.Remark {
		updateMap["remark"] = form.Remark
	}
	if https.EnableMonitor != form.EnableMonitor {
		updateMap["enable_monitor"] = form.EnableMonitor
	}
	if https.Port != form.Port {
		updateMap["port"] = form.Port
	}
	if https.AlarmDaysBeforeExpired != form.AlarmDaysBeforeExpired {
		updateMap["alarm_days_before_expired"] = form.AlarmDaysBeforeExpired
	}
	if len(updateMap) > 0 {
		err = app.DB().Model(&https).Updates(updateMap).Error
	}
	return
}

func (https DomainHttps) Delete() (err error) {
	err = app.DB().Delete(&https).Error
	return
}

func GetDomainHttps(offset, limit int, keyword *string, enableMonitor *bool, isSecure *int) (count int64, data []DomainHttps, err error) {
	dbop := app.DB()
	if keyword != nil {
		dbop = db.MLike(dbop, *keyword, "domain")
	}
	if enableMonitor != nil {
		dbop = dbop.Where("enable_monitor = ?", *enableMonitor)
	}
	if isSecure != nil {
		dbop = dbop.Where("is_secure = ?", *isSecure)
	}
	err = dbop.Model(&DomainHttps{}).Count(&count).Order("is_secure DESC,not_after").Offset(offset).Limit(limit).Find(&data).Error
	return
}

func GetDomainHttpsByID(id int) (data DomainHttps, err error) {
	err = app.DB().Where("id = ?", id).Take(&data).Error
	return
}

func (domainHttps DomainHttps) Check() (err error) {
	if domainHttps.Port == 0 {
		domainHttps.Port = 443
	}
	// 重试3次
	checkCount := 0
	for ; checkCount < 3; checkCount++ {
		conn, err := tls.DialWithDialer(&net.Dialer{
			Timeout:  time.Second * 3,
			Deadline: time.Now().Add(time.Second * 5),
		}, "tcp", domainHttps.Domain+":"+strconv.Itoa(int(domainHttps.Port)), &tls.Config{
			InsecureSkipVerify: true,
		})
		if err != nil {
			continue
		}
		defer conn.Close()
		stats := conn.ConnectionState()
		certs := stats.PeerCertificates
		for _, cert := range certs {
			if !cert.IsCA {
				domainHttps.NotBefore = &cert.NotBefore
				domainHttps.NotAfter = &cert.NotAfter
				if cert.VerifyHostname(domainHttps.Domain) == nil {
					domainHttps.IsSecure = DomainHttpsSecureSafe
				} else {
					domainHttps.IsSecure = DomainHttpsSecureDanger
				}
			}
		}
		if domainHttps.NotAfter.Before(time.Now()) {
			domainHttps.IsSecure = DomainHttpsSecureDanger
		}
		break
	}
	if checkCount == 3 {
		domainHttps.IsSecure = DomainHttpsSecureUnreachable
	}
	err = app.DB().Model(&domainHttps).Updates(map[string]any{
		"not_before": domainHttps.NotBefore,
		"not_after":  domainHttps.NotAfter,
		"is_secure":  domainHttps.IsSecure,
	}).Error
	return
}

func CheckAllDomainHttps() (err error) {
	domainHttpss := []DomainHttps{}
	err = app.DB().Where("enable_monitor = ?", true).Find(&domainHttpss).Error
	if err != nil {
		return
	}
	var err1 error
	for i := range domainHttpss {
		err1 = domainHttpss[i].Check()
		if err1 != nil {
			app.Log().Error("检查域名证书失败", "域名", domainHttpss[i].Domain, "err", err1)
		}
	}
	return
}

func CheckDomainHttpsWillExpired() (domainHttpss []DomainHttps, err error) {
	err = app.DB().Select(
		"id", "domain", "alarm_days_before_expired", "remark",
		"not_before", "not_after", "updated_at",
	).Where(
		"enable_monitor = ? AND TIMESTAMPDIFF(DAY,?,not_after) <= alarm_days_before_expired", true, time.Now(),
	).Order("not_after").Find(&domainHttpss).Error
	return
}

// 检查证书是否是安全
func CheckDomainHttpsIsSecure() (domainHttpss []DomainHttps, err error) {
	err = app.DB().Where("enable_monitor = ? AND is_secure > ?", true, DomainHttpsSecureSafe).Order("not_after").Find(&domainHttpss).Error
	return
}

// 检查并通知
func CheckDomainSSLAndNotice() (err error) {
	now := time.Now()
	var contents, expiredContents bytes.Buffer
	domainHttpss, err := CheckDomainHttpsWillExpired()
	if err != nil {
		return
	}
	for _, domain := range domainHttpss {
		expiredContents.WriteString(fmt.Sprintf("%s 过期时间:%s（还有 %.1f 天）\n", domain.Domain, domain.NotAfter.Format("2006-01-02"), (domain.NotAfter.Sub(now)).Hours()/24.0))
	}
	if expiredContents.String() != "" {
		contents.WriteString(fmt.Sprintf("即将过期的域名证书:%d  个\n%s\n", len(domainHttpss), expiredContents.String()))
	}
	if contents.String() != "" {
		notice.CreateMessage(
			"域名证书过期检查 - "+now.Format("2006-01-02 15:04:05"),
			contents.String(),
			notice.MessageDingtalkRobot,
			"域名证书检查通知",
			setting.GetDomainHttpsSetting().DingtalkRobotURL,
			"all",
		)
	}
	return
}

// 自动检查域名和检查是否过期并进行通知
func AutoUpdateAndCheckDomainHttps() (err error) {
	err = CheckAllDomainHttps()
	if err != nil {
		app.Log().Error("更新所有的域名中，出现错误", "err", err)
	}
	err = CheckDomainSSLAndNotice()
	return
}

func BatCheckDomainHttpsIsSecure(ids ...int) (err error) {
	if len(ids) == 0 {
		return
	}
	domainHttpss := []DomainHttps{}
	err = app.DB().Where("id IN ?", ids).Find(&domainHttpss).Error
	if err != nil {
		return
	}
	for i := range domainHttpss {
		err = domainHttpss[i].Check()
		if err != nil {
			app.Log().Error("检查域名证书失败", "域名", domainHttpss[i].Domain, "err", err)
		}
	}
	return
}

// AutoImportDomain 自动导入域名监控
func AutoImportDomain() (err error) {
	hosts, err := asset.GetPublicDomainHosts()
	if err != nil {
		return
	}
	var okNum int
	for i := range hosts {
		err1 := app.DB().Select("id").Where("domain = ?", hosts[i]).Take(&DomainHttps{}).Error
		if errors.Is(err1, gorm.ErrRecordNotFound) {
			conn, err1 := tls.DialWithDialer(&net.Dialer{
				Timeout:  time.Second * 3,
				Deadline: time.Now().Add(time.Second * 5),
			}, "tcp", hosts[i]+":443", &tls.Config{
				InsecureSkipVerify: true,
			})
			if err1 == nil {
				domainHttps := DomainHttps{
					Domain:                 hosts[i],
					Port:                   443,
					AlarmDaysBeforeExpired: 30,
					EnableMonitor:          true,
				}
				stats := conn.ConnectionState()
				certs := stats.PeerCertificates
				for i := range certs {
					if !certs[i].IsCA {
						domainHttps.NotBefore = &certs[i].NotBefore
						domainHttps.NotAfter = &certs[i].NotAfter
						if certs[i].VerifyHostname(domainHttps.Domain) == nil {
							domainHttps.IsSecure = DomainHttpsSecureSafe
						} else {
							domainHttps.IsSecure = DomainHttpsSecureDanger
						}
					}
				}
				app.DB().Create(&domainHttps)
				okNum++
				conn.Close()
			}
		}
	}
	app.Log().Info("自动导入" + strconv.Itoa(okNum) + "/" + strconv.Itoa(len(hosts)) + " 个域名监控完成")
	return
}

// GetInfo 获取证书信息
func (domain DomainHttps) GetInfo() (info map[string]any, err error) {
	info = map[string]any{}
	conn, err := tls.DialWithDialer(&net.Dialer{
		Timeout:  time.Second * 1,
		Deadline: time.Now().Add(time.Second * 1),
	}, "tcp", domain.Domain+":"+strconv.Itoa(int(domain.Port)), &tls.Config{
		InsecureSkipVerify: true,
	})
	if err == nil {
		defer conn.Close()
		stats := conn.ConnectionState()
		certs := stats.PeerCertificates
		for i := range certs {
			if !certs[i].IsCA {
				info = map[string]any{
					"dns_names":           certs[i].DNSNames,
					"country":             certs[i].Subject.Country,
					"organization":        certs[i].Subject.Organization,
					"organizational_unit": certs[i].Subject.OrganizationalUnit,
					"province":            certs[i].Subject.Province,
					"city":                certs[i].Subject.Locality,
					"not_before":          certs[i].NotBefore,
					"not_after":           certs[i].NotAfter,
					"domains":             certs[i].DNSNames,
				}
				break
			}
		}
	}
	return
}

func BatDeleteDomainHttps(ids ...uint) (okCount int64, err error) {
	result := app.DB().Where("id IN ?", ids).Delete(&DomainHttps{})
	okCount = result.RowsAffected
	err = result.Error
	return
}

func BatSwitchNoticeDomainHttps(enable bool, ids ...uint) (okCount int64, err error) {
	result := app.DB().Model(&DomainHttps{}).Where("id IN ?", ids).Update("enable_monitor", enable)
	okCount = result.RowsAffected
	err = result.Error
	return
}
