package statistics

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"errors"
	"time"

	"gorm.io/gorm"
)

type DomainBps struct {
	ID     uint    `gorm:"column:id;primaryKey;comment:主键id" json:"id"`
	Month  string  `gorm:"column:month;index;comment:统计月份" json:"month"`
	Domain string  `gorm:"column:domain;comment:域名" json:"domain"`
	InBps  float64 `gorm:"column:in_bps;comment:in_bps" json:"in_bps"`
	OutBps float64 `gorm:"column:out_bps;comment:out_bps" json:"out_bps"`
}

func (DomainBps) TableName() string {
	return "statistics_domain_bps"
}

func statDomainBps(month string) (err error) {

	monthTime, err := time.ParseInLocation("2006-01", month, time.Local)
	if err != nil {
		return
	}
	data, err := asset.GetCloudDDosDomainMonthAvgBps(monthTime)
	if err != nil {
		return
	}
	for i := range data {
		exist := DomainBps{}
		err = app.DB().Where("month = ? and domain = ?", month, data[i].Domain).Take(&exist).Error
		if err == nil {
			updateMap := map[string]any{}
			if exist.InBps != data[i].InBps {
				updateMap["in_bps"] = data[i].InBps
			}
			if exist.OutBps != data[i].OutBps {
				updateMap["out_bps"] = data[i].OutBps
			}
			if len(updateMap) > 0 {
				err = app.DB().Model(&exist).Updates(updateMap).Error
				if err != nil {
					return
				}
			}
		}
		if errors.Is(err, gorm.ErrRecordNotFound) {
			bps := DomainBps{
				Month:  month,
				Domain: data[i].Domain,
				InBps:  data[i].InBps,
				OutBps: data[i].OutBps,
			}
			err = app.DB().Create(&bps).Error
		}
		if err != nil {
			return
		}
	}
	return
}
