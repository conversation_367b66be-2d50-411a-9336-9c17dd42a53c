package robot

// 支持的命令
type CommandFunc struct {
	Func  func(usage string, params ...string) (msg HTTPBodyMarkdown)
	Usage string
}

var dingChatCommands = map[string]CommandFunc{
	"用途": {
		Func:  queryIP,
		Usage: "ip",
	},
	"查": {
		Func:  queryIP,
		Usage: "ip",
	},
	// "报表": {
	// 	Func:  getAlertReport,
	// 	Usage: "近一天/近七天/近一小时 [ip]",
	// },
	"监控": {
		Func:  switchMonitor,
		Usage: "抑制/关闭/开启 主机 [抑制小时（默认2）]）",
	},
	"批量监控": {
		Func:  switchMonitor,
		Usage: "抑制/关闭/开启 主机(逗号分隔) [抑制小时（默认2）]",
	},
	"查5xx": {
		Func:  Get5xxLogs,
		Usage: "[几分钟前至今，默认5分钟,最大100分钟] [域名，多个域名以逗号分割] ",
	},
	"查异常": {
		Func:  GetExceptions,
		Usage: "[几分钟前至今，默认5分钟,最大100分钟]",
	},
}
