package mytime

import (
	"time"
)

// IsHoliday 判断给定时间是否是节假日，这里只是一个示例实现，实际中需要根据具体情况实现
func IsHoliday(t time.Time) bool {
	// 假设周末是节假日
	weekday := t.Weekday()
	return weekday == time.Saturday || weekday == time.Sunday
}

func NextWorkingDay(t time.Time) time.Time {
	nextDay := t.AddDate(0, 0, 1)
	for IsHoliday(nextDay) {
		nextDay = nextDay.AddDate(0, 0, 1)
	}
	return nextDay
}

func NextXWorkingDays(t time.Time, x int) time.Time {
	for i := 0; i < x; i++ {
		t = NextWorkingDay(t)
	}
	return t
}

func GetLastMonth(t time.Time) time.Time {
	thisMonth := t.Format("2006-01")
	monthTime, _ := time.Parse("2006-01", thisMonth)
	return monthTime.AddDate(0, -1, 0)
}
