package monitor

import (
	"cmdb/app"
	"cmdb/app/service/audit"
	"cmdb/app/service/monitor/prometheus"
	"errors"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

func CreatePrometheusInstances(c *gin.Context) {
	var form prometheus.InstanceForm
	if err := c.ShouldBindJSON(&form); err != nil {
		app.Log().Debug("创建Prometheus实例失败", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	err := form.Create()
	if err != nil {
		msg := "创建实例失败"
		if err == prometheus.ErrInstanceExist {
			msg = "实例已存在"
		} else {
			app.Log().Error(msg, "err", err)
		}
		app.FailedResponseMsg(c, msg)
		return
	}
	audit.LogAPIOP(c, "监控管理", "添加Prometheus实例"+form.Name)
	app.SuccessResponseMsg(c, "创建成功")
}

func GetPrometheusInstances(c *gin.Context) {
	var keyword *string
	var instanceType *prometheus.InstanceType
	if val := strings.TrimSpace(c.Query("keyword")); val != "" {
		keyword = &val
	}
	if val := strings.TrimSpace(c.Query("instance_type")); val != "" {
		tmp := prometheus.InstanceType(val)
		instanceType = &tmp
	}
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	offset := (page - 1) * limit
	count, data, err := prometheus.GetInstances(offset, limit, instanceType, keyword)
	if err != nil {
		app.Log().Error("获取Prometheus实例失败", "err", err)
		app.FailedResponseMsg(c, "获取失败")
		return
	}
	app.SuccessResponseDataCount(c, data, count)
}

func DeletePrometheusInstance(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.Log().Error("删除Prometheus实例失败", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	instance, err := prometheus.GetInstance(id)
	if err != nil {
		msg := "删除实例失败"
		if errors.Is(err, gorm.ErrRecordNotFound) {
			msg = "实例不存在"
		} else {
			app.Log().Error(msg, "err", err)
		}
		app.FailedResponseMsg(c, msg)
		return
	}
	err = instance.Delete()
	if err != nil {
		msg := "删除实例失败"
		app.Log().Error(msg, "err", err)
		app.FailedResponseMsg(c, msg)
		return
	}
	audit.LogAPIOP(c, "监控管理", "删除Prometheus实例"+instance.Name)
	app.SuccessResponseMsg(c, "删除成功")
}

func TestConnectPrometheusInstance(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.Log().Error("测试连接Prometheus实例失败", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	instance, err := prometheus.GetInstance(id)
	if err != nil {
		msg := "测试连接实例失败"
		if errors.Is(err, gorm.ErrRecordNotFound) {
			msg = "实例不存在"
		} else {
			app.Log().Error(msg, "err", err)
		}
		app.FailedResponseMsg(c, msg)
		return
	}
	result, responseCode, err := instance.TestConnect()
	if err != nil {
		msg := "测试连接实例失败"
		app.Log().Error(msg, "err", err)
		app.FailedResponseMsg(c, msg)
		return
	}
	app.SuccessResponseData(c, map[string]any{
		"result":       result,
		"responseCode": responseCode,
	})
}

func UpdatePrometheusInstance(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.Log().Error("更新Prometheus实例失败", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	instance, err := prometheus.GetInstance(id)
	if err != nil {
		msg := "更新实例失败"
		if errors.Is(err, gorm.ErrRecordNotFound) {
			msg = "实例不存在"
		} else {
			app.Log().Error(msg, "err", err)
		}
		app.FailedResponseMsg(c, msg)
		return
	}
	var form prometheus.InstanceForm
	if err := c.ShouldBindJSON(&form); err != nil {
		app.Log().Error("更新Prometheus实例失败", "err", err)
		app.FailedResponseMsg(c, "表单验证失败")
		return
	}
	if err := instance.Update(form); err != nil {
		msg := "更新实例失败"
		app.Log().Error(msg, "err", err)
		app.FailedResponseMsg(c, msg)
		return
	}
	audit.LogAPIOP(c, "监控管理", "更新Prometheus实例"+instance.Name)
	app.SuccessResponseMsg(c, "更新成功")
}
