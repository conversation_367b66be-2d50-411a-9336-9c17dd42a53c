package workflow

import (
	"cmdb/app"
	"errors"

	"gorm.io/gorm"
)

type OrderExtType uint

const (
	ServerExtType OrderExtType = iota + 1
	PermissionExtType
	DomainExtType
	DatabaseExtType
	ServiceNeedExtType
	MongoDBExtType
	GitlabExtType
	CloudAccountExtType
	DesktopOPSExtType
)

func (ot OrderExtType) Name() string {
	switch ot {
	case ServerExtType:
		return "服务器申请工单"
	case PermissionExtType:
		return "权限申请工单"
	case DomainExtType:
		return "域名申请工单"
	case DatabaseExtType:
		return "数据库工单"
	case ServiceNeedExtType:
		return "业务需求工单"
	case MongoDBExtType:
		return "MongoDB工单"
	case GitlabExtType:
		return "Gitlab权限工单"
	case CloudAccountExtType:
		return "云账号工单"
	case DesktopOPSExtType:
		return "桌面技术支持工单"
	default:
		return ""
	}
}

func GetExtTypeByID(id int) (et OrderExtType, err error) {
	switch id {
	case 1:
		return ServerExtType, nil
	case 2:
		return PermissionExtType, nil
	case 3:
		return DomainExtType, nil
	case 4:
		return DatabaseExtType, nil
	case 5:
		return ServiceNeedExtType, nil
	case 6:
		return MongoDBExtType, nil
	case 7:
		return GitlabExtType, nil
	case 8:
		return CloudAccountExtType, nil
	case 9:
		return DesktopOPSExtType, nil
	default:
		return 0, errors.New("非法的工单类型")
	}
}

type OrderType string

const (
	ServerOrderType        OrderType = "server"
	PermissionOrderType    OrderType = "permission"
	PermissionDGCOrderType OrderType = "permission_dgc"
	// 权限申请工单(DGC)-敏感
	PermissionDGCSensitiveOrderType OrderType = "permission_dgc_sensitive"
	DomainOrderType                 OrderType = "domain"
	MySQLSensitiveOderType          OrderType = "database_sensitive"
	MySQLOrderType                  OrderType = "database"
	ServiceNeedOrderType            OrderType = "service_need"
	MongoDBOrderType                OrderType = "mongodb"
	GitlabOrderType                 OrderType = "gitlab"
	CloudAccountOrderType           OrderType = "cloud_account"
	DesktopOPSOrderType             OrderType = "desktop_ops"
)

func (ot OrderType) Code() string {
	return string(ot)
}

func (ot OrderType) Name() string {
	switch ot {
	case ServerOrderType:
		return "服务器申请工单"
	case PermissionOrderType:
		return "权限申请工单"
	case PermissionDGCOrderType:
		return "权限申请工单(DGC)"
	case PermissionDGCSensitiveOrderType:
		return "权限申请工单(DGC)-敏感"
	case DomainOrderType:
		return "域名申请工单"
	case MySQLOrderType:
		return "数据库工单"
	case MySQLSensitiveOderType:
		return "数据库敏感工单"
	case ServiceNeedOrderType:
		return "业务需求"
	case MongoDBOrderType:
		return "MongoDB工单"
	case GitlabOrderType:
		return "Gitlab权限工单"
	case CloudAccountOrderType:
		return "云账号工单"
	case DesktopOPSOrderType:
		return "桌面技术支持工单"
	default:
		return ""
	}
}

var OrderTypes = []Flow{
	// 1 服务器申请工单
	{Code: ServerOrderType, Name: ServerOrderType.Name(), ExtType: ServerExtType},
	// 2 权限申请工单
	{Code: PermissionOrderType, Name: PermissionOrderType.Name(), ExtType: PermissionExtType},
	// 2 权限申请工单(DGC)
	{Code: PermissionDGCOrderType, Name: PermissionDGCOrderType.Name(), ExtType: PermissionExtType},
	// 2 权限申请工单(DGC)-敏感
	{Code: PermissionDGCSensitiveOrderType, Name: PermissionDGCSensitiveOrderType.Name(), ExtType: PermissionExtType},
	// 3 域名申请工单
	{Code: DomainOrderType, Name: DomainOrderType.Name(), ExtType: DomainExtType},
	// 4 数据库工单
	{Code: MySQLOrderType, Name: MySQLOrderType.Name(), ExtType: DatabaseExtType},
	// 4 数据库敏感工单
	{Code: MySQLSensitiveOderType, Name: MySQLSensitiveOderType.Name(), ExtType: DatabaseExtType},
	// 5 业务需求
	{Code: ServiceNeedOrderType, Name: ServiceNeedOrderType.Name(), ExtType: ServiceNeedExtType},
	// 6 MongoDB工单
	{Code: MongoDBOrderType, Name: MongoDBOrderType.Name(), ExtType: MongoDBExtType},
	// 7 Gitlab权限工单
	{Code: GitlabOrderType, Name: GitlabOrderType.Name(), ExtType: GitlabExtType},
	// 8 云账号工单
	{Code: CloudAccountOrderType, Name: CloudAccountOrderType.Name(), ExtType: CloudAccountExtType},
	// 9 桌面技术支持工单
	{Code: DesktopOPSOrderType, Name: DesktopOPSOrderType.Name(), ExtType: DesktopOPSExtType},
}

func GetFlowByCode(code string) (flow Flow, err error) {
	err = app.DB().Where("code = ?", code).Take(&flow).Error
	return
}

func SyncOrderTypeFlowToDB() (err error) {
	for _, ot := range OrderTypes {
		exist := Flow{}
		err = app.DB().Where("code  = ?", ot.Code).Take(&exist).Error
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = app.DB().Create(&ot).Error
		} else if err == nil && ot.Name != exist.Name {
			err = app.DB().Model(&exist).Update("name", ot.Name).Error
		}
	}
	return
}

func (et OrderExtType) GenForm() ApplicantForm {
	switch et {
	case ServerExtType:
		return &OrderServerForm{}
	case PermissionExtType, CloudAccountExtType:
		return &OrderPermissionForm{}
	case DomainExtType:
		return &OrderDomainForm{}
	case DatabaseExtType:
		return &OrderMySQLForm{}
	case ServiceNeedExtType:
		return &OrderServiceNeedForm{}
	case MongoDBExtType:
		return &OrderMongoDBForm{}
	case GitlabExtType:
		return &OrderGitlabForm{}
	case DesktopOPSExtType:
		return &OrderDesktopOPSForm{}
	default:
		return nil
	}
}
