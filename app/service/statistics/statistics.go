package statistics

import "errors"

func GetAssetMetrics(start, end string, assetType string, metricType string, name string) (data []MetricsItem, err error) {
	switch assetType {
	case "cloud":
		return GetCloudComputerMetrics(start, end, metricType, name)
	case "region":
		return GetRegionComputerMetrics(start, end, name, metricType)
	case "optimizable":
		return GetOptimizableMetrics(start, end, metricType)
	case "computer":
		return GetComputerMetrics(start, end, metricType)
	case "data":
		return GetDataAssetMetrics(start, end, metricType)
	default:
		err = errors.New("不支持的资产类型")
	}
	return
}
