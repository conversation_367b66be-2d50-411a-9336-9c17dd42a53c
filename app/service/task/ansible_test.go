package task

import (
	"cmdb/app"
	"encoding/json"
	"os/exec"
	"testing"
)

func TestAnsibleRun(t *testing.T) {
	err := app.NewApp("../../../app.ini")
	if err != nil {
		t.Error(err)
		return
	}
	varMap := map[string]interface{}{
		"hosts": "127.0.0.1",
	}
	varMapStr, _ := json.Marshal(varMap)
	params := []string{"7200", app.Conf().Ansible.AnsiblePlaybookBinary, "--extra-vars", string(varMapStr), "--inventory", app.Conf().Ansible.AnsibleInventoryFile, "--ssh-common-args='-o StrictHostKeyChecking=no'", "/data/my/code/cmdb/script/test.yaml"}
	cmd := exec.Command("timeout", params...)
	var cmdOutput []byte
	cmdOutput, err = cmd.CombinedOutput()
	if err != nil {
		t.Error(err)
	}
	output := cmd.String() + "\n" + string(cmdOutput)
	t.Log(output)
}

func TestShellRun(t *testing.T) {
	err := app.NewApp("../../../app.ini")
	if err != nil {
		t.Error(err)
		return
	}
	params := []string{"127.0.0.1", "--inventory", app.Conf().Ansible.AnsibleInventoryFile, "--ssh-common-args='-o StrictHostKeyChecking=no'", "-m", "script", "-a", "/data/my/code/cmdb/script/test.sh"}
	cmd := exec.Command(app.Conf().Ansible.AnsibleBinary, params...)
	var cmdOutput []byte
	cmdOutput, err = cmd.CombinedOutput()
	if err != nil {
		t.Error(err)
	}
	output := cmd.String() + "\n" + string(cmdOutput)
	t.Log(output)
}
