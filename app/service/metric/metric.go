package metric

import (
	"cmdb/app"
	"runtime"
	"time"

	"github.com/prometheus/client_golang/prometheus"
)

const MetricPrefix = "cmdb_"

var (
	gcCount = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: MetricPrefix + "gc_count",
			Help: "Number of garbage collections. (count)",
		},
		[]string{
			"pid",
		},
	)

	gcPause = prometheus.NewSummaryVec(
		prometheus.SummaryOpts{
			Name:       MetricPrefix + "gc_pause",
			Help:       "Pause time for garbage collections. (seconds)",
			Objectives: map[float64]float64{0.5: 0.05, 0.9: 0.01, 0.99: 0.001},
		},
		[]string{
			"pid",
		},
	)

	gcHeapAlloc = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: MetricPrefix + "gc_heap_alloc",
			Help: "Allocated heap size. (bytes)",
		},
		[]string{
			"pid",
		},
	)

	gcHeapObjects = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: MetricPrefix + "gc_heap_objects",
			Help: "Number of heap objects. (count)",
		},
		[]string{
			"pid",
		},
	)

	gcPauseTotal = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: MetricPrefix + "gc_pause_total",
			Help: "Total pause time for garbage collections. (seconds)",
		},
		[]string{
			"pid",
		},
	)

	gcNextGC = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: MetricPrefix + "gc_next_gc",
			Help: "Heap size threshold for next GC. (bytes)",
		},
		[]string{
			"pid",
		},
	)
)

func init() {
	r := prometheus.NewRegistry()
	r.MustRegister(gcCount)
	r.MustRegister(gcPause)
	r.MustRegister(gcHeapAlloc)
	r.MustRegister(gcHeapObjects)
	r.MustRegister(gcPauseTotal)
	r.MustRegister(gcNextGC)
	prometheus.MustRegister(r)
	prometheus.DefaultGatherer = r

	collectGC()
}

func collectGC() {
	go func() {
		for {
			time.Sleep(1 * time.Second)
			collectGCOnce()
		}
	}()
}

func collectGCOnce() {
	// memStats.Alloc 表示已分配的堆内存字节数。
	// memStats.HeapObjects 表示堆内存中的对象总数。
	// memStats.NumGC 表示已完成的 GC 周期数。
	// memStats.PauseNs[(memStats.NumGC+255)%256] 表示最近一次 GC 暂停的时间，单位是纳秒。因为 PauseNs 是一个循环数组，存储最近 256 次的暂停时间，所以使用 (memStats.NumGC+255)%256 来访问最近一次的暂停时间。
	// memStats.PauseTotalNs 表示累计的 GC 暂停时间，单位是纳秒。
	// memStats.NextGC 表示下一次 GC 触发的堆大小阈值，当堆内存使用量达到这个阈值时，将触发下一次 GC。
	var stats runtime.MemStats
	runtime.ReadMemStats(&stats)
	gcCount.WithLabelValues(app.PID()).Add(float64(stats.NumGC))
	gcPause.WithLabelValues(app.PID()).Observe(float64(stats.PauseNs[(stats.NumGC+255)%256]) / 1e9)
	gcHeapAlloc.WithLabelValues(app.PID()).Set(float64(stats.Alloc))
	gcHeapObjects.WithLabelValues(app.PID()).Set(float64(stats.HeapObjects))
	gcPauseTotal.WithLabelValues(app.PID()).Add(float64(stats.PauseTotalNs) / 1e9)
	gcNextGC.WithLabelValues(app.PID()).Set(float64(stats.NextGC))

}
