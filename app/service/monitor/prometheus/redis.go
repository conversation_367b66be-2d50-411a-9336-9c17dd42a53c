package prometheus

import (
	"cmdb/app"
	"cmdb/app/service/database/redis"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"gorm.io/gorm"
)

func GetRedisInstances() (instances []*redis.Instance, err error) {
	// 获取所有 Prometheus 实例
	promInstances, err := GetAllInstances()
	if err != nil {
		return nil, fmt.Errorf("获取 Prometheus 实例失败: %v", err)
	}

	// 用于去重的 map
	instanceMap := make(map[string]*redis.Instance)

	// 遍历每个 Prometheus 实例
	for _, promInstance := range promInstances {
		app.Log().Info("prometheus实例", "datasource", promInstance.Datasource)
		// 查询 redis_used_memory 指标
		result, err := QueryInstant(promInstance.Datasource, "redis_used_memory", &time.Time{}, nil)
		if err != nil {
			app.Log().Error("查询 Redis 指标失败", "err", err, "datasource", promInstance.Datasource)
			continue
		}

		// 获取查询结果
		vector, err := result.AsVector()
		if err != nil {
			app.Log().Error("解析 Redis 指标结果失败", "err", err)
			continue
		}

		// 处理每个 Redis 实例
		for _, sample := range vector {
			// 获取标签
			ident := string(sample.Metric["ident"])
			address := string(sample.Metric["address"])
			instance := string(sample.Metric["instance"])
			myapp := string(sample.Metric["myapp"])
			replicaRole := string(sample.Metric["replica_role"])
			busigroup := string(sample.Metric["busigroup"])

			// 从 address 标签中提取端口
			parts := strings.Split(address, ":")
			if len(parts) != 2 {
				continue
			}
			portStr := parts[1]
			port, err := strconv.Atoi(portStr)
			if err != nil {
				app.Log().Error("端口转换失败", "port", portStr, "err", err)
				continue
			}

			// 构建备注信息
			var remarkParts []string
			if instance != "" {
				remarkParts = append(remarkParts, "instance:"+instance)
			}
			if myapp != "" {
				remarkParts = append(remarkParts, "myapp:"+myapp)
			}
			if replicaRole != "" {
				remarkParts = append(remarkParts, "replica_role:"+replicaRole)
			}
			if busigroup != "" {
				remarkParts = append(remarkParts, "busigroup:"+busigroup)
			}
			remark := strings.Join(remarkParts, ", ")

			// 创建 Redis 实例
			redisInstance := &redis.Instance{
				Host:   ident,
				Port:   port,
				Remark: remark,
			}

			// 使用 ident + ":" + strconv.Itoa(port) 作为唯一标识
			instanceMap[ident+":"+strconv.Itoa(port)] = redisInstance
		}
	}

	// 将 map 转换为切片
	for _, instance := range instanceMap {
		instances = append(instances, instance)
	}

	return instances, nil
}

func SyncRedisInstanceFromPrometheus() (err error) {
	instances, err := GetRedisInstances()
	if err != nil {
		return fmt.Errorf("获取 Redis 实例失败: %v", err)
	}
	for _, instance := range instances {
		exist := redis.Instance{}
		if err1 := app.DB().Where("host = ? AND port = ?", instance.Host, instance.Port).Take(&exist).Error; errors.Is(err1, gorm.ErrRecordNotFound) {
			err1 = app.DB().Create(&instance).Error
			if err1 != nil {
				app.Log().Error("创建 Redis 实例失败", "err", err1)
			}
		} else {
			app.Log().Info("Redis 实例已存在", "host", instance.Host, "port", instance.Port)
		}
	}
	return nil
}
