package asset

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"strconv"
	"sync"

	"github.com/gin-gonic/gin"
)

type ResourceGroupList struct {
	asset.ResourceGroup
	Account   asset.CloudAccount `grom:"-" json:"account"`
	HostTotal int64              `gorm:"-" json:"host_total"`
}

func GetResourceGroups(c *gin.Context) {
	var keyword *string
	var accountID *int
	if val := c.Query("keyword"); val != "" {
		keyword = &val
	}
	if val := c.Query("account_id"); val != "" {
		id, err := strconv.Atoi(val)
		if err == nil {
			accountID = &id
		}
	}
	page, _ := strconv.Atoi(c.<PERSON>("page", "1"))
	limit, _ := strconv.Atoi(c.<PERSON>fault<PERSON>("limit", "10"))
	// 计算偏移量
	offset := (page - 1) * limit
	count, data, err := asset.GetResourceGroups(offset, limit, accountID, keyword)
	if err != nil {
		app.Log().Error("获取资源组失败", "err", err)
		app.FailedResponseMsg(c, "获取资源组失败")
		return
	}
	newData := make([]ResourceGroupList, len(data))
	if len(data) > 0 {
		accountMap := sync.Map{}
		for i, v := range data {
			newData[i].ResourceGroup = v
			newData[i].HostTotal, _ = v.GetHostTotal()
			if val, ok := accountMap.Load(int(v.AccountID)); ok {
				newData[i].Account = val.(asset.CloudAccount)
			} else {
				a, err1 := asset.GetCloudAccountByID(int(v.AccountID))
				if err1 == nil {
					accountMap.Store(int(v.AccountID), a)
					newData[i].Account = a
				}
			}
		}
	}
	app.SuccessResponseDataCount(c, newData, count)
}

type AllResourceGroups struct {
	asset.ResourceGroup
	AccountName string `gorm:"-" json:"account_name"`
}

func GetAllResourceGroups(c *gin.Context) {
	data, err := asset.GetAllResourceGroups()
	if err != nil {
		app.Log().Error("获取资源组失败", "err", err)
		app.FailedResponseMsg(c, "获取资源组失败")
		return
	}
	newData := make([]AllResourceGroups, len(data))
	if len(data) > 0 {
		accountMap := sync.Map{}
		for i, v := range data {
			newData[i].ResourceGroup = v
			if val, ok := accountMap.Load(int(v.AccountID)); ok {
				newData[i].AccountName = val.(string)
			} else {
				a, err1 := asset.GetCloudAccountByID(int(v.AccountID))
				if err1 == nil {
					accountMap.Store(int(v.AccountID), a.Name)
					newData[i].AccountName = a.Name
				}
			}
		}
	}
	app.SuccessResponseData(c, newData)
}
