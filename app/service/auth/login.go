package auth

import (
	"cmdb/app"
	"cmdb/pkg/utils"
)

// Login 用户名密码登录
func Login(username, password string) (user User, err error) {
	err = app.DB().Where("username = ?", username).Take(&user).Error
	// 查询失败，直接报用户不存在
	if err != nil {
		err = ErrUserNotExists
		return
	}
	// 密码不正确
	if ok := utils.ComparePassword(password, user.Password); !ok {
		err = ErrAuthFail
		return
	}
	// 被禁用
	if user.IsDisabled {
		err = ErrUserDisabled
		return
	}
	return
}
