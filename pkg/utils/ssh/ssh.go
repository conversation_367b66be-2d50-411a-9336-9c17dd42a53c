package ssh

import (
	"fmt"
	"net"
	"os"
	"time"

	"golang.org/x/crypto/ssh"
)

// NewSession 开启一个会话
func NewSession(host string, port uint, user string, authMehtods ...ssh.AuthMethod) (sshClient *ssh.Client, err error) {
	clientConfig := &ssh.ClientConfig{
		User:    user,
		Auth:    authMehtods,
		Timeout: 5 * time.Second,
		HostKeyCallback: func(hostname string, remote net.Addr, key ssh.PublicKey) error {
			return nil
		},
	}
	sshClient, err = ssh.Dial("tcp", fmt.Sprintf("%s:%d", host, port), clientConfig)
	return
}

// NewSessionWithPassword 使用密码登录
func NewSessionWithPassword(host string, port uint, user, password string) (sshClient *ssh.Client, err error) {
	sshClient, err = NewSession(host, port, user, ssh.Password(password))
	return
}

// NewSessionWithKey 使用key登录
func NewSessionWithKey(host string, port uint, user, keyPath, keyPassword string) (sshClient *ssh.Client, err error) {
	keyData, err := os.ReadFile(keyPath)
	if err != nil {
		return
	}
	var singer ssh.Signer
	if keyPassword == "" {
		singer, err = ssh.ParsePrivateKey(keyData)
	} else {
		singer, err = ssh.ParsePrivateKeyWithPassphrase(keyData, []byte(keyPassword))
	}
	if err != nil {
		return
	}
	sshClient, err = NewSession(host, port, user, ssh.PublicKeys(singer))
	return
}

func SSHRunCommand(host string, port uint, user, keyPath, keyPassword, cmd string) (result []byte, err error) {
	sshClient, err := NewSessionWithKey(host, port, user, keyPath, keyPassword)
	if err != nil {
		return
	}
	defer sshClient.Close()
	session, err := sshClient.NewSession()
	if err != nil {
		return
	}
	defer session.Close()
	result, err = session.CombinedOutput(cmd)
	return
}
