package workflow

import (
	"cmdb/app"
	"cmdb/app/service/auth"
	"cmdb/app/service/workflow"
	"strconv"
	"sync"

	"github.com/gin-gonic/gin"
)

func GetApproverOrders(c *gin.Context) {
	var page, limit int
	var err error
	if page, err = strconv.Atoi(c<PERSON>("page", "1")); err != nil {
		page = 1
	}
	if limit, err = strconv.Atoi(c<PERSON>("limit", "10")); err != nil {
		limit = 10
	}
	offset := (page - 1) * limit
	var keyword *string
	if val := c.Query("keyword"); val != "" {
		keyword = &val
	}
	var extType *workflow.OrderExtType
	if val := c.Query("ext_type"); val != "" {
		extTypeID, err := strconv.Atoi(val)
		if err == nil {
			if et, err := workflow.GetExtTypeByID(extTypeID); err == nil {
				extType = &et
			}
		}
	}
	var applicantID *int
	if val := c.Que<PERSON>("applicant_id"); val != "" {
		if id, err := strconv.Atoi(val); err == nil {
			applicantID = &id
		}
	}
	var status *workflow.OrderStatus
	if val := c.Query("status"); val != "" {
		if id, err := strconv.Atoi(val); err == nil {
			if s, err := workflow.OrderStatus(id).GetOrderStatusByID(id); err == nil {
				status = &s
			}
		}
	}
	loginUser, err := auth.GetLoginUser(c)
	if err != nil {
		app.Log().Error("获取用户信息失败", "err", err)
		app.FailedResponseMsg(c, "获取用户信息失败")
		return
	}
	approver, err := workflow.NewApprover(loginUser)
	if err != nil {
		app.Log().Error("获取用户信息失败", "err", err)
		app.FailedResponseMsg(c, "获取用户信息失败")
		return
	}
	count, data, err := approver.GetApproverOrders(offset, limit, keyword, extType, status, applicantID)
	if err != nil {
		app.Log().Error("获取用户工单失败", "err", err)
		app.FailedResponseMsg(c, "获取用户工单失败")
		return
	}
	var newData []OrderList
	if len(data) > 0 {
		userMap := sync.Map{}
		for _, v := range data {
			if val, ok := userMap.Load(v.ApplicantID); ok {
				if applicant, ok1 := val.(workflow.Applicant); ok1 {
					newData = append(newData, OrderList{
						Order:     v,
						Applicant: applicant,
					})
				}
			} else {
				if applicant, err := workflow.GetApplicantByID(v.ApplicantID); err == nil {
					userMap.Store(v.ApplicantID, applicant)
					newData = append(newData, OrderList{
						Order:     v,
						Applicant: applicant,
					})
				}
			}
		}
	}
	app.SuccessResponseDataCount(c, newData, count)
}

func ApproveOrder(c *gin.Context) {
	// 获取工单ID
	var orderId int
	var err error
	if orderId, err = strconv.Atoi(c.Param("id")); err != nil {
		app.Log().Debug("获取工单ID失败", "id", c.Param("id"))
		app.FailedResponseMsg(c, "参数异常")
		return
	}
	var form workflow.ApproveFrom
	if err := c.ShouldBindJSON(&form); err != nil {
		app.Log().Debug("获取审批信息失败", "err", err)
		app.FailedResponseMsg(c, "参数异常")
		return
	}
	loginUser, err := auth.GetLoginUser(c)
	if err != nil {
		app.Log().Debug("获取用户信息失败", "err", err)
		app.FailedResponseMsg(c, "获取用户信息失败")
		return
	}
	approver, err := workflow.NewApprover(loginUser)
	if err != nil {
		app.Log().Error("获取审批人信息失败", "err", err)
		app.FailedResponseMsg(c, "获取审批人信息失败")
		return
	}
	order, err := workflow.GetOrderByID(orderId)
	if err != nil {
		app.Log().Error("获取工单信息失败", "err", err)
		app.FailedResponseMsg(c, "获取工单信息失败")
		return
	}
	if yes, err := order.CanApproveByApprover(approver); !yes {
		app.Log().Error("审批人无权限审批该工单", "err", err)
		app.FailedResponseMsg(c, "审批人无权限审批该工单")
		return
	}
	if err := approver.Approve(order, form); err != nil {
		msg := "审批工单失败"
		if err == workflow.ErrForbiddenApprove {
			msg = err.Error()
		}
		app.Log().Error("审批工单失败", "err", err)
		app.FailedResponseMsg(c, msg)
		return
	}
	app.SuccessResponseMsg(c, "审批工单成功")
}

// 加签或者转签
// id指定工单
// action 执行动作：add、transfer
func AddOrTransferOrderProcess(c *gin.Context) {
	// 加签转签表单
	type Form struct {
		ApproversIDs []uint `json:"approvers_ids" binding:"required"`
		NodeName     string `json:"node_name" binding:"max=255"`
		Comment      string `json:"comment" binding:"max=255"`
	}
	action := c.Param("action")
	if action != "add" && action != "transfer" {
		app.FailedResponseMsg(c, "非法参数")
		return
	}
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil || id == 0 {
		app.Log().Debug("非法工单ID", "id", c.Param("id"))
		app.FailedResponseMsg(c, "非法参数")
		return
	}
	var form Form
	err = c.ShouldBind(&form)
	if err != nil {
		app.Log().Debug("审批工单，表单验证失败", "err", err)
		app.FailedResponseMsg(c, "表单验证失败")
		return
	}
	o, err := workflow.GetOrderByID(id)
	if err != nil {
		app.Log().Error("查询工单对象失败", "err", err)
		app.FailedResponseMsg(c, "非法参数")
		return
	}
	loginUser, err := auth.GetLoginUser(c)
	if err != nil {
		app.Log().Debug("获取用户信息失败", "err", err)
		app.FailedResponseMsg(c, "获取用户信息失败")
		return
	}
	approver, err := workflow.NewApprover(loginUser)
	if err != nil {
		app.Log().Error("获取审批人信息失败", "err", err)
		app.FailedResponseMsg(c, "获取审批人信息失败")
		return
	}
	order, err := workflow.GetOrderByID(id)
	if err != nil {
		app.Log().Error("获取工单信息失败", "err", err)
		app.FailedResponseMsg(c, "获取工单信息失败")
		return
	}
	if yes, err := order.CanApproveByApprover(approver); !yes {
		app.Log().Error("审批人无权限审批该工单", "err", err)
		app.FailedResponseMsg(c, "审批人无权限审批该工单")
		return
	}
	tos, err := workflow.GetApproversByIDs(form.ApproversIDs...)
	if err != nil {
		app.Log().Error("审批工单，目标获取审批人对象失败", "err", err)
		app.FailedResponseMsg(c, "获取审批人失败")
		return
	}
	if action == "add" {
		err = o.AddProcess(tos, form.NodeName)
		if err != nil {
			app.Log().Error("审批工单，加签失败", "err", err)
			app.FailedResponseMsg(c, "加签失败")
			return
		}
	} else if action == "transfer" {
		err = o.TransferProcess(approver, tos, form.Comment)
		if err != nil {
			app.Log().Error("审批工单，转签失败", "err", err)
			app.FailedResponseMsg(c, "转签失败")
			return
		}
	}
	app.SuccessResponseMsg(c, "操作成功")
}
