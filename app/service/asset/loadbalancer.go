package asset

import (
	"cmdb/app"
	"cmdb/pkg/db"
	"fmt"
	"time"

	"gorm.io/gorm"
)

type Loadbalancer struct {
	ID               uint           `gorm:"column:id;primaryKey;comment:主键" json:"id"`
	Name             string         `gorm:"column:name;type:varchar(255);index;comment:名称" json:"name"`
	Host             string         `gorm:"column:host;type:varchar(255);index;comment:host" json:"host"`
	LoadbalancerID   string         `gorm:"column:loadbalancer_id;type:varchar(255);index;comment:负载均衡ID" json:"loadbalancer_id"`
	LoadbalancerType string         `gorm:"column:loadbalancer_type;type:varchar(255);index;comment:负载均衡类型" json:"loadbalancer_type"`
	Status           string         `gorm:"column:status;type:varchar(255);comment:状态" json:"status"`
	Spec             string         `gorm:"column:spec;type:varchar(255);comment:规格" json:"spec"`
	SyncTime         time.Time      `gorm:"column:sync_time;index;comment:同步时间" json:"sync_time"`
	DatacenterID     uint           `gorm:"column:datacenter_id;index;comment:关联dc id" json:"datacenter_id"`
	AccountID        uint           `gorm:"column:account_id;index;comment:关联云账户id" json:"account_id"`
	ResourceGroupID  string         `gorm:"column:resource_group_id;type:varchar(255);index;comment:资源组ID" json:"resource_group_id"`
	CreatedAt        time.Time      `gorm:"column:created_at;index" json:"created_at"`
	UpdatedAt        time.Time      `gorm:"column:updated_at;index" json:"updated_at"`
	DeletedAt        gorm.DeletedAt `gorm:"column:deleted_at;index" json:"-"`
}

func (Loadbalancer) TableName() string {
	return "asset_loadbalancers"
}

type GetLoadbalancerParams struct {
	Offset           int
	Limit            int
	AccountID        *int
	DatacenterID     *int
	Keyword          *string
	Host             *string
	LoadbalancerType *string
	LoadbalancerID   *string
	ResourceGroupID  *string
}

func GetLoadbalancers(params GetLoadbalancerParams) (count int64, loadbalancers []Loadbalancer, err error) {
	dbop := app.DB()
	if params.LoadbalancerType != nil {
		dbop = dbop.Where("loadbalancer_type = ?", *params.LoadbalancerType)
	}
	if params.Host != nil {
		dbop = dbop.Where("host = ?", *params.Host)
	}
	if params.AccountID != nil {
		dbop = dbop.Where("account_id = ?", *params.AccountID)
	}
	if params.DatacenterID != nil {
		dbop = dbop.Where("datacenter_id = ?", *params.DatacenterID)
	}
	if params.ResourceGroupID != nil {
		dbop = dbop.Where("resource_group_id =?", *params.ResourceGroupID)
	}
	if params.Keyword != nil {
		dbop = db.MLike(dbop, *params.Keyword, "name", "host")
	}
	if params.LoadbalancerID != nil {
		dbop = dbop.Where("loadbalancer_id = ?", *params.LoadbalancerID)
	}
	err = dbop.Model(&Loadbalancer{}).Count(&count).Order("created_at desc").Offset(params.Offset).Limit(params.Limit).Find(&loadbalancers).Error
	return
}

func (l Loadbalancer) Delete() error {
	return app.DB().Delete(&l).Error
}

func (l Loadbalancer) GetDatacenterCode() (code string) {
	var datacenter Datacenter
	err := app.DB().Where("id = ?", l.DatacenterID).First(&datacenter).Error
	if err != nil {
		return
	}
	return datacenter.Code
}

func (l Loadbalancer) GetCloudURL() (url string) {
	switch l.LoadbalancerType {
	case "elb":
		return fmt.Sprintf("https://console.huaweicloud.com/vpc/?region=%s&locale=zh-cn#/elb/detail/basicInfo?ulbId=%s", l.GetDatacenterCode(), l.LoadbalancerID)
	case "alb":
		return fmt.Sprintf("https://slb.console.aliyun.com/alb/%s/albs/%s", l.GetDatacenterCode(), l.LoadbalancerID)
	case "nlb":
		return fmt.Sprintf("https://slb.console.aliyun.com/nlb/%s/nlbs/%s", l.GetDatacenterCode(), l.LoadbalancerID)
	case "slb":
		return fmt.Sprintf("https://slb.console.aliyun.com/slb/%s/slbs/%s", l.GetDatacenterCode(), l.LoadbalancerID)
	default:
		return ""
	}
}
