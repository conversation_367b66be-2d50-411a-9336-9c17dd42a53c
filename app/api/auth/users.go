package auth

import (
	"cmdb/app"
	"cmdb/app/service/audit"
	"cmdb/app/service/auth"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

// 获取用户列表
func GetUsers(c *gin.Context) {
	// 获取页码，默认为1
	page, _ := strconv.Atoi(c.<PERSON>("page", "1"))
	// 获取每页显示数量，默认为10
	limit, _ := strconv.Atoi(c<PERSON>("limit", "10"))
	// 计算偏移量
	offset := (page - 1) * limit
	var isAdmin, isDisabled *bool
	// 判断是否为管理员
	if c.<PERSON>ry("is_admin") == "true" {
		t := true
		isAdmin = &t
	} else if c.<PERSON><PERSON>("is_admin") == "false" {
		t := false
		isAdmin = &t
	}
	// 判断是否禁用
	if c.<PERSON><PERSON>("is_disabled") == "true" {
		t := true
		isDisabled = &t
	} else if c.<PERSON>ry("is_disabled") == "false" {
		t := false
		isDisabled = &t
	}
	var groupID *int
	if val, err := strconv.Atoi(c.<PERSON><PERSON>("group_id")); err == nil {
		groupID = &val
	}
	// 构造查询参数
	params := auth.GetUsersParams{
		Offset:     offset,
		Limit:      limit,
		Keyword:    strings.TrimSpace(c.Query("keyword")),
		IsAdmin:    isAdmin,
		IsDisabled: isDisabled,
		GroupID:    groupID,
	}
	// 调用服务层获取用户列表
	count, data, err := auth.GetUsers(params)
	if err != nil {
		// 返回错误信息
		app.FailedResponseMsg(c, "获取用户失败")
		return
	}
	// 返回成功信息
	app.SuccessResponseDataCount(c, data, count)
}

// 编辑用户
func EditUser(c *gin.Context) {
	// 获取用户ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		// 记录日志
		app.Log().Debug("更新用户失败,参数错误", "id", c.Param("id"), "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	// 初始化表单
	var form auth.UpdateUserForm
	if err := c.ShouldBind(&form); err != nil {
		// 记录日志
		app.Log().Debug("更新用户失败,参数错误", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	// 调用服务层获取用户信息
	u, err := auth.GetUserByID(id)
	if err != nil {
		// 记录日志
		app.Log().Error("获取编辑用户失败", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "更新失败")
		return
	}
	// 更新用户信息
	err = u.Update(form)
	if err != nil {
		// 记录日志
		app.Log().Error("更新用户失败", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "更新失败")
		return
	}
	audit.LogAPIOP(c, "认证模块", "编辑用户："+u.Name+"("+u.Username+")")
	// 返回成功信息
	app.SuccessResponseMsg(c, "更新成功")
}

func BatGroupUsers(c *gin.Context) {
	var form auth.BatGroupUsersFrom
	if err := c.ShouldBind(&form); err != nil {
		app.Log().Debug("批量分组用户失败,参数错误", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	if err := form.Do(); err != nil {
		app.Log().Error("批量分组用户失败", "err", err)
		app.FailedResponseMsg(c, "操作失败")
		return
	}
	audit.LogAPIOP(c, "认证模块", "批量分组用户,操作："+form.OP)
	app.SuccessResponseMsg(c, "操作成功")
}

// 更新个人电话
func UpdatePersonalPhone(c *gin.Context) {
	var form auth.UpdatePersonalForm
	if err := c.ShouldBind(&form); err != nil {
		app.Log().Debug("更新个人电话失败,参数错误", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	loginUser, err := auth.GetLoginUser(c)
	if err != nil {
		app.Log().Error("获取登录用户失败", "err", err)
		app.FailedResponseMsg(c, "更新失败")
		return
	}
	if err := loginUser.UpdateUserPhone(form.Phone); err != nil {
		app.Log().Error("更新个人电话失败", "err", err)
		app.FailedResponseMsg(c, "更新失败")
		return
	}
	app.SuccessResponseMsg(c, "更新成功")
}

// 更新个人密码
func UpdatePersonalPassword(c *gin.Context) {
	var form auth.UpdatePersonalPasswordFrom
	if err := c.ShouldBind(&form); err != nil {
		app.Log().Debug("更新个人密码失败,参数错误", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	loginUser, err := auth.GetLoginUser(c)
	if err != nil {
		app.Log().Error("获取登录用户失败", "err", err)
		app.FailedResponseMsg(c, "更新失败")
		return
	}
	if err := loginUser.UpdatePersonalPassword(form); err != nil {
		app.Log().Error("更新个人密码失败", "err", err)
		app.FailedResponseMsg(c, "旧密码不正确")
		return
	}
	app.SuccessResponseMsg(c, "更新成功")
}

func GetPersonalInfo(c *gin.Context) {
	loginUser, err := auth.GetLoginUser(c)
	if err != nil {
		app.Log().Error("获取登录用户失败", "err", err)
		app.FailedResponseMsg(c, "更新失败")
		return
	}
	u, err := auth.GetUserByID(int(loginUser.ID))
	if err != nil {
		app.Log().Error("获取用户信息失败", "err", err)
		app.FailedResponseMsg(c, "获取用户信息失败")
		return
	}
	app.SuccessResponseData(c, u)
}
