package mysql

import (
	"cmdb/app"
	"cmdb/app/service/database/mysql"
	"cmdb/app/service/notice"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

func GetBackups(c *gin.Context) {
	page, _ := strconv.Atoi(c.<PERSON>("page", "1"))
	limit, _ := strconv.Atoi(c<PERSON>("limit", "10"))
	offset := (page - 1) * limit
	var keyword *string
	if val := c.Query("keyword"); val != "" {
		keyword = &val
	}
	var host *string
	if val := c.Query("host"); val != "" {
		host = &val
	}
	count, data, err := mysql.GetBackups(offset, limit, keyword, host)
	if err != nil {
		app.Log().Error("获取数据库备份失败", "err", err)
		app.FailedResponseMsg(c, "获取数据库备份失败")
		return
	}
	app.SuccessResponseDataCount(c, data, count)
}

// 保存备份记录信息
func CreateBackup(c *gin.Context) {
	var recordForm mysql.RecordForm
	err := c.ShouldBind(&recordForm)
	if err != nil {
		app.Log().Error(err.Error())
		app.FailedResponseMsg(c, err.Error())
		return
	}
	if recordForm.Host == "" {
		recordForm.Host = c.ClientIP()
	}
	backupTime := time.Now()
	if recordForm.BackupTime != "" {
		backupTime1, err := time.ParseInLocation("2006-01-02 15:04:05", recordForm.BackupTime, time.Local)
		if err == nil {
			backupTime = backupTime1
		}
	}
	err = mysql.CreateBackup(recordForm, backupTime)
	if err != nil {
		app.FailedResponseMsg(c, "保存备份记录失败")
		return
	}
	// 判断备份是否异常
	go func(host string, port uint, filename string, filesize int64) {
		ok, contents, err := mysql.CompatWithLastRecord(host, port, filename, filesize)
		if err != nil {
			app.Log().Error("对比上次备份异常 ", "err", err)
			return
		}
		if ok {
			app.Log().Debug("备份正常")
			return
		}
		contact, err := notice.GetContactByName("mysql_backup")
		if err != nil {
			app.Log().Error("获取 dba 联系人失败 ", "err", err)
		}
		err = notice.CreateMessage(host+":"+strconv.Itoa(int(port))+"备份异常", contents, contact.ContactType, contact.Name, contact.Link)
		if err != nil {
			app.Log().Error("添加通知信息异常 ", "err", err)
			return
		}
	}(recordForm.Host, recordForm.Port, recordForm.Filename, recordForm.Filesize)
	app.SuccessResponseMsg(c, "保存备份记录成功")

}

func CheckTodyBackup(c *gin.Context) {
	err := mysql.CheckTodyBackup()
	if err != nil {
		app.Log().Error("检查今天是否备份异常 ", "err", err)
		app.FailedResponseMsg(c, "检查今天是否备份异常")
		return
	}
	app.SuccessResponseMsg(c, "提交成功")
}
