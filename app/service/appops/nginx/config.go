package nginx

import (
	"bytes"
	"cmdb/app"
	"cmdb/app/service/appops/domain"
	"cmdb/pkg/utils/ssh"
	"encoding/json"
	"errors"
	"regexp"
	"sort"
	"strings"
	"time"

	"gorm.io/gorm"
)

// 同步nginx配置
func SyncConfig(host string, port uint, user, keyPath string, remark string) (err error) {
	var result []byte
	var err1 error
	trysCommands := []string{"/opt/nginx-proxy/sbin/nginx -T", "/opt/nginx/sbin/nginx -T", "nginx -T"}
	for _, cmd := range trysCommands {
		result, err1 = ssh.SSHRunCommand(host, port, user, keyPath, "", cmd)
		if err1 != nil {
			app.Log().Error("同步nginx配置，获取失败", "host", host, "cmd", cmd, "err", err1)
			continue
		}
		servernames, err0 := parseServerName(result)
		if err0 != nil {
			app.Log().Error("同步nginx配置失败,解析server_name失败", "host", host, "err", err0)
		} else {
			for i := range servernames {
				err0 = domain.GenDomain(servernames[i])
				if err0 != nil {
					app.Log().Error("同步nginx配置失败,生成域名失败", "host", host, "err", err0)
				}
			}
		}
		backends, err1 := parseConfigBackends(result)
		if err1 != nil {
			app.Log().Error("同步nginx配置失败,解析配置失败", "host", host, "err", err1)
			continue
		}
		// 排序
		sort.Strings(backends)
		backendsByte, _ := json.Marshal(backends)
		servernamesByte, _ := json.Marshal(servernames)
		var exist NginxBackend
		err = app.DB().Where("ip = ?", host).Take(&exist).Error
		if err == nil {
			updateMap := map[string]any{
				"sync_time": time.Now(),
			}
			if !bytes.Equal(exist.Backends, backendsByte) {
				updateMap["backends"] = backendsByte
			}
			if !bytes.Equal(exist.ServerNames, servernamesByte) {
				updateMap["server_names"] = servernamesByte
			}
			if remark != exist.Remark {
				updateMap["remark"] = remark
			}
			err1 := app.DB().Model(&exist).Updates(updateMap).Error
			if err1 != nil {
				app.Log().Error("同步nginx配置失败,更新配置失败", "host", host, "err", err1)
				return
			}
		} else if errors.Is(err, gorm.ErrRecordNotFound) {
			if len(backends) == 0 {
				continue
			}
			err1 := app.DB().Create(&NginxBackend{
				IP:       host,
				Backends: backendsByte,
				SyncTime: time.Now(),
				Remark:   remark,
			}).Error
			if err1 != nil {
				app.Log().Error("同步nginx配置失败,创建配置失败", "host", host, "err", err1)
				return
			}
		}
	}
	return
}

// 解析nginx配置文件
func parseConfigBackends(config []byte) (backends []string, err error) {
	backends = []string{}
	lines := strings.Split(string(config), "\n")
	isUpstream := false
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}
		if strings.Contains(line, "upstream") {
			isUpstream = true
		}
		if isUpstream && strings.Contains(line, "server") {
			// 正则匹配IP
			ipRegex := regexp.MustCompile(`\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b`)
			ips := ipRegex.FindAllString(line, -1)
			if len(ips) > 0 {
				backends = append(backends, ips[0])
			}
		}
		if isUpstream && strings.Contains(line, "}") {
			isUpstream = false
			continue
		}
		if strings.HasPrefix(line, "proxy_pass") {
			// 正则匹配IP
			ipRegex := regexp.MustCompile(`\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b`)
			ips := ipRegex.FindAllString(line, -1)
			if len(ips) > 0 {
				backends = append(backends, ips[0])
			}
		}
	}
	// 去重
	tmpMap := map[string]struct{}{}
	for i := range backends {
		tmpMap[backends[i]] = struct{}{}
	}
	backends = []string{}
	for k := range tmpMap {
		backends = append(backends, k)
	}
	return
}

// 解析nginx  配置 servername
func parseServerName(config []byte) (serverNames []string, err error) {
	serverNames = []string{}
	lines := strings.Split(string(config), "\n")
	isServerName := false
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}
		if strings.HasPrefix(line, "server_name") {
			isServerName = true
		}
		if isServerName && strings.Contains(line, ";") {
			fields := strings.Fields(strings.Split(line, ";")[0])
			if len(fields) > 1 {
				serverNames = append(serverNames, fields[1:]...)
			}
			isServerName = false
			continue
		}
	}
	// 去重
	tmpMap := map[string]struct{}{}
	// 过滤掉无效的server_name
	unnames := []string{
		"localhost",
		"localhost.localdomain",
		"localhost.localdomain.localdomain",
		"default",
		"_",
	}
	for i := range serverNames {
		// 过滤IP
		ipRegex := regexp.MustCompile(`\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b`)
		if ipRegex.MatchString(serverNames[i]) {
			continue
		}
		for _, unname := range unnames {
			if unname == serverNames[i] {
				continue
			}
		}
		tmpMap[serverNames[i]] = struct{}{}
	}
	serverNames = []string{}
	for k := range tmpMap {
		serverNames = append(serverNames, k)
	}
	return
}
