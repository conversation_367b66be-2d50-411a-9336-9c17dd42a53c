package n9e

import (
	"cmdb/app"
	"database/sql"
	"time"
)

// GetAlertStatistics 获取告警统计数据
func GetAlertStatistics(startTime, endTime time.Time, offset, limit int) (*AlertStatistics, error) {
	var stats AlertStatistics

	// 转换时间为Unix时间戳
	startTimestamp := startTime.Unix()
	endTimestamp := endTime.Unix()

	// 查询告警总数
	err := app.DB().Model(&AlertHisEvent{}).
		Where("trigger_time >= ? AND trigger_time <= ?", startTimestamp, endTimestamp).
		Count(&stats.TotalCount).Error
	if err != nil {
		return nil, err
	}

	// 查询各严重程度的告警数量
	var severityStats []struct {
		Severity int8  `json:"severity"`
		Count    int64 `json:"count"`
	}

	err = app.DB().Model(&AlertHisEvent{}).
		Select("severity, COUNT(*) as count").
		Where("trigger_time >= ? AND trigger_time <= ?", startTimestamp, endTimestamp).
		Group("severity").
		Find(&severityStats).Error
	if err != nil {
		return nil, err
	}

	// 分配各严重程度的数量
	for _, stat := range severityStats {
		switch SeverityType(stat.Severity) {
		case SeverityEmergency:
			stats.EmergencyCount = stat.Count
		case SeverityWarning:
			stats.WarningCount = stat.Count
		case SeverityNotice:
			stats.NoticeCount = stat.Count
		}
	}

	// 查询已恢复告警数量
	err = app.DB().Model(&AlertHisEvent{}).
		Where("trigger_time >= ? AND trigger_time <= ? AND is_recovered = ?",
			startTimestamp, endTimestamp, true).
		Count(&stats.RecoveredCount).Error
	if err != nil {
		return nil, err
	}

	// 计算恢复率
	stats.RecoveryRate = CalculateRecoveryRate(stats.RecoveredCount, stats.TotalCount)

	// 查询平均持续时间（只计算已恢复的告警）
	var avgDurationResult sql.NullFloat64
	err = app.DB().Model(&AlertHisEvent{}).
		Select("AVG(recover_time - trigger_time) as avg_duration").
		Where("trigger_time >= ? AND trigger_time <= ? AND is_recovered = ? AND recover_time > 0 AND recover_time > trigger_time",
			startTimestamp, endTimestamp, true).
		Scan(&avgDurationResult).Error
	if err != nil {
		return nil, err
	}

	stats.AvgDuration = CalculateAvgDuration(avgDurationResult, stats.RecoveredCount)

	return &stats, nil
}

// GetAlertStatisticsByInstance 按实例维度获取告警统计
func GetAlertStatisticsByInstance(startTime, endTime time.Time, offset, limit int) (int64, []InstanceAlertStatistics, error) {
	// 转换时间为Unix时间戳
	startTimestamp := startTime.Unix()
	endTimestamp := endTime.Unix()

	var count int64
	var results []InstanceAlertStatistics

	// 首先获取总数
	err := app.DB().Model(&AlertHisEvent{}).
		Select("DISTINCT target_ident").
		Where("trigger_time >= ? AND trigger_time <= ?", startTimestamp, endTimestamp).
		Count(&count).Error
	if err != nil {
		return 0, nil, err
	}

	// 查询实例维度统计数据
	query := `
		SELECT
			target_ident,
			MAX(target_note) as target_note,
			COUNT(*) as total_count,
			SUM(CASE WHEN severity = 0 THEN 1 ELSE 0 END) as emergency_count,
			SUM(CASE WHEN severity = 1 THEN 1 ELSE 0 END) as warning_count,
			SUM(CASE WHEN severity = 2 THEN 1 ELSE 0 END) as notice_count,
			SUM(CASE WHEN is_recovered = 1 THEN 1 ELSE 0 END) as recovered_count,
			AVG(CASE WHEN is_recovered = 1 AND recover_time > 0 AND recover_time > trigger_time THEN recover_time - trigger_time ELSE NULL END) as avg_duration
		FROM alert_his_event
		WHERE trigger_time >= ? AND trigger_time <= ?
		GROUP BY target_ident
		ORDER BY total_count DESC
		LIMIT ? OFFSET ?
	`

	rows, err := app.DB().Raw(query, startTimestamp, endTimestamp, limit, offset).Rows()
	if err != nil {
		return 0, nil, err
	}
	defer rows.Close()

	for rows.Next() {
		var stat InstanceAlertStatistics
		var avgDuration sql.NullFloat64

		err := rows.Scan(
			&stat.TargetIdent,
			&stat.TargetNote,
			&stat.TotalCount,
			&stat.EmergencyCount,
			&stat.WarningCount,
			&stat.NoticeCount,
			&stat.RecoveredCount,
			&avgDuration,
		)
		if err != nil {
			return 0, nil, err
		}

		// 计算恢复率
		stat.RecoveryRate = CalculateRecoveryRate(stat.RecoveredCount, stat.TotalCount)

		// 计算平均持续时间
		stat.AvgDuration = CalculateAvgDuration(avgDuration, stat.RecoveredCount)

		results = append(results, stat)
	}

	return count, results, nil
}

// GetAlertStatisticsByRule 按规则维度获取告警统计
func GetAlertStatisticsByRule(startTime, endTime time.Time, offset, limit int) (int64, []RuleAlertStatistics, error) {
	// 转换时间为Unix时间戳
	startTimestamp := startTime.Unix()
	endTimestamp := endTime.Unix()

	var count int64
	var results []RuleAlertStatistics

	// 首先获取总数
	err := app.DB().Model(&AlertHisEvent{}).
		Select("DISTINCT rule_id").
		Where("trigger_time >= ? AND trigger_time <= ?", startTimestamp, endTimestamp).
		Count(&count).Error
	if err != nil {
		return 0, nil, err
	}

	// 查询规则维度统计数据
	query := `
		SELECT
			rule_id,
			MAX(rule_name) as rule_name,
			MAX(rule_note) as rule_note,
			COUNT(*) as trigger_count,
			COUNT(DISTINCT target_ident) as affected_instances,
			SUM(CASE WHEN severity = 0 THEN 1 ELSE 0 END) as emergency_count,
			SUM(CASE WHEN severity = 1 THEN 1 ELSE 0 END) as warning_count,
			SUM(CASE WHEN severity = 2 THEN 1 ELSE 0 END) as notice_count,
			SUM(CASE WHEN is_recovered = 1 THEN 1 ELSE 0 END) as recovered_count,
			AVG(CASE WHEN is_recovered = 1 AND recover_time > 0 AND recover_time > trigger_time THEN recover_time - trigger_time ELSE NULL END) as avg_duration
		FROM alert_his_event
		WHERE trigger_time >= ? AND trigger_time <= ?
		GROUP BY rule_id
		ORDER BY trigger_count DESC
		LIMIT ? OFFSET ?
	`

	rows, err := app.DB().Raw(query, startTimestamp, endTimestamp, limit, offset).Rows()
	if err != nil {
		return 0, nil, err
	}
	defer rows.Close()

	for rows.Next() {
		var stat RuleAlertStatistics
		var avgDuration sql.NullFloat64

		err := rows.Scan(
			&stat.RuleID,
			&stat.RuleName,
			&stat.RuleNote,
			&stat.TriggerCount,
			&stat.AffectedInstances,
			&stat.EmergencyCount,
			&stat.WarningCount,
			&stat.NoticeCount,
			&stat.RecoveredCount,
			&avgDuration,
		)
		if err != nil {
			return 0, nil, err
		}

		// 计算恢复率
		stat.RecoveryRate = CalculateRecoveryRate(stat.RecoveredCount, stat.TriggerCount)

		// 计算平均持续时间
		stat.AvgDuration = CalculateAvgDuration(avgDuration, stat.RecoveredCount)

		results = append(results, stat)
	}

	return count, results, nil
}

// GetAlertStatisticsByCluster 按集群维度获取告警统计
func GetAlertStatisticsByCluster(startTime, endTime time.Time, offset, limit int) (int64, []ClusterAlertStatistics, error) {
	// 转换时间为Unix时间戳
	startTimestamp := startTime.Unix()
	endTimestamp := endTime.Unix()

	var count int64
	var results []ClusterAlertStatistics

	// 首先获取总数
	err := app.DB().Model(&AlertHisEvent{}).
		Select("DISTINCT cluster").
		Where("trigger_time >= ? AND trigger_time <= ?", startTimestamp, endTimestamp).
		Count(&count).Error
	if err != nil {
		return 0, nil, err
	}

	// 查询集群维度统计数据
	query := `
		SELECT
			cluster,
			COUNT(*) as total_count,
			COUNT(DISTINCT target_ident) as affected_instances,
			SUM(CASE WHEN severity = 0 THEN 1 ELSE 0 END) as emergency_count,
			SUM(CASE WHEN severity = 1 THEN 1 ELSE 0 END) as warning_count,
			SUM(CASE WHEN severity = 2 THEN 1 ELSE 0 END) as notice_count,
			SUM(CASE WHEN is_recovered = 1 THEN 1 ELSE 0 END) as recovered_count,
			AVG(CASE WHEN is_recovered = 1 AND recover_time > 0 AND recover_time > trigger_time THEN recover_time - trigger_time ELSE NULL END) as avg_duration
		FROM alert_his_event
		WHERE trigger_time >= ? AND trigger_time <= ?
		GROUP BY cluster
		ORDER BY total_count DESC
		LIMIT ? OFFSET ?
	`

	rows, err := app.DB().Raw(query, startTimestamp, endTimestamp, limit, offset).Rows()
	if err != nil {
		return 0, nil, err
	}
	defer rows.Close()

	for rows.Next() {
		var stat ClusterAlertStatistics
		var avgDuration sql.NullFloat64

		err := rows.Scan(
			&stat.Cluster,
			&stat.TotalCount,
			&stat.AffectedInstances,
			&stat.EmergencyCount,
			&stat.WarningCount,
			&stat.NoticeCount,
			&stat.RecoveredCount,
			&avgDuration,
		)
		if err != nil {
			return 0, nil, err
		}

		// 计算恢复率
		stat.RecoveryRate = CalculateRecoveryRate(stat.RecoveredCount, stat.TotalCount)

		// 计算平均持续时间
		stat.AvgDuration = CalculateAvgDuration(avgDuration, stat.RecoveredCount)

		results = append(results, stat)
	}

	return count, results, nil
}
