
import configparser
import pymysql
import logging
import os
from datetime import datetime
from contextlib import contextmanager
from typing import List, Set, Any


class N9eMonitorSync:
    """N9e监控数据同步工具"""

    """
    N9eMonitorSync 类 - N9e监控数据同步工具
    
    功能概述:
    1. 从配置文件中加载数据库连接信息
    2. 从多个N9e监控数据库收集主机标识(ident)
    3. 将收集到的主机标识同步到CMDB系统的asset_hosts表中
    
    主要方法:
    - sync_idents(): 主同步方法，协调整个同步流程
    - _collect_idents_from_db(): 从指定数据库收集主机标识
    - _update_host_records(): 更新CMDB主机监控状态
    
    配置要求:
    - 需要sync_n9e_monitor.ini配置文件
    - 支持多数据库配置节(primary_db等)
    - 兼容旧版app.ini配置(会输出警告)
    
    日志记录:
    - 默认INFO级别
    - 可通过配置文件修改日志级别
    - 格式: 时间 - 级别 - 消息
    
    异常处理:
    - 数据库连接失败会记录错误并抛出异常
    - 主流程异常会记录并退出程序(exit code 1)
    """

    def __init__(self):
        self.logger = self._setup_logger()
        self.config = self._load_config()
        self.current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    @staticmethod
    def _setup_logger() -> logging.Logger:
        """初始化日志记录器"""
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.INFO)
        
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        return logger
    
    def _load_config(self) -> configparser.ConfigParser:
        """加载配置文件"""
        config = configparser.ConfigParser()
        config_path = os.path.join(os.path.dirname(__file__), 'sync_n9e_monitor.ini')
        
        if not os.path.exists(config_path):
            self._create_default_config(config_path)
            self.logger.warning(f"配置文件不存在，已创建默认配置: {config_path}")
        
        config.read(config_path)
        
        # 兼容旧版app.ini
        app_ini_path = os.path.join(os.path.dirname(__file__), 'app.ini')
        if os.path.exists(app_ini_path):
            config.read(app_ini_path)
            self.logger.warning("检测到旧版app.ini配置，建议迁移到sync_n9e_monitor.ini")
        
        # 更新日志级别
        if config.has_section('logging'):
            self.logger.setLevel(config.get('logging', 'level', fallback='INFO'))
        
        return config
    
    @staticmethod
    def _create_default_config(config_path: str) -> None:
        """创建默认配置文件"""
        config = configparser.ConfigParser()
        
        # 主数据库配置
        config['primary_db'] = {
            'host': '127.0.0.1',
            'port': '3306',
            'user': 'your_username',
            'password': 'your_password',
            'database': 'n9e'
        }
        
        # 日志配置
        config['logging'] = {
            'level': 'INFO'
        }
        
        with open(config_path, 'w') as f:
            config.write(f)
    
    def _get_db_sections(self) -> List[str]:
        """获取所有数据库配置节"""
        return [section for section in self.config.sections() 
                if section not in ['logging'] and not section.startswith('DEFAULT')]
    
    @contextmanager
    def _get_db_connection(self, section: str) -> Any:
        """获取数据库连接上下文管理器"""
        conn = None
        try:
            conn = pymysql.connect(
                host=self.config.get(section, 'host'),
                port=self.config.getint(section, 'port'),
                user=self.config.get(section, 'user'),
                password=self.config.get(section, 'password'),
                database=self.config.get(section, 'database'),
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor
            )
            yield conn
        except Exception as e:
            self.logger.error(f"数据库[{section}]连接失败: {str(e)}")
            raise
        finally:
            if conn:
                conn.close()
    
    def _collect_idents_from_db(self, section: str) -> Set[str]:
        """从指定数据库收集ident"""
        try:
            with self._get_db_connection(section) as conn:
                with conn.cursor() as cursor:
                    cursor.execute("SELECT DISTINCT ident AS ip FROM target")
                    ips = {row['ip'] for row in cursor.fetchall()}
                    self.logger.info(f"从数据库[{section}]获取到{len(ips)}个唯一ip")
                    return ips
        except Exception as e:
            self.logger.error(f"处理数据库[{section}]时出错: {str(e)}")
            return set()
    
    def _update_host_records(self, section: str, idents: Set[str]) -> None:
        """更新host表记录"""
        update_sql = """
        UPDATE cmdb3.asset_hosts 
        SET n9e_monitor = TRUE,
            n9e_monitor_time = %s
        WHERE ip = %s
        AND deleted_at IS NULL
        """
        
        try:
            with self._get_db_connection(section) as conn:
                with conn.cursor() as cursor:
                    for ip in idents:
                        cursor.execute(update_sql, (self.current_time, ip))
                        self.logger.debug(f"已更新cmdb3.asset_hosts记录: {ip}")
                    conn.commit()
        except Exception as e:
            self.logger.error(f"更新host表时出错: {str(e)}")
            raise
    
    def sync_idents(self) -> None:
        """同步所有ident到host表"""
        db_sections = self._get_db_sections()
        if not db_sections:
            self.logger.error("未找到有效的数据库配置")
            return
        
        # 从所有数据库收集ident
        all_idents: Set[str] = set()
        for section in db_sections:
            idents = self._collect_idents_from_db(section)
            all_idents.update(idents)
        
        if not all_idents:
            self.logger.warning("没有找到需要同步的ident")
            return
        
        # 使用第一个数据库连接更新host表
        self._update_host_records(db_sections[0], all_idents)
        self.logger.info(f"成功从{len(db_sections)}个数据库同步{len(all_idents)}条唯一记录")

if __name__ == "__main__":
    try:
        sync = N9eMonitorSync()
        sync.sync_idents()
    except Exception as e:
        logging.getLogger(__name__).error(f"程序执行出错: {str(e)}")
        exit(1)