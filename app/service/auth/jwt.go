package auth

import (
	"cmdb/app"
	"encoding/json"
	"errors"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
)

var jwtKey = []byte("lzGSP6RMWz6Ho7xTmJ/Ln6WHSvoXDTYv")

func (u User) GenerateToken(expiredDuration time.Duration) (tokenStr string, err error) {
	roles := []string{}
	json.Unmarshal(u.Roles, &roles)
	if u.IsAdmin {
		roles = append(roles, "admin")
	}
	roles = append(roles, "common")
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
		"id":    u.ID,
		"name":  u.Name,
		"email": u.Email,
		"role":  u.IsAdmin,
		"roles": roles,
		"sub":   u.Username,
		"aud":   "ops",
		"iss":   "ops",
		"exp":   jwt.NewNumericDate(time.Now().Add(expiredDuration)),
		"nbf":   jwt.NewNumericDate(time.Now()),
		"iat":   jwt.NewNumericDate(time.Now()),
	})
	tokenStr, err = token.SignedString(jwtKey)
	return
}

func ParseToken(token string) (user User, err error) {
	claims := jwt.MapClaims{}
	_, err = jwt.ParseWithClaims(token, claims, func(token *jwt.Token) (interface{}, error) {
		return jwtKey, nil
	})
	if err != nil {
		return
	}
	if value, ok := claims["id"].(float64); ok {
		user.ID = uint(value)
	} else {
		err = jwt.ErrTokenInvalidId
		return
	}
	if value, ok := claims["name"].(string); ok {
		user.Name = value
	}
	if value, ok := claims["email"].(string); ok {
		user.Email = value
	}
	if value, ok := claims["role"].(bool); ok {
		user.IsAdmin = value
	}
	if value, ok := claims["roles"].([]string); ok {
		var rolesByte []byte
		rolesByte, err = json.Marshal(value)
		if err != nil {
			return
		}
		user.Roles = rolesByte
	}
	if value, ok := claims["sub"].(string); ok {
		user.Username = value
	}
	if value, ok := claims["exp"].(float64); ok {
		if int64(value) < time.Now().Unix() {
			err = jwt.ErrTokenExpired
			return
		}
	}
	if value, ok := claims["nbf"].(float64); ok {
		if int64(value) > time.Now().Unix() {
			err = jwt.ErrTokenNotValidYet
			return
		}
	}
	return
}

func GetLoginUser(c *gin.Context) (u User, err error) {
	u, err = ParseToken(app.GetToken(c))
	if err != nil {
		return
	}
	if u.ID == 0 {
		err = errors.New("用户不存在")
		return
	}
	return
}

func (key Key) GenerateToken(expiredDuration time.Duration) (tokenStr string, err error) {
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
		"id":   key.ID,
		"name": key.Name,
		"aud":  "ops",
		"iss":  "ops",
		"exp":  jwt.NewNumericDate(time.Now().Add(expiredDuration)),
		"nbf":  jwt.NewNumericDate(time.Now()),
		"iat":  jwt.NewNumericDate(time.Now()),
	})
	tokenStr, err = token.SignedString(jwtKey)
	return
}

func ParseKeyToken(token string) (key Key, err error) {
	claims := jwt.MapClaims{}
	_, err = jwt.ParseWithClaims(token, claims, func(token *jwt.Token) (interface{}, error) {
		return jwtKey, nil
	})
	if err != nil {
		return
	}
	if value, ok := claims["id"].(float64); ok {
		key.ID = uint(value)
	} else {
		err = jwt.ErrTokenInvalidId
		return
	}
	if value, ok := claims["name"].(string); ok {
		key.Name = value
	}
	if value, ok := claims["exp"].(float64); ok {
		if int64(value) < time.Now().Unix() {
			err = jwt.ErrTokenExpired
			return
		}
	}
	if value, ok := claims["nbf"].(float64); ok {
		if int64(value) > time.Now().Unix() {
			err = jwt.ErrTokenNotValidYet
			return
		}
	}
	return
}
