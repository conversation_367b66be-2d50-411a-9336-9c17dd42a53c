package workflow

import (
	"bytes"
	"cmdb/app"
	"cmdb/app/service/auth"
	"encoding/json"
	"errors"
	"reflect"
	"strings"
	"time"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// 工单模板
type Flow struct {
	ID      uint         `gorm:"column:id;primarykey;comment:id" json:"id"`
	Name    string       `gorm:"column:name;type:varchar(255);comment:流程模板名称" json:"name"`
	Code    OrderType    `gorm:"column:code;type:varchar(255);unique;comment:流程模板代码" json:"code"`
	ExtType OrderExtType `gorm:"column:ext_type;comment:工单类型" json:"ext_type"`
	Nodes   []FlowNode   `gorm:"-" json:"nodes"`
}

func (Flow) TableName() string {
	return "workflow_flows"
}

// 流程
type FlowNode struct {
	ID        uint   `gorm:"column:id;primarykey;comment:id" json:"id"`
	Name      string `gorm:"column:name;type:varchar(255);index;comment:节点名称" json:"name"`
	FlowID    uint   `gorm:"column:flow_id;index;comment:流程模板id" json:"flow_id"`
	FlowIndex uint   `gorm:"column:flow_index;index;comment:顺序" json:"flow_index"`
	// 审批人或者分组
	ApproverType   ApproverType   `gorm:"approver_type;comment:审批人类型" json:"approver_type"`
	ApproverIDs    datatypes.JSON `gorm:"approver_ids;comment:审批人（组）ID"  json:"approver_ids"`
	Approvers      []Approver     `gorm:"-" json:"approvers"`
	ApproversNames string         `gorm:"-" json:"approvers_names"`
	// 抄送
	CCType  ApproverType   `gorm:"cc_type;type:varchar(255);comment:抄送人类型" json:"cc_type"`
	CCIDs   datatypes.JSON `gorm:"cc_ids;comment:抄送人（组）ID"  json:"cc_ids"`
	CCs     []Approver     `gorm:"-" json:"ccs"`
	CCNames string         `gorm:"-" json:"cc_names"`
}

func (FlowNode) TableName() string {
	return "workflow_flow_nodes"
}

type FlowNodeForm struct {
	Name         string       `json:"name" binding:"required,max=255"`
	ApproverType ApproverType `json:"approver_type"`
	ApproverIDs  []uint       `json:"approver_ids"`
	CCType       ApproverType `json:"cc_type"`
	CCIDs        []uint       `json:"cc_ids"`
	FlowIndex    uint         `json:"flow_index"`
	ID           uint         `json:"id"`
}
type FlowNodesForm struct {
	FlowID int            `json:"flow_id"`
	Nodes  []FlowNodeForm `json:"nodes"`
}

func (t *Flow) UpdateNodes(form FlowNodesForm) (err error) {
	indexes := []uint{}
	dbop := app.DB().Begin()
	if len(form.Nodes) == 0 {
		// 清理不存在的步骤
		err = dbop.Where("flow_id = ? ", t.ID).Delete(&FlowNode{}).Error
		if err != nil {
			dbop.Rollback()
			return
		}
		err = dbop.Commit().Error
		if err != nil {
			dbop.Rollback()
		}
		return
	}
	index := 0
	// 遍历更新或者创建模板步骤
	for i := range form.Nodes {
		if form.Nodes[i].FlowIndex < 1 {
			dbop.Rollback()
			return errors.New("非法索引")
		}
		existProcess := FlowNode{}
		approverIDsByte, _ := json.Marshal(form.Nodes[i].ApproverIDs)
		ccIDsByte, _ := json.Marshal(form.Nodes[i].CCIDs)
		err = dbop.Where("id = ? AND flow_id = ? ", form.Nodes[i].ID, t.ID).Take(&existProcess).Error
		if err == nil {
			updateMap := map[string]any{}
			if form.Nodes[i].Name != existProcess.Name {
				updateMap["name"] = form.Nodes[i].Name
			}
			if form.Nodes[i].FlowIndex != existProcess.FlowIndex {
				updateMap["flow_index"] = form.Nodes[i].FlowIndex
			}
			if form.Nodes[i].ApproverType != existProcess.ApproverType {
				updateMap["approver_type"] = form.Nodes[i].ApproverType
			}
			if !bytes.Equal(approverIDsByte, existProcess.ApproverIDs) {
				updateMap["approver_ids"] = approverIDsByte
			}
			if !bytes.Equal(ccIDsByte, existProcess.CCIDs) {
				updateMap["cc_ids"] = ccIDsByte
			}
			if form.Nodes[i].CCType != existProcess.CCType {
				updateMap["cc_type"] = form.Nodes[i].CCType
			}
			if len(updateMap) > 0 {
				err = dbop.Model(&existProcess).Updates(updateMap).Error
				if err != nil {
					dbop.Rollback()
					return err
				}
			}
		} else if err == gorm.ErrRecordNotFound {
			err = dbop.Create(&FlowNode{
				FlowID:       t.ID,
				FlowIndex:    form.Nodes[i].FlowIndex,
				Name:         form.Nodes[i].Name,
				ApproverType: form.Nodes[i].ApproverType,
				ApproverIDs:  approverIDsByte,
				CCType:       form.Nodes[i].CCType,
				CCIDs:        ccIDsByte,
			}).Error
		}
		if err != nil {
			dbop.Rollback()
			return
		}
		indexes = append(indexes, form.Nodes[i].FlowIndex)
		index++
	}
	// 清理不存在的步骤
	err = dbop.Where("flow_index NOT IN (?) AND flow_id = ? ", indexes, t.ID).
		Delete(&FlowNode{}).Error
	if err != nil {
		dbop.Rollback()
		return
	}
	err = dbop.Commit().Error
	if err != nil {
		dbop.Rollback()
	}
	return
}

func GetAllFlows() (flows []Flow, err error) {
	err = app.DB().Order("name").Find(&flows).Error
	if err != nil {
		return
	}
	for i := range flows {
		flows[i].Nodes, err = flows[i].GetFlowNodes()
		if err != nil {
			return
		}
	}
	return
}

func (flow Flow) GetFlowNodes() (nodes []FlowNode, err error) {
	err = app.DB().Where("flow_id = ?", flow.ID).Order("flow_index").Find(&nodes).Error
	if err != nil {
		return
	}
	for i := range nodes {
		ids := []uint{}
		json.Unmarshal(nodes[i].ApproverIDs, &ids)
		if names, err := nodes[i].ApproverType.GetNames(ids); err == nil {
			nodes[i].ApproversNames = strings.Join(names, "、")
		}
		json.Unmarshal(nodes[i].CCIDs, &ids)
		if names, err := nodes[i].CCType.GetNames(ids); err == nil {
			nodes[i].CCNames = strings.Join(names, "、")
		}
	}
	return
}

// 根据ID获取Flow
func GetFlowByID(id int) (flow Flow, err error) {
	// 从数据库中查询ID为id的Flow
	err = app.DB().Where("id = ?", id).Take(&flow).Error
	// 返回查询结果和错误信息
	return
}

func (flow Flow) GenProcesses(code string, a Applicant, admin *auth.User) (ps []Process, err error) {
	sn := genSN(code)
	ps, err = flow.genProcesses(sn, a, admin)
	return
}

// 根据流程和申请人生成流程节点
func (flow Flow) genProcesses(sn string, a Applicant, admin *auth.User) (ps []Process, err error) {
	now := time.Now()
	flow.Nodes, err = flow.GetFlowNodes()
	if err != nil {
		return
	}
	// 初始化流程节点列表
	firstNodeName := "申请人"
	byteApplicantID, _ := json.Marshal(&[]uint{a.ID})
	firstComment := "发起申请"
	if admin != nil {
		firstNodeName = "申请人:" + a.Name + "(" + a.Username + ")"
		byteApplicantID, _ = json.Marshal(&[]uint{a.ID, admin.ID})
		firstComment = "管理员:" + admin.Name + "(" + admin.Username + ") 帮 " + a.Name + "(" + a.Username + ") 发起申请"
	}
	ps = []Process{
		{
			NodeName:     firstNodeName,
			ProcessIndex: 0,
			SN:           sn,
			ApproverID:   a.ID,
			ApproversIDs: byteApplicantID,
			Status:       PassProcessStatus,
			Comment:      firstComment,
			ApproveTime:  &now,
			CreatedAt:    now,
		},
	}
	preApproveIDs := []uint{a.ID}
	// 遍历流程节点
	for i := range flow.Nodes {
		// 初始化审批人ID列表
		approverIDs := []uint{}
		// 根据审批人类型获取审批人ID
		switch flow.Nodes[i].ApproverType {
		case ApproverTypeUser:
			// 如果审批人类型为用户，则从流程节点中获取审批人ID
			json.Unmarshal(flow.Nodes[i].ApproverIDs, &approverIDs)
		case ApproverTypeLeader:
			// 如果审批人类型为领导，则从申请人中获取领导ID
			l, err1 := a.GetLeader()
			if err1 != nil {
				// 如果获取领导ID失败，则返回错误
				err = err1
				return
			}
			isSuperleader := false
			for i := range SUPERLEADER_SNs {
				if SUPERLEADER_SNs[i] == l.SN {
					isSuperleader = true
					break
				}
			}
			if isSuperleader {
				continue
			}
			approverIDs = []uint{l.ID}
		case ApproverTypeGroup:
			// 如果审批人类型为组，则从流程节点中获取组ID，并根据组ID获取审批人ID
			groupIDs := []uint{}
			json.Unmarshal(flow.Nodes[i].ApproverIDs, &groupIDs)
			approvers, err1 := GetApproversByGroupIDs(groupIDs...)
			if err1 != nil {
				// 如果获取审批人ID失败，则返回错误
				err = err1
				return
			}
			for _, approver := range approvers {
				approverIDs = append(approverIDs, approver.ID)
			}
		}
		if reflect.DeepEqual(preApproveIDs, approverIDs) {
			// 过滤重复的审批
			continue
		} else {
			preApproveIDs = approverIDs
		}
		// 将审批人ID列表转换为字节数组
		byteApproversIDs, _ := json.Marshal(approverIDs)
		// 初始化抄送人ID列表
		ccIDs := []uint{}
		// 根据抄送人类型获取抄送人ID
		switch flow.Nodes[i].CCType {
		case ApproverTypeUser:
			// 如果抄送人类型为用户，则从流程节点中获取抄送人ID
			json.Unmarshal(flow.Nodes[i].CCIDs, &ccIDs)
		case ApproverTypeLeader:
			// 如果抄送人类型为领导，则从申请人中获取领导ID
			l, err1 := a.GetLeader()
			if err1 != nil {
				// 如果获取领导ID失败，则返回错误
				err = err1
				return
			}
			ccIDs = []uint{l.ID}
		case ApproverTypeGroup:
			// 如果抄送人类型为组，则从流程节点中获取组ID，并根据组ID获取抄送人ID
			groupIDs := []uint{}
			json.Unmarshal(flow.Nodes[i].CCIDs, &groupIDs)
			approvers, err1 := GetApproversByGroupIDs(groupIDs...)
			if err1 != nil {
				// 如果获取抄送人ID失败，则返回错误
				err = err1
				return
			}
			for _, approver := range approvers {
				ccIDs = append(ccIDs, approver.ID)
			}
		}
		// 将抄送人ID列表转换为字节数组
		byteccIDs, _ := json.Marshal(ccIDs)
		// 将流程节点添加到流程节点列表中
		ps = append(ps, Process{
			NodeName:     flow.Nodes[i].Name,
			ProcessIndex: flow.Nodes[i].FlowIndex,
			SN:           sn,
			ApproversIDs: byteApproversIDs,
			CCIDs:        byteccIDs,
			CreatedAt:    now,
		})
	}
	// 返回流程节点列表和错误信息
	return
}
