package note

import (
	"cmdb/app"
	"cmdb/pkg/db"
	"errors"
	"time"

	"gorm.io/gorm"
)

var (
	ErrBookmarkExist  = errors.New("书签已经存在")
	ErrBookmarkSetTop = errors.New("顶置书签失败")
)

// Bookmark 书签
type Bookmark struct {
	ID        uint      `gorm:"primaryKey;column:id;comment:ID" json:"id"`
	Title     string    `gorm:"type:varchar(255);not null;unique;column:title;comment:标题" json:"title"`
	URL       string    `gorm:"type:varchar(1000);column:url;comment:URL地址" json:"url"`
	Remark    string    `gorm:"type:varchar(5000);column:remark;comment:备注" json:"remark"`
	SetTop    bool      `gorm:"column:set_top;index;DEFAULT:0;comment:1 是设置为常用，其他未非常用" json:"set_top"`
	CreatedAt time.Time `gorm:"column:created_at;index;comment:创建时间" json:"created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at;comment:更新时间" json:"updated_at"`
}

// TableName 设置表名
func (*Bookmark) TableName() string {
	return "note_bookmarks"
}

// 分页获取书签列表
func GetBookmarks(offset, limit int, keyword *string) (count int64, bookmarks []Bookmark, err error) {
	dbop := app.DB()
	if keyword != nil {
		dbop = db.MLike(dbop, *keyword, "title", "url", "remark")
	}
	err = dbop.Model(&Bookmark{}).Count(&count).Order("updated_at DESC").Offset(offset).Limit(limit).Find(&bookmarks).Error
	return
}

// BookmarkForm 书签表单
type BookmarkForm struct {
	Title  string `json:"title" binding:"required,max=255"`
	URL    string `json:"url" binding:"required,max=1000"`
	Remark string `json:"remark" binding:"max=5000"`
}

// 添加书签
func (form *BookmarkForm) Create() (err error) {
	err = app.DB().Select("id").Where("title = ?", form.Title).Take(&Bookmark{}).Error
	if err == nil {
		return ErrBookmarkExist
	}
	if err == gorm.ErrRecordNotFound {
		err = app.DB().Create(&Bookmark{
			Title:  form.Title,
			URL:    form.URL,
			Remark: form.Remark,
		}).Error
	}
	return
}

// Update 更新书签
func (bookmark *Bookmark) Update(form BookmarkForm) (err error) {
	updateMap := map[string]any{}
	if bookmark.Title != form.Title {
		// 如果有更新标题就要判断唯一性
		exist := Bookmark{}
		err = app.DB().Select("id").Where("title = ?", form.Title).Take(&exist).Error
		// 保证标题的唯一
		if err == nil && exist.ID != bookmark.ID {
			// 查得到，但是不是他本身
			return ErrBookmarkExist
		} else if err != nil && err != gorm.ErrRecordNotFound {
			// 数据库查询错误
			return
		}
		err = nil
		updateMap["title"] = form.Title
	}
	if bookmark.Remark != form.Remark {
		updateMap["remark"] = form.Remark
	}
	if bookmark.URL != form.URL {
		updateMap["url"] = form.URL
	}
	if len(updateMap) > 0 {
		err = app.DB().Model(bookmark).Updates(updateMap).Error
	}
	return
}

// 删除书签
func (bookmark *Bookmark) Delete() (err error) {
	err = app.DB().Delete(bookmark).Error
	return
}

// 通过ID获取书签
func GetBookmarkByID(id int) (bookmark Bookmark, err error) {
	err = app.DB().Where("id = ?", id).Take(&bookmark).Error
	return
}

// Top 设置书签为常用书签
func (bookmark *Bookmark) Top(setTop bool) (err error) {
	if setTop {
		// 判断顶置的数量是否到10条
		var count int64
		err = app.DB().Model(&Bookmark{}).Where("set_top = ?", setTop).Count(&count).Error
		if err == nil && count >= 10 {
			return ErrBookmarkSetTop
		}
	}
	err = app.DB().Model(bookmark).Update("set_top", setTop).Error
	return
}

// 获取所有的标签
func GetAllBookmark(unTop bool) (bookmarks []Bookmark, err error) {
	dbop := app.DB()
	if unTop {
		dbop = dbop.Where("set_top != ?", unTop)
	}
	err = dbop.Find(&bookmarks).Error
	return
}

// 获取常用的标签
func GetTopBookmarks() (bookmarks []Bookmark, err error) {
	err = app.DB().Where("set_top = ?", true).Find(&bookmarks).Error
	return
}
