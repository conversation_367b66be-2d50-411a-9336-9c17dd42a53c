package notice

import (
	"cmdb/app"
	"cmdb/pkg/db"
	"errors"
	"time"

	"gorm.io/gorm"
)

var (
	ErrContactExist = errors.New("联系人已经存在")
)

// 联系人结构体
type Contact struct {
	ID          uint           `gorm:"primaryKey;column:id;comment:ID" json:"id"`
	Name        string         `gorm:"type:varchar(255);index;column:name;comment:名称" json:"name"`
	ContactType MessageType    `gorm:"type:varchar(255);column:contact_type;index;comment:联系人类型" json:"contact_type"`
	Link        string         `gorm:"type:varchar(1000);column:link;comment:邮箱或者链接" json:"link"`
	Remark      string         `gorm:"type:varchar(255);column:remark;comment:备注" json:"remark"`
	CreatedAt   time.Time      `gorm:"column:created_at;comment:创建时间" json:"created_at"`
	UpdatedAt   time.Time      `gorm:"column:updated_at;comment:更新时间" json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"column:deleted_at;comment:删除时间" json:"deleted_at"`
	Groups      []ContactGroup `gorm:"many2many:notice_contact_contact_groups" json:"groups,omitempty"`
}

// 获取联系人表名
func (*Contact) TableName() string {
	return "notice_contacts"
}

// 获取联系人列表
func GetContacts(offset, limit int, keyword *string) (count int64, contacts []Contact, err error) {
	dbop := app.DB()
	if keyword != nil {
		dbop = db.MLike(dbop, *keyword, "name", "link")
	}
	err = dbop.Model(&Contact{}).Count(&count).
		Order("updated_at DESC").Offset(offset).Limit(limit).Find(&contacts).Error
	if err != nil {
		return
	}
	for i := range contacts {
		contacts[i].Groups, _ = contacts[i].GetGroups()
	}
	return
}

// 获取所有联系人
func GetAllContacts() (contacts []Contact, err error) {
	err = app.DB().Order("name DESC").Find(&contacts).Error
	return
}

// 联系人表单结构体
type ContactForm struct {
	Name        string      `json:"name" binding:"required,max=255"`
	Link        string      `json:"link" binding:"required,max=255"`
	ContactType MessageType `json:"contact_type" binding:"required,max=255"`
	GroupIDs    []uint      `json:"group_ids"`
	Remark      string      `json:"remark" binding:"max=255"`
}

// 创建联系人
func (form *ContactForm) Create() (err error) {
	err = app.DB().Where("name = ?", form.Name).Take(&Contact{}).Error
	if err == nil {
		err = ErrContactExist
		return
	}
	if !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	err = app.DB().Create(&Contact{
		Name:        form.Name,
		Link:        form.Link,
		ContactType: form.ContactType,
		Remark:      form.Remark,
	}).Error
	if err != nil {
		return err
	}
	if len(form.GroupIDs) > 0 {
		var contact Contact
		err = app.DB().Where("name = ?", form.Name).Take(&contact).Error
		if err != nil {
			return
		}
		var groups []ContactGroup
		err = app.DB().Select("id").Where("id IN (?)", form.GroupIDs).Find(&groups).Error
		if err != nil {
			return err
		}
		err = app.DB().Model(&contact).Association("Groups").Append(groups)
	}
	return
}

// 更新联系人
func (contact *Contact) Update(form ContactForm) (err error) {
	updateMap := map[string]any{}
	if form.Name != contact.Name {
		exist := Contact{}
		err = app.DB().Where("name = ?", form.Name).Take(&exist).Error
		if err == nil && exist.ID != contact.ID {
			err = ErrContactExist
			return
		} else if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		} else {
			err = nil
		}
		updateMap["name"] = form.Name
	}
	if form.Link != contact.Link {
		updateMap["link"] = form.Link
	}
	if form.ContactType != contact.ContactType {
		updateMap["contact_type"] = form.ContactType
	}
	if form.Remark != contact.Remark {
		updateMap["remark"] = form.Remark
	}
	if len(updateMap) > 0 {
		err = app.DB().Model(contact).Updates(updateMap).Error
	}
	if err != nil {
		return
	}
	if len(form.GroupIDs) > 0 {
		var groups []ContactGroup
		err = app.DB().Select("id").Where("id IN (?)", form.GroupIDs).Find(&groups).Error
		if err != nil {
			return
		}
		err = app.DB().Model(contact).Association("Groups").Replace(groups)
	}
	return
}

// 删除联系人
func (contact *Contact) Delete() (err error) {
	dbop := app.DB().Begin()
	err = dbop.Model(contact).Association("Groups").Clear()
	if err != nil {
		dbop.Rollback()
		return
	}
	err = dbop.Delete(contact).Error
	if err != nil {
		dbop.Rollback()
		return
	}
	err = dbop.Commit().Error
	if err != nil {
		dbop.Rollback()
	}
	return
}

// 根据ID获取联系人
func GetContactByID(id int) (contact Contact, err error) {
	err = app.DB().Where("id = ?", id).Take(&contact).Error
	return
}

// 根据名称获取联系人
func GetContactByName(name string) (contact Contact, err error) {
	err = app.DB().Where("name = ?", name).Order("id").Take(&contact).Error
	return
}

// 获取联系人的关联组
func (contact *Contact) GetGroups() (groups []ContactGroup, err error) {
	err = app.DB().Model(contact).Association("Groups").Find(&groups)
	return
}

// 根据ID列表获取联系人
func GetContactsByIDs(ids ...uint) (contacts []Contact, err error) {
	if len(ids) > 0 {
		err = app.DB().Where("id IN (?)", ids).Find(&contacts).Error
	}
	return
}
