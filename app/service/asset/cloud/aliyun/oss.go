package aliyun

import (
	"cmdb/app"
	"cmdb/pkg/utils"
	"fmt"
	"time"

	"github.com/aliyun/aliyun-oss-go-sdk/oss"
)

func StatsAllAccountsOssStorage() (storageTotal int64, err error) {
	accounts, err := GetAllAccounts()
	if err != nil {
		app.Log().Error("统计oss存储容量时，获取阿里云账户列表异常", "err", err.Error())
		return
	}
	for _, account := range accounts {
		total, err := account.getOSSStorageTotal()
		if err != nil {
			app.Log().Error("获取阿里云OSS存储容量异常", "account", account.Name, "err", err.Error())
			continue
		}
		storageTotal += total
	}
	return
}

// getOSSStorageTotal 获取OSS存储总量
// 首先尝试使用OSS SDK直接获取，如果失败则使用CMS API获取
func (a Account) getOSSStorageTotal() (storageTotal int64, err error) {
	// 解码访问密钥
	accessSecret, _ := utils.Base64Decode(a.AccessSecret)

	// 记录开始时间，用于计算耗时
	startTime := time.Now()

	// 默认使用杭州区域的endpoint
	defaultEndpoint := "https://oss-cn-hangzhou.aliyuncs.com"

	// 创建默认OSS客户端，用于获取所有Bucket列表
	client, err := oss.New(defaultEndpoint, a.AccessKey, accessSecret)
	if err != nil {
		app.Log().Error("初始化OSS客户端失败，尝试使用CMS API", "account", a.Name, "err", err.Error())
		return
	}

	// 获取所有Bucket列表
	listResult, err := client.ListBuckets()
	if err != nil {
		app.Log().Error("获取Bucket列表失败，尝试使用CMS API", "account", a.Name, "err", err.Error())
		return
	}

	// 记录获取到的Bucket数量
	app.Log().Info("获取到Bucket列表", "account", a.Name, "count", len(listResult.Buckets))

	// 创建一个map，用于存储每个区域的客户端
	regionClients := make(map[string]*oss.Client)
	// 将默认客户端添加到map中
	regionClients["cn-hangzhou"] = client

	// 遍历统计存储量
	bucketStats := make(map[string]int64)
	for _, bucket := range listResult.Buckets {
		// 获取bucket的区域信息
		location, err := client.GetBucketLocation(bucket.Name)
		if err != nil {
			app.Log().Error("获取Bucket区域失败", "account", a.Name, "bucket", bucket.Name, "err", err.Error())
			continue
		}

		// 根据bucket的区域创建对应的客户端
		regionClient, ok := regionClients[location]
		if !ok {
			// 如果该区域的客户端不存在，则创建一个新的客户端
			endpoint := fmt.Sprintf("https://%s.aliyuncs.com", location)
			regionClient, err = oss.New(endpoint, a.AccessKey, accessSecret)
			if err != nil {
				app.Log().Error("创建区域客户端失败", "account", a.Name, "bucket", bucket.Name, "location", location, "err", err.Error())
				continue
			}
			// 将新创建的客户端添加到map中
			regionClients[location] = regionClient
		}

		// 使用对应区域的客户端获取bucket的统计信息
		stat, err := regionClient.GetBucketStat(bucket.Name)
		if err != nil {
			app.Log().Error("获取Bucket统计失败", "account", a.Name, "bucket", bucket.Name, "location", location, "err", err.Error())
			continue
		}

		bucketStats[bucket.Name] = stat.Storage
		storageTotal += stat.Storage

		app.Log().Info("获取Bucket存储量成功",
			"account", a.Name,
			"bucket", bucket.Name,
			"location", location,
			"storage", stat.Storage,
			"storageGB", float64(stat.Storage)/1024/1024/1024)
	}

	// 记录耗时和结果
	elapsedTime := time.Since(startTime)
	app.Log().Info("OSS存储容量统计完成(OSS SDK)",
		"account", a.Name,
		"bucketCount", len(listResult.Buckets),
		"totalStorage", storageTotal,
		"totalStorageGB", float64(storageTotal)/1024/1024/1024,
		"elapsedTime", elapsedTime.String())

	return storageTotal, nil
}
