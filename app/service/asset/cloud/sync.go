package cloud

import (
	"strings"
)

// SyncResult 结构体，包含 Key 和 Result 两个字段
type SyncResult struct {
	Key    string
	Result string
}

// SyncResults 结构体切片，包含多个 SyncResult 结构体
type SyncResults []SyncResult

// String 方法，将 SyncResults 结构体切片转换为字符串
func (ss SyncResults) String() string {
	var buffer strings.Builder
	for i := range ss {
		buffer.WriteString(ss[i].Key + "：" + ss[i].Result + "；\n")
	}
	return buffer.String()
}
