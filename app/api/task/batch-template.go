package task

import (
	"cmdb/app"
	"cmdb/app/service/audit"
	"cmdb/app/service/task"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

func GetBatchTemplates(c *gin.Context) {
	page, _ := strconv.Atoi(c<PERSON>("page", "1"))
	limit, _ := strconv.Atoi(<PERSON><PERSON>("limit", "10"))
	offset := (page - 1) * limit
	var keyword *string
	if val := strings.TrimSpace(c.Query("keyword")); val != "" {
		keyword = &val
	}
	count, data, err := task.GetBatchTemplates(offset, limit, keyword)
	if err != nil {
		app.Log().Error("获取批量任务模板列表失败", "err", err)
		app.FailedResponseMsg(c, "获取数据失败")
		return
	}
	app.SuccessResponseDataCount(c, data, count)
}

func CreateBatchTemplate(c *gin.Context) {
	var form task.BatchTemplateForm
	if err := c.Should<PERSON>ind<PERSON>(&form); err != nil {
		app.Log().Error("创建批量任务模板失败", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	if err := form.Create(); err != nil {
		msg := "创建失败"
		if err == task.ErrBatchTemplateExist {
			msg = err.Error()
		} else {
			app.Log().Error("创建批量任务模板失败", "err", err)
		}
		app.FailedResponseMsg(c, msg)
		return
	}
	audit.LogAPIOP(c, "任务管理", "添加任务模板："+form.Name)
	app.SuccessResponseMsg(c, "创建成功")
}

func UpdateBatchTemplate(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	var form task.BatchTemplateForm
	if err := c.ShouldBindJSON(&form); err != nil {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	template, err := task.GetBatchTemplateByID(id)
	if err != nil {
		app.FailedResponseMsg(c, "模板不存在")
		return
	}
	err = template.Update(form)
	if err != nil {
		msg := "更新失败"
		if err == task.ErrBatchTemplateExist {
			msg = err.Error()
		} else {
			app.Log().Error("更新批量任务模板失败", "err", err)
		}
		app.FailedResponseMsg(c, msg)
		return
	}
	audit.LogAPIOP(c, "任务管理", "更新任务模板："+form.Name)
	app.SuccessResponseMsg(c, "更新成功")
}

func DeleteBatchTemplate(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	template, err := task.GetBatchTemplateByID(id)
	if err != nil {
		app.FailedResponseMsg(c, "模板不存在")
		return
	}
	err = template.Delete()
	if err != nil {
		app.FailedResponseMsg(c, "删除失败")
		return
	}
	audit.LogAPIOP(c, "任务管理", "删除任务模板："+template.Name)
	app.SuccessResponseMsg(c, "删除成功")
}

func GetAllBatchTemplates(c *gin.Context) {
	data, err := task.GetAllBatchTemplates()
	if err != nil {
		app.Log().Error("获取批量任务模板失败", "err", err)
		app.FailedResponseMsg(c, "获取失败")
		return
	}
	app.SuccessResponseData(c, data)
}
