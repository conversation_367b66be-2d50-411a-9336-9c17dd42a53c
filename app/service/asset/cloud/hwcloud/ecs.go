package hwcloud

import (
	"bytes"
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/asset/cloud"
	"cmdb/pkg/utils"
	"fmt"
	"net"
	"strconv"
	"strings"
	"time"

	"github.com/huaweicloud/huaweicloud-sdk-go-v3/core/auth/basic"
	ecs "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/ecs/v2"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/ecs/v2/model"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/ecs/v2/region"
	"gorm.io/gorm"
)

var amdSpecPres = []string{
	"am7",
	"am8",
	"ac7",
	"ac8",
}

func (a Account) syncECSs() (result cloud.SyncResults, err error) {
	dcs, err := asset.GetDatacentersByCloudType(asset.HuaweiCloudType)
	if err != nil {
		return
	}
	for _, dc := range dcs {
		r, err := a.syncRegionECSs(dc, 1)
		sr := cloud.SyncResult{
			Key: dc.Name,
		}
		if err == nil {
			sr.Result = "同步成功"
			// 回收过期主机
			err = a.expiredHosts(time.Now().Add(-10*time.Minute), dc)
			if err != nil {
				sr.Result = "同步成功，但是回收主机失败" + err.Error()
			}
		} else {
			sr.Result = r + " " + err.Error()
		}
		result = append(result, sr)
	}
	return
}

func (a Account) syncRegionECSs(dc asset.Datacenter, offset int32) (result string, err error) {
	secret, _ := utils.Base64Decode(a.AccessSecret)
	auth, err := basic.NewCredentialsBuilder().WithAk(a.AccessKey).WithSk(secret).SafeBuild()
	if err != nil {
		result = "构建认证失败"
		return
	}
	regionID, err := region.SafeValueOf(dc.Code)
	if err != nil {
		result = "非法regionID："
		return
	}
	clientBuild, err := ecs.EcsClientBuilder().WithRegion(regionID).WithCredential(auth).SafeBuild()
	if err != nil {
		result = "构建客户端失败"
		return
	}
	client := ecs.NewEcsClient(clientBuild)
	var limit int32 = 1000
	response, err := client.ListServersDetails(&model.ListServersDetailsRequest{
		Limit:  &limit,
		Offset: &offset,
	})
	if err != nil {
		return
	}
	err = a.saveECS(dc, *response.Servers)
	if err != nil {
		return
	}
	total := *response.Count
	if total > int32(offset+limit) {
		result, err = a.syncRegionECSs(dc, offset+limit)
	}
	return
}

func (a Account) saveECS(dc asset.Datacenter, hosts []model.ServerDetail) (err error) {
	for i := range hosts {
		exist := asset.Host{}
		sn := hosts[i].Id
		err = app.DB().Where("sn = ? AND host_type = ?", sn, asset.HuaweiHostType).Take(&exist).Error
		if err != nil && err != gorm.ErrRecordNotFound {
			// 如果不是存在的错误，就直接返回错误
			return
		}
		createTime, _ := time.Parse("2006-01-02T15:04Z", hosts[i].Created)
		ip := ""
		publicIP := ""
		for _, addrs := range hosts[i].Addresses {
			for ii := range addrs {
				if ip == "" && addrs[ii].OSEXTIPStype.Value() == model.GetServerAddressOSEXTIPStypeEnum().FIXED.Value() {
					if net.ParseIP(addrs[ii].Addr).To4().IsPrivate() {
						ip = addrs[ii].Addr
					}
				}
				if publicIP == "" && addrs[ii].OSEXTIPStype.Value() == model.GetServerAddressOSEXTIPStypeEnum().FLOATING.Value() {
					publicIP = addrs[ii].Addr
				}
			}
		}
		var gpuAmount uint
		gpuSpec := []string{}
		for _, gpu := range hosts[i].Flavor.Gpus {
			if gpu.Count != nil {
				gpuAmount += uint(*gpu.Count)
				gpuSpec = append(gpuSpec, fmt.Sprintf("%d x %s (%d GB)", *gpu.Count, *gpu.Name, int(*gpu.MemoryMb/1024)))
			}
		}
		isAmd := false
		for _, spec := range amdSpecPres {
			if strings.Contains(hosts[i].Flavor.Name, spec) {
				isAmd = true
			}
		}
		position := ""
		tags := *hosts[i].Tags
		for ii := range tags {
			if strings.HasPrefix(tags[ii], "AZ=") {
				position = tags[ii][3:]
			}
		}
		var status asset.HostStatus = asset.RunningHostStatus
		if hosts[i].HostStatus == "UP" {
			status = asset.RunningHostStatus
		} else if hosts[i].HostStatus == "DOWN" {
			status = asset.StoppedHostStatus
		}
		instanceName := hosts[i].Name
		chargeType := asset.PrepaidChargeType
		if hosts[i].Metadata["charging_mode"] == "0" || hosts[i].Metadata["charging_mode"] == "2" {
			chargeType = asset.PostpaidChargeType
		}
		var isMonitor bool = true
		if chargeType == asset.PostpaidChargeType {
			isMonitor = false
		}
		// 接着就是不存在记录的情况了
		remotePort := 22
		osType := "linux"
		if hosts[i].Metadata["os_type"] == "Windows" {
			remotePort = 3389
			osType = "windows"
		}
		now := time.Now()
		cpu, _ := strconv.Atoi(hosts[i].Flavor.Vcpus)
		memory, _ := strconv.Atoi(hosts[i].Flavor.Ram)
		describe := ""
		if hosts[i].Description != nil {
			describe = *hosts[i].Description
		}
		if err == nil {
			updateMap := map[string]interface{}{"sync_time": time.Now()}
			var changeLog bytes.Buffer
			if exist.GPUAmount != gpuAmount {
				changeLog.WriteString("GPU数量：" + strconv.Itoa(int(exist.GPUAmount)) + " => " + strconv.Itoa(int(gpuAmount)) + "\n")
				updateMap["gpu_amount"] = gpuAmount
			}
			if exist.GPUSpec != strings.Join(gpuSpec, ",") {
				changeLog.WriteString("GPU规格：" + exist.GPUSpec + " => " + strings.Join(gpuSpec, ",") + "\n")
				updateMap["gpu_spec"] = strings.Join(gpuSpec, ",")
			}
			if exist.CPUThread != uint(cpu) {
				changeLog.WriteString("CPU：" + strconv.Itoa(int(exist.CPUThread)) + " => " + strconv.Itoa(cpu) + "\n")
				updateMap["cpu_thread"] = uint(cpu)
			}
			if exist.Memory != uint(memory) {
				changeLog.WriteString("内存：" + strconv.Itoa(int(exist.Memory)) + " => " + strconv.Itoa(memory) + "\n")
				updateMap["memory"] = uint(memory)
			}
			if exist.IsAMD != isAmd {
				changeLog.WriteString("是否AMD：" + strconv.FormatBool(exist.IsAMD) + " => " + strconv.FormatBool(isAmd) + "\n")
				updateMap["is_amd"] = isAmd
			}
			if exist.IP != ip {
				changeLog.WriteString("IP：" + exist.IP + " => " + ip + "\n")
				updateMap["ip"] = ip
			}
			if exist.PublicIP != publicIP {
				changeLog.WriteString("公网IP：" + exist.PublicIP + " => " + publicIP + "\n")
				updateMap["public_ip"] = publicIP
			}
			if exist.Name != instanceName {
				changeLog.WriteString("名称：" + exist.Name + " => " + instanceName + "\n")
				updateMap["name"] = instanceName
			}
			if exist.Status != status {
				changeLog.WriteString("主机状态：" + exist.Status.String() +
					" => " + status.String() + "\n")
				updateMap["status"] = status
			}
			if exist.Cabinet != hosts[i].OSEXTAZavailabilityZone {
				changeLog.WriteString("机柜变更：" + exist.Cabinet + " => " + hosts[i].OSEXTAZavailabilityZone + "\n")
				updateMap["cabinet"] = hosts[i].OSEXTAZavailabilityZone
			}
			if exist.OSType != osType {
				changeLog.WriteString("系统类型：" + exist.OSType + " => " + osType + "\n")
				updateMap["os_type"] = osType
			}
			if position != exist.Position {
				changeLog.WriteString("机柜柜位/vpc：" + exist.Position + " => " + position + "\n")
				updateMap["position"] = position
			}
			if exist.Model != hosts[i].Flavor.Name {
				changeLog.WriteString("型号：" + exist.Model + " => " + hosts[i].Flavor.Name + "\n")
				updateMap["model"] = hosts[i].Flavor.Name
			}
			if exist.ChargeType != chargeType {
				updateMap["charge_type"] = chargeType
				changeLog.WriteString("付费类型：" + exist.ChargeType.String() + " => " + chargeType.String() + "\n")
			}
			if exist.AccountID != a.ID {
				updateMap["account_id"] = a.ID
			}
			if hosts[i].EnterpriseProjectId != nil && *hosts[i].EnterpriseProjectId != exist.ResourceGroupID {
				changeLog.WriteString("资源组：" + exist.ResourceGroupID + " => " + *hosts[i].EnterpriseProjectId + "\n")
				updateMap["resource_group_id"] = *hosts[i].EnterpriseProjectId
			}
			// if isMonitor == 1 {
			// 	updateMap["ping_monitor"] = isMonitor
			// 	updateMap["nagios_monitor"] = isMonitor
			// }
			err = app.DB().Model(&exist).Updates(updateMap).Error
			if err != nil {
				return
			}
			if changeLog.String() != "" {
				exist.LogChanges("华为云 ecs 同步", changeLog.String())
			}
			continue
		}
		err = app.DB().Create(&asset.Host{
			IP:              ip,
			AccountID:       a.ID,
			PublicIP:        publicIP,
			OS:              hosts[i].Metadata["image_name"],
			SN:              sn,
			Name:            instanceName,
			Position:        position,
			HostType:        asset.HuaweiHostType,
			ChargeType:      chargeType,
			DatacenterID:    dc.ID,
			Cabinet:         hosts[i].OSEXTAZavailabilityZone,
			CreatedAt:       createTime,
			Memory:          uint(memory),
			CPUThread:       uint(cpu),
			GPUAmount:       gpuAmount,
			GPUSpec:         strings.Join(gpuSpec, ","),
			OSType:          osType,
			Status:          status,
			SyncTime:        &now,
			PingMonitor:     isMonitor,
			RemotePort:      uint(remotePort),
			Model:           hosts[i].Flavor.Name,
			IsAMD:           isAmd,
			Remark:          describe,
			ResourceGroupID: *hosts[i].EnterpriseProjectId,
		}).Error
		if err != nil {
			return
		}
	}
	return
}

func (a Account) expiredHosts(expiredTime time.Time, dc asset.Datacenter) (err error) {
	hosts := []asset.Host{}
	err = app.DB().Where("account_id = ? and sync_time < ? and datacenter_id = ?", a.ID, expiredTime, dc.ID).Find(&hosts).Error
	if err != nil {
		return
	}
	for i := range hosts {
		err = app.DB().Model(&hosts[i]).Updates(map[string]any{
			"status":     asset.RecycleHostStatus,
			"deleted_at": time.Now(),
		}).Error
		if err != nil {
			return
		}
		hosts[i].LogChanges("同步更新", "回收主机")
	}
	return
}
