package gitlab

import (
	"cmdb/app"
	"testing"
)

func TestGetProjectByName(t *testing.T) {
	err := app.NewApp("../../../app.ini")
	if err != nil {
		t.<PERSON><PERSON>(err)
	}
	var token = "5zyvizxfYtSJ-y8i2V68"
	var baseURL = "http://192.168.142.95:8099"
	c, err := NewClient(token, baseURL)
	if err != nil {
		t.<PERSON><PERSON>(err)
	}
	p, err := c.GetProjectByName("admin")
	if err != nil {
		t.Fatal(err)
	}
	for i := range p {
		m, _ := c.GetProjectMembers(p[i], 1)
		if m != nil {
			t.Log(m)
		}
	}
}
