package nginx

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/pkg/db"
	"fmt"
	"os"
	"sync"
	"time"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type NginxBackend struct {
	ID          uint           `gorm:"column:id;primaryKey;comment:主键" json:"id"`
	IP          string         `gorm:"column:ip;type:varchar(255);index;comment:IP地址" json:"ip"`
	Backends    datatypes.JSON `gorm:"column:backends;comment:后端地址" json:"backends"`
	ServerNames datatypes.JSON `gorm:"column:server_names;comment:域名" json:"server_names"`
	Remark      string         `gorm:"column:remark;type:varchar(255);comment:备注" json:"remark"`
	SyncTime    time.Time      `gorm:"column:sync_time;index;comment:同步时间" json:"sync_time"`
	DeletedAt   gorm.DeletedAt `gorm:"column:deleted_at;index;comment:删除时间" json:"deleted_at"`
}

func (NginxBackend) TableName() string {
	return "appops_nginx_backend"
}
func GetNginxBackends(offset, limit int, ip, serverName, keyword *string) (count int64, backends []NginxBackend, err error) {
	dbop := app.DB().Model(&NginxBackend{})
	if ip != nil && *ip != "" {
		queryValue := fmt.Sprintf("\"%s\"", *ip)
		dbop = dbop.Where("ip = ? OR JSON_CONTAINS(backends, ?, '$')", *ip, queryValue)
	}
	if serverName != nil && *serverName != "" {
		queryValue := fmt.Sprintf("\"%s\"", *serverName)
		dbop = dbop.Where("JSON_CONTAINS(server_names, ?, '$')", queryValue)
	}
	if keyword != nil && *keyword != "" {
		dbop = db.MLike(dbop, *keyword, "remark")
	}
	err = dbop.Count(&count).Offset(offset).Limit(limit).Find(&backends).Error
	return
}

// 同步nginx后端配置
func Sync() (err error) {
	app.Log().Info("开始 同步nginx配置")
	startTime := time.Now()
	HOME := os.Getenv("HOME")
	if HOME == "" {
		HOME = "/root"
	}
	// 获取所有nginx主机
	var hosts []asset.Host
	err = app.DB().Model(&asset.Host{}).Where("name LIKE ?", "prod-nginx-proxy%").Find(&hosts).Error
	if err != nil {
		return
	}
	var wg sync.WaitGroup
	// 并发数量
	concurrency := make(chan struct{}, 1)
	for _, host := range hosts {
		wg.Add(1)
		go func() {
			concurrency <- struct{}{}
			app.Log().Info("同步nginx配置", "host", host.Name, "ip", host.IP)
			err1 := SyncConfig(host.IP, host.RemotePort, "root", HOME+"/.ssh/id_rsa", host.Name)
			if err1 != nil {
				app.Log().Error("同步nginx配置失败", "host", host.Name, "ip", host.IP, "err", err1)
				err = err1
			}
			<-concurrency
			wg.Done()
		}()
	}
	wg.Wait()
	// 删除同步前1分钟的记录
	if err == nil {
		err1 := app.DB().Where("sync_time < ?", startTime.Add(-time.Minute*1)).Delete(&NginxBackend{}).Error
		if err1 != nil {
			app.Log().Error("删除nginx配置失败", "err", err1)
		}
	}
	app.Log().Info("同步nginx配置完成", "err", err)
	return
}
