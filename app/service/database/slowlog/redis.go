package slowlog

import "time"

// RedisBigKeys 生产redis bigkey表结构
type RedisBigKeys struct {
	ID              int64     `gorm:"column:id;primaryKey;autoIncrement;comment:'自增字段'" json:"id"`
	DBIP            string    `gorm:"column:db_ip;not null;default:'';comment:'ip'" json:"db_ip"`
	DBPort          int       `gorm:"column:db_port;not null;default:6379;comment:'port'" json:"db_port"`
	DBTitle         string    `gorm:"column:db_title;not null;default:'';comment:'数据库标签'" json:"db_title"`
	CloudType       string    `gorm:"column:cloud_type;not null;default:'';comment:'所属cloud,如huaweiyun,aliyun'" json:"cloud_type"`
	DBEnv           string    `gorm:"column:db_env;not null;default:'';comment:'数据库环境 test:测试环境, prod:生产环境'" json:"db_env"`
	DBProjectName   string    `gorm:"column:db_project_name;not null;default:'';comment:'数据库实例企业项目名称'" json:"db_project_name"`
	BigkeysDatabase int       `gorm:"column:bigkeys_database;not null;default:0;comment:'bigkey所属db'" json:"bigkeys_database"`
	BigkeysType     string    `gorm:"column:bigkeys_type;not null;default:'';comment:'bigkey key类型'" json:"bigkeys_type"`
	BigkeysKey      string    `gorm:"column:bigkeys_key;not null;default:'';comment:'bigkey key名称'" json:"bigkeys_key"`
	BigkeysSize     int64     `gorm:"column:bigkeys_size;not null;default:0;comment:'bigkey长度,string默认1MB,其余的默认是10000'" json:"bigkeys_size"`
	CreateTime      time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP;comment:'当前时间'" json:"create_time"`
}

// TableName 设置表名
func (r *RedisBigKeys) TableName() string {
	return "t_prod_redis_bigkeys"
}

// RedisBigKeysStringStatistic redis string类型bigkey统计表结构
type RedisBigKeysStringStatistic struct {
	ID                      int64     `gorm:"column:id;primaryKey;autoIncrement;comment:'自增字段'" json:"id"`
	DBProjectName           string    `gorm:"column:db_project_name;not null;default:'';comment:'数据库实例企业项目名称'" json:"db_project_name"`
	BigkeysStringCount      int       `gorm:"column:bigkeys_string_count;not null;default:0;comment:'string类型bigkey数量'" json:"bigkeys_string_count"`
	BigkeysString100kbCount int       `gorm:"column:bigkeys_string_100kb_count;not null;default:0;comment:'string类型bigkey数量(100kb至1mb)'" json:"bigkeys_string_100kb_count"`
	BigkeysString1mbCount   int       `gorm:"column:bigkeys_string_1mb_count;not null;default:0;comment:'string类型bigkey数量(1mb至32mb)'" json:"bigkeys_string_1mb_count"`
	BigkeysString32mbCount  int       `gorm:"column:bigkeys_string_32mb_count;not null;default:0;comment:'string类型bigkey数量(32mb至64mb)'" json:"bigkeys_string_32mb_count"`
	BigkeysString64mbCount  int       `gorm:"column:bigkeys_string_64mb_count;not null;default:0;comment:'string类型bigkey数量(64mb至128mb)'" json:"bigkeys_string_64mb_count"`
	BigkeysString128mbCount int       `gorm:"column:bigkeys_string_128mb_count;not null;default:0;comment:'string类型bigkey数量(128mb至256mb)'" json:"bigkeys_string_128mb_count"`
	BigkeysString256mbCount int       `gorm:"column:bigkeys_string_256mb_count;not null;default:0;comment:'string类型bigkey数量(256mb及以上)'" json:"bigkeys_string_256mb_count"`
	CreateTime              time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP;comment:'当前时间'" json:"create_time"`
}

// TableName 设置表名
func (r *RedisBigKeysStringStatistic) TableName() string {
	return "t_redis_bigkeys_string_statistic"
}
