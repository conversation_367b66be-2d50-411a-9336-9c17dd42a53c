package n9e

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/pkg/utils/ssh"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/tidwall/gjson"
)

const MEIYOUN9EHost = "https://n9e-ops.meiyou.com"
const MEETYOUN9EHost = "https://n9e-ops.meetyouintl.com"

func GenN9EAccessToken(n9eHost string) (token string, err error) {
	httpClient := &http.Client{
		Timeout: time.Second * 5,
	}
	payload := strings.NewReader("{\"username\":\"root\",\"password\":\"op-admin\"}")
	resp, err := httpClient.Post(n9eHost+"/api/n9e/auth/login", "application/json", payload)
	if err != nil {
		return
	}
	var data []byte
	data, err = io.ReadAll(resp.Body)
	if err != nil {
		return
	}
	token = gjson.Get(string(data), "dat.access_token").String()
	return
}

type deleteBody struct {
	Idents []string `json:"idents"`
}

func DeleteN9ETargets(hosts ...string) (result string, err error) {
	if len(hosts) == 0 {
		return
	}
	results := []string{}
	r1 := RemoteSwitchCatgraf("off", hosts...)
	results = append(results, r1)
	n9eHost := MEIYOUN9EHost
	if strings.HasPrefix(hosts[0], "10.2.") || strings.HasPrefix(hosts[0], "10.15.") || strings.HasPrefix(hosts[0], "10.16.") {
		n9eHost = MEETYOUN9EHost
	}
	token, err := GenN9EAccessToken(n9eHost)
	if err != nil {
		results = append(results, "获取token失败:"+err.Error())
		result = strings.Join(results, "\n")
		app.Log().Error("删除夜莺主机", "server", n9eHost, "host", hosts, "获取token失败", err)
		return
	}
	httpClient := &http.Client{
		Timeout: time.Second * 3,
	}
	body := deleteBody{
		Idents: hosts,
	}
	results = append(results, fmt.Sprintf("删除主机:%s", strings.Join(hosts, ",")))
	app.Log().Info("删除夜莺主机", "server", n9eHost, "host", hosts)
	data, _ := json.Marshal(&body)
	payload := strings.NewReader(string(data))
	req, err := http.NewRequest("DELETE", n9eHost+"/api/n9e/targets", payload)
	if err != nil {
		results = append(results, "构建请求失败:"+err.Error())
		result = strings.Join(results, "\n")
		app.Log().Error("删除夜莺主机", "构建请求失败", err)
		return
	}
	req.Header.Add("contentType", "application/json")
	req.Header.Add("Accept-Charset", "utf-8")
	req.Header.Add("Authorization", "Bearer "+token)
	resp, err := httpClient.Do(req)
	if err != nil {
		results = append(results, "请求失败:"+err.Error())
		result = strings.Join(results, "\n")
		app.Log().Error("删除夜莺主机", "请求失败", err, "url", req.URL.String())
		return
	}
	respData, err := io.ReadAll(resp.Body)
	if err != nil {
		results = append(results, "解析请求失败:"+err.Error())
		result = strings.Join(results, "\n")
		app.Log().Error("删除夜莺主机", "解析请求失败", err, "body", result)
		return
	}
	resp.Body.Close()
	results = append(results, string(respData))
	if resp.StatusCode != int(http.StatusOK) {
		results = append(results, "状态码非200")
		result = strings.Join(results, "\n")
		app.Log().Error("删除夜莺主机", "解析请求失败", err, "body", result)
		err = errors.New("状态码非200")
	} else {
		results = append(results, "删除成功")
		result = strings.Join(results, "\n")
		app.Log().Info("删除夜莺主机成功", "body", result)
	}
	return
}

func RemoteSwitchCatgraf(op string, hosts ...string) (result string) {
	results := []string{}
	finished := make(chan bool)
	go func() {
		for i := range hosts {
			sshHost := hosts[i]
			if strings.HasPrefix(hosts[i], "10.2.") || strings.HasPrefix(hosts[i], "10.15.") || strings.HasPrefix(hosts[i], "10.16.") {
				err1 := app.DB().Model(&asset.Host{}).Select("public_ip").Limit(1).Where("ip = ?", hosts[i]).Scan(&sshHost).Error
				if err1 != nil {
					results = append(results, "获取公网IP失败:"+err1.Error())
					continue
				}
			}
			switch op {
			case "on", "开启":
				r, err2 := ssh.SSHRunCommand(sshHost, 22, "root", "/root/.ssh/id_rsa", "", "timeout 5 /opt/categraf/categraf -start")
				if err2 != nil {
					results = append(results, "操作 categraf 失败："+err2.Error()+"\n"+string(r))
				} else {
					results = append(results, "操作 categraf 成功"+"\n"+string(r))
				}
			case "off", "关闭":
				r, err3 := ssh.SSHRunCommand(sshHost, 22, "root", "/root/.ssh/id_rsa", "", "timeout 5 /opt/categraf/categraf -stop")
				if err3 != nil {
					results = append(results, "操作 categraf 失败："+err3.Error()+"\n"+string(r))
				} else {
					results = append(results, "操作 categraf 成功"+"\n"+string(r))
				}
			}
		}
		finished <- true
	}()
	select {
	case <-finished:
		result = strings.Join(results, "\n")
		return
	case <-time.After(time.Second * 3):
		result = strings.Join(results, "\n") + "\n后台执行中"
		return
	}
}

func N9eAlertMetu(durationHours int, col string, hosts ...string) (result string, err error) {
	if len(hosts) == 0 {
		return
	}
	startTimestamp := time.Now().Unix()
	endTimestamp := time.Now().Add(time.Duration(durationHours) * time.Hour).Unix()
	// data := fmt.Sprintf(`{"note":"%s","group_id":%d,"prod":"metric","cate":"prometheus","datasource_ids":[0],"severities":[1,2,3],"mute_time_type":0,"btime":%d,"etime":%d,"periodic_mutes":[{"enable_days_of_week":"1 2 3 4 5 6 0","enable_stime":"00:00","enable_etime":"00:00"}],"tags":[{"func":"in","key":"%s","value":"%s"}],"cause":"cmdb抑制","cluster":"0"}`, strings.Join(hosts, "_"), 1, startTimestamp, endTimestamp, col, strings.Join(hosts, " "))
	var groupIDs []int
	n9eHost := MEIYOUN9EHost
	if strings.HasPrefix(hosts[0], "10.2.") || strings.HasPrefix(hosts[0], "10.15.") || strings.HasPrefix(hosts[0], "10.16.") {
		// 国际版只能抑制默认
		n9eHost = MEETYOUN9EHost
		groupIDs = []int{1, 2, 3, 4}
	} else {
		err1 := app.DB().Table("n9e.busi_group").Select("id").Scan(&groupIDs).Error
		if err1 != nil {
			groupIDs = []int{1}
		}
	}
	token, err := GenN9EAccessToken(n9eHost)
	if err != nil {
		return
	}
	httpClient := &http.Client{
		Timeout: time.Second * 10,
	}
	for i := range groupIDs {
		data := fmt.Sprintf(`{"note":"%s","group_id":%d,"prod":"metric","cate":"prometheus","datasource_ids":[0],"severities":[1,2,3],"mute_time_type":0,"btime":%d,"etime":%d,"periodic_mutes":[{"enable_days_of_week":"1 2 3 4 5 6 0","enable_stime":"00:00","enable_etime":"00:00"}],"tags":[{"func":"in","key":"%s","value":"%s"}],"cause":"cmdb抑制","cluster":"0"}`, strings.Join(hosts, "_"), groupIDs[i], startTimestamp, endTimestamp, col, strings.Join(hosts, " "))
		payload := strings.NewReader(data)
		req, err1 := http.NewRequest("POST", n9eHost+"/api/n9e/busi-group/"+strconv.Itoa(groupIDs[i])+"/alert-mutes", payload)
		if err1 != nil {
			err = err1
			result = "创建告警屏蔽的请求失败"
			return
		}
		req.Header.Add("contentType", "application/json")
		req.Header.Add("Accept-Charset", "utf-8")
		req.Header.Add("Authorization", "Bearer "+token)
		resp, err1 := httpClient.Do(req)
		if err1 != nil {
			err = err1
			result = "创建告警屏蔽，请求失败," + err1.Error()
			return
		}
		respData, err1 := io.ReadAll(resp.Body)
		if err1 != nil {
			err = err1
			result = "创建告警屏蔽，解析body操作失败"
			return
		}
		resp.Body.Close()
		result = string(respData)
		if resp.StatusCode != int(http.StatusOK) {
			// result = "状态码：" + strconv.Itoa(resp.StatusCode)
			err = errors.New("状态码：" + strconv.Itoa(resp.StatusCode))
		}
	}
	return
}
func N9eAlertMetuRulename(durationHours int, rulename, col string, hosts ...string) (result string, err error) {
	startTimestamp := time.Now().Unix()
	endTimestamp := time.Now().Add(time.Duration(durationHours) * time.Hour).Unix()
	data := fmt.Sprintf(`{"note":"%s","group_id":1,"prod":"metric","cate":"prometheus","datasource_ids":[0],"severities":[1,2,3],"mute_time_type":0,"btime":%d,"etime":%d,"periodic_mutes":[{"enable_days_of_week":"1 2 3 4 5 6 0","enable_stime":"00:00","enable_etime":"00:00"}],"tags":[{"func":"in","key":"%s","value":"%s"},{"func":"in","key":"rulename","value":"%s"}],"cause":"cmdb抑制","cluster":"0"}`, strings.Join(hosts, "_"), startTimestamp, endTimestamp, col, strings.Join(hosts, " "), rulename)
	if len(hosts) == 0 {
		return
	}
	n9eHost := MEIYOUN9EHost
	if strings.HasPrefix(hosts[0], "10.2.") || strings.HasPrefix(hosts[0], "10.15.") || strings.HasPrefix(hosts[0], "10.16.") {
		n9eHost = MEETYOUN9EHost
	}
	token, err := GenN9EAccessToken(n9eHost)
	if err != nil {
		return
	}
	httpClient := &http.Client{
		Timeout: time.Second * 3,
	}
	payload := strings.NewReader(data)
	req, err := http.NewRequest("POST", n9eHost+"/api/n9e/busi-group/1/alert-mutes", payload)
	if err != nil {
		return
	}
	req.Header.Add("contentType", "application/json")
	req.Header.Add("Accept-Charset", "utf-8")
	req.Header.Add("Authorization", "Bearer "+token)
	resp, err := httpClient.Do(req)
	if err != nil {
		return
	}
	respData, err := io.ReadAll(resp.Body)
	if err != nil {
		return
	}
	resp.Body.Close()
	result = string(respData)
	if resp.StatusCode != int(http.StatusOK) {
		err = errors.New("状态码非200," + strconv.Itoa(resp.StatusCode))
	}
	return
}

func SyncN9eMonitorToHost(ips ...string) (err error) {
	// 检查传入的IP列表是否为空
	if len(ips) == 0 {
		return
	}
	// 获取当前时间
	now := time.Now()
	// 更新数据库中指定IP的主机信息
	// 将"n9e_monitor"字段设置为true，将"n9e_monitor_time"字段设置为当前时间
	err = app.DB().Model(&asset.Host{}).Updates(map[string]any{
		"n9e_monitor":      true, // 设置n9e_monitor字段为true
		"n9e_monitor_time": &now, // 设置n9e_monitor_time字段为当前时间
	}).Where("ip IN (?) ", ips).Error // 指定更新条件为IP在传入的IP列表中
	return
}

func ExpiredN9eMonitorToHost(expiredTime time.Time) (err error) {
	// 更新数据库中满足条件的主机信息
	// 将"n9e_monitor"字段设置为false，将"n9e_monitor_time"字段设置为nil
	err = app.DB().Unscoped().Model(&asset.Host{}).Updates(map[string]any{
		"n9e_monitor":      false, // 设置n9e_monitor字段为false
		"n9e_monitor_time": nil,   // 设置n9e_monitor_time字段为nil
	}).Where("n9e_monitor_time < ? AND n9e_monitor = ?", expiredTime, true, expiredTime).Or("n9e_monitor_time IS NULL AND n9e_monitor = ?", true).Error // 指定更新条件为n9e_monitor_time小于指定时间
	return
}
