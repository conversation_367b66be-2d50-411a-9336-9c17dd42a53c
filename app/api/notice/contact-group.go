package notice

import (
	"cmdb/app"
	"cmdb/app/service/audit"
	"cmdb/app/service/notice"
	"strconv"

	"github.com/gin-gonic/gin"
)

func GetContactGroups(c *gin.Context) {
	page, _ := strconv.Atoi(<PERSON><PERSON>("page", "1"))
	limit, _ := strconv.Atoi(c<PERSON>("limit", "10"))
	offset := (page - 1) * limit
	var keyword *string
	if val := c.<PERSON>("keyword"); val != "" {
		keyword = &val
	}
	count, data, err := notice.GetContactGroups(offset, limit, keyword)
	if err != nil {
		app.Log().Error("获取联系人组数据失败", "err", err)
		app.FailedResponseMsg(c, "获取数据失败")
		return
	}
	app.SuccessResponseDataCount(c, data, count)
}

func AddContactGroup(c *gin.Context) {
	var form notice.ContactGroupForm
	if err := c.<PERSON>(&form); err != nil {
		app.Log().Debug("参数错误", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	if err := form.Create(); err != nil {
		msg := "添加失败"
		if err == notice.ErrContactGroupExist {
			msg = err.Error()
		} else {
			app.Log().Error("创建联系人组失败", "err", err)
		}
		app.FailedResponseMsg(c, msg)
		return
	}
	audit.LogAPIOP(c, "通知管理", "添加联系人分组："+form.Name)
	app.SuccessResponseMsg(c, "添加成功")
}

func UpdateContactGroup(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.Log().Debug("参数错误", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	var form notice.ContactGroupForm
	err = c.ShouldBind(&form)
	if err != nil {
		app.Log().Debug("参数错误", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	group, err := notice.GetContactGroupByID(id)
	if err != nil {
		app.Log().Error("获取联系人组失败", "err", err)
		app.FailedResponseMsg(c, "获取联系人组失败")
		return
	}
	if err := group.Update(form); err != nil {
		app.Log().Error("更新联系人组失败", "err", err)
		app.FailedResponseMsg(c, "更新联系人组失败")
		return
	}
	audit.LogAPIOP(c, "通知管理", "更新联系人组:"+group.Name)
	app.SuccessResponseMsg(c, "更新成功")
}

func DeleteContactGroup(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.Log().Debug("参数错误", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	group, err := notice.GetContactGroupByID(id)
	if err != nil {
		app.Log().Error("获取联系人组失败", "err", err)
		app.FailedResponseMsg(c, "获取联系人组失败")
		return
	}
	if err := group.Delete(); err != nil {
		app.Log().Error("删除联系人组失败", "err", err)
		app.FailedResponseMsg(c, "删除失败")
		return
	}
	audit.LogAPIOP(c, "通知管理", "删除联系人组:"+group.Name)
	app.SuccessResponseMsg(c, "删除成功")
}

func GetAllContactGroups(c *gin.Context) {
	data, err := notice.GetAllContactGroups()
	if err != nil {
		app.Log().Error("获取联系人组失败", "err", err)
		app.FailedResponseMsg(c, "获取联系人组失败")
		return
	}
	app.SuccessResponseData(c, data)
}
