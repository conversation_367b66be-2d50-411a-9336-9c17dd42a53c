package tencentcloud

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/task"
	"cmdb/pkg/utils/mytime"
	"errors"
	"time"
)

type jobFunc func(a Account) (err error)

var jobTypes = map[string]jobFunc{
	"region": func(a Account) (err error) {
		return a.SyncRegions()
	},
	"ecs": func(a Account) (err error) {
		return a.SyncECSs()
	},
	// "eip": func(a Account) (err error) {
	// 	return a.SyncEIPs()
	// },
	// "loadbalancer": func(a Account) (err error) {
	// 	return a.SyncLoadbalancers()
	// },
	"domain": func(a Account) (err error) {
		return a.SyncDomains()
	},
}

func (a Account) Sync(assetType string) (err error) {
	if job, ok := jobTypes[assetType]; ok {
		return job(a)
	} else {
		return errors.New("不支持同步")
	}
}

func SyncAllAccounts() (err error) {
	accounts := []Account{}
	err = app.DB().Where("cloud_type = ?", asset.TencentCloudType).Find(&accounts).Error
	if err != nil {
		return
	}
	for _, account := range accounts {
		for jobName, job := range jobTypes {
			err1 := job(account)
			if err1 != nil {
				app.Log().Error("腾讯云同步资产异常", "account", account.Name, "job_type", jobName, "err", err1.Error())
			}
		}
	}
	return
}
func SyncAllAccountsMonthlyBills() (err error) {
	accounts := []Account{}
	err = app.DB().Where("cloud_type = ?", asset.TencentCloudType).Find(&accounts).Error
	if err != nil {
		return
	}
	for _, account := range accounts {
		err = account.SyncMonthlyBills(time.Now().Format("2006-01"))
		if err != nil {
			app.Log().Error("腾讯云同步账单异常", "account", account.Name, "err", err.Error())
		}
	}
	return
}

// 同步所有账户的上月账单
func SyncAllAccountsLastMonthMonthlyBills() (err error) {
	accounts := []Account{}
	err = app.DB().Where("cloud_type = ?", asset.TencentCloudType).Find(&accounts).Error
	if err != nil {
		return
	}
	for _, account := range accounts {
		err = account.SyncMonthlyBills(mytime.GetLastMonth(time.Now()).Format("2006-01"))
		if err != nil {
			app.Log().Error("腾讯云同步账单异常", "account", account.Name, "err", err.Error())
		}
	}
	return
}

func (a Account) SyncDomains() (err error) {
	err = task.RunJob("sync-tencentcloud-domains", "同步腾讯云DNS", func() (result string, err error) {
		rs, err := a.syncPublicDomains()
		result += "外网域名同步：" + rs.String() + "\n"
		if err != nil {
			return
		}
		rs, err = a.syncPublicDomainsRecords()
		result += "外网域名解析同步：" + rs.String() + "\n"
		return
	})
	return
}

func (a Account) SyncRegions() (err error) {
	err = task.RunJob("sync-tencentcloud-regions", "同步腾讯云region", func() (result string, err error) {
		rs, err := a.syncRegions()
		result += "region同步：" + rs.String() + "\n"
		return
	})
	return
}
func (a Account) SyncECSs() (err error) {
	err = task.RunJob("sync-tencentcloud-cvms", "同步腾讯云cvm", func() (result string, err error) {
		rs, err := a.syncECSs()
		result += "cvm同步：" + rs.String() + "\n"
		return
	})
	return
}

func (a Account) SyncMonthlyBills(billCycle string) (err error) {
	err = task.RunJob("sync-tencentcloud-monthly-bills", "同步腾讯云账单", func() (result string, err error) {
		rs, err := a.syncMonthlyBills(billCycle)
		result = rs
		return
	})
	return
}
