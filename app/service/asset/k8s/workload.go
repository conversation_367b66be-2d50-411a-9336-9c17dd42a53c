package k8s

import (
	"cmdb/app"
	"context"
	"encoding/base64"
	"errors"
	"strings"
	"time"

	"gorm.io/gorm"
	appsv1 "k8s.io/api/apps/v1"
	batchv1 "k8s.io/api/batch/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/clientcmd"
)

type WorkloadType string

var (
	WorkloadTypeDeployment  WorkloadType = "Deployment"
	WorkloadTypeStatefulSet WorkloadType = "StatefulSet"
	WorkloadTypeDaemonSet   WorkloadType = "DaemonSet"
	WorkloadTypeJob         WorkloadType = "Job"
	WorkloadTypeCronJob     WorkloadType = "CronJob"
)
var WorkloadTypes = []WorkloadType{WorkloadTypeDeployment, WorkloadTypeStatefulSet, WorkloadTypeDaemonSet, WorkloadTypeJob, WorkloadTypeCronJob}

func GetWorkloadType(name string) *WorkloadType {
	for _, v := range WorkloadTypes {
		if v == WorkloadType(name) {
			return &v
		}
	}
	return nil
}

type Workload struct {
	ID             uint           `gorm:"column:id;primaryKey;comment:主键" json:"id"`
	Name           string         `gorm:"column:name;type:varchar(255);index" json:"name"`
	WorkloadType   WorkloadType   `gorm:"column:workload_type;type:varchar(255);index" json:"workload_type"`
	Namespace      string         `gorm:"column:namespace;type:varchar(255);index" json:"namespace"`
	Labels         []Label        `gorm:"many2many:asset_k8s_workload_labels;joinForeignKey:workload_id;joinReferences:label_id" json:"labels"`
	SelectorLabels []Label        `gorm:"many2many:asset_k8s_workload_selector_labels;joinForeignKey:workload_id;joinReferences:label_id" json:"selector_labels"`
	ClusterID      uint           `gorm:"column:cluster_id;index" json:"cluster_id"`
	SyncTime       time.Time      `gorm:"column:sync_time;index" json:"sync_time"`
	DeletedAt      gorm.DeletedAt `gorm:"column:deleted_at;index" json:"-"`
}

func (Workload) TableName() string {
	return "asset_k8s_workloads"
}

func GetWorkloads(offset, limit int, clusterID *int, workloadType *WorkloadType, keyword, namespace *string) (count int64, workloads []Workload, err error) {
	dbop := app.DB()
	if namespace != nil {
		dbop = dbop.Where("namespace = ?", namespace)
	}
	if keyword != nil && *keyword != "" {
		if strings.Contains(*keyword, "=") {
			kv := strings.Split(*keyword, "=")
			if len(kv) == 2 {
				dbop = dbop.Where("name LIKE ? OR namespace LIKE ? OR id IN (select workload_id from asset_k8s_workload_labels where label_id in (?))", "%"+*keyword+"%", "%"+*keyword+"%", app.DB().Model(&Label{}).Select("id").Where("label_key = ? AND label_value = ? ", kv[0], kv[1]))
			}
		} else {
			dbop = dbop.Where("name LIKE ? OR namespace LIKE ? OR id IN (select workload_id from asset_k8s_workload_labels where label_id in (?))", "%"+*keyword+"%", "%"+*keyword+"%", app.DB().Model(&Label{}).Select("id").Where("label_key LIKE ? OR label_value LIKE ?", "%"+*keyword+"%", "%"+*keyword+"%"))
		}
	}
	if clusterID != nil {
		dbop = dbop.Where("cluster_id = ?", *clusterID)
	}
	if workloadType != nil {
		dbop = dbop.Where("workload_type = ?", workloadType)
	}
	err = dbop.Model(&Workload{}).Count(&count).Order("namespace,name").Offset(offset).Limit(limit).Find(&workloads).Error
	return
}

func (workload Workload) GetLabels() (labels []Label, err error) {
	err = app.DB().Model(&workload).Association("Labels").Find(&labels)
	return
}

func (workload Workload) GetSelectorLabels() (labels []Label, err error) {
	err = app.DB().Model(&workload).Association("SelectorLabels").Find(&labels)
	return
}

func (workload Workload) SetLabels(labels []Label) (err error) {
	ls := []Label{}
	for i := range labels {
		var l Label
		l, err = GenLabelKey(labels[i].LabelKey, labels[i].LabelValue)
		if err != nil {
			return
		}
		ls = append(ls, l)
	}
	err = app.DB().Model(&workload).Association("Labels").Replace(ls)
	return
}

func (workload Workload) SetSelectorLabels(labels []Label) (err error) {
	ls := []Label{}
	for i := range labels {
		var l Label
		l, err = GenLabelKey(labels[i].LabelKey, labels[i].LabelValue)
		if err != nil {
			return
		}
		ls = append(ls, l)
	}
	err = app.DB().Model(&workload).Association("SelectorLabels").Replace(ls)
	return
}

func (c Cluster) getWorkloads(workloadType WorkloadType) (workloads []Workload, err error) {
	// 获取kubernetes集群的workload信息
	// 获取 kubeconfig 文件路径
	kubeconfigBytes, err := base64.StdEncoding.DecodeString(c.Token)
	if err != nil {
		return
	}
	// 使用 kubeconfig 文件创建一个 Kubernetes 客户端
	config, err := clientcmd.Load(kubeconfigBytes)
	if err != nil {
		return
	}
	client, err := clientcmd.NewDefaultClientConfig(*config, &clientcmd.ConfigOverrides{}).ClientConfig()
	if err != nil {
		return
	}
	clientset, err := kubernetes.NewForConfig(client)
	if err != nil {
		return
	}
	now := time.Now()
	switch workloadType {
	case WorkloadTypeDeployment:
		var deployments *appsv1.DeploymentList
		deployments, err = clientset.AppsV1().Deployments("").List(context.TODO(), metav1.ListOptions{})
		if err != nil {
			return
		}
		if deployments != nil {
			for _, deployment := range deployments.Items {
				workload := Workload{
					Name:         deployment.Name,
					Namespace:    deployment.Namespace,
					WorkloadType: WorkloadTypeDeployment,
					ClusterID:    c.ID,
					SyncTime:     now,
				}
				labels := make([]Label, 0)
				if deployment.Labels != nil {
					for key, value := range deployment.Labels {
						labels = append(labels, Label{LabelKey: key, LabelValue: value})
					}
				}
				selectorLabels := make([]Label, 0)
				if deployment.Spec.Selector != nil {
					for key, value := range deployment.Spec.Selector.MatchLabels {
						selectorLabels = append(selectorLabels, Label{LabelKey: key, LabelValue: value})
					}
				}
				workload.Labels = labels
				workload.SelectorLabels = selectorLabels
				workloads = append(workloads, workload)
			}
		}
	case WorkloadTypeStatefulSet:
		// TODO: implement StatefulSet
		var statefulSets *appsv1.StatefulSetList
		statefulSets, err = clientset.AppsV1().StatefulSets("").List(context.TODO(), metav1.ListOptions{})
		if err != nil {
			return
		}
		if statefulSets != nil {
			for _, statefulSet := range statefulSets.Items {
				workload := Workload{
					Name:         statefulSet.Name,
					Namespace:    statefulSet.Namespace,
					WorkloadType: WorkloadTypeStatefulSet,
					ClusterID:    c.ID,
					SyncTime:     now,
				}
				labels := make([]Label, 0)
				if statefulSet.Labels != nil {
					for key, value := range statefulSet.Labels {
						labels = append(labels, Label{LabelKey: key, LabelValue: value})
					}
				}
				workload.Labels = labels
				selectorLabels := make([]Label, 0)
				if statefulSet.Spec.Selector != nil {
					for key, value := range statefulSet.Spec.Selector.MatchLabels {
						selectorLabels = append(selectorLabels, Label{LabelKey: key, LabelValue: value})
					}
				}
				workload.SelectorLabels = selectorLabels
				workloads = append(workloads, workload)
			}
		}
	case WorkloadTypeDaemonSet:
		var daemonSets *appsv1.DaemonSetList
		daemonSets, err = clientset.AppsV1().DaemonSets("").List(context.TODO(), metav1.ListOptions{})
		if err != nil {
			return
		}
		if daemonSets != nil {
			for _, daemonSet := range daemonSets.Items {
				workload := Workload{
					Name:         daemonSet.Name,
					Namespace:    daemonSet.Namespace,
					WorkloadType: WorkloadTypeDaemonSet,
					ClusterID:    c.ID,
					SyncTime:     now,
				}
				labels := make([]Label, 0)
				if daemonSet.Labels != nil {
					for key, value := range daemonSet.Labels {
						labels = append(labels, Label{LabelKey: key, LabelValue: value})
					}
				}
				workload.Labels = labels
				selectorLabels := make([]Label, 0)
				if daemonSet.Spec.Selector != nil {
					for key, value := range daemonSet.Spec.Selector.MatchLabels {
						selectorLabels = append(selectorLabels, Label{LabelKey: key, LabelValue: value})
					}
				}
				workload.SelectorLabels = selectorLabels
				workloads = append(workloads, workload)
			}
		}
	case WorkloadTypeJob:
		var jobs *batchv1.JobList
		jobs, err = clientset.BatchV1().Jobs("").List(context.TODO(), metav1.ListOptions{})
		if err != nil {
			return
		}
		if jobs != nil {
			for _, job := range jobs.Items {
				workload := Workload{
					Name:         job.Name,
					Namespace:    job.Namespace,
					WorkloadType: WorkloadTypeJob,
					ClusterID:    c.ID,
					SyncTime:     now,
				}
				labels := make([]Label, 0)
				if job.Labels != nil {
					for key, value := range job.Labels {
						labels = append(labels, Label{LabelKey: key, LabelValue: value})
					}
				}
				workload.Labels = labels
				selectorLabels := make([]Label, 0)
				if job.Spec.Selector != nil {
					for key, value := range job.Spec.Selector.MatchLabels {
						selectorLabels = append(selectorLabels, Label{LabelKey: key, LabelValue: value})
					}
				}
				workload.SelectorLabels = selectorLabels
				workloads = append(workloads, workload)
			}
		}
	case WorkloadTypeCronJob:
		var cronJobs *batchv1.CronJobList
		cronJobs, err = clientset.BatchV1().CronJobs("").List(context.TODO(), metav1.ListOptions{})
		if err != nil {
			return
		}
		if cronJobs != nil {
			for _, cronJob := range cronJobs.Items {
				workload := Workload{
					Name:         cronJob.Name,
					Namespace:    cronJob.Namespace,
					WorkloadType: WorkloadTypeCronJob,
					ClusterID:    c.ID,
					SyncTime:     now,
				}
				labels := make([]Label, 0)
				if cronJob.Labels != nil {
					for key, value := range cronJob.Labels {
						labels = append(labels, Label{LabelKey: key, LabelValue: value})
					}
				}
				workload.Labels = labels
				selectorLabels := make([]Label, 0)
				if cronJob.Spec.JobTemplate.Spec.Selector != nil {
					for key, value := range cronJob.Spec.JobTemplate.Spec.Selector.MatchLabels {
						selectorLabels = append(selectorLabels, Label{LabelKey: key, LabelValue: value})
					}
				}
				workload.SelectorLabels = selectorLabels
				workloads = append(workloads, workload)
			}
		}
	}
	return
}

func (c Cluster) SyncWorkloads() (err error) {
	workloads := []Workload{}
	for i := range WorkloadTypes {
		var ws = []Workload{}
		ws, err = c.getWorkloads(WorkloadTypes[i])
		if err != nil {
			return
		}
		if len(ws) > 0 {
			workloads = append(workloads, ws...)
		}
	}
	for i := range workloads {
		exist := Workload{}
		err = app.DB().Where("name = ? AND namespace = ? AND cluster_id = ?", workloads[i].Name, workloads[i].Namespace, c.ID).Take(&exist).Error
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = app.DB().Create(&Workload{
				Name:         workloads[i].Name,
				Namespace:    workloads[i].Namespace,
				WorkloadType: workloads[i].WorkloadType,
				ClusterID:    c.ID,
				SyncTime:     workloads[i].SyncTime,
			}).Error
			if err != nil {
				return
			}
			err = app.DB().Where("name = ? AND namespace = ? AND cluster_id = ?", workloads[i].Name, workloads[i].Namespace, c.ID).Take(&exist).Error
			if err != nil {
				return
			}
		} else if err == nil {
			err = app.DB().Model(&exist).Update("sync_time", workloads[i].SyncTime).Error
			if err != nil {
				return
			}
		}
		err = exist.SetLabels(workloads[i].Labels)
		if err != nil {
			return
		}
		err = exist.SetSelectorLabels(workloads[i].SelectorLabels)
		if err != nil {
			return
		}
	}
	err = app.DB().Where("sync_time < ? AND cluster_id = ?", time.Now().Add(-10*time.Minute), c.ID).Delete(&Workload{}).Error
	return
}

func GetWorkloadByID(id int) (workload Workload, err error) {
	err = app.DB().Where("id = ?", id).Take(&workload).Error
	return
}

func (workload Workload) GetPods() (pods []Pod, err error) {
	selectorLabels, err := workload.GetSelectorLabels()
	if err != nil {
		return
	}
	ids := []uint{}
	for i := range selectorLabels {
		ids = append(ids, selectorLabels[i].ID)
	}
	err = app.DB().Where("id IN (select aa.pod_id from (select pod_id,count(pod_id) as count from asset_k8s_pod_labels where label_id IN ? group by pod_id having count = ?) aa)  AND namespace = ? AND cluster_id = ?", ids, len(ids), workload.Namespace, workload.ClusterID).Find(&pods).Error
	return
}

func (workload Workload) GetServices() (services []Service, err error) {
	labels, err := workload.GetSelectorLabels()
	if err != nil {
		return
	}
	ids := []uint{}
	for i := range labels {
		ids = append(ids, labels[i].ID)
	}
	err = app.DB().Where("id IN (SELECT aa.service_id AS service_id FROM ( SELECT service_id, COUNT(1) AS total FROM asset_k8s_service_selector_labels WHERE label_id IN (?) GROUP BY 1) aa , (SELECT service_id , COUNT(1) AS total FROM asset_k8s_service_selector_labels GROUP BY 1) bb Where aa.total = bb.total and aa.service_id = bb.service_id) AND namespace = ? AND cluster_id = ?", ids, workload.Namespace, workload.ClusterID).Find(&services).Error
	return
}
