package service

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/auth"
	statisticAsset "cmdb/app/service/statistic/asset"
	"cmdb/app/service/workflow"
	"cmdb/pkg/utils/mytime"
	"errors"
	"sort"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type CloudAccountMonthlyBill struct {
	AccountName  string   `gorm:"account_name" json:"account_name"`
	CurrentMonth *float64 `gorm:"current_month" json:"current_month"`
	LastMonth    *float64 `gorm:"last_month" json:"last_month"`
}

type CloudTypeStat struct {
	CloudType asset.CloudType `json:"-"`
	Name      string          `json:"name"`
	Count     int64           `json:"count"`
	Cost      *float64        `json:"cost"`
	LastCost  *float64        `json:"last_cost"`
}
type HostTypeStat struct {
	HostType asset.HostType `json:"-"`
	Name     string         `json:"name"`
	Count    int64          `json:"count"`
}

type WorkflowStats struct {
	WaitingEvaluationCount int64 `json:"waiting_evaluation_count"`
	MyOrderApprovingCount  int64 `json:"my_order_approving_count"`
	MyOrderCount           int64 `json:"my_order_count"`
	CanApproveCount        int64 `json:"can_approve_count"`
	ApprovedCount          int64 `json:"approved_count"`
}

type K8sStats struct {
	ClusterID uint   `gorm:"column:cluster_id" json:"-"`
	Name      string `gorm:"-" json:"name"`
	NodeCount int64  `gorm:"column:count" json:"node_count"`
}

type DashboardData struct {
	WorkflowStats            WorkflowStats                   `json:"workflow_stats"`
	CloudAccountTypeStats    []CloudTypeStat                 `json:"cloud_account_type_stats,omitempty"`
	HostTypeStats            []HostTypeStat                  `json:"host_type_stats,omitempty"`
	CloudAccountMonthlyBills []CloudAccountMonthlyBill       `json:"cloud_account_monthly_bills,omitempty"`
	K8sStats                 []K8sStats                      `json:"k8s_stats,omitempty"`
	ComputerResource         statisticAsset.ComputerResource `json:"computer_resource,omitempty"`
	DataResource             statisticAsset.DataResource     `json:"data_resource,omitempty"`
}

// 获取仪表盘数据
func GetDashboardData(user auth.User) (data DashboardData, err error) {
	data = DashboardData{}
	// 获取待评价的订单
	app.DB().Model(&workflow.Order{}).Where("applicant_id = ? AND status = ? AND NOT EXISTS (SELECT 1 FROM workflow_order_evaluations WHERE workflow_order_evaluations.sn = workflow_orders.sn)", user.ID, workflow.CompletedStatus).Count(&data.WorkflowStats.WaitingEvaluationCount)
	// 获取我正在进行的订单
	app.DB().Model(&workflow.Order{}).Where("applicant_id = ? AND status = ?", user.ID, workflow.ApprovingStatus).Count(&data.WorkflowStats.MyOrderApprovingCount)
	// 获取我的订单
	app.DB().Model(&workflow.Order{}).Where("applicant_id = ?", user.ID).Count(&data.WorkflowStats.MyOrderCount)
	// 获取待我审批的工单
	app.DB().Model(&workflow.Order{}).Where("status = ? AND sn IN (?)", workflow.ApprovingStatus, app.DB().Model(&workflow.Process{}).Distinct("sn").
		Where("status = ? AND process_index > 0 ", workflow.ApprovingStatus).
		Clauses(clause.Expr{
			SQL:  "JSON_CONTAINS(approvers_id,JSON_ARRAY(?))",
			Vars: []interface{}{user.ID},
		}),
	).Count(&data.WorkflowStats.CanApproveCount)
	// 获取已审批订单
	app.DB().Model(&workflow.Process{}).Distinct("sn").Where("approver_id = ? AND status > ?", user.ID, workflow.ApprovingStatus).Count(&data.WorkflowStats.ApprovedCount)
	if !user.IsAdmin {
		return
	}
	// 获取云账号类型统计
	err = app.DB().Model(&asset.CloudAccount{}).Select("cloud_type, count(*) AS count").Group("cloud_type").Find(&data.CloudAccountTypeStats).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	// 获取云账号类型统计
	if len(data.CloudAccountTypeStats) > 0 {
		for i := range data.CloudAccountTypeStats {
			data.CloudAccountTypeStats[i].Name = data.CloudAccountTypeStats[i].CloudType.String()
			var err1 error
			data.CloudAccountTypeStats[i].Cost, err1 = data.CloudAccountTypeStats[i].CloudType.GetCloudTypeMonthlyBills(time.Now().Format("2006-01"))
			if err1 != nil {
				app.Log().Error("获取云服务商月度账单失败", "err", err1)
			}
			data.CloudAccountTypeStats[i].LastCost, err1 = data.CloudAccountTypeStats[i].CloudType.GetCloudTypeMonthlyBills(mytime.GetLastMonth(time.Now()).Format("2006-01"))
			if err1 != nil {
				app.Log().Error("获取云服务商月度账单失败", "err", err1)
			}
		}
	}
	// 获取主机类型统计
	app.DB().Model(&asset.Host{}).Select("host_type, count(*) AS count").Group("host_type").Find(&data.HostTypeStats)
	if len(data.HostTypeStats) > 0 {
		for i := range data.HostTypeStats {
			data.HostTypeStats[i].Name = data.HostTypeStats[i].HostType.String()
		}
	}
	cloudAccounts := []asset.CloudAccount{}
	err = app.DB().Find(&cloudAccounts).Error
	if err == nil {
		for _, account := range cloudAccounts {
			// 获取云账号月度账单
			var currentAmount, lastAmount *float64
			currentAmount, _ = account.GetMonthlyBillAmount(time.Now().Format("2006-01"))
			lastAmount, _ = account.GetMonthlyBillAmount(mytime.GetLastMonth(time.Now()).Format("2006-01"))
			if (lastAmount != nil && *lastAmount > 0) || (currentAmount != nil && *currentAmount > 0) {
				data.CloudAccountMonthlyBills = append(data.CloudAccountMonthlyBills, CloudAccountMonthlyBill{
					AccountName:  account.Name,
					CurrentMonth: currentAmount,
					LastMonth:    lastAmount,
				})
			}
		}
		// 需要根据上月账单金额排序一下，倒序
		sort.Slice(data.CloudAccountMonthlyBills, func(i, j int) bool {
			if data.CloudAccountMonthlyBills[i].CurrentMonth == nil {
				return false
			}
			if data.CloudAccountMonthlyBills[j].CurrentMonth == nil {
				return true
			}
			return *data.CloudAccountMonthlyBills[i].CurrentMonth < *data.CloudAccountMonthlyBills[j].CurrentMonth
		})
	}
	// 获取k8s集群统计（只统计未删除的集群）
	// 使用JOIN查询同时获取集群ID、名称和节点数量
	type ClusterNodeStat struct {
		ClusterID uint   `gorm:"column:cluster_id"`
		Name      string `gorm:"column:name"`
		Count     int64  `gorm:"column:count"`
	}

	var clusterNodeStats []ClusterNodeStat
	err = app.DB().Table("asset_k8s_nodes AS n").
		Select("n.cluster_id, c.name, COUNT(*) AS count").
		Joins("JOIN asset_k8s_clusters AS c ON n.cluster_id = c.id").
		Where("c.deleted_at IS NULL").Where("n.deleted_at IS NULL").
		Group("n.cluster_id, c.name").
		Find(&clusterNodeStats).Error

	if err != nil {
		app.Log().Error("获取集群节点统计失败", "err", err)
	} else {
		// 将查询结果转换为K8sStats格式
		data.K8sStats = make([]K8sStats, len(clusterNodeStats))
		for i, stat := range clusterNodeStats {
			data.K8sStats[i] = K8sStats{
				ClusterID: stat.ClusterID,
				Name:      stat.Name,
				NodeCount: stat.Count,
			}
		}
	}
	// 获取主机资源统计
	err1 := app.DB().Model(&statisticAsset.ComputerResource{}).Order("stat_date DESC").Limit(1).Take(&data.ComputerResource).Error
	if err1 != nil && !errors.Is(err1, gorm.ErrRecordNotFound) {
		err = err1
		app.Log().Error("获取最新主机资源统计数据失败", "err", err1)
		return
	}
	// 获取数据资源统计
	err1 = app.DB().Model(&statisticAsset.DataResource{}).Order("stat_date DESC").Limit(1).Take(&data.DataResource).Error
	if err1 != nil && !errors.Is(err1, gorm.ErrRecordNotFound) {
		err = err1
		app.Log().Error("获取最新数据资源统计数据失败", "err", err1)
		return
	}
	return
}
