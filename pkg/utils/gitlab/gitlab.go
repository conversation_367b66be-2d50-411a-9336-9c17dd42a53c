package gitlab

import (
	"errors"
	"fmt"
	"strings"

	"github.com/xanzy/go-gitlab"
)

type Client struct {
	*gitlab.Client
}

var ErrorGitlabProjectNotFound = errors.New("没有找到对应的项目")
var ErrorGitlabUserNotFound = errors.New("没有找到对应的用户")
var ErrProjectExistMember = errors.New("中该用户已经在项目中")
var ErrInvidatePermissions = errors.New("不可用权限")

// GuestPermissions         AccessLevelValue = 10
// ReporterPermissions      AccessLevelValue = 20
// DeveloperPermissions     AccessLevelValue = 30
// MaintainerPermissions    AccessLevelValue = 40
func GetAccessLevel(code int) (accessLevel gitlab.AccessLevelValue, err error) {
	switch gitlab.AccessLevelValue(code) {
	case gitlab.GuestPermissions:
		accessLevel = gitlab.GuestPermissions
	case gitlab.ReporterPermissions:
		accessLevel = gitlab.ReporterPermissions
	case gitlab.DeveloperPermissions:
		accessLevel = gitlab.DeveloperPermissions
	case gitlab.MaintainerPermissions:
		accessLevel = gitlab.MaintainerPermissions
	default:
		err = ErrInvidatePermissions
	}
	return
}

func NewClient(token string, baseURL string) (c Client, err error) {
	client, err := gitlab.NewClient(token, gitlab.WithBaseURL(baseURL))
	if err != nil {
		return
	}
	c = Client{Client: client}
	return
}

func (c Client) GetProjectByName(name string) (ps []*gitlab.Project, err error) {
	i := 1
	ps = []*gitlab.Project{}
	for {
		fmt.Println("gitlab搜索项目", i, "页")
		opt := gitlab.SearchOptions{
			ListOptions: gitlab.ListOptions{
				PerPage: 50,
				Page:    i,
			},
		}
		pss, _, err1 := c.Search.Projects(name, &opt)
		if err1 != nil {
			err = err1
			return
		}
		if len(pss) > 0 {
			ps = append(ps, pss...)
		} else {
			break
		}
		if len(pss) < 50 {
			// 没有下一页就退出
			break
		}
		i++
	}
	return
}
func (c Client) GetUsersByName(name string) (us []*gitlab.User, err error) {
	i := 1
	us = []*gitlab.User{}
	for {
		opt := gitlab.SearchOptions{
			ListOptions: gitlab.ListOptions{
				PerPage: 50,
				Page:    i,
			},
		}
		rs, _, err1 := c.Search.Users(name, &opt)
		if err1 != nil {
			err = err1
			return
		}
		if len(rs) > 0 {
			us = append(us, rs...)
		}
		if len(rs) == 0 || len(rs) < opt.PerPage {
			// 没有下一页就退出
			break
		}
	}
	return
}
func (c Client) GetUserByName(name string) (u *gitlab.User, err error) {
	us, err := c.GetUsersByName(name)
	if err != nil {
		return
	}
	for i := range us {
		if us[i].Username == name || us[i].PublicEmail == name || us[i].Email == name {
			u = us[i]
			return
		}
	}
	err = ErrorGitlabUserNotFound
	return
}

func GetFileNameFromURL(address string) (name string) {
	if strings.HasSuffix(address, ".git") && strings.HasPrefix(address, "git@") && strings.Contains(address, ":") {
		fileds := strings.Split(address, "/")
		if len(fileds) > 0 {
			last := fileds[len(fileds)-1]
			if len(last) >= 4 {
				name = last[:len(last)-4]
			}
		}
	} else if strings.HasPrefix(address, "http://") || strings.HasPrefix(address, "https://") {
		fileds := strings.Split(address, "/")
		if len(fileds) > 0 {
			name = strings.TrimSuffix(fileds[len(fileds)-1], ".git")
		}
	}
	return
}

func (c *Client) GetProjectByURL(url string) (p *gitlab.Project, err error) {
	name := GetFileNameFromURL(url)
	if name == "" {
		err = errors.New("非法项目路径")
		return
	}
	fmt.Println("获取项目对象", "url", url, "name", name)
	ps, err := c.GetProjectByName(name)
	if err != nil {
		return
	}
	for i := range ps {
		if ps[i].WebURL == url || ps[i].SSHURLToRepo == url || ps[i].HTTPURLToRepo == url {
			p = ps[i]
			err = nil
			return
		}
	}
	err = ErrorGitlabProjectNotFound
	return
}

func (c *Client) GetProjectMembers(p *gitlab.Project, uid int) (m *gitlab.ProjectMember, err error) {
	m, _, err = c.ProjectMembers.GetProjectMember(p.ID, uid)
	if err != nil {
		return
	}
	return
}

func (c Client) AddProjectMember(p *gitlab.Project, uid int, permission *gitlab.AccessLevelValue, expiredAt *string) (err error) {
	m, _ := c.GetProjectMembers(p, uid)
	if m != nil {
		// return ErrProjectExistMember
		// 删除已经存在
		_, err = c.ProjectMembers.DeleteProjectMember(p.ID, uid)
		if err != nil {
			return
		}
	}
	opt := &gitlab.AddProjectMemberOptions{
		UserID:      uid,
		AccessLevel: permission,
		ExpiresAt:   expiredAt,
	}
	_, _, err = c.ProjectMembers.AddProjectMember(p.ID, opt)
	return
}
