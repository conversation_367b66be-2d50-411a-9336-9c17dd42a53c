package k8s

import (
	"cmdb/app"
	"context"
	"encoding/base64"
	"errors"
	"strings"
	"time"

	"gorm.io/gorm"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/clientcmd"
)

type Pod struct {
	ID            int            `gorm:"column:id;primary_key;auto_increment;comment:ID" json:"id"`
	ClusterID     uint           `gorm:"column:cluster_id;index;comment:集群ID" json:"cluster_id"`
	Name          string         `gorm:"column:name;index;comment:名称" json:"name"`
	Namespace     string         `gorm:"column:namespace;index;comment:命名空间" json:"namespace"`
	Status        string         `gorm:"column:status;index;comment:状态" json:"status"`
	ControllerBy  string         `gorm:"column:controller_by;index;comment:控制器" json:"controller_by"`
	Labels        []Label        `gorm:"many2many:asset_k8s_pod_labels" json:"labels"`
	PodIP         string         `gorm:"column:pod_ip;index;comment:Pod IP" json:"pod_ip"`
	HostIP        string         `gorm:"column:host_ip;index;comment:Host IP" json:"host_ip"`
	SyncTime      time.Time      `gorm:"column:sync_time;comment:同步时间" json:"sync_time"`
	DeletedAt     gorm.DeletedAt `gorm:"column:deleted_at;comment:删除时间" json:"deleted_at"`
	Containers    []Container    `gorm:"-" json:"containers"`
	RequestCPU    int64          `gorm:"-" json:"request_cpu"`
	RequestMemory int64          `gorm:"-" json:"request_memory"`
	LimitCPU      int64          `gorm:"-" json:"limit_cpu"`
	LimitMemory   int64          `gorm:"-" json:"limit_memory"`
}

func (Pod) TableName() string {
	return "asset_k8s_pods"
}

func GetPods(offset, limit int, clusterID *int, ip, keyword *string, workloadIDs ...uint) (count int64, pods []Pod, err error) {
	dbop := app.DB().Model(&Pod{})
	if clusterID != nil {
		dbop = dbop.Where("cluster_id = ?", clusterID)
	}
	if ip != nil {
		dbop = dbop.Where("pod_ip = ? OR host_ip = ?", ip, ip)
	}
	if len(workloadIDs) > 0 {
		dbop = dbop.Where("id IN (select pod_id from asset_k8s_pod_labels where label_id in (select label_id from asset_k8s_workload_selector_labels where workload_id in (?)))", workloadIDs)
	}
	if keyword != nil && *keyword != "" {
		if strings.Contains(*keyword, "=") {
			kv := strings.Split(*keyword, "=")
			if len(kv) == 2 {
				dbop = dbop.Where("name LIKE ? OR namespace LIKE ? OR id IN (select pod_id from asset_k8s_pod_labels where label_id in (?))", "%"+*keyword+"%", "%"+*keyword+"%", app.DB().Model(&Label{}).Select("id").Where("label_key = ? AND label_value = ? ", kv[0], kv[1]))
			}
		} else {
			dbop = dbop.Where("name LIKE ? OR namespace LIKE ? OR id IN (select pod_id from asset_k8s_pod_labels where label_id in (?))", "%"+*keyword+"%", "%"+*keyword+"%", app.DB().Model(&Label{}).Select("id").Where("label_key LIKE ? OR label_value LIKE ?", "%"+*keyword+"%", "%"+*keyword+"%"))
		}
	}
	err = dbop.Count(&count).Order("create_time DESC").Offset(offset).Limit(limit).Find(&pods).Error
	return
}
func (pod Pod) GetLabels() (labels []Label, err error) {
	err = app.DB().Model(&pod).Association("Labels").Find(&labels)
	return
}

func (c Cluster) getPods() (pods []Pod, err error) {
	// 获取kubernetes集群的pod信息
	// 获取 kubeconfig 文件路径
	kubeconfigBytes, err := base64.StdEncoding.DecodeString(c.Token)
	if err != nil {
		return
	}
	// 使用 kubeconfig 文件创建一个 Kubernetes 客户端
	config, err := clientcmd.Load(kubeconfigBytes)
	if err != nil {
		return
	}
	client, err := clientcmd.NewDefaultClientConfig(*config, &clientcmd.ConfigOverrides{}).ClientConfig()
	if err != nil {
		return
	}
	clientset, err := kubernetes.NewForConfig(client)
	if err != nil {
		return
	}
	// 获取所有节点
	podList, err := clientset.CoreV1().Pods("").List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		return
	}
	for i := range podList.Items {
		labels := []Label{}
		for k, v := range podList.Items[i].Labels {
			labels = append(labels, Label{
				LabelKey:   k,
				LabelValue: v,
			})
		}
		pods = append(pods, Pod{
			Name:      podList.Items[i].Name,
			Namespace: podList.Items[i].Namespace,
			PodIP:     podList.Items[i].Status.PodIP,
			HostIP:    podList.Items[i].Status.HostIP,
			Status:    string(podList.Items[i].Status.Phase),
			ClusterID: c.ID,
			SyncTime:  time.Now(),
			Labels:    labels,
		})
		for j := range podList.Items[i].Spec.Containers {
			restartPolicy := ""
			if podList.Items[i].Spec.Containers[j].RestartPolicy != nil {
				restartPolicy = string(*podList.Items[i].Spec.Containers[j].RestartPolicy)
			}
			envs := []string{}
			for _, env := range podList.Items[i].Spec.Containers[j].Env {
				envs = append(envs, env.Name+"="+env.Value)
			}
			pods[i].Containers = append(pods[i].Containers, Container{
				ClusterID:     c.ID,
				Name:          podList.Items[i].Spec.Containers[j].Name,
				Pod:           podList.Items[i].Name,
				Namespace:     podList.Items[i].Namespace,
				Image:         podList.Items[i].Spec.Containers[j].Image,
				Status:        string(podList.Items[i].Status.Phase),
				Command:       strings.Join(podList.Items[i].Spec.Containers[j].Command, " "),
				Args:          strings.Join(podList.Items[i].Spec.Containers[j].Args, " "),
				WorkingDir:    podList.Items[i].Spec.Containers[j].WorkingDir,
				RestartPolicy: restartPolicy,
				Envs:          strings.Join(envs, ";"),
				RequestCPU:    podList.Items[i].Spec.Containers[j].Resources.Requests.Cpu().MilliValue(),
				RequestMemory: podList.Items[i].Spec.Containers[j].Resources.Requests.Memory().Value(),
				LimitCPU:      podList.Items[i].Spec.Containers[j].Resources.Limits.Cpu().MilliValue(),
				LimitMemory:   podList.Items[i].Spec.Containers[j].Resources.Limits.Memory().Value(),
				SyncTime:      time.Now(),
			})
		}
	}
	return
}

func (pod Pod) SetLabels(labels []Label) (err error) {
	ls := []Label{}
	for i := range labels {
		var l Label
		l, err = GenLabelKey(labels[i].LabelKey, labels[i].LabelValue)
		if err != nil {
			return
		}
		ls = append(ls, l)
	}
	err = app.DB().Model(&pod).Association("Labels").Replace(ls)
	return
}

func (c Cluster) SyncPods() (err error) {
	pods, err := c.getPods()
	if err != nil {
		return
	}
	lastTime := time.Now().Add(-10 * time.Minute)
	for i := range pods {
		exist := Pod{}
		err = app.DB().Where("name = ? AND namespace = ? AND cluster_id = ?", pods[i].Name, pods[i].Namespace, c.ID).Take(&exist).Error
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = app.DB().Create(&pods[i]).Error
			if err != nil {
				return
			}
			err = app.DB().Where("name = ? AND namespace = ? AND cluster_id = ?", pods[i].Name, pods[i].Namespace, c.ID).Take(&exist).Error
			if err != nil {
				return
			}
		} else if err == nil {
			updateMap := map[string]any{
				"sync_time": pods[i].SyncTime,
			}
			if pods[i].Status != exist.Status {
				updateMap["status"] = pods[i].Status
			}
			if pods[i].PodIP != exist.PodIP {
				updateMap["pod_ip"] = pods[i].PodIP
			}
			if pods[i].HostIP != exist.HostIP {
				updateMap["host_ip"] = pods[i].HostIP
			}
			err = app.DB().Model(&exist).Updates(updateMap).Error
			if err != nil {
				return
			}
		}
		err = exist.SetLabels(pods[i].Labels)
		if err != nil {
			return
		}
		for j := range pods[i].Containers {
			existContainer := Container{}
			err = app.DB().Where("name = ? AND pod = ? AND namespace = ? AND cluster_id = ?", pods[i].Containers[j].Name, pods[i].Name, pods[i].Namespace, c.ID).Take(&existContainer).Error
			if errors.Is(err, gorm.ErrRecordNotFound) {
				err = app.DB().Create(&Container{
					ClusterID:     c.ID,
					Name:          pods[i].Containers[j].Name,
					Pod:           pods[i].Name,
					Namespace:     pods[i].Namespace,
					Image:         pods[i].Containers[j].Image,
					Status:        pods[i].Containers[j].Status,
					Command:       pods[i].Containers[j].Command,
					Args:          pods[i].Containers[j].Args,
					WorkingDir:    pods[i].Containers[j].WorkingDir,
					RestartPolicy: pods[i].Containers[j].RestartPolicy,
					Envs:          pods[i].Containers[j].Envs,
					RequestCPU:    pods[i].Containers[j].RequestCPU,
					RequestMemory: pods[i].Containers[j].RequestMemory,
					LimitCPU:      pods[i].Containers[j].LimitCPU,
					LimitMemory:   pods[i].Containers[j].LimitMemory,
					SyncTime:      pods[i].SyncTime,
				}).Error
				if err != nil {
					return
				}
			} else if err == nil {
				updateMap := map[string]any{
					"sync_time": pods[i].Containers[j].SyncTime,
				}
				if pods[i].Containers[j].Status != existContainer.Status {
					updateMap["status"] = pods[i].Containers[j].Status
				}
				if pods[i].Containers[j].RequestCPU != existContainer.RequestCPU {
					updateMap["request_cpu"] = pods[i].Containers[j].RequestCPU
				}
				if pods[i].Containers[j].RequestMemory != existContainer.RequestMemory {
					updateMap["request_memory"] = pods[i].Containers[j].RequestMemory
				}
				if pods[i].Containers[j].LimitCPU != existContainer.LimitCPU {
					updateMap["limit_cpu"] = pods[i].Containers[j].LimitCPU
				}
				if pods[i].Containers[j].LimitMemory != existContainer.LimitMemory {
					updateMap["limit_memory"] = pods[i].Containers[j].LimitMemory
				}
				if pods[i].Containers[j].RestartPolicy != existContainer.RestartPolicy {
					updateMap["restart_policy"] = pods[i].Containers[j].RestartPolicy
				}
				if pods[i].Containers[j].Envs != existContainer.Envs {
					updateMap["envs"] = pods[i].Containers[j].Envs
				}
				if pods[i].Containers[j].Command != existContainer.Command {
					updateMap["command"] = pods[i].Containers[j].Command
				}
				if pods[i].Containers[j].Args != existContainer.Args {
					updateMap["args"] = pods[i].Containers[j].Args
				}
				if pods[i].Containers[j].WorkingDir != existContainer.WorkingDir {
					updateMap["working_dir"] = pods[i].Containers[j].WorkingDir
				}
				if pods[i].Containers[j].Image != existContainer.Image {
					updateMap["image"] = pods[i].Containers[j].Image
				}
				err = app.DB().Model(&existContainer).Updates(updateMap).Error
				if err != nil {
					return
				}
			}
			err = app.DB().Where("name = ? AND pod = ? AND namespace = ? AND cluster_id = ? AND sync_time < ?", pods[i].Containers[j].Name, pods[i].Name, pods[i].Namespace, c.ID, lastTime).Delete(&Container{}).Error
			if err != nil {
				return
			}
		}
		err = exist.SetLabels(pods[i].Labels)
		if err != nil {
			return
		}
	}
	app.DB().Where("sync_time < ? AND cluster_id =? ", lastTime, c.ID).Delete(&Pod{})
	return
}

func (pod Pod) GetContainers() (containers []Container, err error) {
	err = app.DB().Where("pod = ? AND namespace = ? AND cluster_id = ?", pod.Name, pod.Namespace, pod.ClusterID).Find(&containers).Error
	return
}
