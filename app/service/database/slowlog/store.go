package slowlog

import (
	"cmdb/app"
	"cmdb/pkg/db"
	"cmdb/pkg/utils"
	"errors"
	"os"
	"time"

	stdlog "log"

	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var (
	ErrStoreNameExists = errors.New("实例已经存在")
)

// 慢日志存储
type SlowlogStore struct {
	ID        int64          `gorm:"column:id;primaryKey;autoIncrement;comment:'自增字段'" json:"id"`
	Name      string         `gorm:"column:name;type:varchar(255);not null;index;default:'';comment:'名称'" json:"name"`
	Host      string         `gorm:"column:host;type:varchar(255);not null;default:'';comment:'主机名'" json:"host"`
	Port      uint           `gorm:"column:port;not null;default:0;comment:'端口'" json:"port"`
	DB        string         `gorm:"column:db;type:varchar(255);not null;default:'';comment:'数据库'" json:"db"`
	User      string         `gorm:"column:user;type:varchar(255);not null;default:'';comment:'用户'" json:"user"`
	Password  string         `gorm:"column:password;not null;default:'';comment:'密码'" json:"password"`
	CreatedAt time.Time      `gorm:"column:created_at;not null;comment:'创建时间'" json:"created_at"`
	UpdatedAt time.Time      `gorm:"column:updated_at;not null;dcomment:'更新时间'" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at;default:null;comment:'删除时间'" json:"deleted_at"`
}

// TableName 设置表名
func (SlowlogStore) TableName() string {
	return "asset_db_slowlog_stores"
}

type SlowlogStoreForm struct {
	Name     string `json:"name" binding:"required,max=255"`
	Host     string `json:"host" binding:"required,max=255"`
	Port     uint   `json:"port" binding:"required"`
	DB       string `json:"db" binding:"required,max=255"`
	User     string `json:"user" binding:"required,max=255"`
	Password string `json:"password" `
}

func (form SlowlogStoreForm) Create() (err error) {
	err = app.DB().Where("name = ?", form.Name).Take(&SlowlogStore{}).Error
	if err == nil {
		err = ErrStoreNameExists
		return
	}
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = app.DB().Create(&SlowlogStore{
			Name:     form.Name,
			Host:     form.Host,
			Port:     form.Port,
			DB:       form.DB,
			User:     form.User,
			Password: utils.Base64Encode(form.Password),
		}).Error
	}
	return
}

func (s SlowlogStore) Update(form SlowlogStoreForm) (err error) {
	exist := SlowlogStore{}
	err = app.DB().Where("name = ?", form.Name).Take(&exist).Error
	if err == nil && exist.ID != s.ID {
		err = ErrStoreNameExists
		return
	} else if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	} else {
		err = nil
	}
	updateMap := map[string]any{}
	if s.Name != form.Name {
		updateMap["name"] = form.Name
	}
	if s.Host != form.Host {
		updateMap["host"] = form.Host
	}
	if s.Port != form.Port {
		updateMap["port"] = form.Port
	}
	if s.User != form.User {
		updateMap["user"] = form.User
	}
	if form.DB != "" && s.DB != form.DB {
		updateMap["db"] = form.DB
	}
	if form.Password != "" && s.Password != utils.Base64Encode(form.Password) {
		updateMap["password"] = utils.Base64Encode(form.Password)
	}
	if len(updateMap) > 0 {
		err = app.DB().Model(&s).Updates(updateMap).Error
	}
	return
}

func GetSlowlogStoreByID(id int) (s SlowlogStore, err error) {
	err = app.DB().Where("id = ?", id).Take(&s).Error
	return
}

func (s SlowlogStore) Delete() (err error) {
	err = app.DB().Delete(&s).Error
	return
}

func GetSlowlogStore(offset, limit int, keyword *string) (count int64, data []SlowlogStore, err error) {
	dbop := app.DB()
	if keyword != nil && *keyword != "" {
		dbop = db.MLike(dbop, *keyword, "name", "host")
	}
	err = dbop.Model(&SlowlogStore{}).Count(&count).Order("name").Offset(offset).Limit(limit).Find(&data).Error
	return
}

func GetAllSlowlogStore() (data []SlowlogStore, err error) {
	err = app.DB().Model(&SlowlogStore{}).Order("name").Find(&data).Error
	return
}

func (s SlowlogStore) NewConnect() (dbop *gorm.DB, err error) {
	password, _ := utils.Base64Decode(s.Password)
	gormConfig := &gorm.Config{
		QueryFields: true, // 禁用彩色打印
	}
	loggerConfig := logger.Config{
		SlowThreshold: time.Second, // 慢 SQL 阈值
		LogLevel:      logger.Info, // Log level
		Colorful:      false,       // 禁用彩色打印
	}
	gormConfig.Logger = logger.New(stdlog.New(os.Stdout, "\r\n", stdlog.LstdFlags), loggerConfig)
	dbop, err = db.NewMySQLDBConnect(db.MysqlConnParmas{
		Host:     s.Host,
		Port:     s.Port,
		User:     s.User,
		Password: password,
		DB:       s.DB,
	}, gormConfig)
	return
}

func (s SlowlogStore) GetMySQLSlowLogs(offset, limit int, keyword *string) (count int64, slowLogs []MySQLSlowLog, err error) {
	dbop, err := s.NewConnect()
	if err != nil {
		return
	}
	if keyword != nil && *keyword != "" {
		dbop = db.MLike(dbop, *keyword, "query_template", "query_sample", "query_user", "query_client_ip", "db_project_name", "db_title", "db_private_ip", "db_instance_id", "db_name")
	}
	err = dbop.Model(&MySQLSlowLog{}).Order("create_time DESC").Count(&count).Offset(offset).Limit(limit).Find(&slowLogs).Error
	return
}

func (s SlowlogStore) GetMySQLSlowLogStatistics(offset, limit int, keyword *string) (count int64, slowLogStatistics []MySQLSlowLogStatistics, err error) {
	dbop, err := s.NewConnect()
	if err != nil {
		return
	}
	defer func() {
		d, err1 := dbop.DB()
		if err1 == nil {
			d.Close()
		}
	}()
	if keyword != nil && *keyword != "" {
		dbop = db.MLike(dbop, *keyword, "db_project_name", "db_title", "db_instance_id", "db_name")
	}
	err = dbop.Model(&MySQLSlowLogStatistics{}).Order("create_time DESC").Count(&count).Offset(offset).Limit(limit).Find(&slowLogStatistics).Error
	return
}

func (s SlowlogStore) GetMySQLDmlLogs(offset, limit int, keyword *string) (count int64, dmlLogs []MySQLDMLLog, err error) {
	dbop, err := s.NewConnect()
	if err != nil {
		return
	}

	defer func() {
		d, err1 := dbop.DB()
		if err1 == nil {
			d.Close()
		}
	}()
	if keyword != nil && *keyword != "" {
		dbop = db.MLike(dbop, *keyword, "idc_type", "db_project_name", "db_title", "db_private_ip", "db_instance_id", "db_name", "tb_name")
	}
	err = dbop.Model(&MySQLDMLLog{}).Order("create_time DESC").Count(&count).Offset(offset).Limit(limit).Find(&dmlLogs).Error
	return
}

func (s SlowlogStore) GetRedisBigKeys(offset, limit int, keyword *string) (count int64, bigKeys []RedisBigKeys, err error) {
	dbop, err := s.NewConnect()
	if err != nil {
		return
	}
	defer func() {
		d, err1 := dbop.DB()
		if err1 == nil {
			d.Close()
		}
	}()
	if keyword != nil && *keyword != "" {
		dbop = db.MLike(dbop, *keyword, "db_project_name", "db_title", "db_private_ip", "db_instance_id", "db_name")
	}
	err = dbop.Model(&RedisBigKeys{}).Order("create_time DESC").Count(&count).Offset(offset).Limit(limit).Find(&bigKeys).Error
	return
}

func (s SlowlogStore) GetRedisBigKeysStringStatistics(offset, limit int, keyword *string) (count int64, bigKeysStringStatistics []RedisBigKeysStringStatistic, err error) {
	dbop, err := s.NewConnect()
	if err != nil {
		return
	}
	defer func() {
		d, err1 := dbop.DB()
		if err1 == nil {
			d.Close()
		}
	}()
	if keyword != nil && *keyword != "" {
		dbop = db.MLike(dbop, *keyword, "db_project_name", "db_title", "db_private_ip", "db_instance_id", "db_name")
	}
	err = dbop.Model(&RedisBigKeysStringStatistic{}).Order("create_time DESC").Count(&count).Offset(offset).Limit(limit).Find(&bigKeysStringStatistics).Error
	return
}

func (s SlowlogStore) GetMongoDBSlowLogs(offset, limit int, keyword *string) (count int64, slowLogs []MongoDBSlowLog, err error) {
	dbop, err := s.NewConnect()
	if err != nil {
		return
	}
	defer func() {
		d, err1 := dbop.DB()
		if err1 == nil {
			d.Close()
		}
	}()
	if keyword != nil && *keyword != "" {
		dbop = db.MLike(dbop, *keyword, "db_project_name", "db_title", "db_private_ip", "db_instance_id", "db_name")
	}
	err = dbop.Model(&MongoDBSlowLog{}).Order("create_time DESC").Count(&count).Offset(offset).Limit(limit).Find(&slowLogs).Error
	return
}

func (s SlowlogStore) GetMongoDBSlowLogStatistics(offset, limit int, keyword *string) (count int64, slowLogStatistics []MongoDBSlowLogStatistics, err error) {
	dbop, err := s.NewConnect()
	if err != nil {
		return
	}
	defer func() {
		d, err1 := dbop.DB()
		if err1 == nil {
			d.Close()
		}
	}()
	if keyword != nil && *keyword != "" {
		dbop = db.MLike(dbop, *keyword, "db_project_name", "db_title", "db_private_ip", "db_instance_id", "db_name")
	}
	err = dbop.Model(&MongoDBSlowLogStatistics{}).Order("create_time DESC").Count(&count).Offset(offset).Limit(limit).Find(&slowLogStatistics).Error
	return
}
