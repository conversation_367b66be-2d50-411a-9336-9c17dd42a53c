package mysql

import (
	"cmdb/app"
	"cmdb/app/service/database/mysql"
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

// 获取用户有权限的库表名称
func GetUserPrivDBTableList(c *gin.Context) {
	email := c.Query("email")
	if email == "" || !strings.Contains(email, "@") {
		app.V2FailedResponseMsg(c, "请输入正确邮箱")
		return
	}
	userPrivDBTable, err := mysql.GetUserPrivDBTableList(strings.Split(email, "@")[0])
	if err != nil {
		app.V2FailedResponseMsg(c, err.Error())
		return
	}
	c.JSON(http.StatusOK, userPrivDBTable)
}

// 获取数据库表中的列
func GetDBTableColumns(c *gin.Context) {
	host := c.Query("host")
	port := c.Query("port")
	dbname := c.Query("db")
	table := c.Query("table")
	columns, err := mysql.GetDBTableColumns(host+":"+port, dbname, table)
	if err != nil {
		app.V2FailedResponseMsg(c, err.Error())
		return
	}
	app.V2SuccessResponseData(c, columns)
}

// 获取ci相关的实例列表
func GetCIInstancesDatabases(c *gin.Context) {
	instancesDatabases, err := mysql.GetCIInstancesDatabases()
	if err != nil {
		app.V2FailedResponseMsg(c, err.Error())
		return
	}
	app.V2SuccessResponseData(c, instancesDatabases)
}

// 获取ci相关的实例列表
func GetCIInstanceDatabaseTables(c *gin.Context) {
	host := c.Query("host")
	port, err := strconv.Atoi(c.Query("port"))
	if err != nil {
		app.V2FailedResponseMsg(c, "非法参数")
		return
	}
	dbname := c.Query("dbname")
	instancesDatabases, err := mysql.GetCIInstanceDatabaseTables(host, uint(port), dbname)
	if err != nil {
		app.V2FailedResponseMsg(c, err.Error())
		return
	}
	app.V2SuccessResponseData(c, instancesDatabases)
}
