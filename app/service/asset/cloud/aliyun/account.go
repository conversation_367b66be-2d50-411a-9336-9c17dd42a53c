package aliyun

import (
	"cmdb/app"
	"cmdb/app/service/asset"
)

type Account struct {
	asset.CloudAccount
}

func (a Account) TableName() string {
	return a.CloudAccount.TableName()
}

func CloudAccount(a asset.CloudAccount) Account {
	return Account{
		CloudAccount: a,
	}
}

func GetAllAccounts() (accounts []Account, err error) {
	err = app.DB().Where("cloud_type = ?", asset.AliyunCloudType).Find(&accounts).Error
	return
}
