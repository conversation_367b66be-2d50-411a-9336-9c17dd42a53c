# 夜莺(N9E)告警统计接口

本模块提供了基于夜莺(N9E)告警数据的多维度统计分析功能，支持时间范围查询、分页查询和多种统计维度。

## 功能特性

- **时间范围告警统计**：支持指定开始时间和结束时间参数
- **多维度统计分析**：
  - 实例维度统计（按 `target_ident` 分组）
  - 告警规则维度统计（按 `rule_id` 和 `rule_name` 分组）
  - 集群维度统计（按 `cluster` 分组）
- **当前活跃告警查询**：查询当前正在发生的告警事件
- **丰富的统计指标**：
  - 告警总数
  - 按严重程度分类统计（Emergency/Warning/Notice）
  - 恢复率统计
  - 平均告警持续时间（已优化计算逻辑）
- **数据库NULL值处理**：使用 `sql.NullFloat64` 安全处理NULL值
- **资源组默认值处理**：空的group_name字段自动设置为'(未设置资源组)'

## API接口

### 1. 获取告警统计数据

**接口地址**：`GET /api/v1/statistic/monitor/n9e/alerts`

**请求参数**：
- `start_time` (必填): 开始时间，格式为 `2006-01-02 15:04:05`
- `end_time` (必填): 结束时间，格式为 `2006-01-02 15:04:05`
- `page` (可选): 页码，默认为1
- `limit` (可选): 每页数量，默认为10

### 2. 按实例维度获取告警统计

**接口地址**：`GET /api/v1/statistic/monitor/n9e/alerts/by-instance`

### 3. 按告警规则维度获取告警统计

**接口地址**：`GET /api/v1/statistic/monitor/n9e/alerts/by-rule`

### 4. 按集群维度获取告警统计

**接口地址**：`GET /api/v1/statistic/monitor/n9e/alerts/by-cluster`

### 5. 获取当前活跃的告警数据

**接口地址**：`GET /api/v1/statistic/monitor/n9e/alerts/current`

**请求参数**：
- `start_time` (可选): 开始时间，格式为 `2006-01-02 15:04:05`
- `end_time` (可选): 结束时间，格式为 `2006-01-02 15:04:05`
- `severity` (可选): 严重程度过滤，0:Emergency 1:Warning 2:Notice
- `cluster` (可选): 集群过滤
- `target_ident` (可选): 实例标识过滤
- `page` (可选): 页码，默认为1
- `limit` (可选): 每页数量，默认为10

**响应示例**：
```json
{
  "success": true,
  "data": [
    {
      "id": 123456,
      "rule_id": 1001,
      "rule_name": "CPU使用率过高",
      "rule_note": "CPU使用率超过80%时触发告警",
      "severity": 1,
      "severity_text": "Warning",
      "target_ident": "192.168.1.100",
      "target_note": "Web服务器1",
      "trigger_time": 1640995200,
      "trigger_time_text": "2022-01-01 08:00:00",
      "trigger_value": "85.6%",
      "first_trigger_time": 1640995200,
      "cluster": "production",
      "group_id": 1,
      "group_name": "生产环境",
      "annotations": "CPU使用率持续过高，请检查系统负载",
      "tags": "env=prod,service=web",
      "notify_cur_number": 3,
      "duration": 3600,
      "duration_text": "1小时"
    }
  ],
  "count": 25
}
```

## 数据模型

### 告警严重程度

- `0`: Emergency (紧急)
- `1`: Warning (警告)  
- `2`: Notice (通知)

### 时间字段说明

- `trigger_time`: 告警触发时间（Unix时间戳）
- `recover_time`: 告警恢复时间（Unix时间戳）
- `first_trigger_time`: 首次触发时间（Unix时间戳）

**重要**：平均持续时间 = `recover_time - trigger_time`

## 使用示例

### 查询当前所有活跃告警

```bash
curl -X GET "http://localhost:8080/api/v1/statistic/monitor/n9e/alerts/current?page=1&limit=10" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 查询紧急级别的当前告警

```bash
curl -X GET "http://localhost:8080/api/v1/statistic/monitor/n9e/alerts/current?severity=0&page=1&limit=10" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 查询指定集群的当前告警

```bash
curl -X GET "http://localhost:8080/api/v1/statistic/monitor/n9e/alerts/current?cluster=production&page=1&limit=10" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 查询最近1小时的当前告警

```bash
curl -X GET "http://localhost:8080/api/v1/statistic/monitor/n9e/alerts/current?start_time=2024-01-01%2007:00:00&page=1&limit=10" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 注意事项

1. **时间格式**：所有时间参数必须使用 `2006-01-02 15:04:05` 格式
2. **权限要求**：需要管理员权限才能访问这些接口
3. **性能优化**：
   - 建议在 `trigger_time` 字段上创建索引
   - 建议创建复合索引：`(is_recovered, recover_time, trigger_time)`
   - 大时间范围查询时建议使用分页
4. **数据质量**：
   - 系统会自动过滤异常数据（如负数持续时间）
   - 只计算已恢复且有有效恢复时间的告警
   - NULL值和边界情况都有适当处理
5. **当前告警特性**：
   - 查询 `alert_cur_event` 表中的实时数据
   - 支持多种过滤条件组合使用
   - 自动计算告警持续时间
   - 提供人性化的时间和持续时间显示

## 错误处理

接口会返回标准的错误响应格式：

```json
{
  "success": false,
  "msg": "错误信息描述"
}
```

常见错误：
- 时间格式错误
- 开始时间大于结束时间
- 严重程度参数值错误
- 数据库查询失败

## 相关文档

- [优化总结](./OPTIMIZATION_SUMMARY.md) - 详细的优化过程和技术细节
- [表名更新记录](./TABLE_NAME_UPDATE.md) - 数据库表名修改记录
- [使用示例](./example.sh) - 完整的API调用示例脚本
