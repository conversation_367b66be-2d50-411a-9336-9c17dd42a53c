package dashboard

import (
	"cmdb/app"
	"cmdb/app/service"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

type SearchForm struct {
	IP         string   `json:"ip"`
	AssetTypes []string `json:"asset_types"`
}

func Search(c *gin.Context) {
	var form SearchForm
	if err := c.ShouldBind(&form); err != nil {
		app.FailedResponseMsg(c, err.Error())
		return
	}
	ip := strings.TrimSpace(form.IP)
	if ip == "" {
		app.SuccessResponseData(c, []any{})
		return
	}
	data, err := service.Search(ip, form.AssetTypes...)
	if err != nil {
		app.Log().Error("查询失败", "err", err)
		app.FailedResponseMsg(c, "查询失败")
		return
	}
	app.SuccessResponseData(c, data)
}

func MachineUse(c *gin.Context) {
	ip := c.Query("ip")
	if ip == "" {
		app.FailedResponseMsg(c, "ip不能为空")
		return
	}
	data, err := service.MachineUse(ip)
	if err != nil {
		app.Log().Error("查询失败", "err", err)
		app.FailedResponseMsg(c, "查询失败")
		return
	}
	c.String(http.StatusOK, data)
}
