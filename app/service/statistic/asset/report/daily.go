package report

import (
	"bytes"
	"cmdb/app"
	"cmdb/app/service/asset"
	statisticAsset "cmdb/app/service/statistic/asset"
	"encoding/json"
	"errors"
	"time"

	"github.com/jinzhu/now"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// CloudComputerResource 云计算机资源统计
type CloudComputerResource struct {
	CloudType           string `gorm:"column:cloud_type;index;comment:云类型" json:"cloud_type"`
	HostTotal           uint64 `gorm:"column:host_total;comment:主机总数" json:"host_total"`
	HostTotalChange     *int64 `gorm:"column:host_total_change;comment:主机总数变化" json:"host_total_change"`
	CPUTotal            uint64 `gorm:"column:cpu_total;comment:CPU总数" json:"cpu_total"`
	CPUTotalChange      *int64 `gorm:"column:cpu_total_change;comment:CPU总数变化" json:"cpu_total_change"`
	MemoryMBTotal       uint64 `gorm:"column:memory_mb_total;comment:内存总数(MB)" json:"memory_mb_total"`
	MemoryMBTotalChange *int64 `gorm:"column:memory_mb_total_change;comment:内存总数变化(MB)" json:"memory_mb_total_change"`
	GPUTotal            uint64 `gorm:"column:gpu_total;comment:GPU总数" json:"gpu_total"`
	GPUTotalChange      *int64 `gorm:"column:gpu_total_change;comment:GPU总数变化" json:"gpu_total_change"`
}

// RegionComputerResource 区域计算机资源统计
type RegionComputerResource struct {
	RegionName          string `gorm:"column:region_name;comment:区域名称" json:"region_name"`
	HostTotal           uint64 `gorm:"column:host_total;comment:主机总数" json:"host_total"`
	HostTotalChange     *int64 `gorm:"column:host_total_change;comment:主机总数变化" json:"host_total_change"`
	CPUTotal            uint64 `gorm:"column:cpu_total;comment:CPU总数" json:"cpu_total"`
	CPUTotalChange      *int64 `gorm:"column:cpu_total_change;comment:CPU总数变化" json:"cpu_total_change"`
	MemoryMBTotal       uint64 `gorm:"column:memory_mb_total;comment:内存总数(MB)" json:"memory_mb_total"`
	MemoryMBTotalChange *int64 `gorm:"column:memory_mb_total_change;comment:内存总数变化(MB)" json:"memory_mb_total_change"`
	GPUTotal            uint64 `gorm:"column:gpu_total;comment:GPU总数" json:"gpu_total"`
	GPUTotalChange      *int64 `gorm:"column:gpu_total_change;comment:GPU总数变化" json:"gpu_total_change"`
}

// ComputerResource 计算机资源统计
type ComputerResource struct {
	HostTotal           uint64                   `gorm:"column:host_total;comment:主机总数" json:"host_total"`
	HostTotalChange     *int64                   `gorm:"column:host_total_change;comment:主机总数变化" json:"host_total_change"`
	CPUTotal            uint64                   `gorm:"column:cpu_total;comment:CPU总数" json:"cpu_total"`
	CPUTotalChange      *int64                   `gorm:"column:cpu_total_change;comment:CPU总数变化" json:"cpu_total_change"`
	MemoryMBTotal       uint64                   `gorm:"column:memory_mb_total;comment:内存总数(MB)" json:"memory_mb_total"`
	MemoryMBTotalChange *int64                   `gorm:"column:memory_mb_total_change;comment:内存总数变化(MB)" json:"memory_mb_total_change"`
	GPUTotal            uint64                   `gorm:"column:gpu_total;comment:GPU总数" json:"gpu_total"`
	GPUTotalChange      *int64                   `gorm:"column:gpu_total_change;comment:GPU总数变化" json:"gpu_total_change"`
	DLICUTotal          uint64                   `gorm:"column:dli_cu_total;comment:Dli 总数" json:"dli_cu_total"`
	DLICUTotalChange    *int64                   `gorm:"column:dli_cu_total_change;comment:Dli 总数变化" json:"dli_cu_total_change"`
	CloudResources      []CloudComputerResource  `gorm:"-" json:"cloud_resource" comment:"云资源统计"`
	RegionResources     []RegionComputerResource `gorm:"-" json:"region_resource" comment:"区域资源统计"`
}

type DataResource struct {
	MySQLStorageTotal            int64  `gorm:"column:mysql_storage_total;comment:MYSQL 总存储大小" json:"mysql_storage_total"`
	MySQLStorageTotalChange      *int64 `gorm:"column:mysql_storage_total_change;comment:MYSQL 存储变化" json:"mysql_storage_total_change"`
	OSSStorageTotal              int64  `gorm:"column:oss_storage_total;comment:OSS 总存储大小" json:"oss_storage_total"`
	OSSStorageTotalChange        *int64 `gorm:"column:oss_storage_total_change;comment:OSS 存储变化" json:"oss_storage_total_change"`
	OBSStorageTotal              int64  `gorm:"column:obs_storage_total;comment:OBS 总存储大小" json:"obs_storage_total"`
	OBSStorageTotalChange        *int64 `gorm:"column:obs_storage_total_change;comment:OBS 存储变化" json:"obs_storage_total_change"`
	SFSStorageTotal              int64  `gorm:"column:sfs_storage_total;comment:SFS 总存储大小" json:"sfs_storage_total"`
	SFSStorageTotalChange        *int64 `gorm:"column:sfs_storage_total_change;comment:SFS 存储变化" json:"sfs_storage_total_change"`
	NASStorageTotal              int64  `gorm:"column:nas_storage_total;comment:NAS 总存储大小" json:"nas_storage_total"`
	NASStorageTotalChange        *int64 `gorm:"column:nas_storage_total_change;comment:NAS 存储变化" json:"nas_storage_total_change"`
	MongodbStorageTotal          int64  `gorm:"column:mongodb_storage_total;comment:Mongodb 总存储大小" json:"mongodb_storage_total"`
	MongodbStorageTotalChange    *int64 `gorm:"column:mongodb_storage_total_change;comment:Mongodb 存储变化" json:"mongodb_storage_total_change"`
	StarrocksStorageTotal        int64  `gorm:"column:starrocks_storage_total;comment:Starrocks 总存储大小" json:"starrocks_storage_total"`
	StarrocksStorageTotalChange  *int64 `gorm:"column:starrocks_storage_total_change;comment:Starrocks 存储变化" json:"starrocks_storage_total_change"`
	ClickhouseStorageTotal       int64  `gorm:"column:clickhouse_storage_total;comment:Clickhouse 总存储大小" json:"clickhouse_storage_total"`
	ClickhouseStorageTotalChange *int64 `gorm:"column:clickhouse_storage_total_change;comment:Clickhouse 存储变化" json:"clickhouse_storage_total_change"`
	TidbStorageTotal             int64  `gorm:"column:tidb_storage_total;comment:Tidb 总存储大小" json:"tidb_storage_total"`
	TidbStorageTotalChange       *int64 `gorm:"column:tidb_storage_total_change;comment:Tidb 存储变化" json:"tidb_storage_total_change"`
	DliStorageTotal              int64  `gorm:"column:dli_storage_total;comment:Dli 总存储大小" json:"dli_storage_total"`
	DliStorageTotalChange        *int64 `gorm:"column:dli_storage_total_change;comment:Dli 存储变化" json:"dli_storage_total_change"`
}

// 可优化资源
type OptimizableAsset struct {
	AMDPercent        float64  `gorm:"column:amd_percent;comment:AMD百分比" json:"amd_percent"`
	AMDPercentChange  *float64 `gorm:"column:amd_percent_change;comment:AMD百分比变化" json:"amd_percent_change"`
	CPUUsage          float64  `gorm:"column:cpu_usage;comment:CPU使用率" json:"cpu_usage"`
	CPUUsageChange    *float64 `gorm:"column:cpu_usage_change;comment:CPU使用率变化" json:"cpu_usage_change"`
	MemoryUsage       float64  `gorm:"column:memory_usage;comment:内存使用率" json:"memory_usage"`
	MemoryUsageChange *float64 `gorm:"column:memory_usage_change;comment:内存使用率变化" json:"memory_usage_change"`
}

// DailyReport 每日资源统计报告
type DailyReport struct {
	ID                    uint           `gorm:"column:id;primaryKey;comment:主键id" json:"id"`
	StatDate              time.Time      `gorm:"column:stat_date;type:date;index;comment:统计日期" json:"stat_date"`
	ComputerResourceStats datatypes.JSON `gorm:"column:computer_resource_stats;type:JSON;comment:计算机资源统计" json:"computer_resource_stats"`
	DataResourceStats     datatypes.JSON `gorm:"column:data_resource_stats;type:JSON;comment:数据资源统计" json:"data_resource_stats" `
	OptimizableAssetStats datatypes.JSON `gorm:"column:optimizable_asset_stats;type:JSON;comment:可优化资源统计" json:"optimizable_asset_stats"`
	DomainBpsStats        datatypes.JSON `gorm:"column:domain_bps_stats;type:JSON;comment:域名带宽统计" json:"domain_bps_stats"`
}

func (DailyReport) TableName() string {
	return "statistic_daily_reports"
}

// 从数据库里面查询对应的对象并赋值到结构体中，转换成json存在数据库
func GenDailyReport(statDate time.Time) (err error) {
	statDate = now.With(statDate).BeginningOfDay()
	app.Log().Info("开始生成每日报告", "date", statDate.Format("2006-01-02"))

	// 获取前一天的日期
	prevDate := statDate.AddDate(0, 0, -1)
	app.Log().Info("计算变化量使用的前一天日期", "prev_date", prevDate.Format("2006-01-02"))

	// 0. 获取计算资源汇总
	var computerResource ComputerResource
	err = app.DB().Model(&statisticAsset.ComputerResource{}).
		Select("host_total, cpu_total, memory_mb_total , gpu_total, dli_cu_total").
		Where("stat_date = ?", statDate.Format("2006-01-02")).
		Take(&computerResource).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			app.Log().Warn("当天没有计算资源汇总数据，将使用默认值", "date", statDate.Format("2006-01-02"))
			// 使用默认值（全部为0）继续处理
		} else {
			app.Log().Error("获取计算资源汇总失败", "err", err)
			return
		}
	}

	// 0.1 获取前一天的计算资源汇总，计算变化量
	var prevComputerResource statisticAsset.ComputerResource
	err = app.DB().Model(&statisticAsset.ComputerResource{}).
		Where("stat_date = ?", prevDate.Format("2006-01-02")).
		Take(&prevComputerResource).Error
	if err == nil {
		// 前一天有数据，计算变化量
		hostTotalChange := int64(computerResource.HostTotal) - int64(prevComputerResource.HostTotal)
		cpuTotalChange := int64(computerResource.CPUTotal) - int64(prevComputerResource.CPUTotal)
		memoryMBTotalChange := int64(computerResource.MemoryMBTotal) - int64(prevComputerResource.MemoryMBTotal)
		gpuTotalChange := int64(computerResource.GPUTotal) - int64(prevComputerResource.GPUTotal)
		dliCUTotalChange := int64(computerResource.DLICUTotal) - int64(prevComputerResource.DLICUTotal)

		computerResource.HostTotalChange = &hostTotalChange
		computerResource.CPUTotalChange = &cpuTotalChange
		computerResource.MemoryMBTotalChange = &memoryMBTotalChange
		computerResource.GPUTotalChange = &gpuTotalChange
		computerResource.DLICUTotalChange = &dliCUTotalChange
	} else if errors.Is(err, gorm.ErrRecordNotFound) {
		// 前一天没有数据，变化量为nil
		app.Log().Info("前一天没有计算资源汇总数据，变化量设为nil", "prev_date", prevDate.Format("2006-01-02"))
	} else {
		app.Log().Error("获取前一天计算资源汇总失败", "err", err, "prev_date", prevDate.Format("2006-01-02"))
	}

	// 1. 获取云计算资源统计数据
	var cloudResources []CloudComputerResource
	err = app.DB().Model(&statisticAsset.CloudComputerResource{}).
		Select("cloud_type_name as cloud_type, host_total, cpu_total, memory_mb_total , gpu_total").
		Where("stat_date = ?", statDate.Format("2006-01-02")).
		Find(&cloudResources).Error
	if err != nil {
		app.Log().Error("获取云计算资源统计数据失败", "err", err)
		return
	}

	// 1.1 获取前一天的云计算资源统计数据，计算变化量
	var prevCloudResources []statisticAsset.CloudComputerResource
	err = app.DB().Model(&statisticAsset.CloudComputerResource{}).
		Where("stat_date = ?", prevDate.Format("2006-01-02")).
		Find(&prevCloudResources).Error
	if err == nil {
		// 前一天有数据，计算变化量
		// 创建一个map用于快速查找前一天的数据
		prevCloudMap := make(map[string]statisticAsset.CloudComputerResource)
		for _, prevCloud := range prevCloudResources {
			prevCloudMap[prevCloud.CloudTypeName] = prevCloud
		}

		// 计算每个云类型的变化量
		for i := range cloudResources {
			if prevCloud, ok := prevCloudMap[cloudResources[i].CloudType]; ok {
				// 找到对应的前一天数据，计算变化量
				hostTotalChange := int64(cloudResources[i].HostTotal) - int64(prevCloud.HostTotal)
				cpuTotalChange := int64(cloudResources[i].CPUTotal) - int64(prevCloud.CPUTotal)
				memoryMBTotalChange := int64(cloudResources[i].MemoryMBTotal) - int64(prevCloud.MemoryMBTotal)
				gpuTotalChange := int64(cloudResources[i].GPUTotal) - int64(prevCloud.GPUTotal)

				cloudResources[i].HostTotalChange = &hostTotalChange
				cloudResources[i].CPUTotalChange = &cpuTotalChange
				cloudResources[i].MemoryMBTotalChange = &memoryMBTotalChange
				cloudResources[i].GPUTotalChange = &gpuTotalChange
			}
		}
	} else if errors.Is(err, gorm.ErrRecordNotFound) {
		app.Log().Info("前一天没有云计算资源统计数据，变化量设为nil", "prev_date", prevDate.Format("2006-01-02"))
	} else {
		app.Log().Error("获取前一天云计算资源统计数据失败", "err", err, "prev_date", prevDate.Format("2006-01-02"))
	}

	// 2. 获取区域计算资源统计数据
	var regionResources []RegionComputerResource
	err = app.DB().Model(&statisticAsset.RegionComputerResource{}).
		Select("region_name, host_total, cpu_total, memory_mb_total, gpu_total").
		Where("stat_date = ?", statDate.Format("2006-01-02")).
		Find(&regionResources).Error
	if err != nil {
		app.Log().Error("获取区域计算资源统计数据失败", "err", err)
		return
	}

	// 2.1 获取前一天的区域计算资源统计数据，计算变化量
	var prevRegionResources []statisticAsset.RegionComputerResource
	err = app.DB().Model(&statisticAsset.RegionComputerResource{}).
		Where("stat_date = ?", prevDate.Format("2006-01-02")).
		Find(&prevRegionResources).Error
	if err == nil {
		// 前一天有数据，计算变化量
		// 创建一个map用于快速查找前一天的数据
		prevRegionMap := make(map[string]statisticAsset.RegionComputerResource)
		for _, prevRegion := range prevRegionResources {
			prevRegionMap[prevRegion.RegionName] = prevRegion
		}

		// 计算每个区域的变化量
		for i := range regionResources {
			if prevRegion, ok := prevRegionMap[regionResources[i].RegionName]; ok {
				// 找到对应的前一天数据，计算变化量
				hostTotalChange := int64(regionResources[i].HostTotal) - int64(prevRegion.HostTotal)
				cpuTotalChange := int64(regionResources[i].CPUTotal) - int64(prevRegion.CPUTotal)
				memoryMBTotalChange := int64(regionResources[i].MemoryMBTotal) - int64(prevRegion.MemoryMBTotal)
				gpuTotalChange := int64(regionResources[i].GPUTotal) - int64(prevRegion.GPUTotal)

				regionResources[i].HostTotalChange = &hostTotalChange
				regionResources[i].CPUTotalChange = &cpuTotalChange
				regionResources[i].MemoryMBTotalChange = &memoryMBTotalChange
				regionResources[i].GPUTotalChange = &gpuTotalChange
			}
		}
	} else if errors.Is(err, gorm.ErrRecordNotFound) {
		app.Log().Info("前一天没有区域计算资源统计数据，变化量设为nil", "prev_date", prevDate.Format("2006-01-02"))
	} else {
		app.Log().Error("获取前一天区域计算资源统计数据失败", "err", err, "prev_date", prevDate.Format("2006-01-02"))
	}

	// 3. 获取数据资源统计数据
	var dataResource DataResource
	err = app.DB().Model(&statisticAsset.DataResource{}).
		Where("stat_date = ?", statDate.Format("2006-01-02")).
		Select("mysql_storage_total, oss_storage_total, obs_storage_total, sfs_storage_total, nas_storage_total,  mongodb_storage_total, starrocks_storage_total, clickhouse_storage_total, tidb_storage_total, dli_storage_total").
		Take(&dataResource).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			app.Log().Warn("当天没有数据资源统计数据，将使用默认值", "date", statDate.Format("2006-01-02"))
			// 使用默认值（全部为0）继续处理
		} else {
			app.Log().Error("获取数据资源统计数据失败", "err", err)
			return
		}
	}

	// 3.1 获取前一天的数据资源统计数据，计算变化量
	var prevDataResource statisticAsset.DataResource
	err = app.DB().Model(&statisticAsset.DataResource{}).
		Where("stat_date = ?", prevDate.Format("2006-01-02")).
		Take(&prevDataResource).Error
	if err == nil {
		// 前一天有数据，计算变化量
		mysqlStorageTotalChange := dataResource.MySQLStorageTotal - prevDataResource.MySQLStorageTotal
		ossStorageTotalChange := dataResource.OSSStorageTotal - prevDataResource.OSSStorageTotal
		obsStorageTotalChange := dataResource.OBSStorageTotal - prevDataResource.OBSStorageTotal
		sfsStorageTotalChange := dataResource.SFSStorageTotal - prevDataResource.SFSStorageTotal
		nasStorageTotalChange := dataResource.NASStorageTotal - prevDataResource.NASStorageTotal
		mongodbStorageTotalChange := dataResource.MongodbStorageTotal - prevDataResource.MongodbStorageTotal
		starrocksStorageTotalChange := dataResource.StarrocksStorageTotal - prevDataResource.StarrocksStorageTotal
		clickhouseStorageTotalChange := dataResource.ClickhouseStorageTotal - prevDataResource.ClickhouseStorageTotal
		tidbStorageTotalChange := dataResource.TidbStorageTotal - prevDataResource.TidbStorageTotal
		dliStorageTotalChange := dataResource.DliStorageTotal - prevDataResource.DliStorageTotal

		dataResource.MySQLStorageTotalChange = &mysqlStorageTotalChange
		dataResource.OSSStorageTotalChange = &ossStorageTotalChange
		dataResource.OBSStorageTotalChange = &obsStorageTotalChange
		dataResource.SFSStorageTotalChange = &sfsStorageTotalChange
		dataResource.NASStorageTotalChange = &nasStorageTotalChange
		dataResource.MongodbStorageTotalChange = &mongodbStorageTotalChange
		dataResource.StarrocksStorageTotalChange = &starrocksStorageTotalChange
		dataResource.ClickhouseStorageTotalChange = &clickhouseStorageTotalChange
		dataResource.TidbStorageTotalChange = &tidbStorageTotalChange
		dataResource.DliStorageTotalChange = &dliStorageTotalChange
	} else if errors.Is(err, gorm.ErrRecordNotFound) {
		app.Log().Info("前一天没有数据资源统计数据，变化量设为nil", "prev_date", prevDate.Format("2006-01-02"))
	} else {
		app.Log().Error("获取前一天数据资源统计数据失败", "err", err, "prev_date", prevDate.Format("2006-01-02"))
	}

	// 4. 获取可优化资源统计数据
	var optimizableAsset OptimizableAsset
	err = app.DB().Model(&statisticAsset.OptimizableStat{}).
		Where("stat_date = ?", statDate.Format("2006-01-02")).
		Select("amd_percent, cpu_usage, memory_usage").
		Take(&optimizableAsset).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			app.Log().Warn("当天没有可优化资源统计数据，将使用默认值", "date", statDate.Format("2006-01-02"))
			// 使用默认值（全部为0）继续处理
		} else {
			app.Log().Error("获取可优化资源统计数据失败", "err", err)
			return
		}
	}

	// 4.1 获取前一天的可优化资源统计数据，计算变化量
	var prevOptimizableAsset statisticAsset.OptimizableStat
	err = app.DB().Model(&statisticAsset.OptimizableStat{}).
		Where("stat_date = ?", prevDate.Format("2006-01-02")).
		Take(&prevOptimizableAsset).Error
	if err == nil {
		// 前一天有数据，计算变化量
		amdPercentChange := optimizableAsset.AMDPercent - prevOptimizableAsset.AMDPercent
		cpuUsageChange := optimizableAsset.CPUUsage - prevOptimizableAsset.CPUUsage
		memoryUsageChange := optimizableAsset.MemoryUsage - prevOptimizableAsset.MemoryUsage

		optimizableAsset.AMDPercentChange = &amdPercentChange
		optimizableAsset.CPUUsageChange = &cpuUsageChange
		optimizableAsset.MemoryUsageChange = &memoryUsageChange
	} else if errors.Is(err, gorm.ErrRecordNotFound) {
		app.Log().Info("前一天没有可优化资源统计数据，变化量设为nil", "prev_date", prevDate.Format("2006-01-02"))
	} else {
		app.Log().Error("获取前一天可优化资源统计数据失败", "err", err, "prev_date", prevDate.Format("2006-01-02"))
	}

	// 5. 获取域名带宽统计数据
	var domainBpsStats []asset.CloudDDosDomainDailyBps
	err = app.DB().Model(&asset.CloudDDosDomainDailyBps{}).
		Where("stat_date = ?", statDate.Format("2006-01-02")).
		Find(&domainBpsStats).Error
	if err != nil {
		app.Log().Error("获取域名带宽统计数据失败", "err", err)
		return
	}

	// 6. 组合成DailyReport对象
	computerResource.RegionResources = regionResources
	computerResource.CloudResources = cloudResources

	byteComputerResource, err := json.Marshal(computerResource)
	if err != nil {
		app.Log().Error("序列化计算机资源统计数据失败", "err", err)
		return
	}
	byteDataResource, err := json.Marshal(dataResource)
	if err != nil {
		app.Log().Error("序列化数据资源统计数据失败", "err", err)
		return
	}
	byteOptimizableAsset, err := json.Marshal(optimizableAsset)
	if err != nil {
		app.Log().Error("序列化可优化资源统计数据失败", "err", err)
		return
	}
	byteDomainBpsStats, err := json.Marshal(domainBpsStats)
	if err != nil {
		app.Log().Error("序列化域名带宽统计数据失败", "err", err)
		return
	}
	dailyReport := DailyReport{
		StatDate:              statDate,
		ComputerResourceStats: byteComputerResource,
		DataResourceStats:     byteDataResource,
		OptimizableAssetStats: byteOptimizableAsset,
		DomainBpsStats:        byteDomainBpsStats,
	}

	// 7. 检查数据库中是否已存在相同日期的报告
	var existReport DailyReport
	err = app.DB().Where("stat_date = ?", statDate.Format("2006-01-02")).Take(&existReport).Error
	if err == nil {
		// 已存在，检查是否有变化再更新
		updateMap := map[string]any{}

		if !bytes.Equal(existReport.ComputerResourceStats, byteComputerResource) {
			updateMap["computer_resource_stats"] = dailyReport.ComputerResourceStats
		}
		if !bytes.Equal(existReport.DomainBpsStats, byteDomainBpsStats) {
			updateMap["domain_bps_stats"] = dailyReport.DomainBpsStats
		}
		if !bytes.Equal(existReport.DataResourceStats, byteDataResource) {
			updateMap["data_resource_stats"] = dailyReport.DataResourceStats
		}
		if !bytes.Equal(existReport.OptimizableAssetStats, byteOptimizableAsset) {
			updateMap["optimizable_asset_stats"] = dailyReport.OptimizableAssetStats
		}
		// 只有在有变化时才进行更新
		if len(updateMap) > 0 {
			err = app.DB().Model(&existReport).Updates(updateMap).Error
			if err != nil {
				app.Log().Error("更新每日报告失败", "err", err)
				return
			}
			app.Log().Info("更新每日报告成功", "date", statDate.Format("2006-01-02"), "updated_fields", len(updateMap))
		} else {
			app.Log().Info("每日报告无变化，无需更新", "date", statDate.Format("2006-01-02"))
		}
	} else if errors.Is(err, gorm.ErrRecordNotFound) {
		// 不存在，创建
		err = app.DB().Create(&dailyReport).Error
		if err != nil {
			app.Log().Error("创建每日报告失败", "err", err)
			return
		}
		app.Log().Info("创建每日报告成功", "date", statDate.Format("2006-01-02"))
	} else {
		// 其他错误
		app.Log().Error("查询每日报告失败", "err", err)
		return
	}

	return nil
}
