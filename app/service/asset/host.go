package asset

import (
	"bytes"
	"cmdb/app"
	"cmdb/app/service/setting"
	"cmdb/pkg/db"
	"cmdb/pkg/utils"
	"errors"
	"fmt"
	"net"
	"os"
	"strings"
	"time"

	"gorm.io/gorm"
)

var (
	ErrHostExist = errors.New("主机已存在")
)

type Host struct {
	ID              uint           `gorm:"column:id;primaryKey;comment:主键" json:"id"`
	SN              string         `gorm:"type:varchar(255);column:sn;index;comment:主机序列号/ECS的实例ID" json:"sn"`
	Name            string         `gorm:"column:name;type:varchar(255);index" json:"name"`
	IP              string         `gorm:"column:ip;type:varchar(255);index" json:"ip"`
	PublicIP        string         `gorm:"column:public_ip;type:varchar(255);index" json:"public_ip"`
	HostType        HostType       `gorm:"column:host_type;index;comment:主机类型" json:"host_type"`
	ChargeType      ChargeType     `gorm:"column:charge_type;index;comment:付费类型：0已付费，1包年包月，2按量付费" json:"charge_type"`
	Tags            []Tag          `gorm:"many2many:asset_host_tags;joinForeignKey:host_id;joinReferences:tag_id" json:"tags"`
	DatacenterID    uint           `gorm:"column:datacenter_id;index;comment:关联dc id" json:"datacenter_id"`
	AccountID       uint           `gorm:"column:account_id;index;comment:关联云账户id" json:"account_id"`
	Cabinet         string         `gorm:"column:cabinet;index;comment:机柜" json:"cabinet"`
	Position        string         `gorm:"type:varchar(255);column:position;comment:柜位/vpc_id" json:"position"`
	Remark          string         `gorm:"type:varchar(500);column:remark;comment:备注" json:"remark"`
	Status          HostStatus     `gorm:"column:status;index;comment:1运行中 2停止 3下架 其他未知" json:"status"`
	RemotePort      uint           `gorm:"column:remote_port;default:22;comment:远程连接端口" json:"remote_port"`
	PingMonitor     bool           `gorm:"column:ping_monitor;index;comment:监控状态" json:"ping_monitor"`
	N9eMonitor      bool           `gorm:"column:n9e_monitor;index;comment:N9E监控状态" json:"n9e_monitor"`
	N9eMonitorTime  *time.Time     `gorm:"column:n9e_monitor_time;index;comment:N9E监控时间" json:"n9e_monitor_time"`
	CPUThread       uint           `gorm:"column:cpu_thread;comment:CPU线程数" json:"cpu_thread"`
	IsAMD           bool           `gorm:"column:is_amd;default:false;comment:是否AMD" json:"is_amd"`
	GPUAmount       uint           `gorm:"column:gpu_amount;comment:GPU数量" json:"gpu_amount"`
	GPUSpec         string         `gorm:"type:varchar(255);column:gpu_spec;comment:GPU规格" json:"gpu_spec"`
	Memory          uint           `gorm:"column:memory;comment:单位MiB" json:"memory"`
	Model           string         `gorm:"type:varchar(255);column:model;comment:机型" json:"model"`
	ResourceGroupID string         `gorm:"column:resource_group_id;index;comment:关联资源组id" json:"resource_group_id"`
	OS              string         `gorm:"type:varchar(255);column:os;comment:操作系统版本" json:"os"`
	OSType          string         `gorm:"type:varchar(255);column:os_type;comment:操作系统类型：linux、windows等" json:"os_type"`
	ExpiredTime     *time.Time     `gorm:"column:expired_time;default:null;comment:过期时间" json:"expired_time"`
	SyncTime        *time.Time     `gorm:"column:sync_time;index;comment:同步时间" json:"sync_time"`
	CreatedAt       time.Time      `gorm:"column:created_at;index" json:"created_at"`
	UpdatedAt       time.Time      `gorm:"column:updated_at;index" json:"updated_at"`
	DeletedAt       gorm.DeletedAt `gorm:"column:deleted_at;index" json:"-"`
}

func (Host) TableName() string {
	return "asset_hosts"
}

func (h Host) Delete() (err error) {
	return app.DB().Delete(&h).Error
}

func (h Host) GetDatacenter() (dc Datacenter, err error) {
	err = app.DB().Where("id = ?", h.DatacenterID).Take(&dc).Error
	return
}

func (h Host) GetCloudAccount() (a CloudAccount, err error) {
	err = app.DB().Where("id = ?", h.AccountID).Take(&a).Error
	return
}

func GetHostByID(id int) (host Host, err error) {
	err = app.DB().Where("id = ?", id).Take(&host).Error
	return
}

type HostForm struct {
	SN           string     `json:"sn" binding:"max=255"`
	Name         string     `json:"name" binding:"required,max=255"`
	IP           string     `json:"ip" binding:"required,max=255"`
	PublicIP     string     `json:"public_ip" binding:"max=255"`
	HostType     HostType   `json:"host_type"`
	DatacenterID uint       `json:"datacenter_id"`
	Cabinet      string     `json:"cabinet" binding:"max=255"`
	Position     string     `json:"position" binding:"max=255"`
	Remark       string     `json:"remark" binding:"max=500"`
	Status       HostStatus `json:"status"`
	RemotePort   uint       `json:"remote_port"`
	PingMonitor  bool       `json:"ping_monitor"`
	CPUThread    uint       `json:"cpu_thread"`
	GPUAmount    uint       `json:"gpu_amount"`
	Memory       uint       `json:"memory"`
	Model        string     `json:"model" binding:"max=255"`
	OS           string     `json:"os" binding:"max=255"`
	OSType       string     `json:"os_type" binding:"max=255"`
}

func (form HostForm) Create() (err error) {
	err = app.DB().Where("datacenter_id = ? AND ip = ?", form.DatacenterID, form.IP).Take(&Host{}).Error
	if err == nil {
		return ErrHostExist
	}
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = app.DB().Create(&Host{
			SN:           form.SN,
			Name:         form.Name,
			IP:           form.IP,
			PublicIP:     form.PublicIP,
			HostType:     form.HostType,
			DatacenterID: form.DatacenterID,
			Cabinet:      form.Cabinet,
			Position:     form.Position,
			Remark:       form.Remark,
			Status:       form.Status,
			RemotePort:   form.RemotePort,
			PingMonitor:  form.PingMonitor,
			CPUThread:    form.CPUThread,
			GPUAmount:    form.GPUAmount,
			Memory:       form.Memory,
			Model:        form.Model,
			OS:           form.OS,
			OSType:       form.OSType,
		}).Error
		return
	}
	return
}

func (host Host) Update(form HostForm) (err error) {
	exist := Host{}
	err = app.DB().Where("datacenter_id = ? AND ip = ?", form.DatacenterID, form.IP).Take(&exist).Error
	if err == nil && exist.ID != host.ID {
		return ErrHostExist
	}
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	updateMap := map[string]any{}
	if form.SN != host.SN {
		updateMap["sn"] = form.SN
	}
	if form.IP != host.IP {
		updateMap["ip"] = form.IP
	}
	if form.PublicIP != host.PublicIP {
		updateMap["public_ip"] = form.PublicIP
	}
	if form.Name != host.Name {
		updateMap["name"] = form.Name
	}
	if form.HostType != host.HostType {
		updateMap["host_type"] = form.HostType
	}
	if form.DatacenterID != host.DatacenterID {
		updateMap["datacenter_id"] = form.DatacenterID
	}
	if form.PingMonitor != host.PingMonitor {
		updateMap["ping_monitor"] = form.PingMonitor
	}
	if form.CPUThread != host.CPUThread {
		updateMap["cpu"] = form.CPUThread
	}
	if form.Memory != host.Memory {
		updateMap["memory"] = form.Memory
	}
	if form.GPUAmount != host.GPUAmount {
		updateMap["gpu_amount"] = form.GPUAmount
	}
	if form.Model != host.Model {
		updateMap["model"] = form.Model
	}
	if form.OS != host.OS {
		updateMap["os"] = form.OS
	}
	if form.OSType != host.OSType {
		updateMap["os_type"] = form.OSType
	}
	if form.Cabinet != host.Cabinet {
		updateMap["cabinet"] = form.Cabinet
	}
	if form.Position != host.Position {
		updateMap["position"] = form.Position
	}
	if form.RemotePort != host.RemotePort {
		updateMap["remote_port"] = form.RemotePort
	}
	if form.Remark != host.Remark {
		updateMap["remark"] = form.Remark
	}
	if len(updateMap) > 0 {
		err = app.DB().Model(&host).Updates(updateMap).Error
	} else {
		err = nil
	}
	return
}

type GetHostsParams struct {
	Offset          int
	Limit           int
	IP              *string
	Keyword         *string
	TagIDs          []int
	AccountID       *int
	DatacenterID    *int
	HostType        *int
	Status          *int
	ResourceGroupID *string
	PingMonitor     *bool
	IsAMD           *bool
	N9eMonitor      *bool
}

func GetHosts(params GetHostsParams) (count int64, hosts []Host, err error) {
	dbop := app.DB()
	if params.IP != nil {
		// 如果包含,或者，就进行分割IP
		if strings.Contains(*params.IP, ",") || strings.Contains(*params.IP, "，") {
			var ips []string
			tmpIPs := strings.Split(*params.IP, ",")
			for _, ip := range tmpIPs {
				if strings.Contains(ip, "，") {
					tmpIPs = append(tmpIPs, strings.Split(ip, "，")...)
				} else {
					ips = append(ips, strings.Split(ip, "，")...)
				}
				ips = append(ips, strings.TrimSpace(ip))
			}
			// 去重
			ips = utils.UniqueStringSlice(ips)
			dbop = dbop.Where("ip IN (?) OR public_ip IN (?)", ips, ips)
		} else if net.ParseIP(*params.IP) == nil {
			dbop = db.MLike(dbop, *params.IP, "ip", "public_ip")
		} else {
			dbop = dbop.Where("ip = ? OR public_ip = ?", *params.IP, *params.IP)
		}
	}
	if params.Keyword != nil {
		dbop = db.MLike(dbop, *params.Keyword, "name", "remark", "sn", "ip", "public_ip")
	}
	if params.N9eMonitor != nil {
		dbop = dbop.Where("n9e_monitor = ?", *params.N9eMonitor)
	}
	if params.PingMonitor != nil {
		dbop = dbop.Where("ping_monitor = ?", *params.PingMonitor)
	}
	if params.IsAMD != nil {
		dbop = dbop.Where("is_amd = ?", *params.IsAMD)
	}
	if len(params.TagIDs) > 0 {
		dbop = dbop.Where("id IN (?)", app.DB().Table("asset_host_tags").Select("host_id").Where("tag_id IN (?)", params.TagIDs))
	}
	if params.AccountID != nil {
		dbop = dbop.Where("account_id = ?", *params.AccountID)
	}
	if params.HostType != nil {
		dbop = dbop.Where("host_type = ?", *params.HostType)
	}
	if params.ResourceGroupID != nil {
		dbop = dbop.Where("resource_group_id = ?", *params.ResourceGroupID)
	}
	if params.DatacenterID != nil {
		dbop = dbop.Where("datacenter_id = ?", *params.DatacenterID)
	}
	if params.Status != nil {
		if *params.Status == int(RecycleHostStatus) {
			err = dbop.Unscoped().Where("deleted_at IS NOT NULL").Model(&Host{}).Count(&count).Order("created_at DESC").Offset(params.Offset).Limit(params.Limit).Find(&hosts).Error
			if err != nil {
				return
			}
			for i := range hosts {
				hosts[i].Status = RecycleHostStatus
				hosts[i].ExpiredTime = &hosts[i].DeletedAt.Time
			}
			return
		}
		dbop = dbop.Where("status = ?", *params.Status)
	}
	err = dbop.Model(&Host{}).Count(&count).Order("created_at DESC").Offset(params.Offset).Limit(params.Limit).Find(&hosts).Error
	return
}

func GetAllHosts() (hosts []Host, err error) {
	err = app.DB().Order("ip").Find(&hosts).Error
	return
}

func (h Host) GetTags() (tags []Tag, err error) {
	err = app.DB().Model(&h).Association("Tags").Find(&tags)
	return
}

type TagHostsForm struct {
	TagIDs  []int  `json:"tag_ids"`
	HostIDs []int  `json:"host_ids"`
	OP      string `json:"op"`
}

func (form TagHostsForm) Do() (err error) {
	switch form.OP {
	case "append":
		tags := []Tag{}
		err = app.DB().Where("id IN (?)", form.TagIDs).Find(&tags).Error
		if err != nil {
			return
		}
		if len(tags) == 0 {
			err = errors.New("tag not found")
		}
		for i := range form.HostIDs {
			exist := Host{}
			err = app.DB().Where("id = ?", form.HostIDs[i]).Take(&exist).Error
			if err != nil {
				return
			}
			err = app.DB().Model(&exist).Association("Tags").Append(&tags)
			if err != nil {
				return
			}
		}
	case "delete":
		tags := []Tag{}
		err = app.DB().Where("id IN (?)", form.TagIDs).Find(&tags).Error
		if err != nil {
			return
		}
		if len(tags) == 0 {
			err = errors.New("tag not found")
		}
		for i := range form.HostIDs {
			exist := Host{}
			err = app.DB().Where("id = ?", form.HostIDs[i]).Take(&exist).Error
			if err != nil {
				return
			}
			err = app.DB().Model(&exist).Association("Tags").Delete(&tags)
			if err != nil {
				return
			}
		}
	case "replace":
		tags := []Tag{}
		err = app.DB().Where("id IN (?)", form.TagIDs).Find(&tags).Error
		if err != nil {
			return
		}
		if len(tags) == 0 {
			err = errors.New("tag not found")
		}
		for i := range form.HostIDs {
			exist := Host{}
			err = app.DB().Where("id = ?", form.HostIDs[i]).Take(&exist).Error
			if err != nil {
				return
			}
			err = app.DB().Model(&exist).Association("Tags").Replace(&tags)
			if err != nil {
				return
			}
		}
	case "clear":
		for i := range form.HostIDs {
			exist := Host{}
			err = app.DB().Where("id = ?", form.HostIDs[i]).Take(&exist).Error
			if err != nil {
				return
			}
			err = app.DB().Model(&exist).Association("Tags").Clear()
			if err != nil {
				return
			}
		}
	default:
		err = errors.New("invalid operation")
	}
	return
}

type BatSetMonitorHostForm struct {
	HostsIDs []int64 `json:"hosts_ids"`
	Monitor  bool    `json:"monitor"`
}

func (form BatSetMonitorHostForm) Do() (err error) {
	err = app.DB().Model(&Host{}).Where("id IN (?)", form.HostsIDs).Update("ping_monitor", form.Monitor).Error
	return
}

type OldHost struct {
	ID         int            `gorm:"column:id;primaryKey;comment:主键" json:"id"`
	MachineUse string         `grom:"column:name;comment:机器用途" json:"machine_use"`
	IP         string         `gorm:"column:ip;comment:IP" json:"ip"`
	PublicIP   string         `gorm:"column:public_ip;comment:公网IP" json:"public_ip"`
	DeletedAt  gorm.DeletedAt `gorm:"column:deleted_at;comment:删除时间" json:"-"`
}

func (OldHost) TableName() string {
	return "asset_hosts"
}

func GetAllOldHosts() (hosts []OldHost, err error) {
	err = app.DB().Select("id", "name AS machine_use", "ip", "public_ip").Order("ip").Find(&hosts).Error
	if err != nil {
		return
	}
	for i := range hosts {
		if !strings.HasPrefix(hosts[i].IP, "10.0.") &&
			!strings.HasPrefix(hosts[i].IP, "10.10.") &&
			!strings.HasPrefix(hosts[i].IP, "10.20.") &&
			!strings.HasPrefix(hosts[i].IP, "172.18.") &&
			!strings.HasPrefix(hosts[i].IP, "172.19.") {
			hosts[i].IP, hosts[i].PublicIP = hosts[i].PublicIP, hosts[i].IP
		}
	}
	return
}

func DumpHostToAnsibleInventory() (err error) {
	hosts, err := GetAllHosts()
	if err != nil {
		return
	}
	var innerData, publicData bytes.Buffer
	innerData.WriteString("[internal]\n")
	publicData.WriteString("[public]\n")
	for i := range hosts {
		innerData.WriteString(fmt.Sprintf("%s\n", hosts[i].IP))
		if hosts[i].PublicIP != "" {
			publicData.WriteString(fmt.Sprintf("%s\n", hosts[i].PublicIP))
		}
	}
	innerData.WriteString("\n")
	publicData.WriteString("\n")
	innerData.Write(publicData.Bytes())
	innerData.WriteString("\n")
	innerData.WriteString("## created_at: " + time.Now().Format("2006-01-02 15:04:05") + "\n")
	ansible, err := setting.GetAnsibleSetting()
	if err != nil {
		return
	}
	err = os.WriteFile(ansible.AnsibleInventory, innerData.Bytes(), 0644)
	if err != nil {
		return
	}
	return
}

func (host Host) GetDatacenterCode() (code string) {
	var datacenter Datacenter
	err := app.DB().Where("id = ?", host.DatacenterID).Take(&datacenter).Error
	if err != nil {
		return ""
	}
	return datacenter.Code
}

func (host Host) GetCloudURL() (url string) {
	switch host.HostType {
	case AliyunHostType:
		url = fmt.Sprintf("https://ecs.console.aliyun.com/server/%s/detail?regionId=%s", host.SN, host.GetDatacenterCode())
	case HuaweiHostType:
		url = fmt.Sprintf("https://console.huaweicloud.com/ecm/?region=%s&locale=zh-cn#/ecs/manager/vmList/vmDetail/basicinfo?instanceId=%s", host.GetDatacenterCode(), host.SN)
	}
	return
}

func GetHostByIP(ip string) (host Host, err error) {
	err = app.DB().Where("ip = ? OR public_ip = ?", ip, ip).Take(&host).Error
	if err == nil {
		return
	}
	if !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	err = app.DB().Unscoped().Where("ip = ? OR public_ip = ?", ip, ip).Take(&host).Error
	if err != nil {
		return
	}
	if host.ID > 0 {
		host.Name += " (已回收)"
	}
	return
}
