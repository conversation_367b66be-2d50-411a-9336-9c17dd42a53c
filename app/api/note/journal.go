package note

import (
	"cmdb/app"
	"cmdb/app/service/audit"
	"cmdb/app/service/auth"
	"cmdb/app/service/note"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// 分页获取事件列表api
func GetJournals(c *gin.Context) {
	var keyword, eventType, project, tag, regionType *string
	if val := strings.TrimSpace(c.Query("keyword")); val != "" {
		keyword = &val
	}
	if val := strings.TrimSpace(c.Query("event_type")); val != "" {
		eventType = &val
	}
	if val := strings.TrimSpace(c.Query("project")); val != "" {
		project = &val
	}
	if val := strings.TrimSpace(c.Query("tag")); val != "" {
		tag = &val
	}
	if val := strings.TrimSpace(c.Query("region_type")); val != "" {
		regionType = &val
	}
	page, _ := strconv.Atoi(c.<PERSON>("page", "1"))
	limit, _ := strconv.Atoi(c.<PERSON>("limit", "10"))
	offset := (page - 1) * limit
	count, data, err := note.GetJournals(offset, limit, keyword, eventType, project, tag, regionType)
	if err != nil {
		app.Log().Error("获取故障记录列表失败 ", "err", err)
		app.FailedResponseMsg(c, "获取列表数据失败")
		return
	}
	app.SuccessResponseDataCount(c, data, count)
}

// 添加事件
func AddJournal(c *gin.Context) {
	var form note.JournalForm
	err := c.ShouldBindJSON(&form)
	if err != nil {
		// 验证表单是否合法
		app.Log().Debug("添加事件时，表单字段非法", "err", err)
		app.FailedResponseMsg(c, "字段非法")
		return
	}
	if form.StartTime.After(*form.EndTime) {
		app.FailedResponseMsg(c, "开始时间大于结束时间")
		return
	}
	// 从会话中获取用户信息
	loginUser, err := auth.GetLoginUser(c)
	if err != nil {
		app.FailedResponseMsg(c, "获取用户信息失败")
		return
	}
	form.CreatedBy = loginUser.Name
	err = form.Create()
	if err != nil {
		app.Log().Error("添加事件时，操作数据库失败", "err", err)
		app.FailedResponseMsg(c, "添加失败")
		return
	}
	// 审计日志
	audit.LogAPIOP(c, "运维事件", "添加事件："+form.Title)
	app.SuccessResponseMsg(c, "添加成功")
}

// EditJournal 编辑事件
func EditJournal(c *gin.Context) {
	var form note.JournalForm
	err := c.ShouldBindJSON(&form)
	if err != nil {
		// 验证表单是否合法
		app.Log().Debug("更新事件时，表单字段非法", "err", err)
		app.FailedResponseMsg(c, "字段非法")
		return
	}
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		// 参数判断
		app.Log().Debug("更新事件时，存在非法参数", "err", err)
		app.FailedResponseMsg(c, "参数不合法")
		return
	}
	journal, err := note.GetJournalByID(id)
	if err != nil {
		// 判断记录是否存在
		app.FailedResponseMsg(c, "事件不存在")
		return
	}
	// 传递对象和表单进行更新
	err = journal.Update(&form)
	if err != nil {
		// 判断是否更新成功
		app.Log().Error("更新事件时，操作数据库失败", "err", err)
		app.FailedResponseMsg(c, "更新失败")
		return
	}
	// 审计日志
	audit.LogAPIOP(c, "运维事件", "编辑事件："+form.Title)
	app.SuccessResponseMsg(c, "更新成功")
}

// DeleteJournal 删除事件
func DeleteJournal(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		// 参数判断
		app.Log().Debug("更新事件时，存在非法参数", "err", err)
		app.FailedResponseMsg(c, "参数不合法")
		return
	}
	journal, err := note.GetJournalByID(id)
	if err != nil {
		msg := "获取事件记录失败"
		if err == gorm.ErrRecordNotFound {
			msg = "事件不存在"
		} else {
			app.Log().Error("删除事件时，获取事件记录失败", "err", err)
		}
		app.FailedResponseMsg(c, msg)
		return
	}
	err = journal.Delete()
	if err != nil {
		app.Log().Error("删除事件失败", "err", err)
		app.FailedResponseMsg(c, "删除失败")
		return
	}
	// 删除日志
	audit.LogAPIOP(c, "运维事件", "删除事件："+journal.Title)
	app.SuccessResponseMsg(c, "删除成功")
}

// 获取单个事件对象
func GetJournal(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.FailedResponseMsg(c, "非法参数")
		return
	}
	journal, err := note.GetJournalByID(id)
	if err != nil {
		msg := "获取事件失败"
		if err == gorm.ErrRecordNotFound {
			msg = "事件不存在"
		} else {
			app.Log().Error("获取日志记录失败", "err", err)
		}
		app.FailedResponseMsg(c, msg)
		return
	}
	app.SuccessResponseData(c, journal)
}
