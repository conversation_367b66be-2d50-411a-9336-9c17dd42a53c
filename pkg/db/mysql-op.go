package db

import (
	"fmt"
	"strings"

	"gorm.io/gorm"
)

// mysqlTranslateSpecialCharacters 转译特殊字符
func mysqlTranslateSpecialCharacters(column string) string {
	column = strings.ReplaceAll(column, "\\", "\\\\")
	column = strings.ReplaceAll(column, "'", "\\'")
	column = strings.ReplaceAll(column, "%", "\\%")
	column = strings.ReplaceAll(column, "_", "\\_")
	column = strings.ReplaceAll(column, ";", "\\;")
	return column
}

// MTSC 转译特殊字符简写
func MTSC(column string) string {
	return mysqlTranslateSpecialCharacters(column)
}

// MLike MySQL like 模糊查询
func MLike(op *gorm.DB, keyword string, column string, columns ...string) *gorm.DB {
	keywords := []interface{}{MTSC(keyword)}
	sqls := []string{
		fmt.Sprintf("%s LIKE CONCAT('%%',?,'%%') ", column),
	}
	for i := range columns {
		sqls = append(sqls, fmt.Sprintf("%s LIKE CONCAT('%%',?,'%%') ", columns[i]))
		keywords = append(keywords, MTSC(keyword))
	}
	op = op.Where(strings.Join(sqls, " OR "), keywords...)
	return op
}

// MPrefixLike MySQL like 前匹配模糊查询
func MPrefixLike(op *gorm.DB, keyword string, column string, columns ...string) *gorm.DB {
	keywords := []interface{}{MTSC(keyword)}
	sqls := []string{
		fmt.Sprintf("%s LIKE CONCAT(?,'%%') ", column),
	}
	for i := range columns {
		sqls = append(sqls, fmt.Sprintf("%s LIKE CONCAT(?,'%%') ", columns[i]))
		keywords = append(keywords, MTSC(keyword))
	}
	op = op.Where(strings.Join(sqls, " OR "), keywords...)
	return op
}
