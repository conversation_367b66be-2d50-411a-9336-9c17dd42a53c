package statistics

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/asset/cloud/aliyun"
	"cmdb/app/service/asset/cloud/hwcloud"
	"cmdb/app/service/database/mysql"
	"cmdb/app/service/monitor"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type DataAsset struct {
	ID                           uint   `gorm:"column:id;primaryKey;comment:主键id" json:"id"`
	Month                        string `gorm:"column:month;index;comment:统计月份" json:"month"`
	MySQLStorageTotal            int64  `gorm:"column:mysql_storage_total;comment:MYSQL 总存储大小" json:"mysql_storage_total"`
	MySQLStorageTotalChange      int64  `gorm:"column:mysql_storage_total_change;comment:MYSQL 存储变化大小" json:"mysql_storage_total_change"`
	OSSStorageTotal              int64  `gorm:"column:oss_storage_total;comment:OSS 总存储大小" json:"oss_storage_total"`
	OSSStorageTotalChange        int64  `gorm:"column:oss_storage_total_change;comment:OSS 存储变化大小" json:"oss_storage_total_change"`
	NASStorageTotal              int64  `gorm:"column:nas_storage_total;comment:NAS 总存储大小" json:"nas_storage_total"`
	NASStorageTotalChange        int64  `gorm:"column:nas_storage_total_change;comment:NAS 存储变化大小" json:"nas_storage_total_change"`
	MongodbStorageTotal          int64  `gorm:"column:mongodb_storage_total;comment:Mongodb 总存储大小" json:"mongodb_storage_total"`
	MongodbStorageTotalChange    int64  `gorm:"column:mongodb_storage_total_change;comment:Mongodb 存储变化大小" json:"mongodb_storage_total_change"`
	StarrocksStorageTotal        int64  `gorm:"column:starrocks_storage_total;comment:Starrocks 总存储大小" json:"starrocks_storage_total"`
	StarrocksStorageTotalChange  int64  `gorm:"column:starrocks_storage_total_change;comment:Starrocks 存储变化大小" json:"starrocks_storage_total_change"`
	ClickhouseStorageTotal       int64  `gorm:"column:clickhouse_storage_total;comment:Clickhouse 总存储大小" json:"clickhouse_storage_total"`
	ClickhouseStorageTotalChange int64  `gorm:"column:clickhouse_storage_total_change;comment:Clickhouse 存储变化大小" json:"clickhouse_storage_total_change"`
	TidbStorageTotal             int64  `gorm:"column:tidb_storage_total;comment:Tidb 总存储大小" json:"tidb_storage_total"`
	TidbStorageTotalChange       int64  `gorm:"column:tidb_storage_total_change;comment:Tidb 存储变化大小" json:"tidb_storage_total_change"`
	DliStorageTotal              int64  `gorm:"column:dli_storage_total;comment:Dli 总存储大小" json:"dli_storage_total"`
	DliStorageTotalChange        int64  `gorm:"column:dli_storage_total_change;comment:Dli 存储变化大小" json:"dli_storage_total_change"`
}

func (DataAsset) TableName() string {
	return "statistics_data_assets"
}

func GetDataAssetMetrics(start, end string, dataType string) (data []MetricsItem, err error) {
	if dataType != "mysql_storage_total" && dataType != "oss_storage_total" && dataType != "nas_storage_total" && dataType != "mongodb_storage_total" && dataType != "starrocks_storage_total" && dataType != "clickhouse_storage_total" && dataType != "tidb_storage_total" && dataType != "dli_storage_total" {
		err = errors.New("不支持的数据类型")
		return
	}
	sql := fmt.Sprintf("select %s AS metric, month from statistics_data_assets where month >= ? and month <= ?", dataType)
	err = app.DB().Raw(sql, start, end).Scan(&data).Error
	return
}

func (s *DataAsset) Compute() {
	monthDate, err := time.Parse("2006-01", s.Month)
	if err != nil {
		app.Log().Error("对比月份数据资产指标，解析月份时间失败", "err", err)
		return
	}
	lastMonthDate := monthDate.AddDate(0, -1, 0).Format("2006-01")
	var exist DataAsset
	err = app.DB().Model(&DataAsset{}).Where("month = ? ", lastMonthDate).Take(&exist).Error
	if err != nil {
		app.Log().Error("对比月份数据资产指标，查询上月数据失败", "err", err)
		return
	}
	s.MySQLStorageTotalChange = s.MySQLStorageTotal - exist.MySQLStorageTotal
	s.OSSStorageTotalChange = s.OSSStorageTotal - exist.OSSStorageTotal
	s.NASStorageTotalChange = s.NASStorageTotal - exist.NASStorageTotal
	s.MongodbStorageTotalChange = s.MongodbStorageTotal - exist.MongodbStorageTotal
	s.StarrocksStorageTotalChange = s.StarrocksStorageTotal - exist.StarrocksStorageTotal
	s.ClickhouseStorageTotalChange = s.ClickhouseStorageTotal - exist.ClickhouseStorageTotal
	s.TidbStorageTotalChange = s.TidbStorageTotal - exist.TidbStorageTotal
	s.DliStorageTotalChange = s.DliStorageTotal - exist.DliStorageTotal
}

func statMongoDBStorage() (storageTotal int64, err error) {
	mongodbTag, err := asset.GetTagByNameValue("db", "mongodb")
	if err != nil {
		return
	}
	hosts, err := mongodbTag.GetHosts()
	if err != nil {
		return
	}
	ips := []string{}
	for _, host := range hosts {
		ips = append(ips, host.IP)
	}
	storageTotal, err = monitor.StatsHostDiskDataXUsage(ips...)
	return
}

func statStarrocksStorage() (storageTotal int64, err error) {
	starrocksTag, err := asset.GetTagByNameValue("db", "starrocks")
	if err != nil {
		return
	}
	hosts, err := starrocksTag.GetHosts()
	if err != nil {
		return
	}
	ips := []string{}
	for _, host := range hosts {
		ips = append(ips, host.IP)
	}
	storageTotal, err = monitor.StatsHostDiskDataXUsage(ips...)
	return
}

func statClickhouseStorage() (storageTotal int64, err error) {
	clickhouseTag, err := asset.GetTagByNameValue("db", "clickhouse")
	if err != nil {
		return
	}
	hosts, err := clickhouseTag.GetHosts()
	if err != nil {
		return
	}
	ips := []string{}
	for _, host := range hosts {
		ips = append(ips, host.IP)
	}
	storageTotal, err = monitor.StatsHostDiskDataXUsage(ips...)
	return
}

type DatasourceStatistic struct {
	DataSize       float64 `json:"data_size"`
	DataSourceName string  `json:"data_source_name"`
}
type DatasourceStatistics struct {
	DatasourceStatistics []DatasourceStatistic `json:"datasource_statistics"`
	Count                int                   `json:"count"`
}

type DILStat struct {
	YMD               string         `gorm:"column:ymd;comment:年月日" json:"ymd"`
	StatisticJsonData datatypes.JSON `gorm:"column:statistic_json_data;comment:统计数据" json:"statistic_json_data"`
	CreatedAt         time.Time      `gorm:"column:created_at;comment:创建时间" json:"created_at"`
	UpdatedAt         time.Time      `gorm:"column:updated_at;comment:更新时间" json:"updated_at"`
}

func (DILStat) TableName() string {
	return "coach_metadata.hw_dli_technical_assets_statistic"
}

func statDliStorage() (storageTotal float64, err error) {
	var instance mysql.Instance
	err = app.DB().Where("name = ?", "柚先森").Take(&instance).Error
	if err != nil {
		return
	}
	var stat DILStat
	dbop, err := instance.NewConnect()
	if err != nil {
		return
	}
	app.Log().Info("数据库连接成功")
	defer func() {
		d, _ := dbop.DB()
		if d != nil {
			d.Close()
		}
	}()
	err = dbop.Model(&DILStat{}).Order("ymd DESC").Take(&stat).Error
	if err != nil {
		return
	}
	app.Log().Info("获取柚先森DLI存储容量成功")
	var stats DatasourceStatistics
	err = json.Unmarshal(stat.StatisticJsonData, &stats)
	if err != nil {
		app.Log().Error("获取柚先森DLI存储容量时，解析统计数据异常", "err", err.Error())
		return
	}
	app.Log().Info("获取柚先森DLI存储容量成功", "stats", stats)
	for i := range stats.DatasourceStatistics {
		storageTotal += stats.DatasourceStatistics[i].DataSize
	}
	return
}

func statDataAssets(month string) (err error) {
	nasStorageTotal, err := aliyun.StatsAllAccountsNasStorage()
	if err != nil {
		app.Log().Error("统计nas存储容量时，获取阿里云NAS存储容量异常", "err", err.Error())
	}
	ossStorageTotal, err := aliyun.StatsAllAccountsOssStorage()
	if err != nil {
		app.Log().Error("统计oss存储容量时，获取阿里云OSS存储容量异常", "err", err.Error())
	}
	sfsStorageTotal, err := hwcloud.StatAllAccountSFSStorages()
	if err != nil {
		app.Log().Error("统计sfs存储容量时，获取阿里云SFS存储容量异常", "err", err.Error())
	}
	obsStorageTotal, err := hwcloud.StatAllAccountsOBSStorage()
	if err != nil {
		app.Log().Error("统计obs存储容量时，获取阿里云OBS存储容量异常", "err", err.Error())
	}
	mysqlInstanceStorage, err := mysql.StatInstanceTypeStorage(mysql.MySQLInstanceType)
	if err != nil {
		app.Log().Error("统计mysql实例存储容量时，获取mysql实例存储容量异常", "err", err.Error())
	}
	tidbStorageTotal, err := mysql.StatInstanceTypeStorage(mysql.TidbInstanceType)
	if err != nil {
		app.Log().Error("统计tidb存储容量时，获取tidb存储容量异常", "err", err.Error())
	}
	mongodbStorageTotal, err := statMongoDBStorage()
	if err != nil {
		app.Log().Error("统计mongodb存储容量时，获取mongodb存储容量异常", "err", err.Error())
	}
	starrocksStorageTotal, err := statStarrocksStorage()
	if err != nil {
		app.Log().Error("统计starrocks存储容量时，获取starrocks存储容量异常", "err", err.Error())
	}
	clickhouseStorageTotal, err := statClickhouseStorage()
	if err != nil {
		app.Log().Error("统计clickhouse存储容量时，获取clickhouse存储容量异常", "err", err.Error())
	}
	dliStorageTotal, err := statDliStorage()
	if err != nil {
		app.Log().Error("统计dli存储容量时，获取dli存储容量异常", "err", err.Error())
	}
	data := DataAsset{
		Month:                  month,
		MySQLStorageTotal:      mysqlInstanceStorage,
		OSSStorageTotal:        ossStorageTotal + obsStorageTotal,
		NASStorageTotal:        nasStorageTotal + sfsStorageTotal,
		MongodbStorageTotal:    mongodbStorageTotal,
		StarrocksStorageTotal:  starrocksStorageTotal,
		ClickhouseStorageTotal: clickhouseStorageTotal,
		TidbStorageTotal:       tidbStorageTotal,
		DliStorageTotal:        int64(dliStorageTotal),
	}
	exist := DataAsset{}
	err = app.DB().Where("month = ? ", month).Take(&exist).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	} else if errors.Is(err, gorm.ErrRecordNotFound) {
		err = app.DB().Create(&data).Error
		if err != nil {
			return
		}
	} else {
		updateMap := map[string]any{}
		if data.MySQLStorageTotal > 0 && data.MySQLStorageTotal != exist.MySQLStorageTotal {
			updateMap["mysql_storage_total"] = data.MySQLStorageTotal
		}
		if data.OSSStorageTotal > 0 && data.OSSStorageTotal != exist.OSSStorageTotal {
			updateMap["oss_storage_total"] = data.OSSStorageTotal
		}
		if data.NASStorageTotal > 0 && data.NASStorageTotal != exist.NASStorageTotal {
			updateMap["nas_storage_total"] = data.NASStorageTotal
		}
		if data.MongodbStorageTotal > 0 && data.MongodbStorageTotal != exist.MongodbStorageTotal {
			updateMap["mongodb_storage_total"] = data.MongodbStorageTotal
		}
		if data.StarrocksStorageTotal > 0 && data.StarrocksStorageTotal != exist.StarrocksStorageTotal {
			updateMap["starrocks_storage_total"] = data.StarrocksStorageTotal
		}
		if data.ClickhouseStorageTotal > 0 && data.ClickhouseStorageTotal != exist.ClickhouseStorageTotal {
			updateMap["clickhouse_storage_total"] = data.ClickhouseStorageTotal
		}
		if data.TidbStorageTotal > 0 && data.TidbStorageTotal != exist.TidbStorageTotal {
			updateMap["tidb_storage_total"] = data.TidbStorageTotal
		}
		if data.DliStorageTotal > 0 && data.DliStorageTotal != exist.DliStorageTotal {
			updateMap["dli_storage_total"] = data.DliStorageTotal
		}
		if len(updateMap) == 0 {
			return
		}
		err = app.DB().Model(&exist).Updates(updateMap).Error
		if err != nil {
			return
		}
	}
	return
}

func UpdateDataAsset(id int, form DataAsset) (err error) {
	data := DataAsset{}
	err = app.DB().Where("id = ?", id).Take(&data).Error
	if err != nil {
		return
	}
	form.Compute()
	updateMap := map[string]any{}
	if data.MySQLStorageTotal != form.MySQLStorageTotal {
		updateMap["mysql_storage_total"] = form.MySQLStorageTotal
	}
	if data.MySQLStorageTotalChange != form.MySQLStorageTotalChange {
		updateMap["mysql_storage_total_change"] = form.MySQLStorageTotalChange
	}
	if data.OSSStorageTotal != form.OSSStorageTotal {
		updateMap["oss_storage_total"] = form.OSSStorageTotal
	}
	if data.OSSStorageTotalChange != form.OSSStorageTotalChange {
		updateMap["oss_storage_total_change"] = form.OSSStorageTotalChange
	}
	if data.NASStorageTotal != form.NASStorageTotal {
		updateMap["nas_storage_total"] = form.NASStorageTotal
	}
	if data.NASStorageTotalChange != form.NASStorageTotalChange {
		updateMap["nas_storage_total_change"] = form.NASStorageTotalChange
	}
	if data.MongodbStorageTotal != form.MongodbStorageTotal {
		updateMap["mongodb_storage_total"] = form.MongodbStorageTotal
	}
	if data.MongodbStorageTotalChange != form.MongodbStorageTotalChange {
		updateMap["mongodb_storage_total_change"] = form.MongodbStorageTotalChange
	}
	if data.StarrocksStorageTotal != form.StarrocksStorageTotal {
		updateMap["starrocks_storage_total"] = form.StarrocksStorageTotal
	}
	if data.StarrocksStorageTotalChange != form.StarrocksStorageTotalChange {
		updateMap["starrocks_storage_total_change"] = form.StarrocksStorageTotalChange
	}
	if data.ClickhouseStorageTotal != form.ClickhouseStorageTotal {
		updateMap["clickhouse_storage_total"] = form.ClickhouseStorageTotal
	}
	if data.ClickhouseStorageTotalChange != form.ClickhouseStorageTotalChange {
		updateMap["clickhouse_storage_total_change"] = form.ClickhouseStorageTotalChange
	}
	if data.TidbStorageTotal != form.TidbStorageTotal {
		updateMap["tidb_storage_total"] = form.TidbStorageTotal
	}
	if data.TidbStorageTotalChange != form.TidbStorageTotalChange {
		updateMap["tidb_storage_total_change"] = form.TidbStorageTotalChange
	}
	if data.DliStorageTotal != form.DliStorageTotal {
		updateMap["dli_storage_total"] = form.DliStorageTotal
	}
	if data.DliStorageTotalChange != form.DliStorageTotalChange {
		updateMap["dli_storage_total_change"] = form.DliStorageTotalChange
	}
	if len(updateMap) == 0 {
		return
	}
	err = app.DB().Model(&data).Updates(updateMap).Error
	if err != nil {
		return
	}
	return
}
