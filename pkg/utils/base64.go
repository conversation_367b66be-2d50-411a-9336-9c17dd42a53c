package utils

import (
	"encoding/base64"
)

// Base64Encode base64编码
func Base64Encode(plaintext string) string {
	if plaintext == "" {
		return ""
	}
	return base64.StdEncoding.EncodeToString([]byte(plaintext))
}

// Base64Decode base64解码
func Base64Decode(text string) (string, error) {
	if text == "" {
		return "", nil
	}
	plainByte, err := base64.StdEncoding.DecodeString(text)
	return string(plainByte), err
}
