package bill

import (
	"cmdb/app"
	"cmdb/app/service/statistic/bill"
	"time"

	"github.com/gin-gonic/gin"
)

// GetResourceGroupBills 获取指定账单周期内的资源组级别账单统计
// @Summary 获取指定账单周期内的资源组级别账单统计
// @Description 获取指定账单周期内的资源组级别账单统计
// @Tags 账单统计
// @Accept json
// @Produce json
// @Param month query string true "账单周期，格式为 2021-01"
// @Success 200 {object} app.ResponseData
// @Router /api/v1/statistic/bill/resource-groups [get]
func GetResourceGroupBills(c *gin.Context) {
	// 获取账单周期参数
	billCycle := c.Query("month")
	if billCycle == "" {
		// 如果未指定账单周期，默认使用当前月份
		billCycle = time.Now().Format("2006-01")
	}

	// 验证账单周期格式
	_, err := time.Parse("2006-01", billCycle)
	if err != nil {
		app.Log().Error("账单周期格式错误", "billCycle", billCycle, "err", err)
		app.FailedResponseMsg(c, "账单周期格式错误，正确格式为：YYYY-MM")
		return
	}

	// 调用服务层函数获取资源组级别账单统计
	data, err := bill.GetResourceGroupBills(billCycle)
	if err != nil {
		app.Log().Error("获取资源组级别账单统计失败", "billCycle", billCycle, "err", err)
		app.FailedResponseMsg(c, "获取资源组级别账单统计失败")
		return
	}

	// 返回成功响应
	app.SuccessResponseData(c, data)
}

// GetResourceGroupBillsByDateRange 获取指定时间范围内的资源组级别账单统计
// @Summary 获取指定时间范围内的资源组级别账单统计
// @Description 获取指定时间范围内的资源组级别账单统计
// @Tags 账单统计
// @Accept json
// @Produce json
// @Param start_time query string true "开始时间，格式为 2021-01"
// @Param end_time query string true "结束时间，格式为 2021-12"
// @Success 200 {object} app.ResponseData
// @Router /api/v1/statistic/bill/resource-groups/range [get]
func GetResourceGroupBillsByDateRange(c *gin.Context) {
	// 获取开始时间和结束时间参数
	startTime := c.Query("start_time")
	endTime := c.Query("end_time")

	// 如果未指定开始时间或结束时间，使用默认值
	now := time.Now()
	if endTime == "" {
		endTime = now.Format("2006-01")
	}
	if startTime == "" {
		// 默认查询最近6个月
		startTime = now.AddDate(0, -6, 0).Format("2006-01")
	}

	// 验证时间格式
	_, err1 := time.Parse("2006-01", startTime)
	_, err2 := time.Parse("2006-01", endTime)
	if err1 != nil || err2 != nil {
		app.Log().Error("时间格式错误", "startTime", startTime, "endTime", endTime)
		app.FailedResponseMsg(c, "时间格式错误，正确格式为：YYYY-MM")
		return
	}

	// 调用服务层函数获取资源组级别账单统计
	data, err := bill.GetResourceGroupBillsByDateRange(startTime, endTime)
	if err != nil {
		app.Log().Error("获取资源组级别账单统计失败", "startTime", startTime, "endTime", endTime, "err", err)
		app.FailedResponseMsg(c, "获取资源组级别账单统计失败")
		return
	}

	// 返回成功响应
	app.SuccessResponseData(c, data)
}

// GetResourceGroupBillsByCloudType 获取指定账单周期内资源组账单汇总统计
// @Summary 获取指定账单周期内资源组账单汇总统计
// @Description 获取指定账单周期内资源组账单汇总统计（包含资源组总数和总金额）
// @Tags 账单统计
// @Accept json
// @Produce json
// @Param month query string true "账单周期，格式为 2021-01"
// @Success 200 {object} app.ResponseData
// @Router /api/v1/statistic/bill/resource-groups/cloud-type [get]
func GetResourceGroupBillsByCloudType(c *gin.Context) {
	// 获取账单周期参数
	billCycle := c.Query("month")
	if billCycle == "" {
		// 如果未指定账单周期，默认使用当前月份
		billCycle = time.Now().Format("2006-01")
	}

	// 验证账单周期格式
	_, err := time.Parse("2006-01", billCycle)
	if err != nil {
		app.Log().Error("账单周期格式错误", "billCycle", billCycle, "err", err)
		app.FailedResponseMsg(c, "账单周期格式错误，正确格式为：YYYY-MM")
		return
	}

	// 调用服务层函数获取资源组账单汇总统计
	data, err := bill.GetResourceGroupBillsByCloudType(billCycle)
	if err != nil {
		app.Log().Error("获取资源组账单汇总统计失败", "billCycle", billCycle, "err", err)
		app.FailedResponseMsg(c, "获取资源组账单汇总统计失败")
		return
	}

	// 返回成功响应
	app.SuccessResponseData(c, data)
}
