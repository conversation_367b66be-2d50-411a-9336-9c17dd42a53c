package statistics

import "cmdb/app"

func StataAssets(month string) (err error) {
	err = statComputerAssets(month)
	if err != nil {
		return
	}
	err = statDataAssets(month)
	if err != nil {
		return
	}
	err = statDomainBps(month)
	if err != nil {
		return
	}
	err = statOptimizableAssets(month)
	if err != nil {
		return
	}
	return
}

type AssetStat struct {
	ComputerAssets   ComputerAsset    `json:"computer_assets"`
	DataAssets       DataAsset        `json:"data_assets"`
	DomainBps        []DomainBps      `json:"domain_bps"`
	OptimizableAsset OptimizableAsset `json:"optimizable_asset"`
}

func GetMonthlyAssetStat(month string) (data AssetStat, err error) {
	data = AssetStat{}

	err = app.DB().Where("month = ?", month).Take(&data.ComputerAssets).Error
	if err != nil {
		return
	}
	err = app.DB().Where("month = ?", month).Find(&data.ComputerAssets.RegionStats).Error
	if err != nil {
		return
	}
	err = app.DB().Where("month = ?", month).Find(&data.ComputerAssets.CloudStats).Error
	if err != nil {
		return
	}
	err = app.DB().Where("month = ?", month).Take(&data.DataAssets).Error
	if err != nil {
		return
	}
	err = app.DB().Where("month = ?", month).Order("in_bps DESC").Find(&data.DomainBps).Error
	if err != nil {
		return
	}
	err = app.DB().Where("month = ?", month).Take(&data.OptimizableAsset).Error
	if err != nil {
		return
	}
	return
}
