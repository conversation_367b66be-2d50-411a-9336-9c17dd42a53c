package mysql

import (
	"cmdb/app"
	"cmdb/app/service/audit"
	"cmdb/app/service/database/mysql"
	"errors"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// 获取MySQL项目列表
func GetProjects(c *gin.Context) {
	// 获取分页参数
	page, _ := strconv.Atoi(c.<PERSON>("page", "1"))
	limit, _ := strconv.Atoi(c<PERSON>("limit", "10"))
	offset := (page - 1) * limit
	var keyword *string
	// 获取关键字参数
	if val := c.Query("keyword"); val != "" {
		keyword = &val
	}
	var env *int
	// 获取环境参数
	if val := c.Query("env"); val != "" {
		if envID, err1 := strconv.Atoi(val); err1 == nil {
			env = &envID
		}
	}
	// 调用数据库获取项目列表
	count, data, err := mysql.GetProjects(offset, limit, env, keyword)

	if err != nil {
		// 记录错误日志
		app.Log().Error("获取MySQL项目列表数据失败", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "获取MySQL项目列表数据失败")
		return
	}
	// 返回成功信息
	app.SuccessResponseDataCount(c, data, count)
}

// 创建MySQL项目
func CreateProject(c *gin.Context) {
	var form mysql.ProjectForm
	// 绑定JSON参数
	if err := c.ShouldBindJSON(&form); err != nil {
		// 记录错误日志
		app.Log().Debug("创建MySQL项目参数错误", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	// 调用数据库创建项目
	if err := form.Create(); err != nil {
		msg := "创建MySQL项目失败"
		// 判断错误类型
		if errors.Is(err, mysql.ErrProjectExist) {
			msg = err.Error()
		} else {
			// 记录错误日志
			app.Log().Error(msg, "err", err)
		}
		// 返回错误信息
		app.FailedResponseMsg(c, msg)
		return
	}
	// 记录操作日志
	audit.LogAPIOP(c, "资产管理", "创建MySQL项目:"+form.Name)
	// 返回成功信息
	app.SuccessResponseMsg(c, "创建MySQL项目成功")
}

// 更新MySQL项目
func UpdateProject(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	// 获取项目ID
	if err != nil {
		// 记录错误日志
		app.Log().Error("更新MySQL项目参数错误", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	var form mysql.ProjectForm
	// 绑定JSON参数
	if err := c.ShouldBindJSON(&form); err != nil {
		// 记录错误日志
		app.Log().Error("更新MySQL项目非法表单误", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "非法表单")
		return
	}
	// 调用数据库获取项目
	project, err := mysql.GetProjectByID(id)
	if err != nil {
		// 记录错误日志
		app.Log().Error("更新MySQL项目参数错误", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	// 调用数据库更新项目
	if err := project.Update(form); err != nil {
		msg := "更新MySQL项目失败"
		// 判断错误类型
		if errors.Is(err, mysql.ErrProjectExist) {
			msg = err.Error()
		} else {
			// 记录错误日志
			app.Log().Error(msg, "err", err)
		}
		// 返回错误信息
		app.FailedResponseMsg(c, msg)
		return
	}
	// 记录操作日志
	audit.LogAPIOP(c, "资产管理", "更新MySQL项目:"+form.Name)
	// 返回成功信息
	app.SuccessResponseMsg(c, "更新MySQL项目成功")
}

// 删除MySQL项目
func DeleteProject(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	// 获取项目ID
	if err != nil {
		// 记录错误日志
		app.Log().Debug("删除MySQL项目参数错误", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	// 调用数据库获取项目
	project, err := mysql.GetProjectByID(id)
	if err != nil {
		// 记录错误日志
		app.Log().Debug("删除MySQL项目参数错误", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	// 调用数据库删除项目
	if err := project.Delete(); err != nil {
		// 记录错误日志
		app.Log().Error("删除MySQL项目失败", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "删除MySQL项目失败")
		return
	}
	// 记录操作日志
	audit.LogAPIOP(c, "资产管理", "删除MySQL项目:"+project.Name)
	// 返回成功信息
	app.SuccessResponseMsg(c, "删除MySQL项目成功")
}

// 获取所有MySQL项目
func GetAllProjects(c *gin.Context) {
	// 调用数据库获取所有项目
	data, err := mysql.GetAllProjects()
	if err != nil {
		// 记录错误日志
		app.Log().Error("获取MySQL项目列表失败", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "获取MySQL项目列表失败")
		return
	}
	// 返回成功信息
	app.SuccessResponseData(c, data)
}

func SyncProjectsDBs(c *gin.Context) {
	// 创建一个done通道
	done := make(chan struct{}, 1)
	// 启动一个goroutine进行同步
	go func() {
		// 记录日志
		audit.LogAPIOP(c, "资产管理", "提交同步 MySQL 项目数据库")
		// 同步数据库
		if err := mysql.SyncProjectsDBs(); err != nil {
			app.FailedResponseMsg(c, "同步数据库失败")
			return
		}
		// 发送完成信号
		done <- struct{}{}
	}()
	ticker := time.NewTicker(500 * time.Millisecond)
	select {
	case <-done:
		app.SuccessResponseMsg(c, "同步数据库成功")
		break
	case <-ticker.C:
		app.SuccessResponseMsg(c, "任务提交成功,正在同步中，请稍后...")
		break
	}
}
