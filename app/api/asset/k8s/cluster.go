package k8s

import (
	"cmdb/app"
	"cmdb/app/service/asset/k8s"
	"cmdb/app/service/audit"
	"errors"
	"strconv"

	"github.com/gin-gonic/gin"
)

type ClusterRow struct {
	k8s.Cluster
	NodeTotal      int64 `json:"node_total"`
	WorkloadTotal  int64 `json:"workload_total"`
	PodTotal       int64 `json:"pod_total"`
	ContainerTotal int64 `json:"container_total"`
	NamespaceTotal int64 `json:"namespace_total"`
	ServiceTotal   int64 `json:"service_total"`
}

func GetClusters(c *gin.Context) {
	// 获取查询参数
	keyword := c.Query("keyword")
	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.<PERSON>fault<PERSON>y("limit", "10"))
	// 计算偏移量
	offset := (page - 1) * limit
	count, data, err := k8s.GetClusters(offset, limit, keyword)
	if err != nil {
		app.Log().Error("获取K8S集群列表失败", "err", err)
		app.FailedResponseMsg(c, "获取K8S集群列表失败")
		return
	}
	newData := []ClusterRow{}
	for i := range data {
		nodeTotal, _ := data[i].CountNode()
		workloadTotal, _ := data[i].CountWorkload()
		podTotal, _ := data[i].CountPod()
		namespaceTotal, _ := data[i].CountNamespace()
		serviceTotal, _ := data[i].CountService()
		containerTotal, _ := data[i].CountContainer()
		newData = append(newData, ClusterRow{data[i], nodeTotal, workloadTotal, podTotal, containerTotal, namespaceTotal, serviceTotal})
	}
	app.SuccessResponseDataCount(c, newData, count)
}

func AddCluster(c *gin.Context) {
	var form k8s.ClusterForm
	if err := c.ShouldBind(&form); err != nil {
		app.Log().Error("参数错误", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	if err := form.Create(); err != nil {
		msg := "添加失败"
		if errors.Is(err, k8s.ErrClusterExist) {
			msg = err.Error()
		} else {
			app.Log().Error("添加K8S集群失败", "err", err)
		}
		app.Log().Error("添加K8S集群失败", "err", err)
		app.FailedResponseMsg(c, msg)
		return
	}
	audit.LogAPIOP(c, "k8s集群管理", "添加K8S集群"+form.Name)
	app.SuccessResponseMsg(c, "添加成功")
}

func UpdateCluster(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.Log().Error("参数错误", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	var form k8s.ClusterForm
	if err := c.ShouldBindJSON(&form); err != nil {
		app.Log().Error("参数错误", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	cluster, err := k8s.GetClusterByID(id)
	if err != nil {
		app.Log().Error("获取K8S集群失败", "err", err)
		app.FailedResponseMsg(c, "获取失败")
		return
	}
	err = cluster.Update(form)
	if err != nil {
		app.Log().Error("更新K8S集群失败", "err", err)
		app.FailedResponseMsg(c, "更新失败")
		return
	}
	audit.LogAPIOP(c, "k8s集群管理", "更新K8S集群"+form.Name)
	app.SuccessResponseMsg(c, "更新成功")
}

func DeleteCluster(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.Log().Error("参数错误", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	cluster, err := k8s.GetClusterByID(id)
	if err != nil {
		app.Log().Error("获取K8S集群失败", "err", err)
		app.FailedResponseMsg(c, "获取失败")
		return
	}
	err = cluster.Delete()
	if err != nil {
		app.Log().Error("删除K8S集群失败", "err", err)
		app.FailedResponseMsg(c, "删除失败")
		return
	}
	audit.LogAPIOP(c, "k8s集群管理", "删除K8S集群"+cluster.Name)
	app.SuccessResponseMsg(c, "删除成功")
}

func GetAllClusters(c *gin.Context) {
	clusters, err := k8s.GetAllClusters()
	if err != nil {
		app.Log().Error("获取K8S集群失败", "err", err)
		app.FailedResponseMsg(c, "获取失败")
		return
	}
	app.SuccessResponseData(c, clusters)
}
