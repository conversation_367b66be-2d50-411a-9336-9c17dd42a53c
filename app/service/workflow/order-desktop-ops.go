package workflow

import (
	"cmdb/app"
	"cmdb/app/service/auth"
	"cmdb/pkg/utils/mytime"
	"errors"
	"time"
)

// 桌面运维管理工单 扩展表
type OrderDesktopOPS struct {
	ID      uint   `gorm:"column:id;primarykey;comment:id" json:"id"`
	SN      string `gorm:"type:varchar(128);unique;column:sn;comment:工单单号" json:"sn"`
	Content string `gorm:"type:text;column:content;comment:工单内容" json:"content"`
	Cubicle string `gorm:"type:varchar(255);column:cubicle;comment:工位" json:"cubicle"`
	Rating  uint   `gorm:"column:rating;comment:评分" json:"rating"`
}

// 设置表名
func (OrderDesktopOPS) TableName() string {
	return "workflow_order_desktop_ops"
}

type OrderDesktopOPSDetail struct {
	Order   `gorm:"-"`
	Content string `gorm:"type:text;column:content;comment:工单内容" json:"content"`
	Cubicle string `gorm:"type:varchar(255);column:cubicle;comment:工位" json:"cubicle"`
	Rating  uint   `gorm:"column:rating;comment:评分" json:"rating"`
}

// 设置表名
func (OrderDesktopOPSDetail) TableName() string {
	return "workflow_order_desktop_ops"
}

func GetOrderDesktopOPSDetail(o Order) (detail OrderDesktopOPSDetail, err error) {
	detail = OrderDesktopOPSDetail{}
	err = app.DB().Where("sn = ?", o.SN).Take(&detail).Error
	if err != nil {
		return
	}
	detail.Order = o
	return
}

type OrderDesktopOPSForm struct {
	Content                string     `json:"content"`
	Cubicle                string     `json:"cubicle"`
	PlanTime               *time.Time `json:"plan_time"`
	ServiceDurationSeconds uint64     `json:"service_duration_seconds"`
}

func (form OrderDesktopOPSForm) GenProcesses(sn string, a Applicant, admin *auth.User) (processes []Process, err error) {
	var flow Flow
	err = app.DB().Where("order_type = ?", DesktopOPSOrderType).Take(&flow).Error
	if err != nil {
		return
	}
	flow.Nodes, err = flow.GetFlowNodes()
	if err != nil {
		return
	}
	processes, err = flow.genProcesses(sn, a, admin)
	return
}

func (form OrderDesktopOPSForm) check() (err error) {
	return
}

func (form OrderDesktopOPSForm) Create(a Applicant, flow Flow, admin *auth.User) (err error) {
	err = form.check()
	if err != nil {
		return
	}
	sn := genSN("desktop-ops")
	processes, err := flow.genProcesses(sn, a, admin)
	if err != nil {
		return
	}
	status := ApprovingStatus
	var index uint
	if len(processes) == 0 {
		err = errors.New("生成流程失败")
		return
	} else if len(processes) <= 1 {
		// 如果流程没有配置就直接结束流程
		status = CompletedStatus
	} else {
		// 如果有流程就获取下一个节点的索引
		index = processes[1].ProcessIndex
		processes[1].Status = ApprovingProcessStatus
	}
	planTime := mytime.NextWorkingDay(time.Now())
	if form.PlanTime != nil && form.PlanTime.After(planTime) {
		planTime = *form.PlanTime
	}
	order := Order{
		Title:                  a.Name + " 申请桌面技术支持",
		OrderType:              DesktopOPSOrderType.Name(),
		ExtType:                DesktopOPSExtType,
		SN:                     sn,
		ApplicantID:            a.ID,
		Status:                 status,
		PlanTime:               &planTime,
		ProcessIndex:           index,
		ServiceDurationSeconds: form.ServiceDurationSeconds,
	}
	extOrder := OrderDesktopOPS{
		SN:      sn,
		Content: form.Content,
		Cubicle: form.Cubicle,
	}
	dbop := app.DB().Begin()
	err = dbop.Create(&order).Error
	if err != nil {
		dbop.Rollback()
		return
	}
	err = dbop.Create(&extOrder).Error
	if err != nil {
		dbop.Rollback()
		return
	}
	err = dbop.Create(&processes).Error
	if err != nil {
		dbop.Rollback()
		return
	}
	err = dbop.Commit().Error
	if err != nil {
		dbop.Rollback()
	}
	if admin != nil {
		go order.AutoCompleteOrder(*admin)
	} else {
		go order.notifyNextProcess()
	}
	return
}

func (form OrderDesktopOPSForm) Update(o Order) (err error) {
	extOrder := OrderDesktopOPS{}
	err = app.DB().Where("sn = ?", o.SN).Take(&extOrder).Error
	if err != nil {
		return
	}
	updateMap := map[string]any{}
	if form.Content != extOrder.Content {
		updateMap["content"] = form.Content
	}
	if form.Cubicle != extOrder.Cubicle {
		updateMap["cubicle"] = form.Cubicle
	}
	if len(updateMap) > 0 {
		err = app.DB().Model(&extOrder).Updates(updateMap).Error
	}
	return
}
