package aliyun

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/asset/cloud"
	"cmdb/pkg/utils"
	"errors"
	"time"

	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/requests"
	vpc "github.com/aliyun/alibaba-cloud-sdk-go/services/vpc"
	"gorm.io/gorm"
)

func (a Account) syncVSwitches() (result cloud.SyncResults, err error) {
	dcs, err := asset.GetDatacentersByCloudType(asset.AliyunCloudType)
	if err != nil {
		result = []cloud.SyncResult{
			{
				Key:    a.Name + " 获取数据中心失败",
				Result: err.Error(),
			},
		}
		return
	}
	for _, dc := range dcs {
		r, err := a.syncRegionVSwitches(dc, 1)
		sr := cloud.SyncResult{
			Key: dc.Name,
		}
		if err == nil {
			sr.Result = "同步成功"
			// 回收过期主机
			err = a.expiredVSwitchs(time.Now().Add(-10*time.Minute), dc)
			if err != nil {
				sr.Result = "同步成功，但是回收VSwitch失败" + err.Error()
			}
		} else {
			sr.Result = r + " " + err.Error()
		}
		result = append(result, sr)
	}
	return
}

func (a Account) expiredVSwitchs(expiredTime time.Time, dc asset.Datacenter) (err error) {
	subnets := []asset.SubNet{}
	err = app.DB().Where("account_id = ? and sync_time < ? and datacenter_id = ?", a.ID, expiredTime, dc.ID).Find(&subnets).Error
	if err != nil {
		return
	}
	for i := range subnets {
		err = subnets[i].Delete()
		if err != nil {
			return
		}
	}
	return
}

func (a Account) syncRegionVSwitches(dc asset.Datacenter, page int) (result string, err error) {
	accessSecret, _ := utils.Base64Decode(a.AccessSecret)
	client, err := vpc.NewClientWithAccessKey(dc.Code, a.AccessKey, accessSecret)
	if err != nil {
		result = "创建客户端失败"
		return
	}
	request := vpc.CreateDescribeVSwitchesRequest()
	request.Scheme = "https"
	request.PageSize = requests.NewInteger(50)
	if page < 1 {
		page = 1
	}
	request.RegionId = dc.Code
	request.PageNumber = requests.NewInteger(page)
	response, err := client.DescribeVSwitches(request)
	if err != nil {
		result = "请求失败"
		return
	}
	if response.TotalCount == 0 {
		return
	}
	err = a.saveVSwitchs(dc, response.VSwitches.VSwitch)
	if err != nil {
		return
	}
	if response.TotalCount > page*50 {
		result, err = a.syncRegionVSwitches(dc, page+1)
	}
	return
}

func (a Account) saveVSwitchs(dc asset.Datacenter, vswitches []vpc.VSwitch) (err error) {
	now := time.Now()
	for _, vswitch := range vswitches {
		exist := asset.SubNet{}
		err = app.DB().Where("account_id = ? and v_switch_id = ? and datacenter_id = ?", a.ID, vswitch.VSwitchId, dc.ID).First(&exist).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		createTime, err1 := time.ParseInLocation("2006-01-02T15:04:05Z", vswitch.CreationTime, time.Local)
		if err1 != nil {
			createTime = now
		}
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = app.DB().Create(&asset.SubNet{
				AccountID:       a.ID,
				DatacenterID:    dc.ID,
				Name:            vswitch.VSwitchName,
				VSwitchID:       vswitch.VSwitchId,
				ResourceGroupID: vswitch.ResourceGroupId,
				IPv4CIDR:        vswitch.CidrBlock,
				IPv6CIDR:        vswitch.Ipv6CidrBlock,
				Description:     vswitch.Description,
				VPCID:           vswitch.VpcId,
				ZoneID:          vswitch.ZoneId,
				CreatedAt:       createTime,
				SyncTime:        &now,
			}).Error
		} else if err == nil {
			updateMap := map[string]any{"sync_time": &now}
			if exist.Name != vswitch.VSwitchName {
				updateMap["name"] = vswitch.VSwitchName
			}
			if exist.Description != vswitch.Description {
				updateMap["description"] = vswitch.Description
			}
			if exist.IPv4CIDR != vswitch.CidrBlock {
				updateMap["ipv4_cidr"] = vswitch.CidrBlock
			}
			if exist.IPv6CIDR != vswitch.Ipv6CidrBlock {
				updateMap["ipv6_cidr"] = vswitch.Ipv6CidrBlock
			}
			if exist.ZoneID != vswitch.ZoneId {
				updateMap["zone_id"] = vswitch.ZoneId
			}
			if exist.ResourceGroupID != vswitch.ResourceGroupId {
				updateMap["resource_group_id"] = vswitch.ResourceGroupId
			}
			err = app.DB().Model(&exist).Updates(updateMap).Error
		}
		if err != nil {
			return
		}
	}
	return
}
