package aliyun

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/asset/cloud"
	"cmdb/pkg/utils"
	"errors"
	"time"

	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/requests"
	vpc "github.com/aliyun/alibaba-cloud-sdk-go/services/vpc"
	"gorm.io/gorm"
)

func (a Account) syncEIPs() (result cloud.SyncResults, err error) {
	dcs, err := asset.GetDatacentersByCloudType(asset.AliyunCloudType)
	if err != nil {
		return
	}
	for _, dc := range dcs {
		r, err := a.syncRegionEIPs(dc, 1)
		sr := cloud.SyncResult{
			Key: dc.Name,
		}
		if err == nil {
			sr.Result = "同步成功"
			// 回收过期主机
			err = a.expiredEIPs(time.Now().Add(-10*time.Minute), dc)
			if err != nil {
				sr.Result = "同步成功，但是回收EIP失败" + err.<PERSON>rror()
			}
		} else {
			sr.Result = r + " " + err.Error()
		}
		result = append(result, sr)
	}
	return
}

func (a Account) syncRegionEIPs(dc asset.Datacenter, page int) (result string, err error) {
	accessSecret, _ := utils.Base64Decode(a.AccessSecret)
	client, err := vpc.NewClientWithAccessKey(dc.Code, a.AccessKey, accessSecret)
	if err != nil {
		result = "创建客户端失败"
		return
	}
	request := vpc.CreateDescribeEipAddressesRequest()
	request.Scheme = "https"
	request.PageSize = requests.NewInteger(100)
	if page < 1 {
		page = 1
	}
	request.RegionId = dc.Code
	request.PageNumber = requests.NewInteger(page)
	response, err := client.DescribeEipAddresses(request)
	if err != nil {
		result = "请求失败"
		return
	}
	if response.TotalCount == 0 {
		return
	}
	err = a.saveEIPs(dc, response.EipAddresses.EipAddress)
	if err != nil {
		return
	}
	if response.TotalCount > (page+1)*100 {
		result, err = a.syncRegionEIPs(dc, page+1)
	}
	return
}

func (a Account) saveEIPs(dc asset.Datacenter, ips []vpc.EipAddress) (err error) {
	now := time.Now()
	for _, ip := range ips {
		exist := asset.PublicIP{}
		err = app.DB().Where("account_id = ? AND sn = ? AND datacenter_id = ?", a.ID, ip.AllocationId, dc.ID).First(&exist).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		// 新增
		createTime, err1 := time.ParseInLocation("2006-01-02T15:04:05Z", ip.AllocationTime, time.Local)
		if err1 != nil {
			createTime = now
		}
		var expiredTime *time.Time
		expiredTimeTmp, err1 := time.ParseInLocation("2006-01-02T15:04:05Z", ip.ExpiredTime, time.Local)
		if err1 != nil {
			expiredTime = nil
		} else {
			expiredTime = &expiredTimeTmp
		}
		if errors.Is(err, gorm.ErrRecordNotFound) {
			publicIP := asset.PublicIP{
				AccountID:    a.ID,
				DatacenterID: dc.ID,
				CloudType:    asset.AliyunCloudType,
				SN:           ip.AllocationId,
				IP:           ip.IpAddress,
				PrivateIP:    ip.PrivateIpAddress,
				Name:         ip.Name,
				CreatedAt:    createTime,
				ExpiredTime:  expiredTime,
				Status:       ip.Status,
				SyncTime:     &now,
			}
			err = app.DB().Create(&publicIP).Error
			if err != nil {
				return
			}
			continue
		}
		// 更新
		updateMap := map[string]any{"sync_time": &now}
		if exist.IP != ip.IpAddress {
			updateMap["ip"] = ip.IpAddress
		}
		if exist.PrivateIP != ip.PrivateIpAddress {
			updateMap["private_ip"] = ip.PrivateIpAddress
		}
		if exist.Name != ip.Name {
			updateMap["name"] = ip.Name
		}
		if exist.ExpiredTime != expiredTime {
			updateMap["expired_time"] = expiredTime
		}
		if exist.CreatedAt != createTime {
			updateMap["created_at"] = createTime
		}
		if exist.Status != ip.Status {
			updateMap["status"] = ip.Status
		}
		if exist.CloudType != asset.AliyunCloudType {
			updateMap["cloud_type"] = asset.AliyunCloudType
		}
		err = app.DB().Model(&exist).Updates(updateMap).Error
		if err != nil {
			return
		}
	}
	return
}

func (a Account) expiredEIPs(expiredTime time.Time, dc asset.Datacenter) (err error) {
	publicIPs := []asset.PublicIP{}
	err = app.DB().Where("account_id = ? and sync_time < ? and datacenter_id = ?", a.ID, expiredTime, dc.ID).Find(&publicIPs).Error
	if err != nil {
		return
	}
	for i := range publicIPs {
		err = publicIPs[i].Delete()
		if err != nil {
			return
		}
	}
	return
}
