# Makefile for CMDB project

# 设置Go命令
GO = go
# 设置环境变量
GOENV = CGO_ENABLED=0
# 设置构建标志
GOFLAGS = -v -ldflags "-s -w"
# 设置输出二进制文件名
BINARY_NAME = cmdb
# 设置主包路径
MAIN_PACKAGE = .

# 默认目标：构建项目
.PHONY: all
all: build

# 构建目标：使用CGO_ENABLED=0构建项目
.PHONY: build
build:
	@echo "Building $(BINARY_NAME)..."
	$(GOENV) $(GO) build $(GOFLAGS) -o $(BINARY_NAME) $(MAIN_PACKAGE)
	@echo "Build complete: $(BINARY_NAME)"

# 清理目标：删除构建产物
.PHONY: clean
clean:
	@echo "Cleaning..."
	rm -fv $(BINARY_NAME)
	@echo "Clean complete"

# 运行目标：构建并运行项目
.PHONY: run
run: build
	@echo "Running $(BINARY_NAME)..."
	./$(BINARY_NAME)

# 测试目标：运行测试
.PHONY: test
test:
	@echo "Running tests..."
	$(GOENV) $(GO) test -v ./...
	@echo "Tests complete"

# 监视目标：使用gowatch监视文件变化并自动重新构建和运行项目
.PHONY: watch
watch:
	@echo "Starting gowatch with CGO_ENABLED=0..."
	@echo "Press Ctrl+C to stop"
	-$(GOENV) gowatch || true
	@echo "gowatch stopped"

# 帮助目标：显示帮助信息
.PHONY: help
help:
	@echo "CMDB Makefile"
	@echo ""
	@echo "Usage:"
	@echo "  make              构建项目 (CGO_ENABLED=0)"
	@echo "  make build        构建项目 (CGO_ENABLED=0)"
	@echo "  make clean        清理构建产物"
	@echo "  make run          构建并运行项目"
	@echo "  make test         运行测试"
	@echo "  make watch        使用gowatch监视文件变化 (CGO_ENABLED=0)"
	@echo "  make help         显示帮助信息"
