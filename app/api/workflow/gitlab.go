package workflow

import (
	"cmdb/app"
	"cmdb/app/service/audit"
	"cmdb/app/service/auth"
	"cmdb/app/service/workflow"
	"encoding/json"
	"strconv"

	"github.com/gin-gonic/gin"
)

func AddProjectMember(c *gin.Context) {
	id, err := strconv.Atoi(c.<PERSON>("id"))
	if err != nil {
		app.FailedResponseMsg(c, "非法参数")
		return
	}
	loginUser, err := auth.GetLoginUser(c)
	if err != nil {
		app.Log().Error("gitlab工单添加项目成员，获取用户失败", "err", err)
		app.FailedResponseMsg(c, "获取用户失败")
		return
	}
	gitlabOrder, err := workflow.GetOrderGitlabDetailByID(id)
	if err != nil {
		app.Log().Error("gitlab工单添加项目成员，获取工单失败", "err", err)
		app.FailedResponseMsg(c, "获取工单失败")
		return
	}
	p, err := gitlabOrder.CurrentProcess()
	if err != nil {
		app.Log().Error("gitlab工单添加项目成员，获取工单流程失败", "err", err)
		app.FailedResponseMsg(c, "获取工单失败")
		return
	}
	apids := []uint{}
	json.Unmarshal(p.ApproversIDs, &apids)
	var pass bool
	uid := loginUser.ID
	for i := range apids {
		if apids[i] == uid {
			pass = true
		}
	}
	if !pass {
		app.FailedResponseMsg(c, "无权限")
		return
	}
	rs, err := gitlabOrder.AddProjectMember()
	if err != nil {
		app.Log().Error("gitlab工单添加项目成员失败", "err", err)
		app.FailedResponseMsg(c, rs.String())
		return
	}
	audit.LogAPIOP(c, "工单管理", "添加gitlab权限，关联工单："+gitlabOrder.Title)
	app.SuccessResponseMsg(c, "添加成功")
}
