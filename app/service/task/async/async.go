package async

import "time"

// 定义一个异步任务结构体
type AsyncTask struct {
	StartTasks    StartTasks
	IntervalTasks IntervalTasks
	CronTasks     CronTasks
}

// 运行异步任务
func (a *AsyncTask) Run() {
	// 运行开始任务
	a.StartTasks.Run()
	// 等待5秒
	time.Sleep(5 * time.Second)
	// 运行间隔任务
	go a.IntervalTasks.Run()
	// 运行定时任务
	go a.CronTasks.Run()
}

// 停止异步任务
func (a *AsyncTask) Stop() {
	// 停止间隔任务
	a.IntervalTasks.Stop()
	// 停止定时任务
	a.CronTasks.Stop()
}
