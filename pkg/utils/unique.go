package utils

// UniqueStringSlice 去重字符串切片
func UniqueStringSlice(slice []string) []string {
	keys := make(map[string]struct{})
	list := []string{}
	for _, entry := range slice {
		if _, value := keys[entry]; !value {
			keys[entry] = struct{}{}
			list = append(list, entry)
		}
	}
	return list
}

// UniqueIntSlice 去重int切片
func UniqueIntSlice(slice []int) []int {
	keys := make(map[int]struct{})
	list := []int{}
	for _, entry := range slice {
		if _, value := keys[entry]; !value {
			keys[entry] = struct{}{}
			list = append(list, entry)
		}
	}
	return list
}
