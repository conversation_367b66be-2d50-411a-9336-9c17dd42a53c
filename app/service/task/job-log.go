package task

import (
	"cmdb/app"
	"time"
)

// JobLog 同步任务记录
type JobLog struct {
	ID        uint          `gorm:"primaryKey;column:id;comment:id" json:"id"`
	Name      string        `gorm:"column:name;type:varchar(255);comment:任务名称" json:"name"`
	Result    string        `gorm:"column:result;type:text;comment:结果" json:"result"`
	Cost      time.Duration `gorm:"column:cost;comment:耗时" json:"cost"`
	Status    StatusTask    `gorm:"index;column:status;comment:0 初始化状态 1 执行中 2 执行成功 3 执行失败" json:"status"`
	CreatedAt time.Time     `gorm:"column:created_at;index;comment:创建时间" json:"created_at"`
}

// TableName 设置表名
func (*JobLog) TableName() string {
	return "task_job_logs"
}

// 分页获取任务日志记录
func GetJobLogs(offset, limit int, name string) (count int64, logs []JobLog, err error) {
	dbop := app.DB()
	if name != "" {
		dbop = dbop.Where("name = ? ", name)
	}
	err = dbop.Model(&JobLog{}).Count(&count).Order("created_at DESC,id DESC").Offset(offset).Limit(limit).Find(&logs).Error
	return
}
