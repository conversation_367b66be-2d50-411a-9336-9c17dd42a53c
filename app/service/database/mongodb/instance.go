package mongodb

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/pkg/db"
	"cmdb/pkg/utils"
	"errors"
	"fmt"
	"time"

	mgo "gopkg.in/mgo.v2"
	"gopkg.in/mgo.v2/bson"

	"gorm.io/gorm"
)

var (
	ErrInstanceExist = errors.New("实例已经存在")
	ErrGroupExist    = errors.New("分组已经存在")
)

type Instance struct {
	ID            uint        `gorm:"column:id;primaryKey;comment:ID" json:"id"`
	Name          string      `gorm:"type:varchar(255);column:name;comment:实例名称" json:"name"`
	Host          string      `gorm:"type:varchar(255);index;column:host;comment:连接主机" json:"host"`
	Port          uint        `gorm:"type:int;index;column:port;comment:连接端口" json:"port"`
	Link          string      `gorm:"type:varchar(255);column:link;comment:链接地址" json:"link"`
	AdminUser     string      `gorm:"type:varchar(255);column:admin_user;comment:管理用户" json:"admin_user"`
	AdminPassword string      `gorm:"type:varchar(255);column:admin_password;comment:管理密码" json:"-"`
	Version       string      `gorm:"type:varchar(255);column:version;comment:数据库版本" json:"version"`
	Env           asset.ENV   `gorm:"column:env;default:3;index;comment:设置环境" json:"env"`
	Remark        string      `gorm:"type:varchar(255);column:remark;comment:备注" json:"remark"`
	CreatedAt     time.Time   `gorm:"column:created_at;comment:创建时间" json:"created_at"`
	UpdatedAt     time.Time   `gorm:"column:updated_at;comment:更新时间" json:"updated_at"`
	Tags          []asset.Tag `gorm:"many2many:asset_db_mongodb_instance_tags;" json:"tags"`
}

// TableName 设置表名
func (*Instance) TableName() string {
	return "asset_db_mongodb_instances"
}

type InstanceForm struct {
	Name          string    `json:"name" binding:"required,max=255"`
	Host          string    `json:"host" binding:"required,max=255"`
	Port          uint      `json:"port" binding:"required,max=65536"`
	Link          string    `json:"link" binding:"max=255"`
	AdminUser     string    `json:"admin_user" binding:"max=255"`
	AdminPassword string    `json:"admin_password" bindnig:"max=100"`
	Env           asset.ENV `json:"env" binding:"max=125"`
	Remark        string    `json:"remark" binding:"max=255"`
	Tags          []uint    `json:"tags" `
}

func (form *InstanceForm) Create() (err error) {
	err = app.DB().Select("id").Where("host = ? AND port = ?", form.Host, form.Port).Take(&Instance{}).Error
	if err == nil {
		return ErrInstanceExist
	}
	if err == gorm.ErrRecordNotFound {
		err = app.DB().Create(&Instance{
			Name:          form.Name,
			Host:          form.Host,
			Port:          form.Port,
			AdminUser:     form.AdminUser,
			AdminPassword: utils.Base64Encode(form.AdminPassword),
			Env:           form.Env,
			Remark:        form.Remark,
			Link:          form.Link,
		}).Error
		if err != nil {
			return
		}
		i := Instance{}
		err1 := app.DB().Select("id").
			Where("host = ? AND port = ?", form.Host, form.Port).Take(&i).Error
		if err1 == nil {
			// 添加后自动更新版本
			go i.UpdateInfo()
			if len(form.Tags) > 0 {
				var tags []asset.Tag
				tags, err1 = asset.GetTagsIDs(form.Tags)
				if err1 == nil && len(tags) > 0 {
					err1 = app.DB().Model(&i).Association("Tags").Append(tags)
				}
			}
		}
		if err1 != nil {
			app.Log().Error("创建mongodb实例时，分组失败", "err", err1)
		}
	}
	return
}

func (instance Instance) Update(form InstanceForm) (err error) {
	updateMap := map[string]any{}
	if instance.Name != form.Name {
		exist := Instance{}
		err = app.DB().Select("id").
			Where("name = ?", form.Name).Take(&exist).Error
		if err == nil && exist.ID != instance.ID {
			return ErrInstanceExist
		} else if err != nil && err != gorm.ErrRecordNotFound {
			return
		}
		updateMap["name"] = form.Name
	}
	if instance.Host != form.Host {
		updateMap["host"] = form.Host
	}
	if instance.Port != form.Port {
		updateMap["port"] = form.Port
	}
	if instance.Remark != form.Remark {
		updateMap["remark"] = form.Remark
	}
	if instance.Env != form.Env {
		updateMap["env"] = form.Env
	}
	if instance.AdminUser != form.AdminUser {
		updateMap["admin_user"] = form.AdminUser
	}
	if instance.Link != form.Link {
		updateMap["link"] = form.Link
	}
	if form.AdminPassword != "" {
		updateMap["admin_password"] = utils.Base64Encode(form.AdminPassword)
	}
	if len(updateMap) > 0 {
		err = app.DB().Model(instance).Updates(updateMap).Error
	}
	tags := []asset.Tag{}
	if len(form.Tags) > 0 {
		var err1 error
		tags, err1 = asset.GetTagsIDs(form.Tags)
		if err1 == nil {
			err1 = app.DB().Model(instance).Association("Tags").Replace(&tags)
		}
		if err1 != nil {
			app.Log().Error("编辑实例时，修改分组失败", "err", err1)
		}
	}
	return
}

func GetInstances(offset, limit int, keyword *string) (count int64, instances []Instance, err error) {
	dbop := app.DB()
	if keyword != nil {
		dbop = db.MLike(dbop, *keyword, "name", "host", "remark", "version")
	}
	err = dbop.Model(&Instance{}).Count(&count).Order("host").Offset(offset).Limit(limit).Find(&instances).Error
	return
}

// 通过ID获取实例对象
func GetInstanceByID(id int) (instance Instance, err error) {
	err = app.DB().Select(
		"id", "name", "host", "port", "admin_user", "env", "admin_user",
		"version", "remark", "created_at", "updated_at", "link",
	).Where("id = ?", id).Take(&instance).Error
	return
}

// 通过查找分组下的实例
func GetAllInstances() (instances []Instance, err error) {
	err = app.DB().Order("name").Find(&instances).Error
	return
}

// 删除实例
func (i *Instance) Delete() (err error) {
	dbop := app.DB().Begin()
	err = dbop.Model(i).Association("Tags").Clear()
	if err != nil {
		dbop.Rollback()
		return err
	}
	err = dbop.Delete(i).Error
	if err != nil {
		dbop.Rollback()
		return err
	}
	err = dbop.Commit().Error
	if err != nil {
		dbop.Rollback()
	}
	return
}

// 获取实例密码
func (instance Instance) getPassword() (password string, err error) {
	err = app.DB().Model(&Instance{}).Select("admin_password").
		Where("id = ?", instance.ID).Scan(&password).Error
	if err != nil {
		return "", err
	}
	password, err = utils.Base64Decode(password)
	return
}

// 连接实例
func (instance Instance) connect(dbname string) (session *mgo.Session, err error) {
	url := fmt.Sprintf("mongodb://%s:%d/"+dbname, instance.Host, instance.Port)
	if instance.AdminUser != "" {
		password := ""
		password, err = instance.getPassword()
		if err != nil {
			return
		}
		url = fmt.Sprintf("mongodb://%s:%s@%s:%d/"+dbname,
			instance.AdminUser, password, instance.Host, instance.Port)
	}
	session, err = mgo.DialWithTimeout(url, time.Second*3)
	return
}

// 更新实例信息
func (instance *Instance) UpdateInfo() (err error) {
	session, err := instance.connect("admin")
	if err != nil {
		return err
	}
	defer session.Close()
	info, err := session.BuildInfo()
	if err != nil {
		return err
	}
	version := info.Version
	err = app.DB().Model(instance).UpdateColumn("version", version).Error
	return
}

// 获取mongodb角色
func GetMongodbRole(name string) (role mgo.Role) {
	switch name {
	case "root":
		role = mgo.RoleRoot
	case "read":
		role = mgo.RoleRead
	case "readAnyDatabase":
		role = mgo.RoleReadAny
	case "readWrite":
		role = mgo.RoleReadWrite
	case "readWriteAnyDatabase":
		role = mgo.RoleReadWriteAny
	case "dbAdmin":
		role = mgo.RoleDBAdmin
	case "dbAdminAnyDatabase":
		role = mgo.RoleDBAdminAny
	case "userAdmin":
		role = mgo.RoleUserAdmin
	case "userAdminAnyDatabase":
		role = mgo.RoleUserAdminAny
	case "clusterAdmin":
		role = mgo.RoleClusterAdmin
	}
	return
}

// 创建实例用户
func (instance Instance) CreateUser(user, password string, dbname string, role mgo.Role) (err error) {
	session, err := instance.connect("admin")
	if err != nil {
		return err
	}
	defer session.Close()
	//初始化新数据库的结构体
	db := mgo.Database{
		Session: session,
		Name:    dbname,
	}
	//给用户信息赋初值
	userInfo := &mgo.User{
		Username: user,
		Password: password,
		Roles:    []mgo.Role{role},
	}
	//插入用户
	err = db.UpsertUser(userInfo)
	if err != nil {
		return
	}
	return
}

// 获取数据库名
func (instance Instance) GetDBNames() (names []string, err error) {
	session, err := instance.connect("admin")
	if err != nil {
		return
	}
	defer session.Close()
	names, err = session.DatabaseNames()
	return
}

type MGOUser struct {
	User  string `json:"user"`
	DB    string `json:"db"`
	Roles string `json:"roles"`
}

func (instance Instance) GetUsers(dbname string) (mgoUsers []MGOUser, err error) {
	mgoUsers = []MGOUser{}
	session, err := instance.connect("admin")
	if err != nil {
		return
	}
	defer session.Close()
	var result bson.D
	err = session.DB(dbname).Run(bson.D{{Name: "usersInfo", Value: 1}}, &result)
	if err != nil {
		return
	}
	m := result.Map()
	for k, v := range m {
		if k == "users" {
			data, ok := v.([]interface{})
			if ok {
				for i := range data {
					items, ok1 := data[i].(bson.D)
					if ok1 {
						u := MGOUser{}
						for j := range items {
							if items[j].Name == "user" {
								if user, oo := items[j].Value.(string); oo {
									u.User = user
								}
							}
							if items[j].Name == "db" {
								if db, oo := items[j].Value.(string); oo {
									u.DB = db
								}
							}
							if items[j].Name == "roles" {
								roles, oo := items[j].Value.([]interface{})
								if oo {
									for ii := range roles {
										roleRecord, oo1 := roles[ii].(bson.D)
										if oo1 {
											rolesMap := roleRecord.Map()
											for kk, vv := range rolesMap {
												if kk == "role" {
													if db, ooo := vv.(string); ooo {
														u.Roles = db
													}
												}
											}
										}
									}
								}
							}
						}
						if u.User != "" {
							mgoUsers = append(mgoUsers, u)
						}
					}
				}
			}
		}
	}
	return
}
