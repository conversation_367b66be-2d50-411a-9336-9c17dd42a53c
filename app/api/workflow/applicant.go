package workflow

import (
	"cmdb/app"
	"cmdb/app/service/audit"
	"cmdb/app/service/auth"
	"cmdb/app/service/workflow"
	"strconv"

	"github.com/gin-gonic/gin"
)

// 申请工单
func ApplyOrder(c *gin.Context) {
	// 将参数转换为int类型
	code := c.Param("code")
	if code == "" {
		// 记录日志
		app.Log().Debug("申请工单时，存在非法参数", "code", c.<PERSON>("code"))
		// 返回错误信息
		app.FailedResponseMsg(c, "存在非法参数")
		return
	}
	// 根据id获取工单类型
	flow, err := workflow.GetFlowByCode(code)
	if err != nil {
		// 记录日志
		app.Log().Error("申请工单时，工单类型不存在", "err", err, "code", code)
		// 返回错误信息
		app.FailedResponseMsg(c, "工单类型不存在")
		return
	}
	// 生成表单
	form := flow.ExtType.GenForm()
	// 绑定参数
	if err := c.ShouldBind(form); err != nil {
		// 记录日志
		app.Log().Debug("申请工单时，参数绑定失败", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "参数绑定失败")
		return
	}
	// 获取登录用户
	loginUser, err := auth.GetLoginUser(c)
	if err != nil {
		// 记录日志
		app.Log().Error("申请工单时，获取用户信息失败", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "获取用户信息失败")
		return
	}
	// 查询申请人
	applicant, err := workflow.NewApplicant(loginUser)
	if err != nil {
		// 记录日志
		app.Log().Error("申请工单时，查询申请人失败", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "查询申请人失败")
		return
	}
	// 创建工单
	err = form.Create(applicant, flow, nil)
	if err != nil {
		// 记录日志
		app.Log().Error("申请工单时，创建工单失败", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "创建工单失败："+err.Error())
		return
	}
	// 记录操作日志
	audit.LogAPIOP(c, "工单管理", "申请工单:"+flow.Name)
	// 返回成功信息
	app.SuccessResponseMsg(c, "提交成功")
}

// 更新工单
func UpdateOrder(c *gin.Context) {
	// 将参数转换为int类型
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		// 记录日志
		app.Log().Debug("更新工单时，存在非法参数", "err", err, "order_id", c.Param("id"))
		// 返回错误信息
		app.FailedResponseMsg(c, "存在非法参数")
		return
	}
	// 根据id获取工单
	order, err := workflow.GetOrderByID(id)
	if err != nil {
		// 记录日志
		app.Log().Error("更新工单时，工单不存在", "err", err, "order_id", c.Param("id"))
		// 返回错误信息
		app.FailedResponseMsg(c, "工单不存在")
		return
	}
	// 获取登录用户
	loginUser, err := auth.GetLoginUser(c)
	if err != nil {
		// 记录日志
		app.Log().Error("更新工单时，获取用户信息失败", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "获取用户信息失败")
		return
	}
	// 判断用户是否有权限操作
	if order.ApplicantID != loginUser.ID {
		// 记录日志
		app.Log().Error("更新工单时，无权限操作", "err", err, "order_id", c.Param("id"))
		// 返回错误信息
		app.FailedResponseMsg(c, "无权限操作")
		return
	}
	// 生成表单
	form := order.ExtType.GenForm()
	// 绑定参数
	if err := c.ShouldBind(form); err != nil {
		// 记录日志
		app.Log().Debug("更新工单时，参数绑定失败", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "参数绑定失败")
		return
	}
	// 更新工单
	if err := form.Update(order); err != nil {
		// 记录日志
		app.Log().Error("更新工单时，更新工单失败", "err", err, "order_id", c.Param("id"))
		// 返回错误信息
		app.FailedResponseMsg(c, "更新工单失败")
		return
	}
	// 记录操作日志
	audit.LogAPIOP(c, "工单管理", "更新工单:"+order.Title)
	// 返回成功信息
	app.SuccessResponseMsg(c, "更新工单成功")
}

// 取消工单
func CancelOrder(c *gin.Context) {
	// 将参数转换为int类型
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		// 记录日志
		app.Log().Debug("更新工单时，存在非法参数", "err", err, "order_id", c.Param("id"))
		// 返回错误信息
		app.FailedResponseMsg(c, "存在非法参数")
		return
	}
	// 获取登录用户
	loginUser, err := auth.GetLoginUser(c)
	if err != nil {
		// 记录日志
		app.Log().Error("取消工单时，获取登录用户失败", "err", err, "order_id", c.Param("id"))
		// 返回错误信息
		app.FailedResponseMsg(c, "获取登录用户失败")
		return
	}
	// 根据id获取工单
	order, err := workflow.GetOrderByID(id)
	if err != nil {
		// 记录日志
		app.Log().Error("取消工单时，工单不存在", "err", err, "order_id", c.Param("id"))
		// 返回错误信息
		app.FailedResponseMsg(c, "工单不存在")
		return
	}
	// 判断用户是否是工单申请人
	if order.ApplicantID != loginUser.ID {
		// 记录日志
		app.Log().Error("取消工单时，当前用户不是工单申请人", "order_id", c.Param("id"))
		// 返回错误信息
		app.FailedResponseMsg(c, "当前用户不是工单申请人")
		return
	}
	// 取消工单
	if err := order.Cancel(); err != nil {
		// 记录日志
		app.Log().Error("取消工单时，取消工单失败", "err", err, "order_id", c.Param("id"))
		// 返回错误信息
		app.FailedResponseMsg(c, "取消工单失败")
		return
	}
	// 记录操作日志
	audit.LogAPIOP(c, "工单管理", "取消工单:"+order.Title)
	// 返回成功信息
	app.SuccessResponseMsg(c, "取消工单成功")
}

// 获取工单流程
func GetOrderProcesses(c *gin.Context) {
	// 将参数转换为int类型
	code := c.Param("code")
	if code == "" {
		// 记录日志
		app.Log().Debug("获取工单流程时，存在非法参数", "code", c.Param("code"))
		// 返回错误信息
		app.FailedResponseMsg(c, "存在非法参数")
		return
	}
	// 根据id获取工单类型
	flow, err := workflow.GetFlowByCode(code)
	if err != nil {
		// 记录日志
		app.Log().Debug("获取工单流程时，工单类型不存在", "err", err, "code", c.Param("code"))
		// 返回错误信息
		app.FailedResponseMsg(c, "工单类型不存在")
		return
	}
	// 获取登录用户
	loginUser, err := auth.GetLoginUser(c)
	if err != nil {
		// 记录日志
		app.Log().Error("获取工单流程时，获取用户信息失败", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "获取用户信息失败")
		return
	}
	// 根据id获取用户信息
	loginUser, err = auth.GetUserByID(int(loginUser.ID))
	if err != nil {
		// 记录日志
		app.Log().Error("获取工单流程时，获取用户信息失败", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "获取用户信息失败")
		return
	}
	// 查询申请人
	applicant, err := workflow.NewApplicant(loginUser)
	if err != nil {
		// 记录日志
		app.Log().Error("获取工单流程时，查询申请人失败", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "查询申请人失败")
		return
	}
	// 生成流程
	processes, err := flow.GenProcesses(code, applicant, nil)
	if err != nil {
		// 记录日志
		app.Log().Error("获取工单流程时，生成流程失败", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "生成流程失败")
		return
	}
	// 获取流程详情
	psDetails := []workflow.ProcessDetail{}
	for i := range processes {
		p := workflow.ProcessDetail{
			Process: processes[i],
		}
		err = p.Detail()
		if err != nil {
			// 记录日志
			app.Log().Error("获取工单流程时，获取流程详情失败", "err", err)
			// 返回错误信息
			app.FailedResponseMsg(c, "获取流程详情失败")
			return
		}
		psDetails = append(psDetails, p)
	}
	// 返回成功信息
	app.SuccessResponseData(c, psDetails)
}

// 自定义工单列表结构体
type MyOrderList struct {
	workflow.Order
	Applicant workflow.Applicant `json:"applicant"`
	Evaluated bool               `json:"evaluated"`
}

// 获取我的工单列表
func GetMyOrders(c *gin.Context) {
	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	offset := (page - 1) * limit
	// 获取关键字
	var keyword *string
	if val := c.Query("keyword"); val != "" {
		keyword = &val
	}
	// 获取工单类型
	var extType *workflow.OrderExtType
	if val := c.Query("ext_type"); val != "" {
		extTypeID, err := strconv.Atoi(val)
		if err == nil {
			if et, err := workflow.GetExtTypeByID(extTypeID); err == nil {
				extType = &et
			}
		}
	}
	// 获取工单状态
	var status *workflow.OrderStatus
	if val, err := strconv.Atoi(c.Query("status")); err == nil {
		a := workflow.GetOrderStatusByID(val)
		status = &a
	}
	// 获取登录用户
	loginUser, err := auth.GetLoginUser(c)
	if err != nil {
		// 记录日志
		app.Log().Error("认证信息，获取用户信息失败", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "获取用户信息失败")
		return
	}
	// 查询申请人
	applicant, err := workflow.NewApplicant(loginUser)
	if err != nil {
		// 记录日志
		app.Log().Error("获取用户信息失败", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "获取用户信息失败")
		return
	}
	// 获取用户订单
	count, data, err := applicant.GetMyOrders(offset, limit, keyword, extType, status)
	if err != nil {
		// 记录日志
		app.Log().Error("获取用户订单失败", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "获取用户订单失败")
		return
	}
	// 构造返回数据
	var newData []MyOrderList
	for _, v := range data {
		evaluated := false
		if v.Status == workflow.CompletedStatus {
			evaluated, _ = v.Evaluated()
		}
		newData = append(newData, MyOrderList{
			Order:     v,
			Applicant: applicant,
			Evaluated: evaluated,
		})
	}
	// 返回成功信息
	app.SuccessResponseDataCount(c, newData, count)
}

// 获取用户所有工单
func GetMyAllOrders(c *gin.Context) {
	// 获取ext_type参数
	var extType *workflow.OrderExtType
	if val := c.Query("ext_type"); val != "" {
		extTypeID, err := strconv.Atoi(val)
		if err == nil {
			if et, err := workflow.GetExtTypeByID(extTypeID); err == nil {
				extType = &et
			}
		}
	}
	// 获取登录用户信息
	loginUser, err := auth.GetLoginUser(c)
	if err != nil {
		// 记录错误日志
		app.Log().Error("获取用户信息失败", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "获取用户信息失败")
		return
	}
	// 创建Applicant对象
	applicant, err := workflow.NewApplicant(loginUser)
	if err != nil {
		// 记录错误日志
		app.Log().Error("获取用户信息失败", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "获取用户信息失败")
		return
	}
	// 获取用户所有工单
	data, err := applicant.GetMyAllOrders(extType)
	if err != nil {
		// 记录错误日志
		app.Log().Error("获取用户工单失败", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "获取用户工单失败")
		return
	}
	// 返回成功信息
	app.SuccessResponseData(c, data)
}

func EveluateOrder(c *gin.Context) {
	var form workflow.OrderEvaluationForm
	if err := c.ShouldBindJSON(&form); err != nil {
		// 记录错误日志
		app.Log().Error("获取评价信息失败", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "获取评价信息失败")
		return
	}
	// 获取登录用户
	loginUser, err := auth.GetLoginUser(c)
	if err != nil {
		// 记录日志
		app.Log().Error("认证信息，获取用户信息失败", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "获取用户信息失败")
		return
	}
	// 查询申请人
	applicant, err := workflow.NewApplicant(loginUser)
	if err != nil {
		// 记录日志
		app.Log().Error("获取用户信息失败", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "获取用户信息失败")
		return
	}
	order, err := workflow.GetOrderBySN(form.SN)
	if err != nil {
		// 记录日志
		app.Log().Error("获取工单信息失败", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "获取工单信息失败")
		return
	}
	// 判断是否是申请人
	if order.ApplicantID != applicant.ID {
		// 记录日志
		app.Log().Error("工单申请人错误", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "无权评价工单")
		return
	}
	// 判断工单状态
	if order.Status != workflow.CompletedStatus {
		// 记录日志
		app.Log().Error("工单状态错误", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "工单状态错误")
		return
	}
	// 评价工单
	err = form.Create()
	if err != nil {
		// 记录日志
		app.Log().Error("评价工单失败", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "评价工单失败")
		return
	}
	// 返回成功信息
	app.SuccessResponseMsg(c, "评价工单成功")
}
