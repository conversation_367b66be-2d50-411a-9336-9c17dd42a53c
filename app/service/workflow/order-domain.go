package workflow

import (
	"bytes"
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/auth"
	"cmdb/pkg/utils/mytime"
	"encoding/json"
	"errors"
	"time"

	"gorm.io/datatypes"
)

type OrderDomain struct {
	ID         uint           `gorm:"column:id;primarykey;comment:id" json:"id"`
	SN         string         `gorm:"type:varchar(128);unique;column:sn;comment:工单单号" json:"sn"`
	Content    string         `gorm:"type:text;column:content;comment:工单内容" json:"content"`
	DomainList datatypes.JSON `gorm:"column:domain_list;comment:域名列表" json:"domain_list"`
}

// 设置表名
func (OrderDomain) TableName() string {
	return "workflow_order_domains"
}

type OrderDomainDetail struct {
	Order      `gorm:"-"`
	Content    string         `gorm:"type:text;column:content;comment:工单内容" json:"content"`
	DomainList datatypes.JSON `gorm:"column:domain_list;comment:域名列表" json:"domain_list"`
}

// 设置表名
func (OrderDomainDetail) TableName() string {
	return "workflow_order_domains"
}

func GetOrderDomainDetail(o Order) (detail OrderDomainDetail, err error) {
	detail = OrderDomainDetail{}
	err = app.DB().Where("sn = ?", o.SN).Take(&detail).Error
	if err != nil {
		return
	}
	detail.Order = o
	return
}

type OrderDomainForm struct {
	Content    string    `json:"content"`
	DomainList any       `json:"domain_list"`
	Orders     []string  `json:"orders"`
	Env        asset.ENV `json:"env"`
	Critical   bool      `json:"critical"`
}

func (form OrderDomainForm) GenProcesses(sn string, a Applicant, admin *auth.User) (processes []Process, err error) {
	var flow Flow
	err = app.DB().Where("order_type = ?", DomainOrderType).Take(&flow).Error
	if err != nil {
		return
	}
	flow.Nodes, err = flow.GetFlowNodes()
	if err != nil {
		return
	}
	processes, err = flow.genProcesses(sn, a, admin)
	return
}

func (form *OrderDomainForm) check() (err error) {
	return
}

func (form *OrderDomainForm) Create(a Applicant, flow Flow, admin *auth.User) (err error) {
	err = form.check()
	if err != nil {
		return
	}
	sn := genSN("domain")
	processes, err := flow.genProcesses(sn, a, admin)
	if err != nil || len(processes) == 0 {
		return
	}
	status := ApprovingStatus
	var index uint
	if len(processes) == 0 {
		err = errors.New("生成流程失败")
		return
	} else if len(processes) <= 1 {
		// 如果流程没有配置就直接结束流程
		status = CompletedStatus
	} else {
		// 如果有流程就获取下一个节点的索引
		index = processes[1].ProcessIndex
		processes[1].Status = ApprovingProcessStatus
	}
	planTime := mytime.NextXWorkingDays(time.Now(), 2)
	snsByte, _ := json.Marshal(&form.Orders)
	order := Order{
		Title:        a.Name + " 申请域名解析",
		OrderType:    DomainOrderType.Name(),
		Env:          form.Env,
		ExtType:      DomainExtType,
		SN:           sn,
		Critical:     form.Critical,
		ApplicantID:  a.ID,
		Status:       status,
		PlanTime:     &planTime,
		ProcessIndex: index,
		Orders:       snsByte,
	}
	byteDomainList, _ := json.Marshal(&form.DomainList)
	extOrder := OrderDomain{
		SN:         sn,
		Content:    form.Content,
		DomainList: byteDomainList,
	}
	dbop := app.DB().Begin()
	err = dbop.Create(&order).Error
	if err != nil {
		dbop.Rollback()
		return
	}
	err = dbop.Create(&extOrder).Error
	if err != nil {
		dbop.Rollback()
		return
	}
	err = dbop.Create(&processes).Error
	if err != nil {
		dbop.Rollback()
		return
	}
	err = dbop.Commit().Error
	if err != nil {
		dbop.Rollback()
	}
	go order.notifyNextProcess()
	return
}
func (form *OrderDomainForm) Update(o Order) (err error) {
	extOrder := OrderDomain{}
	err = app.DB().Where("sn = ?", o.SN).Take(&extOrder).Error
	if err != nil {
		return
	}
	updateMap := map[string]any{}
	if form.Content != extOrder.Content {
		updateMap["content"] = form.Content
	}
	bytesDomainList, _ := json.Marshal(&form.DomainList)
	if !bytes.Equal(bytesDomainList, extOrder.DomainList) {
		updateMap["domain_list"] = bytesDomainList
	}
	if len(updateMap) > 0 {
		err = app.DB().Model(&extOrder).Updates(updateMap).Error
	}
	return
}
