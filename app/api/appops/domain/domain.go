package domain

import (
	"cmdb/app"
	"cmdb/app/service/appops/domain"
	"cmdb/app/service/asset"
	"cmdb/app/service/audit"
	"errors"
	"strconv"
	"strings"
	"sync"

	"github.com/gin-gonic/gin"
)

// 添加域名
func AddDomain(c *gin.Context) {
	var form domain.DomainForm
	err := c.BindJSON(&form)
	if err != nil {
		app.Log().Debug("添加域名接口，非法字段", "err", err)
		app.FailedResponseMsg(c, "非法字段")
		return
	}
	err = form.Create()
	if err != nil {
		msg := "添加失败"
		if errors.Is(err, domain.ErrDomainExist) {
			msg = err.Error()
		} else {
			app.Log().Error("添加域名失败", "err", err)
		}
		app.FailedResponseMsg(c, msg)
		return
	}
	audit.LogAPIOP(c, "域名管理", "添加域名："+form.Name)
	app.SuccessResponseMsg(c, "添加成功")
}

// 更新域名
func UpdateDomain(c *gin.Context) {
	var form domain.DomainForm
	err := c.BindJSON(&form)
	if err != nil {
		app.Log().Debug("修改域名接口，非法字段", "err", err)
		app.FailedResponseMsg(c, "非法字段")
		return
	}
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.Log().Debug("修改域名接口，存在非法参数", "err", err)
		app.FailedResponseMsg(c, "非法参数")
		return
	}
	d, err := domain.GetDomainByID(id)
	if err != nil {
		app.Log().Debug("修改域名接口，获取域名失败", "err", err)
		app.FailedResponseMsg(c, "获取域名失败")
		return
	}
	err = d.Update(form)
	if err != nil {
		msg := "修改失败"
		if errors.Is(err, domain.ErrDomainExist) {
			msg = err.Error()
		} else {
			app.Log().Error("修改域名失败", "err", err)
		}
		app.FailedResponseMsg(c, msg)
		return
	}
	audit.LogAPIOP(c, "域名管理", "修改域名："+form.Name)
	app.SuccessResponseMsg(c, "修改成功")
}

// 删除域名
func DeleteDomain(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.Log().Debug("删除域名接口，存在非法参数", "err", err)
		app.FailedResponseMsg(c, "非法参数")
		return
	}
	d, err := domain.GetDomainByID(id)
	if err != nil {
		app.Log().Debug("删除域名接口，获取域名失败", "err", err)
		app.FailedResponseMsg(c, "获取域名失败")
		return
	}
	err = d.Delete()
	if err != nil {
		app.Log().Error("删除域名失败", "err", err)
		app.FailedResponseMsg(c, "删除失败")
		return
	}
	audit.LogAPIOP(c, "域名管理", "删除域名："+d.Name)
	app.SuccessResponseMsg(c, "删除成功")
}

type DomainItem struct {
	domain.Domain
	Business string `json:"business"`
}

// 获取域名列表，支持分页和关键词搜索
func GetDomains(c *gin.Context) {
	var keyword *string
	var businessID *int
	// 业务ID
	if val := strings.TrimSpace(c.Query("business_id")); val != "" {
		id, err1 := strconv.Atoi(val)
		if err1 == nil {
			businessID = &id
		}
	}
	if val := strings.TrimSpace(c.Query("keyword")); val != "" {
		keyword = &val
	}
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	offset := (page - 1) * limit
	count, domains, err := domain.GetDomains(offset, limit, businessID, keyword)
	if err != nil {
		app.Log().Error("获取域名列表失败", "err", err)
		app.FailedResponseMsg(c, "获取域名列表失败")
		return
	}
	data := make([]DomainItem, len(domains))
	// 获取业务名称
	if len(domains) > 0 {
		// 并发获取业务名称
		businessMap := sync.Map{}
		wg := sync.WaitGroup{}
		max := make(chan struct{}, 10)
		for i := range domains {
			wg.Add(1)
			max <- struct{}{}
			go func(i int) {
				data[i].Domain = domains[i]
				defer wg.Done()
				defer func() { <-max }()
				if domains[i].BusinessID == 0 {
					data[i].Business = ""
					return
				}
				if v, ok := businessMap.Load(domains[i].BusinessID); ok {
					data[i].Business = v.(string)
					return
				} else {
					name, err1 := asset.GetBusinessNameByID(int(domains[i].BusinessID))
					if err1 != nil {
						app.Log().Error("获取业务名称失败", "err", err1)
						data[i].Business = ""
					}
					businessMap.Store(domains[i].BusinessID, name)
					// 获取业务名称
					data[i].Business, _ = asset.GetBusinessNameByID(int(domains[i].BusinessID))
				}
			}(i)
		}
		wg.Wait()
		close(max)
	}
	app.SuccessResponseDataCount(c, data, count)
}

// 获取所有业务域名
func GetAllDomains(c *gin.Context) {
	domains, err := domain.GetAllDomains()
	if err != nil {
		app.Log().Error("获取域名列表失败", "err", err)
		app.FailedResponseMsg(c, "获取域名列表失败")
		return
	}
	app.SuccessResponseData(c, domains)
}
