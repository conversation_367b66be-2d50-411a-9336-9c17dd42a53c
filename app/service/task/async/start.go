package async

import (
	"cmdb/app"
	"context"
	"time"
)

type StartTask struct {
	Name      string
	Func      func() (err error)
	CtxCancel context.CancelFunc
}

type StartTasks []*StartTask

func (ts StartTasks) Run() (err error) {
	for _, t := range ts {
		now := time.Now()
		app.Log().Info("开始任务 " + t.Name)
		err = t.Func()
		if err != nil {
			app.Log().Error("完成任务 "+t.Name, "cost", time.Since(now).String(), "result", "失败", "error", err)
		} else {
			app.Log().Info("完成任务 "+t.Name, "cost", time.Since(now).String(), "result", "成功")
		}
	}
	return
}
