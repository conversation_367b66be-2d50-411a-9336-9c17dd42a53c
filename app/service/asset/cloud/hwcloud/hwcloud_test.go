package hwcloud

import (
	"cmdb/app"
	"testing"
)

func TestJob(t *testing.T) {
	err := app.NewApp("../../../../../app.ini")
	if err != nil {
		t.<PERSON>al(err)
	}
	err = app.ConnectDB()
	if err != nil {
		t.<PERSON><PERSON>(err)
	}
	defer func() {
		dbop, err := app.DB().DB()
		if err != nil {
			t.<PERSON><PERSON>(err)
		}
		err = dbop.Close()
		if err != nil {
			t.<PERSON>al(err)
		}
	}()
	storage, err := StatAllAccountsOBSStorage()
	if err != nil {
		t.<PERSON>al(err)
	}
	app.Log().Info("统计华为云SFS存储容量", "容量", storage)
}
