package auth

import (
	"cmdb/pkg/db"
	"cmdb/pkg/utils"
	"errors"
	"time"

	"cmdb/app"

	"gorm.io/gorm"
)

type Key struct {
	ID        uint      `gorm:"column:id;primaryKey;comment:主键" json:"id"`
	Name      string    `gorm:"type:varchar(255);column:name;comment:key名称" json:"name"`
	Secret    string    `grom:"type:varchar(255);column:secret;comment:key密码" json:"secret"`
	Remark    string    `grom:"type:varchar(255);column:remark;comment:备注" json:"remark"`
	APIs      []API     `gorm:"many2many:auth_key_apis" json:"apis"`
	CreatedAt time.Time `gorm:"column:created_at;comment:创建时间" json:"created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at;comment:更新时间" json:"updated_at"`
}

// TableName 设置表名
func (key *Key) TableName() string {
	return "auth_keys"
}

// API 名称和地址
type API struct {
	ID   uint   `gorm:"column:id;primaryKey;comment:主键" json:"id"`
	Name string `gorm:"type:varchar(255);column:name;comment:API名称" json:"name"`
	URI  string `gorm:"type:varchar(1000);column:uri;comment:uri地址" json:"uri"`
	Keys []Key  `gorm:"many2many:auth_key_apis" json:"keys"`
}

// TableName 设置表名
func (api *API) TableName() string {
	return "auth_api"
}

// APIs 系统存在的API路径
var APIs = []API{
	{Name: "获取所有IDC的主机（旧的接口）", URI: "/api/v2/assets/all"},
	{Name: "获取所有的主机", URI: "/api/v2/assets/all/hosts"},
	{Name: "监控主机数据（可以通过IDC过滤)", URI: "/api/v2/asset/monitor/hosts"},
	{Name: "通过IP查询内网解析域名", URI: "/api/v2/query/pvtz/r"},
	// 数据库接口
	{Name: "保存备份信息接口", URI: "/api/v1/dbbackups/records"},
	{Name: "用户权限数据库列表", URI: "/api/v1/mysql/user_priv_db_table_list"},
	{Name: "数据库表列信息", URI: "/api/v1/mysql/db_table_columns"},
	{Name: "ci实例列表和库名信息", URI: "/api/v1/mysql/instances_databases"},
	{Name: "ci实例库表信息详情", URI: "/api/v1/mysql/instance_database_tables"},
}

// SyncAPIsToDB 同步API到数据库
func SyncAPIsToDB() (err error) {
	for i := range APIs {
		exist := API{}
		err = app.DB().Where("uri = ?", APIs[i].URI).Take(&exist).Error
		if err == nil {
			if exist.Name != APIs[i].Name {
				err = app.DB().Model(&exist).Update("name", APIs[i].Name).Error
			}
			continue
		} else if errors.Is(err, gorm.ErrRecordNotFound) {
			err = app.DB().Create(&APIs[i]).Error
		}
	}
	return
}

// KeyAuthForm key登录表单
type KeyAuthForm struct {
	Key    string `json:"key_name" form:"key_name" binding:"required"`
	Secret string `json:"key_secret" form:"key_secret" binding:"required"`
}

// GrantKeyAPIsForm 授权API
type GrantKeyAPIsForm struct {
	IDs []int `json:"ids"`
}

// KeyForm key表单
type KeyForm struct {
	Name   string `json:"name" binding:"required,max=255"`
	Secret string `json:"secret" binding:"max=255"`
	Remark string `json:"remark" binding:"max=500"`
}

var (
	ErrKeyExists   = errors.New("Key-URI已经存在")
	ErrKeyAuthFail = errors.New("key验证失败")
)

// CreateKey 添加key
func (form *KeyForm) Create() (err error) {
	err = app.DB().Select("id").Where("name = ?", form.Name).Take(&Key{}).Error
	if err == nil {
		return ErrKeyExists
	}
	secret := form.Secret
	if secret == "" {
		secret = utils.RandomPassword()
	}
	if err == gorm.ErrRecordNotFound {
		err = app.DB().Create(&Key{
			Name:   form.Name,
			Remark: form.Remark,
			Secret: secret,
		}).Error
	}
	return
}

// GetKeys 分页获取key
func GetKeys(offset, limit int, keyword *string) (count int64, keys []Key, err error) {
	dbop := app.DB()
	if keyword != nil && *keyword != "" {
		dbop = db.MLike(dbop, *keyword, "name", "remark")
	}
	err = dbop.Model(&Key{}).Count(&count).Order("updated_at DESC").Order("id").Find(&keys).Error
	if err != nil {
		return
	}
	for i, key := range keys {
		err = app.DB().Model(&key).Association("APIs").Find(&keys[i].APIs)
		if err != nil {
			return
		}
	}
	return
}

// GetKeyByID 通过ID获取key
func GetKeyByID(id int) (key Key, err error) {
	err = app.DB().Where("id = ?", id).Take(&key).Error
	if err != nil {
		return
	}
	err = app.DB().Model(&key).Association("APIs").Find(&key.APIs)
	return
}

// Delete 删除key
func (key *Key) Delete() (err error) {
	dbop := app.DB().Begin()
	err = dbop.Model(key).Association("APIs").Clear()
	if err != nil {
		dbop.Rollback()
		return err
	}
	err = dbop.Delete(key).Error
	if err != nil {
		dbop.Rollback()
		return err
	}
	err = dbop.Commit().Error
	if err != nil {
		dbop.Rollback()
	}
	return
}

// Update 更新key
func (key *Key) Update(form *KeyForm) (err error) {
	updateMap := map[string]any{}
	if form.Name != key.Name {
		exist := Key{}
		err = app.DB().Where("name = ? ", form.Name).Take(&exist).Error
		if err == nil && exist.ID != key.ID {
			return ErrKeyExists
		} else if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		} else {
			err = nil
			updateMap["name"] = form.Name
		}
	}
	if form.Secret != "" && form.Secret != key.Secret {
		updateMap["secret"] = form.Secret
	}
	if form.Remark != key.Remark {
		updateMap["remark"] = form.Remark
	}
	if len(updateMap) > 0 {
		err = app.DB().Model(key).Updates(updateMap).Error
	}
	return
}

// KeyAuth 验证key是否存在
func KeyAuth(name, secret string) (key Key, err error) {
	err = app.DB().Where("name = ? AND secret = ?", name, secret).Take(&key).Error
	return
}

// key验证
func CheckKey(token, uri string) (err error) {
	keyObject := Key{}
	key, err := ParseKeyToken(token)
	if err != nil {
		err = app.DB().Where("name = ? AND secret = ?", "token", token).Take(&keyObject).Error
		if err != nil {
			err = ErrAuthFail
			return
		}
	} else {
		keyObject.ID = key.ID
		keyObject.Name = key.Name
	}
	apis := []API{}
	err = app.DB().Where("uri = ?", uri).Model(&keyObject).Association("APIs").Find(&apis)
	if err != nil {
		err = ErrAuthFail
		return
	}
	err = ErrAuthFail
	for i := range apis {
		if apis[i].URI == uri {
			app.Log().Info(key.Name + "请求接口：" + apis[i].Name)
			err = nil
		}
	}
	return
}

// GetAllAPIs 获取所有api
func GetAllAPIs() (apis []API, err error) {
	err = app.DB().Order("name").Find(&apis).Error
	return
}

// GetAPIsByIDs 通过id获取多个api对象
func GetAPIsByIDs(ids []int) (apis []API, err error) {
	err = app.DB().Order("name").Where("id IN (?)", ids).Find(&apis).Error
	return
}

// GrantAPIs 授权API
func (key Key) GrantAPIs(apis *[]API) (err error) {
	err = app.DB().Model(&key).Association("APIs").Replace(apis)
	return
}
