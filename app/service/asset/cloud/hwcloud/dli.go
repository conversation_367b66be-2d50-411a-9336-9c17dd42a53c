package hwcloud

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/pkg/utils"

	"github.com/huaweicloud/huaweicloud-sdk-go-v3/core/auth/basic"
	dli "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/dli/v1"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/dli/v1/model"
	region "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/dli/v1/region"
)

func GetAllDILCPUCount() (cpuTotal uint, err error) {
	accounts := []Account{}
	err = app.DB().Where("cloud_type = ?", asset.HuaweiCloudType).Find(&accounts).Error
	if err != nil {
		return
	}
	for _, account := range accounts {
		cpuTotal, err = account.GetDILCPUCount()
		if err != nil {
			return
		}
	}
	return
}

func (a Account) GetDILCPUCount() (cpuTotal uint, err error) {
	dcs, err := asset.GetDatacentersByCloudType(asset.HuaweiCloudType)
	if err != nil {
		return
	}
	for _, dc := range dcs {
		app.Log().Info("开始同步", "账户", a.Name, "数据中心", dc.Name)
		count, err := a.getDILCPUCount(dc, 0)
		app.Log().Info("完成同步", "账户", a.Name, "数据中心", dc.Name, "CPU数量", count)
		if err != nil {
			continue
		}
		cpuTotal += count
	}
	return
}

func (a Account) getDILCPUCount(dc asset.Datacenter, offset int32) (cpuTotal uint, err error) {
	secret, _ := utils.Base64Decode(a.AccessSecret)
	auth, err := basic.NewCredentialsBuilder().WithAk(a.AccessKey).WithSk(secret).SafeBuild()
	if err != nil {
		return
	}
	regionID, err := region.SafeValueOf(dc.Code)
	if err != nil {
		return
	}
	clientBuild, err := dli.DliClientBuilder().WithRegion(regionID).WithCredential(auth).SafeBuild()
	if err != nil {
		return
	}
	client := dli.NewDliClient(clientBuild)
	request := &model.ListElasticResourcePoolsRequest{}
	limitRequest := int32(100)
	request.Limit = &limitRequest
	request.Offset = &offset
	response, err := client.ListElasticResourcePools(request)
	if err != nil {
		return
	}
	for _, pool := range *response.ElasticResourcePools {
		if pool.PrepayCu != nil {
			cpuTotal += uint(*pool.PrepayCu)
		}
	}
	if response.Count != nil && *response.Count > offset+limitRequest {
		var pageCpu uint
		pageCpu, err = a.getDILCPUCount(dc, offset+limitRequest)
		if err != nil {
			return
		}
		cpuTotal += pageCpu
	}
	return
}
