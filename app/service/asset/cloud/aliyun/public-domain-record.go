package aliyun

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/asset/cloud"
	"cmdb/pkg/utils"
	"errors"
	"time"

	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/requests"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/alidns"
	"gorm.io/gorm"
)

func (a Account) syncPublicDomainRecords() (result cloud.SyncResults, err error) {
	domains := []asset.PublicDomain{}
	err = app.DB().Where("account_id = ? ", a.ID).Find(&domains).Error
	if err != nil {
		return
	}
	for _, domain := range domains {
		r, err := a.syncPageDomainRecords(domain.Name, domain.DomainID, 1)
		sr := cloud.SyncResult{
			Key: domain.Name + "(" + domain.DomainID + ")",
		}
		if err == nil {
			sr.Result = "同步成功"
			// 回收过期主机
			err = asset.ExpiredPublicDomainRecords(domain.DomainID, time.Now().Add(-10*time.Minute))
			if err != nil {
				sr.Result = "同步成功，但是回收Domain Records失败" + err.Error()
			}
		} else {
			sr.Result = r + " " + err.Error()
		}
		result = append(result, sr)
	}

	return
}

func (a Account) syncPageDomainRecords(domainName, domainID string, page int) (result string, err error) {
	accessSecret, _ := utils.Base64Decode(a.AccessSecret)
	client, err := alidns.NewClientWithAccessKey("cn-beijing", a.AccessKey, accessSecret)
	if err != nil {
		result = "创建客户端失败"
		return
	}
	request := alidns.CreateDescribeDomainRecordsRequest()
	request.Scheme = "https"
	request.PageSize = requests.NewInteger(100)
	if page < 1 {
		page = 1
	}
	request.PageNumber = requests.NewInteger(page)
	request.DomainName = domainName
	response, err := client.DescribeDomainRecords(request)
	if err != nil {
		return
	}
	now := time.Now()
	for _, record := range response.DomainRecords.Record {
		exist := asset.PublicDomainRecord{}
		err = app.DB().Where("domain_id = ? AND account_id = ? AND record_id = ?", domainID, a.ID, record.RecordId).Take(&exist).Error
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = app.DB().Create(&asset.PublicDomainRecord{
				AccountID: a.ID,
				DomainID:  domainID,
				RecordID:  record.RecordId,
				Rr:        record.RR,
				Value:     record.Value,
				Remark:    record.Remark,
				TTL:       int(record.TTL),
				Type:      record.Type,
				Line:      record.Line,
				Status:    record.Status,
				CreatedAt: now,
				UpdatedAt: now,
				SyncTime:  now,
			}).Error
		} else if err == nil {
			updateMap := map[string]any{
				"sync_time": now,
			}
			if exist.Rr != record.RR {
				updateMap["rr"] = record.RR
			}
			if exist.Value != record.Value {
				updateMap["value"] = record.Value
			}
			if exist.Remark != record.Remark {
				updateMap["remark"] = record.Remark
			}
			if exist.TTL != int(record.TTL) {
				updateMap["ttl"] = int(record.TTL)
			}
			if exist.Type != record.Type {
				updateMap["type"] = record.Type
			}
			if exist.Status != record.Status {
				updateMap["status"] = record.Status
			}
			err = app.DB().Model(&exist).Updates(updateMap).Error
		}
		if err != nil {
			return
		}
	}
	if response.TotalCount > int64(page*100) {
		result, err = a.syncPageDomainRecords(domainName, domainID, page+1)
	}
	return
}
