package robot

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/monitor/elasticsearch"
	"cmdb/app/service/monitor/prometheus"
	"cmdb/app/service/note"
	"fmt"
	"strconv"
	"strings"
	"time"
)

func GetExceptions(usage string, params ...string) (msg HTTPBodyMarkdown) {
	msg = HTTPBodyMarkdown{
		MsgType: "markdown",
	}
	msg.Markdown.Title = "查询异常检查"
	contents := []string{}
	duration := 5
	var err error
	if len(params) > 0 {
		duration, err = strconv.Atoi(params[0])
		if err != nil {
			msg.Markdown.Text = "非法参数:" + strings.Join(params, " ")
			return
		}
	}
	endTime := time.Now()
	startTime := endTime.Add(-time.Duration(duration) * time.Minute)
	contents = append(contents, "# "+startTime.Format(time.DateTime)+"~"+endTime.Format(time.DateTime)+" 异常检查")
	// ping丢包检查

	contents = append(contents, "## ping丢包")
	if len(prometheus.PingPrometheusHosts) == 0 {
		contents = append(contents, ">无丢包")
	} else {
		for i := range prometheus.PingPrometheusHosts {
			losts, err := prometheus.PingPrometheusHosts[i].GetPingLost(startTime, endTime)
			if err == nil {
				for j := range losts {
					var useFor string
					app.DB().Model(&asset.Host{}).Select("name").Limit(1).Scan(&useFor)
					contents = append(contents, fmt.Sprintf("- %s -> %s%s 丢包(时间/个数):", losts[j].Source, losts[j].Instance, "("+useFor+")"))
					for k := range losts[j].Values {
						contents = append(contents, fmt.Sprintf("> %s / %d 个\n", losts[j].Values[k].Timestamp.Time().Format(time.DateTime), int64(losts[j].Values[k].Value)))
					}
				}
			}
			contents = append(contents, "---")
		}
	}
	// elk日志检查
	contents = append(contents, "## 访问日志")
	logs, err := note.Get5xxLogs("", startTime, endTime)
	if err != nil {
		app.Log().Error("查询5xx失败", "err", err)
	} else {
		if len(logs) == 0 {
			contents = append(contents, ">无异常")
		} else {
			for i := range logs {
				contents = append(contents, fmt.Sprintf("- 域名:%s \n", logs[i].HttpHost))
				for j := range logs[i].StatusCounts {
					contents = append(contents, fmt.Sprintf("> 状态码 %d 转发地址(数量):", logs[i].StatusCounts[j].Status))
					for k := range logs[i].StatusCounts[j].UpstreamAddrCounts {
						contents = append(contents, fmt.Sprintf("%s (%d) ;\n", logs[i].StatusCounts[j].UpstreamAddrCounts[k].UpstreamAddr, logs[i].StatusCounts[j].UpstreamAddrCounts[k].Count))
					}
				}
			}
		}
	}
	contents = append(contents, "---")

	contents = append(contents, "## logs-center")
	// logs-center检查
	if len(elasticsearch.LogCenterErrorTypes) == 0 {
		contents = append(contents, ">无异常")
	} else {
		for i := range elasticsearch.LogCenterErrorTypes {
			stats, addrStats, err := elasticsearch.LogCenterErrorTypes[i].CheckError(startTime, endTime)
			if err == nil {
				for i := range stats {
					contents = append(contents, fmt.Sprintf("> 应用：%s 类型：%s(%s) 数量：%d\n", stats[i].Domain, stats[i].StatSummary, stats[i].ErrorType, stats[i].Count))
				}
				if len(stats) > 0 {
					contents = append(contents, "---")
				}
				for i := range addrStats {
					contents = append(contents, fmt.Sprintf("> 应用：%s 类型：%s(%s) 地址：%s 数量：%d\n", addrStats[i].Domain, addrStats[i].StatSummary, addrStats[i].ErrorType, addrStats[i].Addr, addrStats[i].Count))
				}
				if len(addrStats) > 0 {
					contents = append(contents, "---")
				}
			} else {
				app.Log().Error("查询logs-center日志异常", "err", err)
			}
		}
	}
	contents = append(contents, "---")
	contents = append(contents, "## nginx_error")
	if len(elasticsearch.NginxErrorTypes) == 0 {
		contents = append(contents, ">无异常")
	} else {
		for i := range elasticsearch.NginxErrorTypes {
			stats, err := elasticsearch.NginxErrorTypes[i].CheckNginxError(startTime, endTime)
			if err == nil {
				for i := range stats {
					contents = append(contents, fmt.Sprintf("> 域名：%s 应用：%s(%s) 数量：%d\n", stats[i].Domain, stats[i].App, stats[i].ErrorType, stats[i].Count))
				}
			} else {
				app.Log().Error("查询nginx_error日志异常", "err", err)
			}
		}
	}
	msg.Markdown.Text = strings.Join(contents, "\n")
	return
}
