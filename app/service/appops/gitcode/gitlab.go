package gitcode

import (
	"cmdb/app"
	"cmdb/pkg/db"
	"encoding/base64"
	"errors"
	"time"

	goGitlab "github.com/xanzy/go-gitlab"
	"gorm.io/gorm"
)

var (
	ErrGitlabExist = errors.New("gitlab已经存在")
)

type Gitlab struct {
	ID        uint           `gorm:"column:id;primaryKey;comment:ID" json:"id"`
	Name      string         `gorm:"type:varchar(255);column:name;index;comment:名称" json:"name"`
	Remark    string         `gorm:"type:varchar(255);column:remark;comment:描述" json:"remark"`
	Address   string         `gorm:"type:varchar(255);column:address;comment:地址" json:"address"`
	Token     string         `gorm:"type:varchar(255);column:token;comment:token" json:"-"`
	UpdatedAt time.Time      `gorm:"column:updated_at;index;comment:同步时间" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at;comment:删除时间" json:"deleted_at"`
}

func (Gitlab) TableName() string {
	return "asset_gitcode_gitlabs"
}

type GitlabForm struct {
	Name    string `json:"name" binding:"required,max=255"`
	Remark  string `json:"remark" binding:"max=255"`
	Address string `json:"address" binding:"required,max=255"`
	Token   string `json:"token" binding:"max=255"`
}

func (form GitlabForm) Create() (err error) {
	// 先判断是否存在，存在就返回已经存在
	if err = app.DB().Where("name = ?", form.Name).Take(&Gitlab{}).Error; err == nil {
		err = ErrGitlabExist
		return
	} else if errors.Is(err, gorm.ErrRecordNotFound) {
		if err = app.DB().Create(&Gitlab{
			Name:    form.Name,
			Remark:  form.Remark,
			Address: form.Address,
			Token:   base64.RawStdEncoding.EncodeToString([]byte(form.Token)), //  form.Token,
		}).Error; err != nil {
			return
		}
	}
	return
}

func GetGitlabs(offset, limit int, keyword *string) (count int64, gitlabs []Gitlab, err error) {
	dbop := app.DB()
	if keyword != nil {
		dbop = db.MLike(dbop, *keyword, "name", "remark", "address")
	}
	err = dbop.Model(&Gitlab{}).Count(&count).Order("name").Offset(offset).Limit(limit).Find(&gitlabs).Error
	return
}

func GetGitlabByID(id int) (gitlab Gitlab, err error) {
	err = app.DB().Where("id = ?", id).Take(&gitlab).Error
	return
}

func (gitlab Gitlab) Update(form GitlabForm) (err error) {
	updateMap := map[string]any{}
	if form.Name != gitlab.Name {
		exist := Gitlab{}
		err = app.DB().Where("name = ?", form.Name).Take(&exist).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		} else if err == nil && exist.ID != gitlab.ID {
			err = ErrGitlabExist
			return
		}
		updateMap["name"] = form.Name
	}
	if form.Remark != gitlab.Remark {
		updateMap["remark"] = form.Remark
	}
	if form.Address != gitlab.Address {
		updateMap["address"] = form.Address
	}
	if base64.RawStdEncoding.EncodeToString([]byte(form.Token)) != gitlab.Token {
		updateMap["token"] = base64.RawStdEncoding.EncodeToString([]byte(form.Token))
	}
	if len(updateMap) > 0 {
		err = app.DB().Model(&gitlab).Updates(updateMap).Error
	}
	return
}

func (gitlab Gitlab) Delete() (err error) {
	err = app.DB().Delete(&gitlab).Error
	return
}

func (gitlab Gitlab) GetClient() (*goGitlab.Client, error) {
	// 解码 base64 格式的 token
	tokenBytes, err := base64.RawStdEncoding.DecodeString(gitlab.Token)
	if err != nil {
		return nil, err
	}
	app.Log().Info("token", "token", string(tokenBytes))
	// 创建 gitlab 客户端
	client, err := goGitlab.NewClient(string(tokenBytes), goGitlab.WithBaseURL(gitlab.Address))
	if err != nil {
		return nil, err
	}

	return client, nil
}

func GetAllGitlabs() (gitlabs []Gitlab, err error) {
	err = app.DB().Order("name").Find(&gitlabs).Error
	return
}

func SyncAllGitlab() (err error) {
	// 获取所有 gitlab
	gitlabs, err := GetAllGitlabs()
	if err != nil {
		return
	}
	for _, gitlab := range gitlabs {
		err = gitlab.SyncGroups()
		if err != nil {
			app.Log().Error("同步GitCode组时，获取组信息失败：", "err", err)
		}
		err = gitlab.SyncProjects()
		if err != nil {
			app.Log().Error("同步GitCode项目时，获取项目信息失败：", "err", err)
		}
	}
	return
}
