package asset

import (
	"cmdb/app"
	"cmdb/app/service"
	"cmdb/app/service/asset"
	"cmdb/app/service/asset/cloud/aliyun"
	"cmdb/app/service/asset/cloud/hwcloud"
	"cmdb/app/service/audit"
	"errors"
	"strconv"
	"strings"
	"sync"

	"github.com/gin-gonic/gin"
)

// 定义主机行结构体
type HostRow struct {
	asset.Host
	Datacenter        string `json:"datacenter"`
	RegionID          string `json:"region_id"`
	Account           string `json:"account"`
	ResourceGroupName string `json:"resource_group_name"`
}

// 获取主机列表
func GetHosts(c *gin.Context) {
	var params asset.GetHostsParams
	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.Default<PERSON>uery("limit", "10"))
	// 计算偏移量
	offset := (page - 1) * limit
	params.Offset = offset
	params.Limit = limit
	//  获取查询参数
	keyword := strings.TrimSpace(c.Query("keyword"))
	if keyword != "" {
		params.Keyword = &keyword
	}
	if resourceGroupID := strings.TrimSpace(c.Query("resource_group_id")); resourceGroupID != "" {
		params.ResourceGroupID = &resourceGroupID
	}
	if ip := c.Query("ip"); ip != "" {
		params.IP = &ip
	}
	if status := c.Query("status"); status != "" {
		status, err1 := strconv.Atoi(status)
		if err1 == nil {
			params.Status = &status
		}
	}
	if datacenterID := c.Query("datacenter_id"); datacenterID != "" {
		datacenterID, err1 := strconv.Atoi(datacenterID)
		if err1 == nil {
			params.DatacenterID = &datacenterID
		}
	}
	if accountID := c.Query("account_id"); accountID != "" {
		accountID, err1 := strconv.Atoi(accountID)
		if err1 == nil {
			params.AccountID = &accountID
		}
	}
	if hostType := c.Query("host_type"); hostType != "" {
		hostType, err1 := strconv.Atoi(hostType)
		if err1 == nil {
			params.HostType = &hostType
		}
	}
	if tagIDs := c.Query("tag_ids"); tagIDs != "" {
		ids := strings.Split(tagIDs, ",")
		for _, idStr := range ids {
			tagIDs, err1 := strconv.Atoi(idStr)
			if err1 == nil {
				params.TagIDs = append(params.TagIDs, tagIDs)
			}
		}
	}
	if val := c.Query("ping_monitor"); val != "" {
		if val == "true" {
			tmpVal := true
			params.PingMonitor = &tmpVal
		} else if val == "false" {
			tmpVal := false
			params.PingMonitor = &tmpVal
		}
	}
	if val := c.Query("n9e_monitor"); val != "" {
		if val == "true" {
			tmpVal := true
			params.N9eMonitor = &tmpVal
		} else if val == "false" {
			tmpVal := false
			params.N9eMonitor = &tmpVal
		}
	}
	if val := c.Query("is_amd"); val != "" {
		if val == "true" {
			tmpVal := true
			params.IsAMD = &tmpVal
		} else if val == "false" {
			tmpVal := false
			params.IsAMD = &tmpVal
		}
	}
	// 获取主机列表数据
	count, data, err := asset.GetHosts(params)
	if err != nil {
		app.Log().Error("获取主机列表数据失败", "err", err)
		app.FailedResponseMsg(c, "获取主机列表数据失败")
		return
	}
	// 将数据转换为HostRow结构体
	newData := make([]HostRow, len(data))
	if len(data) > 0 {
		tmpCloudAccountStore := sync.Map{}
		tmpDatacenterStore := sync.Map{}
		tmpDatacenterCodeStore := sync.Map{}
		tmpResourceGroupStore := sync.Map{}
		// 异步获取数据中心名称和云账户名称
		wg := sync.WaitGroup{}
		wg.Add(len(data))
		for i := range data {
			go func() {
				newData[i].Host = data[i]
				// 获取云账户名称
				if val, ok := tmpCloudAccountStore.Load(data[i].AccountID); ok {
					newData[i].Account, _ = val.(string)
				} else {
					account, err1 := asset.GetAccountNameByID(int(data[i].AccountID))
					if err1 == nil {
						newData[i].Account = account
						tmpCloudAccountStore.Store(data[i].AccountID, account)
					}
				}
				// 获取数据中心名称
				if val, ok := tmpDatacenterStore.Load(data[i].DatacenterID); ok {
					newData[i].Datacenter, _ = val.(string)
				} else {
					datacenter, err1 := asset.GetDatacenterNameByID(int(data[i].DatacenterID))
					if err1 == nil {
						newData[i].Datacenter = datacenter
						tmpDatacenterStore.Store(data[i].DatacenterID, datacenter)
					}
				}
				// 获取数据中心code
				if val, ok := tmpDatacenterCodeStore.Load(data[i].DatacenterID); ok {
					newData[i].RegionID, _ = val.(string)
				} else {
					code, err1 := asset.GetDatacenterCodeByID(int(data[i].DatacenterID))
					if err1 == nil {
						newData[i].RegionID = code
						tmpDatacenterCodeStore.Store(data[i].DatacenterID, code)
					}
				}
				// 获取资源组名称
				if val, ok := tmpResourceGroupStore.Load(data[i].ResourceGroupID); ok {
					newData[i].ResourceGroupName, _ = val.(string)
				} else {
					resourceGroupName, err1 := asset.GetResourceGroupNameByID(data[i].ResourceGroupID)
					if err1 == nil {
						newData[i].ResourceGroupName = resourceGroupName
						tmpResourceGroupStore.Store(data[i].ResourceGroupID, resourceGroupName)
					}
				}
				// 获取主机标签
				newData[i].Tags, _ = data[i].GetTags()
				wg.Done()
			}()
		}
		wg.Wait()
	}
	// 返回主机列表数据
	app.SuccessResponseDataCount(c, newData, count)
}

func BatTagHosts(c *gin.Context) {
	var form asset.TagHostsForm
	err := c.ShouldBind(&form)
	if err != nil {
		app.Log().Error("存在非法字段", "err", err)
		app.FailedResponseMsg(c, "存在非法字段")
		return
	}
	err = form.Do()
	if err != nil {
		app.Log().Error("批量设置标签失败", "err", err)
		app.FailedResponseMsg(c, "操作失败")
		return
	}
	audit.LogAPIOP(c, "资产管理", "批量打标签")
	app.SuccessResponseMsg(c, "操作成功")
}

func CreateHost(c *gin.Context) {
	var form asset.HostForm
	err := c.ShouldBind(&form)
	if err != nil {
		app.Log().Error("存在非法字段", "err", err)
		app.FailedResponseMsg(c, "存在非法字段")
		return
	}
	err = form.Create()
	if err != nil {
		msg := "添加失败"
		if errors.Is(err, asset.ErrHostExist) {
			msg = err.Error()
		} else {
			app.Log().Error("添加主机失败", "err", err)
		}
		app.FailedResponseMsg(c, msg)
		return
	}
	audit.LogAPIOP(c, "资产管理", "添加主机："+form.Name+" "+form.IP)
	app.SuccessResponseMsg(c, "操作成功")
}

func UpdateHost(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.Log().Error("参数错误", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	var form asset.HostForm
	err = c.ShouldBind(&form)
	if err != nil {
		app.Log().Error("存在非法字段", "err", err)
		app.FailedResponseMsg(c, "存在非法字段")
		return
	}
	host, err := asset.GetHostByID(id)
	if err != nil {
		app.Log().Error("主机不存在", "err", err)
		app.FailedResponseMsg(c, "主机不存在")
		return
	}
	err = host.Update(form)
	if err != nil {
		msg := "更新失败"
		if errors.Is(err, asset.ErrHostExist) {
			msg = err.Error()
		} else {
			app.Log().Error("更新主机失败", "err", err)
		}
		app.Log().Error("操作失败", "err", err)
		app.FailedResponseMsg(c, msg)
		return
	}
	audit.LogAPIOP(c, "资产管理", "更新主机:"+form.Name)
	app.SuccessResponseMsg(c, "更新成功")
}

func DeleteHost(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.Log().Error("参数错误", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	host, err := asset.GetHostByID(id)
	if err != nil {
		app.Log().Error("获取主机失败", "err", err)
		app.FailedResponseMsg(c, "主机不存在")
		return
	}
	err = host.Delete()
	if err != nil {
		msg := "删除失败"
		if errors.Is(err, asset.ErrHostExist) {
			msg = err.Error()
		} else {
			app.Log().Error("删除主机失败", "err", err)
		}
		app.Log().Error("操作失败", "err", err)
		app.FailedResponseMsg(c, msg)
		return
	}
	audit.LogAPIOP(c, "资产管理", "删除主机:"+c.Param("id"))
	app.SuccessResponseMsg(c, "删除成功")
}

func GetAllHosts(c *gin.Context) {
	data, err := asset.GetAllHosts()
	if err != nil {
		app.Log().Error("获取主机列表失败", "err", err)
		app.V2FailedResponseMsg(c, "获取主机列表失败")
		return
	}
	app.V2SuccessResponseData(c, data)
}
func GetAllOldHosts(c *gin.Context) {
	data, err := asset.GetAllOldHosts()
	if err != nil {
		app.Log().Error("获取主机列表失败", "err", err)
		app.V2FailedResponseMsg(c, "获取主机列表失败")
		return
	}
	app.V2SuccessResponseData(c, data)
}

func BatSetMonitorHost(c *gin.Context) {
	var form asset.BatSetMonitorHostForm
	if err := c.ShouldBindJSON(&form); err != nil {
		app.Log().Error("参数错误", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	err := form.Do()
	if err != nil {
		app.Log().Error("设置监控主机失败", "err", err)
		app.FailedResponseMsg(c, "设置监控主机失败")
		return
	}
	audit.LogAPIOP(c, "资产管理", "批量设置监控主机")
	app.SuccessResponseMsg(c, "设置成功")
}

func GetLoadbalancersByHost(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.Log().Error("参数错误", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	host, err := asset.GetHostByID(id)
	if err != nil {
		app.Log().Error("获取主机失败", "err", err)
		app.FailedResponseMsg(c, "主机不存在")
		return
	}
	dc, err := host.GetDatacenter()
	if err != nil {
		app.Log().Error("获取数据中心失败", "err", err)
		app.FailedResponseMsg(c, "获取数据中心失败")
		return
	}
	ca, err := host.GetCloudAccount()
	if err != nil {
		app.Log().Error("获取云账户失败", "err", err)
		app.FailedResponseMsg(c, "获取云账户失败")
		return
	}
	var lbsRows []LoadbalancerRow
	var lbs []asset.Loadbalancer
	switch ca.CloudType {
	case asset.AliyunCloudType:
		lbs, err = aliyun.CloudAccount(ca).GetLoadbalancersByIP(dc, host.IP, 0)
		if err != nil {
			app.Log().Error("获取负载均衡失败", "err", err)
			app.FailedResponseMsg(c, "获取负载均衡失败")
			return
		}
	case asset.HuaweiCloudType:
		lbs, err = hwcloud.CloudAccount(ca).GetLoadbalancersByIP(dc, host.IP, nil)
		if err != nil {
			app.Log().Error("获取负载均衡失败", "err", err)
			app.FailedResponseMsg(c, "获取负载均衡失败")
			return
		}
	}
	for _, lb := range lbs {
		resourceGroupName, _ := asset.GetResourceGroupNameByID(lb.ResourceGroupID)
		lbsRows = append(lbsRows, LoadbalancerRow{
			Loadbalancer:      lb,
			Datacenter:        dc.Name,
			Account:           ca.Name,
			RegionID:          dc.Code,
			ResourceGroupName: resourceGroupName,
		})
	}
	app.SuccessResponseData(c, lbsRows)
}

type HostAssetRow struct {
	AssetInfos    []service.AssetInfo     `json:"asset_infos"`
	Loadbalancers []LoadbalancerRow       `json:"loadbalancers"`
	DDoSDomains   []asset.CloudDDosDomain `json:"ddos_domains"`
}

func GetHostAssets(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.Log().Error("参数错误", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	host, err := asset.GetHostByID(id)
	if err != nil {
		app.Log().Error("获取主机失败", "err", err)
		app.FailedResponseMsg(c, "主机不存在")
		return
	}
	var lbsRows []LoadbalancerRow
	var lbs []asset.Loadbalancer
	var ddos []asset.CloudDDosDomain
	var assets []service.AssetInfo
	if host.HostType > asset.PhysicalHostType {
		dc, err := host.GetDatacenter()
		if err != nil {
			app.Log().Error("获取数据中心失败", "err", err)
			app.FailedResponseMsg(c, "获取数据中心失败")
			return
		}
		ca, err := host.GetCloudAccount()
		if err != nil {
			app.Log().Error("获取云账户失败", "err", err)
			app.FailedResponseMsg(c, "获取云账户失败")
			return
		}
		switch ca.CloudType {
		case asset.AliyunCloudType:
			lbs, err = aliyun.CloudAccount(ca).GetLoadbalancersByIP(dc, host.IP, 0)
			if err != nil {
				app.Log().Error("获取负载均衡失败", "err", err)
				app.FailedResponseMsg(c, "获取负载均衡失败")
				return
			}
			// DDOS
			ddos, err = asset.GetCloudDDosDomainByIP(host.PublicIP)
			if err != nil {
				app.Log().Error("获取DDOS失败", "err", err)
				app.FailedResponseMsg(c, "获取DDOS失败")
				return
			}
		case asset.HuaweiCloudType:
			lbs, err = hwcloud.CloudAccount(ca).GetLoadbalancersByIP(dc, host.IP, nil)
			if err != nil {
				app.Log().Error("获取负载均衡失败", "err", err)
				app.FailedResponseMsg(c, "获取负载均衡失败")
				return
			}
		}
		for _, lb := range lbs {
			resourceGroupName, _ := asset.GetResourceGroupNameByID(lb.ResourceGroupID)
			lbsRows = append(lbsRows, LoadbalancerRow{
				Loadbalancer:      lb,
				Datacenter:        dc.Name,
				Account:           ca.Name,
				RegionID:          dc.Code,
				ResourceGroupName: resourceGroupName,
			})
		}
	}
	if host.PublicIP != "" {
		assets, err = service.Search(host.PublicIP)
		if err != nil {
			app.Log().Error("获取资产失败", "err", err)
			app.FailedResponseMsg(c, "获取资产失败")
			return
		}
	}
	assets1, err := service.Search(host.IP)
	if err != nil {
		app.Log().Error("获取资产失败", "err", err)
		app.FailedResponseMsg(c, "获取资产失败")
		return
	}
	assets = append(assets, assets1...)
	app.SuccessResponseData(c, HostAssetRow{
		Loadbalancers: lbsRows,
		AssetInfos:    assets,
		DDoSDomains:   ddos,
	})
}
