package aliyun

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/asset/cloud"
	"cmdb/pkg/utils"
	"errors"
	"time"

	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/requests"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/alb"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/nlb"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/slb"
	"gorm.io/gorm"
)

func (a Account) syncLoadbanlancers() (results cloud.SyncResults, err error) {
	dcs, err := asset.GetDatacentersByCloudType(asset.AliyunCloudType)
	if err != nil {
		return
	}
	for _, dc := range dcs {
		r, err := a.syncRegionSLBs(dc, 1)
		sr := cloud.SyncResult{
			Key: dc.Name,
		}
		if err == nil {
			sr.Result = "SLB同步成功"
			// 回收过期SLB
			err = a.expiredLoadbanlancers(time.Now().Add(-10*time.Minute), dc, "slb")
			if err != nil {
				sr.Result = "同步成功，但是回收SLB失败" + err.Error()
			}
		} else {
			sr.Result = r + " " + err.Error()
		}
		results = append(results, sr)
		r1, err := a.syncRegionALBs(dc, "")
		sr1 := cloud.SyncResult{
			Key: dc.Name,
		}
		if err == nil {
			sr1.Result = "ALB同步成功"
			// 回收过期SLB
			err = a.expiredLoadbanlancers(time.Now().Add(-10*time.Minute), dc, "alb")
			if err != nil {
				sr1.Result = "同步成功，但是回收ALB失败" + err.Error()
			}
		} else {
			sr1.Result = r1 + " " + err.Error()
		}
		results = append(results, sr1)
		r2, err := a.syncRegionNLBs(dc, "")
		sr2 := cloud.SyncResult{
			Key: dc.Name,
		}
		if err == nil {
			sr2.Result = "NLB同步成功"
			// 回收过期SLB
			err = a.expiredLoadbanlancers(time.Now().Add(-10*time.Minute), dc, "nlb")
			if err != nil {
				sr2.Result = "同步成功，但是回收NLB失败" + err.Error()
			}
		} else {
			sr2.Result = r2 + " " + err.Error()
		}
		results = append(results, sr2)
	}
	return
}

func (a Account) GetLoadbalancersByIP(dc asset.Datacenter, ip string, page int) (lbs []asset.Loadbalancer, err error) {
	accessSecret, _ := utils.Base64Decode(a.AccessSecret)
	client, err := slb.NewClientWithAccessKey(dc.Code, a.AccessKey, accessSecret)
	if err != nil {
		err = errors.New("创建客户端失败")
		return
	}
	request := slb.CreateDescribeLoadBalancersRequest()
	request.Scheme = "https"
	request.PageSize = requests.NewInteger(100)
	if page < 1 {
		page = 1
	}
	request.RegionId = dc.Code
	request.ServerIntranetAddress = ip
	request.PageNumber = requests.NewInteger(page)
	response, err := client.DescribeLoadBalancers(request)
	if err != nil {
		err = errors.New("请求失败")
		return
	}
	if response.TotalCount == 0 {
		return
	}
	for _, lb := range response.LoadBalancers.LoadBalancer {
		lbs = append(lbs, asset.Loadbalancer{
			AccountID:        a.ID,
			DatacenterID:     dc.ID,
			LoadbalancerType: "slb",
			Host:             lb.Address,
			Name:             lb.LoadBalancerName,
			LoadbalancerID:   lb.LoadBalancerId,
			Status:           lb.LoadBalancerStatus,
			Spec:             lb.LoadBalancerSpec,
			ResourceGroupID:  lb.ResourceGroupId,
		})
	}
	if response.TotalCount > (page)*100 {
		var sublbs []asset.Loadbalancer
		sublbs, err = a.GetLoadbalancersByIP(dc, ip, page+1)
		if err != nil {
			return
		}
		lbs = append(lbs, sublbs...)
	}
	return
}

func (a Account) syncRegionSLBs(dc asset.Datacenter, page int) (result string, err error) {
	accessSecret, _ := utils.Base64Decode(a.AccessSecret)
	client, err := slb.NewClientWithAccessKey(dc.Code, a.AccessKey, accessSecret)
	if err != nil {
		result = "创建客户端失败"
		return
	}
	request := slb.CreateDescribeLoadBalancersRequest()
	request.Scheme = "https"
	request.PageSize = requests.NewInteger(100)
	if page < 1 {
		page = 1
	}
	request.RegionId = dc.Code
	request.PageNumber = requests.NewInteger(page)
	response, err := client.DescribeLoadBalancers(request)
	if err != nil {
		result = "请求失败"
		return
	}
	if response.TotalCount == 0 {
		return
	}
	err = a.saveSLBs(dc, response.LoadBalancers.LoadBalancer)
	if err != nil {
		return
	}
	if response.TotalCount > (page)*100 {
		result, err = a.syncRegionSLBs(dc, page+1)
	}
	return
}

func (a Account) syncRegionALBs(dc asset.Datacenter, token string) (result string, err error) {
	accessSecret, _ := utils.Base64Decode(a.AccessSecret)
	client, err := alb.NewClientWithAccessKey(dc.Code, a.AccessKey, accessSecret)
	if err != nil {
		result = "创建客户端失败"
		return
	}
	request := alb.CreateListLoadBalancersRequest()
	request.Scheme = "https"
	if token != "" {
		request.NextToken = token
	}
	request.RegionId = dc.Code
	response, err := client.ListLoadBalancers(request)
	if err != nil {
		result = "请求失败"
		return
	}
	if response.TotalCount == 0 {
		return
	}
	err = a.saveALBs(dc, response.LoadBalancers)
	if err != nil {
		return
	}
	if response.NextToken != "" {
		result, err = a.syncRegionALBs(dc, response.NextToken)
	}
	return
}

func (a Account) syncRegionNLBs(dc asset.Datacenter, token string) (result string, err error) {
	accessSecret, _ := utils.Base64Decode(a.AccessSecret)
	client, err := nlb.NewClientWithAccessKey(dc.Code, a.AccessKey, accessSecret)
	if err != nil {
		result = "创建客户端失败"
		return
	}
	request := nlb.CreateListLoadBalancersRequest()
	request.Scheme = "https"
	if token != "" {
		request.NextToken = token
	}
	request.RegionId = dc.Code
	response, err := client.ListLoadBalancers(request)
	if err != nil {
		result = "请求失败"
		return
	}
	if response.TotalCount == 0 {
		return
	}
	err = a.saveNLBs(dc, response.LoadBalancers)
	if err != nil {
		return
	}
	if response.NextToken != "" {
		result, err = a.syncRegionNLBs(dc, response.NextToken)
	}
	return
}
func (a Account) saveSLBs(dc asset.Datacenter, lbs []slb.LoadBalancer) (err error) {
	now := time.Now()
	for _, lb := range lbs {
		exist := asset.Loadbalancer{}
		createTime, _ := time.Parse("2006-01-02T15:04Z", lb.CreateTime)
		err = app.DB().Where("account_id = ? AND datacenter_id = ? AND loadbalancer_id = ?", a.ID, dc.ID, lb.LoadBalancerId).Take(&exist).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = app.DB().Create(&asset.Loadbalancer{
				AccountID:        a.ID,
				DatacenterID:     dc.ID,
				SyncTime:         now,
				LoadbalancerType: "slb",
				Host:             lb.Address,
				Name:             lb.LoadBalancerName,
				LoadbalancerID:   lb.LoadBalancerId,
				Status:           lb.LoadBalancerStatus,
				Spec:             lb.LoadBalancerSpec,
				ResourceGroupID:  lb.ResourceGroupId,
				CreatedAt:        createTime,
			}).Error
			if err != nil {
				return
			}
		} else if err == nil {
			updateMap := map[string]any{
				"sync_time": now,
			}
			if exist.Name != lb.LoadBalancerName {
				updateMap["name"] = lb.LoadBalancerName
			}
			if exist.Status != lb.LoadBalancerStatus {
				updateMap["status"] = lb.LoadBalancerStatus
			}
			if exist.Host != lb.Address {
				updateMap["host"] = lb.Address
			}
			if exist.Spec != lb.LoadBalancerSpec {
				updateMap["spec"] = lb.LoadBalancerSpec
			}
			if exist.ResourceGroupID != lb.ResourceGroupId {
				updateMap["resource_group_id"] = lb.ResourceGroupId
			}
			if exist.CreatedAt != createTime {
				updateMap["created_at"] = createTime
			}
			err = app.DB().Model(&exist).Updates(updateMap).Error
			if err != nil {
				return
			}
		} else {
			return
		}
	}
	return
}

func (a Account) saveALBs(dc asset.Datacenter, lbs []alb.LoadBalancer) (err error) {
	now := time.Now()
	for _, lb := range lbs {
		exist := asset.Loadbalancer{}
		createTime, _ := time.ParseInLocation("2006-01-02T15:04:05Z", lb.CreateTime, time.Local)
		err = app.DB().Where("account_id = ? AND datacenter_id = ? AND loadbalancer_id = ?", a.ID, dc.ID, lb.LoadBalancerId).Take(&exist).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = app.DB().Create(&asset.Loadbalancer{
				AccountID:        a.ID,
				DatacenterID:     dc.ID,
				SyncTime:         now,
				LoadbalancerType: "alb",
				Host:             lb.DNSName,
				Name:             lb.LoadBalancerName,
				LoadbalancerID:   lb.LoadBalancerId,
				Status:           lb.LoadBalancerStatus,
				Spec:             lb.LoadBalancerEdition,
				ResourceGroupID:  lb.ResourceGroupId,
				CreatedAt:        createTime,
			}).Error
			if err != nil {
				return
			}

		} else if err == nil {
			updateMap := map[string]any{
				"sync_time": now,
			}
			if exist.Name != lb.LoadBalancerName {
				updateMap["name"] = lb.LoadBalancerName
			}
			if exist.Status != lb.LoadBalancerStatus {
				updateMap["status"] = lb.LoadBalancerStatus
			}
			if exist.Spec != lb.LoadBalancerEdition {
				updateMap["spec"] = lb.LoadBalancerEdition
			}
			if exist.Host != lb.DNSName {
				updateMap["host"] = lb.DNSName
			}
			if exist.ResourceGroupID != lb.ResourceGroupId {
				updateMap["resource_group_id"] = lb.ResourceGroupId
			}
			if exist.CreatedAt != createTime {
				updateMap["created_at"] = createTime
			}
			err = app.DB().Model(&exist).Updates(updateMap).Error
			if err != nil {
				return
			}
		} else {
			return
		}
	}
	return
}

func (a Account) saveNLBs(dc asset.Datacenter, lbs []nlb.LoadbalancerInfo) (err error) {
	now := time.Now()
	for _, lb := range lbs {
		exist := asset.Loadbalancer{}
		createTime, _ := time.ParseInLocation("2006-01-02T15:04:05Z", lb.CreateTime, time.Local)
		err = app.DB().Where("account_id = ? and datacenter_id = ? and loadbalancer_id = ?", a.ID, dc.ID, lb.LoadBalancerId).Take(&exist).Error
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = app.DB().Create(&asset.Loadbalancer{
				AccountID:        a.ID,
				DatacenterID:     dc.ID,
				SyncTime:         now,
				LoadbalancerType: "nlb",
				Host:             lb.DNSName,
				Name:             lb.LoadBalancerName,
				LoadbalancerID:   lb.LoadBalancerId,
				Status:           lb.LoadBalancerStatus,
				Spec:             lb.LoadBalancerType,
				ResourceGroupID:  lb.ResourceGroupId,
				CreatedAt:        createTime,
			}).Error
			if err != nil {
				return
			}
		} else if err == nil {
			updateMap := map[string]any{
				"sync_time": now,
			}
			if exist.Name != lb.LoadBalancerName {
				updateMap["name"] = lb.LoadBalancerName
			}
			if exist.Status != lb.LoadBalancerStatus {
				updateMap["status"] = lb.LoadBalancerStatus
			}
			if exist.Spec != lb.LoadBalancerType {
				updateMap["spec"] = lb.LoadBalancerType
			}
			if exist.ResourceGroupID != lb.ResourceGroupId {
				updateMap["resource_group_id"] = lb.ResourceGroupId
			}
			if exist.Host != lb.DNSName {
				updateMap["host"] = lb.DNSName
			}
			if exist.CreatedAt != createTime {
				updateMap["created_at"] = createTime
			}
			err = app.DB().Model(&exist).Updates(updateMap).Error
			if err != nil {
				return
			}
		} else {
			return
		}
	}
	return
}

func (a Account) expiredLoadbanlancers(expiredTime time.Time, dc asset.Datacenter, loadbalancerType string) (err error) {
	lbs := []asset.Loadbalancer{}
	err = app.DB().Where("account_id = ? and sync_time < ? and datacenter_id = ? AND loadbalancer_type = ?", a.ID, expiredTime, dc.ID, loadbalancerType).Delete(&lbs).Error
	return
}
