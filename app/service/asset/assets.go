package asset

// 定义计费类型
type ChargeType uint

// 定义计费类型常量
const (
	PaidChargeType     ChargeType = iota // 已付费
	PrepaidChargeType                    // 预付费，包年包月
	PostpaidChargeType                   // 后付费
)

// 将计费类型转换为字符串
func (ct ChargeType) String() string {
	switch ct {
	case PaidChargeType:
		return "已付费"
	case PrepaidChargeType:
		return "预付费"
	case PostpaidChargeType:
		return "后付费"
	default:
		return "未知计费类型"
	}
}

// 定义云类型
type CloudType uint

// 返回云类型的指针
func (ct CloudType) Point() *CloudType {
	return &ct
}

// 返回云类型的指针
func (ct CloudType) String() string {
	switch ct {
	case AliyunCloudType:
		return "阿里云"
	case HuaweiCloudType:
		return "华为云"
	case TencentCloudType:
		return "腾讯云"
	case AWSCloudType:
		return "亚马逊云"
	default:
		return "未知云类型"
	}
}

// 定义云类型常量
const (
	AliyunCloudType  CloudType = iota + 2 // 阿里云
	HuaweiCloudType                       // 华为云
	TencentCloudType                      // 腾讯云
	AWSCloudType                          // 亚马逊云
)

// 定义主机类型
type HostType uint

// 定义主机类型常量
const (
	VirtualHostType  HostType = iota // 虚拟主机
	PhysicalHostType                 // 物理主机
	AliyunHostType                   // 阿里云主机
	HuaweiHostType                   // 华为云主机
	TencentHostType                  // 腾讯云主机
	AWSHostType                      // 亚马逊云主机
)

func (hostType HostType) String() string {
	switch hostType {
	case VirtualHostType:
		return "虚拟主机"
	case PhysicalHostType:
		return "物理主机"
	case AliyunHostType:
		return "阿里云主机"
	case HuaweiHostType:
		return "华为云主机"
	case TencentHostType:
		return "腾讯云主机"
	case AWSHostType:
		return "亚马逊云主机"
	default:
		return "未知主机类型"
	}
}

// 定义主机状态
type HostStatus uint

// 定义主机状态常量
const (
	RunningHostStatus  HostStatus = iota + 1 // 运行中
	StoppedHostStatus                        // 已停止
	CreatingHostStatus                       // 创建中
	DeletingHostStatus                       // 删除中
	ErrorHostStatus                          // 错误
	RecycleHostStatus                        // 已回收
)

// 将主机状态转换为字符串
func (hs HostStatus) String() string {
	switch hs {
	case RunningHostStatus:
		return "运行中"
	case StoppedHostStatus:
		return "已停止"
	case CreatingHostStatus:
		return "创建中"
	case DeletingHostStatus:
		return "删除中"
	case ErrorHostStatus:
		return "错误"
	default:
		return "未知主机状态"
	}
}

type ENV uint

const (
	DEVENV ENV = iota
	TESTENV
	YFENV
	PRODENV
)

func (env ENV) String() string {
	switch env {
	case DEVENV:
		return "开发环境"
	case TESTENV:
		return "测试环境"
	case YFENV:
		return "预发布环境"
	case PRODENV:
		return "生产环境"
	default:
		return "未知环境"
	}
}
