package monitor

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/monitor/prometheus"
	"strconv"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
)

// SyncHostMetrics 同步主机指标
func SyncHostMetrics(c *gin.Context) {
	statDay := time.Now()
	var err error
	statDay, err = time.Parse(time.DateOnly, c.Query("stat_day"))
	if err != nil {
		app.FailedResponseMsg(c, "invalid stat_day")
		return
	}
	// 异步执行
	go func() {
		err = prometheus.SyncHostMetrics(statDay)
	}()
	time.Sleep(100 * time.Millisecond)
	if err != nil {
		app.Log().Error("同步主机指标失败", "err", err)
		app.FailedResponseMsg(c, "同步主机指标失败")
	} else {
		app.SuccessResponseMsg(c, "同步主机指标中, 请稍后刷新页面查看")
	}
}

// GetHostMetrics 获取主机指标
func GetHostMetrics(c *gin.Context) {
	ip := c.Que<PERSON>("ip")
	startTime, err := time.ParseInLocation(time.DateOnly, c.Query("start_time"), time.Local)
	if err != nil {
		app.Log().Error("invalid start_time", "err", err, "start_time", c.Query("start_time"))
		app.FailedResponseMsg(c, "invalid start_time")
		return
	}
	endTime, err := time.ParseInLocation(time.DateOnly, c.Query("end_time"), time.Local)
	if err != nil {
		app.Log().Error("invalid end_time", "err", err, "end_time", c.Query("end_time"))
		app.FailedResponseMsg(c, "invalid end_time")
		return
	}
	metrics, err := prometheus.GetHostMetrics(ip, startTime, endTime)
	if err != nil {
		app.Log().Error("获取主机指标失败", "err", err)
		app.FailedResponseMsg(c, "获取主机指标失败")
		return
	}
	app.SuccessResponseData(c, metrics)
}

type HostLowUsageHost struct {
	Host asset.Host `json:"host"`
	prometheus.HostMetric
}

func GetLowUsageHosts(c *gin.Context) {
	startTime, err := time.ParseInLocation(time.DateOnly, c.Query("start_time"), time.Local)
	if err != nil {
		app.Log().Error("invalid start_time", "err", err, "start_time", c.Query("start_time"))
		app.FailedResponseMsg(c, "invalid start_time")
		return
	}
	endTime, err := time.ParseInLocation(time.DateOnly, c.Query("end_time"), time.Local)
	if err != nil {
		app.Log().Error("invalid end_time", "err", err, "end_time", c.Query("end_time"))
		app.FailedResponseMsg(c, "invalid end_time")
		return
	}
	threshold := c.Query("threshold")
	// 解析阈值
	thresholdFloat, err := strconv.ParseFloat(threshold, 64)
	if err != nil {
		app.Log().Error("invalid threshold", "err", err, "threshold", threshold)
		app.FailedResponseMsg(c, "invalid threshold")
		return
	}
	var status *int
	if statusStr := c.Query("status"); statusStr != "" {
		statusInt, err := strconv.Atoi(statusStr)
		if err != nil {
			app.Log().Error("invalid status", "err", err, "status", statusStr)
			app.FailedResponseMsg(c, "invalid status")
			return
		}
		status = &statusInt
	}
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	offset := (page - 1) * limit
	// 获取低使用率主机
	count, hosts, err := prometheus.GetLowUsageHosts(offset, limit, status, thresholdFloat, startTime, endTime)
	if err != nil {
		app.Log().Error("获取低使用率主机失败", "err", err)
		app.FailedResponseMsg(c, "获取低使用率主机失败")
		return
	}
	wg := sync.WaitGroup{}
	max := make(chan struct{}, 10)
	defer close(max)
	lowUsageHosts := make([]HostLowUsageHost, len(hosts))
	for i, host := range hosts {
		wg.Add(1)
		lowUsageHosts[i].IP = host.IP
		lowUsageHosts[i].MaxCpuUsage = host.MaxCpuUsage
		lowUsageHosts[i].MaxMemoryUsage = host.MaxMemoryUsage
		lowUsageHosts[i].MaxLoadUsage = host.MaxLoadUsage
		lowUsageHosts[i].MaxIOUsage = host.MaxIOUsage
		go func(i int, host HostLowUsageHost) {
			defer wg.Done()
			max <- struct{}{}
			defer func() { <-max }()
			// 获取主机信息
			lowUsageHosts[i].Host, _ = asset.GetHostByIP(host.IP)
		}(i, lowUsageHosts[i])
	}
	wg.Wait()
	app.SuccessResponseDataCount(c, lowUsageHosts, count)
}
