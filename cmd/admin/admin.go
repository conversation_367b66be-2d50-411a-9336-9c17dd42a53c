package admin

import (
	"cmdb/app"
	"cmdb/app/service/appops"
	"cmdb/app/service/appops/domain"
	"cmdb/app/service/appops/gitcode"
	"cmdb/app/service/appops/nginx"
	"cmdb/app/service/asset"
	"cmdb/app/service/asset/cloud/aliyun"
	"cmdb/app/service/asset/cloud/hwcloud"
	"cmdb/app/service/asset/cloud/tencentcloud"
	"cmdb/app/service/asset/k8s"
	"cmdb/app/service/assets"
	"cmdb/app/service/audit"
	"cmdb/app/service/auth"
	"cmdb/app/service/database/mongodb"
	"cmdb/app/service/database/mysql"
	"cmdb/app/service/database/redis"
	"cmdb/app/service/database/slowlog"
	"cmdb/app/service/monitor/https"
	"cmdb/app/service/monitor/prometheus"
	"cmdb/app/service/note"
	"cmdb/app/service/notice"
	"cmdb/app/service/setting"
	staticsticAsset "cmdb/app/service/statistic/asset"
	staticsticReport "cmdb/app/service/statistic/asset/report"
	"cmdb/app/service/statistics"
	"cmdb/app/service/task"
	"cmdb/app/service/workflow"
)

// 创建管理员用户
func CreateAdminUser(configFile string) (err error) {
	// 初始化应用程序
	err = app.NewApp(configFile)
	if err != nil {
		return
	}
	// 连接数据库
	err = app.ConnectDB()
	if err != nil {
		return
	}
	// 关闭数据库连接
	defer func() {
		dbop, err1 := app.DB().DB()
		if err1 == nil {
			dbop.Close()
		}
	}()
	// 创建管理员用户
	err = auth.CreateAdminUser()
	return
}

// 同步数据库
func SyncDB(configFile string) (err error) {
	// 初始化应用程序
	err = app.NewApp(configFile)
	if err != nil {
		return
	}
	// 连接数据库
	err = app.ConnectDB()
	if err != nil {
		return
	}
	// 关闭数据库连接
	defer func() {
		dbop, err1 := app.DB().DB()
		if err1 == nil {
			dbop.Close()
		}
	}()
	// 自动迁移数据库表
	err = app.DB().AutoMigrate(
		audit.LoginLog{}, audit.OPLog{}, notice.Contact{}, notice.ContactGroup{}, notice.Message{},
		auth.User{}, auth.Group{}, auth.Department{}, task.Job{}, task.JobLog{}, auth.API{}, auth.Key{},
		setting.SettingSection{}, setting.SettingItem{},
		asset.CloudAccount{}, asset.Datacenter{}, asset.Tag{}, asset.Host{}, asset.HostChangeLog{}, asset.PublicIP{},
		asset.Loadbalancer{}, asset.PrivateDomain{}, asset.PrivateDomainRecord{}, asset.PublicDomain{}, asset.PublicDomainRecord{}, asset.Business{}, asset.CloudDDosProtection{}, asset.CloudDDosDomain{}, asset.CloudDDosDomainDailyBps{}, asset.SubNet{},
		gitcode.Gitlab{}, gitcode.Project{}, gitcode.Group{},
		asset.ResourceGroup{}, aliyun.MonthlyBill{}, hwcloud.MonthlyBill{}, tencentcloud.MonthlyBill{},
		assets.AssetNode{}, assets.AssetSystem{},
		mysql.Project{}, mysql.Instance{}, mysql.Schema{}, mysql.Table{}, mysql.Backup{}, mongodb.Instance{}, mysql.TableLengthHistory{},
		slowlog.SlowlogStore{},
		redis.Instance{},
		k8s.Cluster{}, k8s.Label{}, k8s.Node{}, k8s.Workload{}, k8s.Pod{}, k8s.Namespace{}, k8s.Service{}, k8s.Container{},
		task.BatchTemplate{}, task.BatchTask{},
		workflow.Flow{}, workflow.FlowNode{}, workflow.Process{}, workflow.Order{}, workflow.OrderServer{}, workflow.OrderPermission{}, workflow.OrderDomain{}, workflow.OrderMySQL{}, workflow.OrderServiceNeed{}, workflow.OrderMongoDB{}, workflow.OrderGitLab{}, workflow.OrderDesktopOPS{},
		workflow.ServerModel{}, workflow.OrderEvaluation{}, workflow.OrderComment{},
		note.Bookmark{}, note.Journal{}, note.Duty{}, note.DutyMember{}, https.DomainHttps{}, prometheus.Instance{}, prometheus.HostMetric{},
		statistics.OptimizableAsset{}, statistics.ComputerAsset{}, statistics.CloudComputerAsset{}, statistics.RegionComputerAsset{}, statistics.DataAsset{}, statistics.DomainBps{},
		// 统计
		staticsticAsset.DataResource{}, staticsticAsset.OptimizableStat{}, staticsticAsset.ComputerResource{}, staticsticAsset.CloudComputerResource{}, staticsticAsset.RegionComputerResource{}, staticsticReport.DailyReport{}, staticsticReport.MonthlyReport{},
		// appops
		appops.Harbor{}, domain.Domain{},
		nginx.NginxBackend{},
	)
	if err != nil {
		return
	}
	err = workflow.SyncOrderTypeFlowToDB()
	if err != nil {
		return
	}
	err = setting.SyncDefaultSettingToDB()
	if err != nil {
		return
	}
	err = auth.SyncAPIsToDB()
	if err != nil {
		return
	}
	return
}
