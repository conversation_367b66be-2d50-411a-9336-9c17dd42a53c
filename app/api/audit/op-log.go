package audit

import (
	"cmdb/app"
	"cmdb/app/service/audit"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// 分页获取操作日志
func GetOPLogs(c *gin.Context) {
	user := c.Query("user")
	ip := c.Query("ip")
	module := c.Query("module")
	page, _ := strconv.Atoi(c<PERSON>("page", "1"))
	limit, _ := strconv.Atoi(c<PERSON>("limit", "10"))
	var startTime, endTime *time.Time
	startTimeStr := strings.TrimSpace(c.Query("start_time"))
	if value, err1 := time.Parse(time.RFC3339, startTimeStr); err1 == nil {
		startTime = &value
	}
	endTimeStr := strings.TrimSpace(c.Query("end_time"))
	if value, err2 := time.Parse(time.RFC3339, endTimeStr); err2 == nil {
		endTime = &value
	}
	offset := (page - 1) * limit
	count, data, err := audit.GetOPLogs(offset, limit, ip, user, module, startTime, endTime)
	if err != nil {
		app.Log().Error("获取操作记录列表失败 ", "err", err)
		c.JSON(http.StatusOK, gin.H{"success": false, "msg": "获取操作记录列表失败"})
		return
	}
	c.JSON(http.StatusOK, gin.H{"success": true, "count": count, "data": data})
}
