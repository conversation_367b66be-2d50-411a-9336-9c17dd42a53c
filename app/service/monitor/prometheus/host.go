package prometheus

import (
	"cmdb/app"
	"fmt"
	"regexp"
	"time"

	"github.com/prometheus/common/model"
	"gorm.io/gorm"
)

// IP地址正则表达式
var ipRegex = regexp.MustCompile(`\b(?:\d{1,3}\.){3}\d{1,3}\b`)

// 主机指标
type HostMetric struct {
	ID             uint      `gorm:"column:id;primaryKey;comment:id" json:"id"`
	IP             string    `gorm:"column:ip;type:varchar(255);index;comment:IP地址" json:"ip"`
	StatDay        time.Time `gorm:"column:stat_day;type:date;index;comment:统计日期" json:"stat_day"`
	MaxCpuUsage    float64   `gorm:"column:max_cpu_usage;comment:最大cpu使用率" json:"max_cpu_usage"`
	AvgCpuUsage    float64   `gorm:"column:avg_cpu_usage;comment:平均cpu使用率" json:"avg_cpu_usage"`
	MinCpuUsage    float64   `gorm:"column:min_cpu_usage;comment:最小cpu使用率" json:"min_cpu_usage"`
	MaxLoadUsage   float64   `gorm:"column:max_load_usage;comment:最大负载使用率" json:"max_load_usage"`
	AvgLoadUsage   float64   `gorm:"column:avg_load_usage;comment:平均负载使用率" json:"avg_load_usage"`
	MinLoadUsage   float64   `gorm:"column:min_load_usage;comment:最小负载使用率" json:"min_load_usage"`
	MaxMemoryUsage float64   `gorm:"column:max_memory_usage;comment:最大内存使用率" json:"max_memory_usage"`
	AvgMemoryUsage float64   `gorm:"column:avg_memory_usage;comment:平均内存使用率" json:"avg_memory_usage"`
	MinMemoryUsage float64   `gorm:"column:min_memory_usage;comment:最小内存使用率" json:"min_memory_usage"`
	MaxIOUsage     float64   `gorm:"column:max_io_usage;comment:最大IO使用率" json:"max_io_usage"`
	AvgIOUsage     float64   `gorm:"column:avg_io_usage;comment:平均IO使用率" json:"avg_io_usage"`
	MinIOUsage     float64   `gorm:"column:min_io_usage;comment:最小IO使用率" json:"min_io_usage"`
}

// 主机指标表
func (HostMetric) TableName() string {
	return "monitor_host_metrics"
}

// 查询主机指标
func QueryHostMetrics(instance Instance, statDay time.Time) ([]HostMetric, error) {
	var metrics []HostMetric
	var queries []string

	// 根据实例类型选择不同的查询语句
	switch instance.InstanceType {
	case InstanceTypeKubernetes:
		// node_exporter指标查询
		queries = []string{
			// CPU使用率
			`max(max_over_time( (1 - sum(rate(node_cpu_seconds_total{mode="idle"}[15m])) by (instance)/sum(rate(node_cpu_seconds_total[15m])) by (instance) )[1d:15m]) * 100) by (instance)`,
			`max(min_over_time( (1 - sum(rate(node_cpu_seconds_total{mode="idle"}[15m])) by (instance)/sum(rate(node_cpu_seconds_total[15m])) by (instance) )[1d:15m]) * 100) by (instance)`,
			`max(avg_over_time( (1 - sum(rate(node_cpu_seconds_total{mode="idle"}[15m])) by (instance)/sum(rate(node_cpu_seconds_total[15m])) by (instance) )[1d:15m]) * 100) by (instance)`,

			// 负载使用率（每核心负载）
			`max(max_over_time( ( avg(node_load1) by (instance)  / count(node_cpu_seconds_total{mode="idle"}) by (instance) )[1d:15m] ) * 100) by (instance)`,
			`max(min_over_time( ( avg(node_load1) by (instance)  / count(node_cpu_seconds_total{mode="idle"}) by (instance) )[1d:15m] ) * 100) by (instance)`,
			`max(avg_over_time( ( avg(node_load1) by (instance)  / count(node_cpu_seconds_total{mode="idle"}) by (instance) )[1d:15m] ) * 100) by (instance)`,

			// 内存使用率
			`max by(instance)(clamp_max(max_over_time(((node_memory_MemTotal_bytes-(node_memory_MemFree_bytes+node_memory_Buffers_bytes+node_memory_Cached_bytes))/node_memory_MemTotal_bytes)[1d:1m]),1)*100)`,
			`max by(instance)(clamp_max(min_over_time(((node_memory_MemTotal_bytes-(node_memory_MemFree_bytes+node_memory_Buffers_bytes+node_memory_Cached_bytes))/node_memory_MemTotal_bytes)[1d:1m]),1)*100)`,
			`max by(instance)(clamp_max(avg_over_time(((node_memory_MemTotal_bytes-(node_memory_MemFree_bytes+node_memory_Buffers_bytes+node_memory_Cached_bytes))/node_memory_MemTotal_bytes)[1d:1m]),1)*100)`,

			// IO使用率
			`max by (instance) ( max_over_time( (irate(node_disk_io_time_seconds_total{device=~".+"}[15m]) * 100)[1d:15m] ))`,
			`max by (instance) ( min_over_time( (irate(node_disk_io_time_seconds_total{device=~".+"}[15m]) * 100)[1d:15m] ))`,
			`max by (instance) ( avg_over_time( (irate(node_disk_io_time_seconds_total{device=~".+"}[15m]) * 100)[1d:15m] ))`,
		}

	case InstanceTypeN9e:
		// categraf指标查询
		queries = []string{
			// CPU使用率
			`max(100 - min_over_time(cpu_usage_idle[1d:1m])) by (ident)`,
			`max(100 - max_over_time(cpu_usage_idle[1d:1m])) by (ident)`,
			`max(100 - avg_over_time(cpu_usage_idle[1d:1m])) by (ident)`,

			// 负载使用率（每核心负载）
			`max(max_over_time((system_load1/system_n_cpus)[1d:1m])) by (ident) * 100`,
			`max(min_over_time((system_load1/system_n_cpus)[1d:1m])) by (ident) * 100`,
			`max(avg_over_time((system_load1/system_n_cpus)[1d:1m])) by (ident) * 100`,

			// 内存使用率
			`max(max_over_time(mem_used_percent[1d:1m])) by (ident)`,
			`max(min_over_time(mem_used_percent[1d:1m])) by (ident)`,
			`max(avg_over_time(mem_used_percent[1d:1m])) by (ident)`,

			// IO使用率
			`max(max_over_time(rate(diskio_io_time[15m])/1000[1d:15m])) by (ident) * 100`,
			`max(min_over_time(rate(diskio_io_time[15m])/1000[1d:15m])) by (ident) * 100`,
			`max(avg_over_time(rate(diskio_io_time[15m])/1000[1d:15m])) by (ident) * 100`,
		}

	default:
		return nil, fmt.Errorf("unsupported instance type: %s", instance.InstanceType)
	}
	nextDay := statDay.Add(24 * time.Hour)
	// 按天查询数据
	timestamp := time.Date(nextDay.Year(), nextDay.Month(), nextDay.Day(), 0, 0, 0, 0, time.Local)
	statDay = time.Date(statDay.Year(), statDay.Month(), statDay.Day(), 0, 0, 0, 0, time.Local)
	// app.Log().Info("日期", "statDay", statDay, "timestamp", timestamp, "nextDay", nextDay)
	// 使用map来保存每个IP的指标记录
	metricMap := make(map[string]*HostMetric)

	// 查询所有指标
	for i := 0; i < len(queries); i += 3 {
		duration := time.Minute
		// 查询最大值
		maxResult, err := QueryInstant(instance.Datasource, queries[i], &timestamp, &duration)
		if err != nil {
			app.Log().Error("查询最大值失败", "instance", instance.Name, "sql", queries[i], "err", err)
			return nil, fmt.Errorf("query max failed: %v", err)
		}

		// 查询最小值
		minResult, err := QueryInstant(instance.Datasource, queries[i+1], &timestamp, &duration)
		if err != nil {
			app.Log().Error("查询最小值失败", "instance", instance.Name, "sql", queries[i+1], "err", err)
			return nil, fmt.Errorf("query min failed: %v", err)
		}

		// 查询平均值
		avgResult, err := QueryInstant(instance.Datasource, queries[i+2], &timestamp, &duration)
		if err != nil {
			app.Log().Error("查询平均值失败", "instance", instance.Name, "sql", queries[i+2], "err", err)
			return nil, fmt.Errorf("query avg failed: %v", err)
		}

		// 获取所有结果
		maxMatrix, err := maxResult.AsVector()
		if err != nil {
			app.Log().Error("获取最大值失败", "instance", instance.Name, "sql", queries[i], "err", err)
			return nil, fmt.Errorf("get max matrix failed: %v", err)
		}

		minMatrix, err := minResult.AsVector()
		if err != nil {
			app.Log().Error("获取最小值失败", "instance", instance.Name, "sql", queries[i+1], "err", err)
			return nil, fmt.Errorf("get min matrix failed: %v", err)
		}

		avgMatrix, err := avgResult.AsVector()
		if err != nil {
			app.Log().Error("获取平均值失败", "instance", instance.Name, "sql", queries[i+2], "err", err)
			return nil, fmt.Errorf("get avg matrix failed: %v", err)
		}

		// 处理每个实例的结果
		for _, series := range maxMatrix {
			// 从标签中提取IP
			var ip string
			if instance.InstanceType == InstanceTypeKubernetes {
				// 使用正则表达式从instance标签中提取IP
				instance := string(series.Metric["instance"])
				matches := ipRegex.FindString(instance)
				if matches == "" {
					return nil, fmt.Errorf("无法从instance标签中提取IP: %s", instance)
				}
				ip = matches
			} else {
				// N9e从ident标签中提取IP
				ident := string(series.Metric["ident"])
				matches := ipRegex.FindString(ident)
				if matches == "" {
					fmt.Println("无法从ident标签中提取IP: ", ident)
					ip = ident
				} else {
					ip = matches
				}
			}

			// 获取或创建该IP的指标记录
			metric, exists := metricMap[ip]
			if !exists {
				metric = &HostMetric{
					IP:      ip,
					StatDay: statDay,
				}
				metricMap[ip] = metric
			}

			// 从结果中提取该IP的值
			maxValue := float64(series.Value)
			minValue, err := getVectorValueByLabels(minMatrix, series.Metric)
			if err != nil {
				return nil, fmt.Errorf("get min value for ip %s failed: %v", ip, err)
			}
			avgValue, err := getVectorValueByLabels(avgMatrix, series.Metric)
			if err != nil {
				return nil, fmt.Errorf("get avg value for ip %s failed: %v", ip, err)
			}

			// 根据指标类型设置值
			switch i {
			case 0:
				metric.MaxCpuUsage = maxValue
				metric.MinCpuUsage = minValue
				metric.AvgCpuUsage = avgValue
			case 3:
				metric.MaxLoadUsage = maxValue
				metric.MinLoadUsage = minValue
				metric.AvgLoadUsage = avgValue
			case 6:
				metric.MaxMemoryUsage = maxValue
				metric.MinMemoryUsage = minValue
				metric.AvgMemoryUsage = avgValue
			case 9:
				metric.MaxIOUsage = maxValue
				metric.MinIOUsage = minValue
				metric.AvgIOUsage = avgValue
			}
		}
	}

	// 将map中的指标转换为切片
	for _, metric := range metricMap {
		metrics = append(metrics, *metric)
	}

	return metrics, nil
}

// getVectorValueByLabels 根据标签匹配从向量中获取值
func getVectorValueByLabels(vector model.Vector, labels model.Metric) (float64, error) {
	for _, sample := range vector {
		if labelsMatch(sample.Metric, labels) {
			return float64(sample.Value), nil
		}
	}
	return 0, fmt.Errorf("未找到匹配的标签")
}

// labelsMatch 检查两个标签集是否匹配
func labelsMatch(l1, l2 model.Metric) bool {
	if len(l1) != len(l2) {
		return false
	}
	for k, v := range l1 {
		if v != l2[k] {
			return false
		}
	}
	return true
}

// 保存主机指标到数据库
func SaveHostMetrics(metrics []HostMetric) error {
	if len(metrics) == 0 {
		return nil
	}

	// 使用事务批量保存
	return app.DB().Transaction(func(tx *gorm.DB) error {
		for _, metric := range metrics {
			// 检查是否已存在相同IP和日期的记录
			var existing HostMetric
			err := tx.Where("ip = ? AND stat_day = ?", metric.IP, metric.StatDay).First(&existing).Error
			if err == nil {
				updateMap := map[string]any{}
				if metric.MaxCpuUsage != 0 && existing.MaxCpuUsage < metric.MaxCpuUsage {
					updateMap["max_cpu_usage"] = metric.MaxCpuUsage
				}
				if metric.AvgCpuUsage != 0 && existing.AvgCpuUsage < metric.AvgCpuUsage {
					updateMap["avg_cpu_usage"] = metric.AvgCpuUsage
				}
				if metric.MinCpuUsage != 0 && existing.MinCpuUsage < metric.MinCpuUsage {
					updateMap["min_cpu_usage"] = metric.MinCpuUsage
				}
				if metric.MaxLoadUsage != 0 && existing.MaxLoadUsage < metric.MaxLoadUsage {
					updateMap["max_load_usage"] = metric.MaxLoadUsage
				}
				if metric.AvgLoadUsage != 0 && existing.AvgLoadUsage < metric.AvgLoadUsage {
					updateMap["avg_load_usage"] = metric.AvgLoadUsage
				}
				if metric.MinLoadUsage != 0 && existing.MinLoadUsage < metric.MinLoadUsage {
					updateMap["min_load_usage"] = metric.MinLoadUsage
				}
				if metric.MaxMemoryUsage != 0 && existing.MaxMemoryUsage < metric.MaxMemoryUsage {
					updateMap["max_memory_usage"] = metric.MaxMemoryUsage
				}
				if metric.AvgMemoryUsage != 0 && existing.AvgMemoryUsage < metric.AvgMemoryUsage {
					updateMap["avg_memory_usage"] = metric.AvgMemoryUsage
				}
				if metric.MinMemoryUsage != 0 && existing.MinMemoryUsage < metric.MinMemoryUsage {
					updateMap["min_memory_usage"] = metric.MinMemoryUsage
				}
				if metric.MaxIOUsage != 0 && existing.MaxIOUsage < metric.MaxIOUsage {
					updateMap["max_io_usage"] = metric.MaxIOUsage
				}
				if metric.AvgIOUsage != 0 && existing.AvgIOUsage < metric.AvgIOUsage {
					updateMap["avg_io_usage"] = metric.AvgIOUsage
				}
				if metric.MinIOUsage != 0 && existing.MinIOUsage < metric.MinIOUsage {
					updateMap["min_io_usage"] = metric.MinIOUsage
				}

				if len(updateMap) > 0 {
					// 更新现有记录
					if err := tx.Model(&existing).Updates(updateMap).Error; err != nil {
						return err
					}
				}
			} else if err == gorm.ErrRecordNotFound {
				// 创建新记录
				if err := tx.Create(&metric).Error; err != nil {
					return err
				}
			} else {
				return err
			}
		}
		return nil
	})
}

func SyncHostMetrics(statDay time.Time) (err error) {
	instances := []Instance{}
	if err = app.DB().Find(&instances).Error; err != nil {
		return
	}
	for _, instance := range instances {
		var metrics []HostMetric
		var err1 error
		metrics, err1 = QueryHostMetrics(instance, statDay)
		if err1 != nil {
			app.Log().Error("查询主机指标失败", "instance", instance.Name, "err", err1)
			continue
		}
		err1 = SaveHostMetrics(metrics)
		if err1 != nil {
			app.Log().Error("保存主机指标失败", "instance", instance.Name, "err", err1)
			continue
		}
	}
	return
}

// 获取主机指标
func GetHostMetrics(ip string, startTime, endTime time.Time) (metrics []HostMetric, err error) {
	dbop := app.DB().Model(&HostMetric{})
	if ip != "" {
		dbop = dbop.Where("ip = ?", ip)
	}
	err = dbop.Where("stat_day >= ? AND stat_day <= ?", startTime, endTime).Order("stat_day ASC").Find(&metrics).Error
	return
}

// 获取低使用率主机
func GetLowUsageHosts(offset, limit int, status *int, threshold float64, startTime, endTime time.Time) (count int64, hosts []HostMetric, err error) {
	if status == nil {
		query := app.DB().Model(&HostMetric{}).
			Where("stat_day >= ? AND stat_day <= ?", startTime, endTime).
			Group("ip").
			Select("ip, MAX(max_cpu_usage) as max_cpu_usage, MAX(avg_cpu_usage) as avg_cpu_usage , MAX(min_cpu_usage) as min_cpu_usage ,  MAX(max_memory_usage) as max_memory_usage,MAX(avg_memory_usage) as avg_memory_usage,MAX(min_memory_usage) as min_memory_usage, MAX(max_load_usage) as max_load_usage,MAX(avg_load_usage) as avg_load_usage,MAX(min_load_usage) as min_load_usage, MAX(max_io_usage) as max_io_usage, MAX(avg_io_usage) as avg_io_usage, MAX(min_io_usage) as min_io_usage").
			Having("max_cpu_usage < ?", threshold).
			Having("max_memory_usage < ?", threshold).
			Having("max_load_usage < ?", threshold).
			Having("max_io_usage < ?", threshold)
		// 执行查询
		err = app.DB().Raw("SELECT COUNT(*) FROM (?) t1", query).Scan(&count).Error
		if err != nil {
			return
		}
		err = query.Order("max_cpu_usage ASC").Offset(offset).Limit(limit).Find(&hosts).Error
	} else {
		query := app.DB().Model(&HostMetric{}).
			Where("stat_day >= ? AND stat_day <= ?", startTime, endTime).
			Group("monitor_host_metrics.ip").
			Select("monitor_host_metrics.ip, MAX(max_cpu_usage) as max_cpu_usage, MAX(avg_cpu_usage) as avg_cpu_usage , MAX(min_cpu_usage) as min_cpu_usage ,  MAX(max_memory_usage) as max_memory_usage,MAX(avg_memory_usage) as avg_memory_usage,MAX(min_memory_usage) as min_memory_usage, MAX(max_load_usage) as max_load_usage,MAX(avg_load_usage) as avg_load_usage,MAX(min_load_usage) as min_load_usage, MAX(max_io_usage) as max_io_usage, MAX(avg_io_usage) as avg_io_usage, MAX(min_io_usage) as min_io_usage").
			Having("max_cpu_usage < ?", threshold).
			Having("max_memory_usage < ?", threshold).
			Having("max_load_usage < ?", threshold).
			Having("max_io_usage < ?", threshold).Joins(" LEFT JOIN asset_hosts ON asset_hosts.ip = monitor_host_metrics.ip").Where("asset_hosts.status = ?", *status)
		// 执行查询
		err = app.DB().Raw("SELECT COUNT(*) FROM (?) t1", query).Scan(&count).Error
		if err != nil {
			return
		}
		err = query.Order("max_cpu_usage ASC").Offset(offset).Limit(limit).Find(&hosts).Error
	}
	return
}
