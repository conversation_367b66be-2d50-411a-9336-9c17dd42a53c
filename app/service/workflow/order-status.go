package workflow

import "errors"

type OrderStatus uint

const (
	DraftStatus OrderStatus = iota
	ApprovingStatus
	// 结束状态
	CompletedStatus
	// 驳回状态
	RejectStatus
	// 撤销状态
	CancelStatus
	// 待我审批状态
	ICanApproveStatus = 100
)

// 获取订单状态ID
func (s OrderStatus) GetOrderStatusByID(id int) (OrderStatus, error) {
	switch id {
	case 0:
		return DraftStatus, nil
	case 1:
		return ApprovingStatus, nil
	case 2:
		return CompletedStatus, nil
	case 3:
		return RejectStatus, nil
	case 4:
		return CancelStatus, nil
	case 100:
		return ICanApproveStatus, nil
	default:
		return DraftStatus, errors.New("订单状态不存在")
	}
}

// 获取订单状态名称
func (s OrderStatus) String() string {
	switch s {
	case DraftStatus:
		return "草稿"
	case ApprovingStatus:
		return "审批中"
	case CompletedStatus:
		return "已结束"
	case RejectStatus:
		return "已驳回"
	case CancelStatus:
		return "已撤销"
	case ICanApproveStatus:
		return "待我审批"
	}
	return ""
}

func GetOrderStatusByID(id int) OrderStatus {
	return OrderStatus(id)
}
