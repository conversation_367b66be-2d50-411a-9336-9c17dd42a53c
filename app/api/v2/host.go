package v2

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/monitor"
	"strings"

	"github.com/gin-gonic/gin"
)

func GetMonitorHosts(c *gin.Context) {
	idcsString := strings.TrimSpace(c.Query("idc"))
	if idcsString == "" {
		app.V2FailedResponseMsg(c, "idcs参数不能为空")
		return
	}
	accountID := c.GetInt("account_id")
	idcs := strings.Split(idcsString, ",")
	hosts, err := monitor.GetMonitorHosts(accountID, idcs...)
	if err != nil {
		app.V2FailedResponseMsg(c, err.Error())
		return
	}
	app.V2SuccessResponseData(c, hosts)
}

func GetAllHosts(c *gin.Context) {
	hosts, err := asset.GetAllOldHosts()
	if err != nil {
		app.V2FailedResponseMsg(c, err.<PERSON><PERSON>r())
		return
	}
	app.V2SuccessResponseData(c, hosts)
}
