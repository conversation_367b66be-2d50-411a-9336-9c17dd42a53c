package mysql

import (
	"cmdb/app"
	"sync"
	"time"
)

// TableLengthHistory 表历史统计
type TableLengthHistory struct {
	ID          uint      `gorm:"column:id;primaryKey;comment:ID" json:"id"`
	InstanceID  uint      `gorm:"column:instance_id;index;comment:数据库实例ID" json:"-"`
	Schema      string    `gorm:"type:varchar(255);index;column:schema;comment:数据库实例" json:"schema"`
	Name        string    `gorm:"type:varchar(255);index;column:name;comment:表名" json:"name"`
	DataLength  uint      `gorm:"column:data_length;comment:数据大小" json:"data_length"`
	IndexLength uint      `gorm:"column:index_length;comment:索引大小" json:"index_length"`
	RecordTime  string    `gorm:"column:record_time;index;comment:记录时间" json:"record_time,omitempty"`
	CreatedAt   time.Time `gorm:"column:created_at;comment:创建时间" json:"created_at,omitempty"`
}

// TableName 设置表名
func (*TableLengthHistory) TableName() string {
	return "asset_db_mysql_table_length_hitories"
}

// InstanceLengthHistory 实例大小历史
type InstanceLengthHistory struct {
	InstanceID uint      `gorm:"column:instance_id;index;comment:数据库实例ID" json:"-"`
	Schema     string    `gorm:"type:varchar(255);index;column:schema;comment:数据库实例" json:"schema"`
	Name       string    `gorm:"type:varchar(255);column:name;comment:表名" json:"name"`
	Length     uint      `gorm:"column:length;comment:实例大小" json:"index_length"`
	RecordTime string    `gorm:"column:record_time;comment:记录时间" json:"record_time,omitempty"`
	CreatedAt  time.Time `gorm:"column:created_at;comment:创建时间" json:"created_at,omitempty"`
}

// TableName 设置表名
func (*InstanceLengthHistory) TableName() string {
	return "asset_db_mysql_table_length_hitories"
}

// 收集所有实例的表大小
func CollectInstancesTableLength() (err error) {
	instances := []Instance{}
	err = app.DB().Select(
		"id", "host", "port", "username", "password",
	).Find(&instances).Error
	if err != nil {
		return
	}
	var wg sync.WaitGroup
	for i := range instances {
		wg.Add(1)
		go func(i int) {
			err := instances[i].SyncTables()
			if err != nil {
				app.Log().Error("同步实例表大小失败", "err", err, "instance", instances[i].Host, "port", instances[i].Port)
			}
			wg.Done()
		}(i)
	}
	wg.Wait()
	return
}

// GetInstanceLengthHistories 实例大小统计历史
func (instance Instance) GetInstanceLengthHistories(startTime, endTime time.Time) (stats []map[string]interface{}, err error) {
	histories := []InstanceLengthHistory{}
	err = app.DB().Select(
		"instance_id", "record_time", "sum(data_length) + sum(index_length) as length",
	).Group("instance_id , record_time").Where("instance_id = ? AND created_at >= ? AND created_at <= ?", instance.ID, startTime, endTime.AddDate(0, 0, 1)).
		Order("record_time").Find(&histories).Error
	if err != nil {
		return
	}
	stats = make([]map[string]interface{}, len(histories))
	for i := range histories {
		stats[i] = map[string]interface{}{
			"date":   histories[i].RecordTime,
			"length": histories[i].Length,
		}
	}
	return
}

// GetInstanceTableLengthHistories 表大小统计历史
func (instance Instance) GetInstanceTableLengthHistories(schema, table string, startTime, endTime time.Time) (stats []map[string]interface{}, err error) {
	histories := []InstanceLengthHistory{}
	err = app.DB().Select(
		"instance_id", "record_time", "schema", "name", "sum(data_length) + sum(index_length) as length",
	).Group("instance_id , record_time, `schema`, name").
		Where("instance_id = ? and `schema` = ? and name = ? AND created_at >= ? AND created_at <= ?", instance.ID, schema, table, startTime, endTime.AddDate(0, 0, 1)).
		Order("record_time").Find(&histories).Error
	if err != nil {
		return
	}
	stats = make([]map[string]interface{}, len(histories))
	for i := range histories {
		stats[i] = map[string]interface{}{
			"date":   histories[i].RecordTime,
			"schema": histories[i].Schema,
			"table":  histories[i].Name,
			"length": histories[i].Length,
		}
	}
	return
}

// GetInstanceSchemaLengthHistories 库大小统计历史
func (instance Instance) GetInstanceSchemaLengthHistories(schema string, startTime, endTime time.Time) (stats []map[string]interface{}, err error) {
	histories := []InstanceLengthHistory{}
	err = app.DB().Select(
		"instance_id", "record_time", "schema", "sum(data_length) + sum(index_length) as length",
	).Group("instance_id , record_time, `schema`").
		Where("instance_id = ? and `schema` = ? AND created_at >= ? AND created_at <= ?", instance.ID, schema, startTime, endTime.AddDate(0, 0, 1)).
		Order("record_time").Find(&histories).Error
	if err != nil {
		return
	}
	stats = make([]map[string]interface{}, len(histories))
	for i := range histories {
		stats[i] = map[string]interface{}{
			"date":   histories[i].RecordTime,
			"schema": histories[i].Schema,
			"length": histories[i].Length,
		}
	}
	return
}
