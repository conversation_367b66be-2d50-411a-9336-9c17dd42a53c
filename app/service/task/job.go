package task

import (
	"cmdb/app"
	"cmdb/pkg/db"
	"errors"
	"time"

	"gorm.io/gorm"
)

var ErrJobRunning = errors.New("任务执行中")

// 同步任务
type Job struct {
	ID        uint       `gorm:"primaryKey;column:id;comment:id" json:"id"`
	Name      string     `gorm:"type:varchar(255);unique;column:name;comment:名称" json:"name"`
	Status    StatusTask `gorm:"index;column:status;comment:0 初始化状态 1 执行中 2 执行成功 3 执行失败" json:"status"`
	Remark    string     `gorm:"type:text;column:remark;comment:备注" json:"remark"`
	UpdatedAt time.Time  `gorm:"column:updated_at;comment:更新时间" json:"updated_at"`
}

// TableName 设置表名
func (*Job) TableName() string {
	return "task_jobs"
}

func RunJob(name, remark string, do func() (string, error)) (err error) {
	startTime := time.Now()
	job := Job{}
	err = app.DB().Where("name = ?", name).Take(&job).Error
	if err == gorm.ErrRecordNotFound {
		err = app.DB().Create(&Job{
			Name:   name,
			Remark: remark,
		}).Error
		if err != nil {
			return
		}
		err = app.DB().Where("name = ?", name).Take(&job).Error
	}
	if err != nil {
		return
	}
	if job.Status == 1 && time.Since(job.UpdatedAt) < 48*time.Hour {
		// 任务执行中就防止重复执行了
		err = ErrJobRunning
		return
	}
	// 更新任务状态为运行中，如果运行时间超过48h，则强制更新为运行中
	updateResult := app.DB().Model(&job).
		Where("status = ? OR updated_at < ?", job.Status, time.Now().Add(-48*time.Hour)).Updates(map[string]any{
		"status": StatusTaskRunning,
	})
	if updateResult.RowsAffected == 0 {
		// 判断是否真的更新成功
		err = ErrJobRunning
		return
	}
	err = updateResult.Error
	if err != nil {
		return
	}
	var result string
	result, err = do()
	err = job.log(err, time.Since(startTime), result)
	return
}

// 创建同步任务记录
func (job *Job) log(jobErr error, cost time.Duration, result string) (err error) {
	status := StatusTaskSuccess
	if jobErr != nil {
		result += " " + jobErr.Error()
		status = StatusTaskFailed
	}
	if jobErr != ErrJobRunning {
		err = app.DB().Model(job).Updates(map[string]any{
			"status": status,
		}).Error
		if err != nil {
			return err
		}
	}
	err = app.DB().Create(&JobLog{
		Name:   job.Name,
		Result: result,
		Status: status,
		Cost:   cost,
	}).Error
	return
}

// 获取任务列表
func GetJobs(offset, limit int, keyword string) (count int64, jobs []Job, err error) {
	dbop := app.DB()
	if keyword != "" {
		dbop = db.MLike(dbop, keyword, "name", "remark")
	}
	err = dbop.Model(&Job{}).Count(&count).Order("updated_at DESC ,name").Offset(offset).Limit(limit).Find(&jobs).Error
	return
}

func ResetJobs() (err error) {
	return app.DB().Model(&Job{}).Where("status = ?", 1).Updates(map[string]interface{}{
		"status": 0,
	}).Error
}
