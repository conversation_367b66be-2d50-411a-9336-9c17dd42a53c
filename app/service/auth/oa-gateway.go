package auth

import (
	"cmdb/app"
	"encoding/json"
	"errors"
	"log/slog"
	"strings"

	"github.com/tidwall/gjson"
	"gorm.io/gorm"
)

// oa登录验证
func OAGatewayAuth(sn string) (user User, err error) {
	response, err := GetOAUsers(sn)
	if err != nil {
		response, err = GetOAUsers(sn)
		if err != nil {
			return
		}
	}
	app.Log().Info("oa-gateway", "response", response)
	userMaps := []any{}
	err = json.Unmarshal([]byte(response), &userMaps)
	if err != nil {
		return
	}
	if len(userMaps) == 0 {
		err = errors.New("用户不存在")
		return
	}
	userobj := userMaps[0].(map[string]any)
	jsonStr, err := json.Marshal(userobj)
	if err != nil {
		return
	}
	app.Log().Info("oa-gateway", "userobj", string(jsonStr))
	email := gjson.Get(string(jsonStr), "email").String()
	username := strings.Split(email, "@")[0]
	// 判断用户是否存在
	// sn := userobj["sn"].(string)
	name := gjson.Get(string(jsonStr), "name").String()
	leader := User{}
	leader.Email = gjson.Get(string(jsonStr), "leaders.0.email").String()
	leader.Username = strings.Split(leader.Email, "@")[0]
	leader.Name = gjson.Get(string(jsonStr), "leaders.0.name").String()
	departments := []Department{}
	if gjson.Get(string(jsonStr), "department.family.company.name").String() != "" {
		departments = append(departments, Department{
			Name:   gjson.Get(string(jsonStr), "department.family.company.name").String(),
			Remark: "公司",
			Family: COMPANYFAMILY,
		})
	}
	if gjson.Get(string(jsonStr), "department.family.business_unit.name").String() != "" {
		departments = append(departments, Department{
			Name:   gjson.Get(string(jsonStr), "department.family.business_unit.name").String(),
			Remark: "事业部",
			Family: BUSINESSUNITFAMILY,
		})
	}
	if gjson.Get(string(jsonStr), "department.family.unit.name").String() != "" {
		departments = append(departments, Department{
			Name:   gjson.Get(string(jsonStr), "department.family.unit.name").String(),
			Remark: "事业部虚线",
			Family: UNITFAMILY,
		})
	}
	if gjson.Get(string(jsonStr), "department.family.center.name").String() != "" {
		departments = append(departments, Department{
			Name:   gjson.Get(string(jsonStr), "department.family.center.name").String(),
			Remark: "中心",
			Family: CENTERFAMILY,
		})
	}
	if gjson.Get(string(jsonStr), "department.family.team.name").String() != "" {
		departments = append(departments, Department{
			Name:   gjson.Get(string(jsonStr), "department.family.team.name").String(),
			Remark: "团队",
			Family: TEAMFAMILY,
		})
	}
	user, err = GetUserByUsername(username)
	if err == gorm.ErrRecordNotFound {
		err = app.DB().Create(&User{
			Username: username,
			Email:    email,
			SN:       sn,
			Name:     name,
		}).Error
		if err != nil {
			app.Log().Error("创建用户异常", slog.Any("err", err))
			err = ErrCreateUser
			return
		}
		user, err = GetUserByUsername(username)
	}
	if err == nil {
		if err := user.OASetLeader(leader); err != nil {
			app.Log().Error("自动设置领导异常", slog.Any("err", err))
		}
		if err := user.OASetDeparment(departments...); err != nil {
			app.Log().Error("自动部门分组异常", slog.Any("err", err))
		}
		// 如果存在要判断是否禁用
		// if user.IsDisabled {
		// 	err = ErrUserDisabled
		// 	return
		// }
		return
	}
	// 如果不是找不到行的错误，认为是查询错误，直接返回
	if err != gorm.ErrRecordNotFound {
		app.Log().Error("数据库操作失败 " + err.Error())
		err = ErrDBOP
		return
	}
	// 判断用户是否存在
	user, _ = GetUserByUsername(username)
	go func() {
		if err1 := user.OASetLeader(leader); err1 != nil {
			app.Log().Error("自动设置领导异常", "err", err1)
		}
	}()
	return
}
