package workflow

import (
	"cmdb/app"
	"errors"
	"time"
)

type OrderEvaluation struct {
	ID         uint      `gorm:"column:id;primaryKey;autoIncrement;comment:'自增字段'" json:"id"`
	SN         string    `gorm:"column:sn;index;comment:'工单号'" json:"sn"`
	Evaluation string    `gorm:"column:evaluation;comment:'评价'" json:"evaluation"`
	Comment    string    `gorm:"column:comment;type:text;comment:'备注'" json:"comment"`
	CreatedAt  time.Time `gorm:"column:created_at;comment:'创建时间'" json:"created_at"`
}

func (OrderEvaluation) TableName() string {
	return "workflow_order_evaluations"
}

type OrderEvaluationForm struct {
	SN         string `form:"sn" binding:"required"`
	Evaluation string `form:"evaluation" binding:"required"`
	Comment    string `form:"comment" binding:"max=2000"`
}

func (form OrderEvaluationForm) Create() error {
	// 判断是否存在
	var data OrderEvaluation
	err := app.DB().Where("sn = ?", form.SN).Take(&data).Error
	if err == nil {
		return errors.New("工单已评价")
	}
	return app.DB().Create(&OrderEvaluation{
		SN:         form.SN,
		Evaluation: form.Evaluation,
		Comment:    form.Comment,
	}).Error
}

func (o Order) GetEvaluations() (data *OrderEvaluation, err error) {
	err = app.DB().Where("sn = ?", o.SN).Take(&data).Error
	return
}

func (o Order) Evaluated() (yes bool, err error) {
	var count int64
	err = app.DB().Model(&OrderEvaluation{}).Where("sn = ?", o.SN).Count(&count).Error
	yes = count > 0
	return
}
