package main

import (
	"cmdb/app"
	"cmdb/app/api"
	"cmdb/cmd/admin"
	asynctask "cmdb/cmd/async-task"
	"flag"
	"log/slog"
	"os"
	"os/signal"
	"path/filepath"
	"time"
)

// init 函数在程序启动时执行，用于确保当前工作目录与程序所在目录一致
func init() {
	// 获取当前工作目录
	pwd, _ := os.Getwd()
	// 获取程序所在目录
	execDir, err := filepath.Abs(filepath.Dir(os.Args[0]))
	if err != nil {
		panic(err)
	}
	// 如果当前工作目录和程序所在目录相同，则直接返回
	if pwd == execDir {
		return
	}
	// 切换工作目录到程序所在目录，如果失败则抛出异常
	if err := os.Chdir(execDir); err != nil {
		panic(err)
	}
}

// main 函数是程序的入口点
func main() {
	// 记录程序启动时间
	bootTime := time.Now()
	var defaultConfig, syncDB, createAdmin, asyncTask bool
	var configFile string
	// 定义命令行参数
	flag.BoolVar(&defaultConfig, "default-config", false, "打印默认配置")
	flag.BoolVar(&syncDB, "syncdb", false, "同步表结构")
	flag.BoolVar(&createAdmin, "createAdmin", false, "创建超级管理员")
	flag.StringVar(&configFile, "f", "app.ini", "指定配置文件")
	flag.BoolVar(&asyncTask, "async-task", false, "异步任务")
	flag.Parse()
	// 如果指定了打印默认配置，则打印并退出
	if defaultConfig {
		app.PrintDefaultConfig()
		return
	}
	// 如果指定了创建超级管理员，则创建并退出
	if createAdmin {
		err := admin.CreateAdminUser(configFile)
		if err != nil {
			slog.Error("创建管理用户失败", "err", err)
			return
		}
		return
	}

	// 如果指定了同步表结构，则同步并退出
	if syncDB {
		err := admin.SyncDB(configFile)
		if err != nil {
			slog.Error("同步表结构失败", "err", err)
			return
		}
		return
	}
	// 如果指定了异步任务，则运行异步任务并退出
	if asyncTask {
		asynctask.Run(configFile)
		return
	}
	// 初始化应用
	err := app.NewApp(configFile)
	if err != nil {
		slog.Error("新建APP失败", "err", err)
		return
	}
	// 生成HTTP路由
	router, err := api.GenRoute()
	if err != nil {
		app.Log().Error("构建http路由失败", "err", err)
		return
	}
	// 启动HTTP服务
	err = app.StartHttp(router)
	if err != nil {
		app.Log().Error("启动http失败", "err", err)
		return
	}
	// 监听系统中断信号，以便优雅地关闭应用
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, os.Interrupt)
	<-quit
	// 停止HTTP服务
	err = app.StopHttp()
	if err != nil {
		app.Log().Error("停止http失败", "err", err)
		return
	}
	// 记录程序关闭时间和运行时长
	app.Log().Info("app 关闭", "运行时长", time.Since(bootTime).String())
}
