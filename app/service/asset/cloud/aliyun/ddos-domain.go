package aliyun

import (
	"bytes"
	"encoding/json"
	"errors"
	"strings"
	"time"

	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/requests"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/ddoscoo"
	"gorm.io/gorm"

	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/asset/cloud"
	"cmdb/pkg/utils"
)

func (a Account) syncDDoSDomains() (result cloud.SyncResults, err error) {
	startTime := time.Now()
	instances, err := a.syncDDosDomain(1)
	if err != nil {
		result = append(result, cloud.SyncResult{
			Key:    "DDoS Domain",
			Result: "同步失败" + err.Error(),
		})
		return
	}
	err = a.saveDDoSDomain(instances)
	if err != nil {
		result = append(result, cloud.SyncResult{
			Key:    "DDoS Domain",
			Result: "同步成功，保存失败" + err.<PERSON><PERSON>r(),
		})
		return
	}
	err = a.cleanDDoSDomains(startTime)
	if err != nil {
		result = append(result, cloud.SyncResult{
			Key:    "DDoS Domain",
			Result: "同步成功，但是清理历史数据失败" + err.Error(),
		})
		return
	}
	result = append(result, cloud.SyncResult{
		Key:    "DDoS Domain",
		Result: "同步成功",
	})
	return
}

// SyncInstances synchronizes DDoS instances from Alibaba Cloud
func (a Account) syncDDosDomain(page int) ([]asset.CloudDDosDomain, error) {
	secret, _ := utils.Base64Decode(a.AccessSecret)
	client, err := ddoscoo.NewClientWithAccessKey("cn-hangzhou", a.AccessKey, secret)
	if err != nil {
		return nil, err
	}
	request := ddoscoo.CreateDescribeWebRulesRequest()
	request.Scheme = "https"
	request.PageNumber = requests.NewInteger(page)
	request.PageSize = requests.NewInteger(10)
	request.RegionId = "cn-hangzhou"

	response, err := client.DescribeWebRules(request)
	if err != nil {
		return nil, err
	}
	total := response.TotalCount
	var instances []asset.CloudDDosDomain
	for _, rule := range response.WebRules {
		proxyTypes := []string{}
		for _, t := range rule.ProxyTypes {
			protoclType := t.ProxyType
			for _, p := range t.ProxyPorts {
				proxyTypes = append(proxyTypes, p+"/"+protoclType)
			}
		}
		realServers := []string{}
		for _, s := range rule.RealServers {
			realServers = append(realServers, strings.Split(s.RealServer, ",")...)
		}
		realServerBytes, _ := json.Marshal(realServers)
		instances = append(instances, asset.CloudDDosDomain{
			AccountID:   a.ID,
			Domain:      rule.Domain,
			Http2Enable: rule.Http2Enable,
			Cname:       rule.Cname,
			ProxyTypes:  strings.Join(proxyTypes, ","),
			RealServers: realServerBytes,
			SyncTime:    time.Now(),
		})
	}
	if total > int64(page*10) {
		subInstances, err := a.syncDDosDomain(page + 1)
		if err != nil {
			return nil, err
		}
		instances = append(instances, subInstances...)
	}
	return instances, nil
}

func (a Account) saveDDoSDomain(instances []asset.CloudDDosDomain) (err error) {
	for _, instance := range instances {
		exist := asset.CloudDDosDomain{}
		err = app.DB().Where("account_id = ? AND domain = ?", a.ID, instance.Domain).Take(&exist).Error
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = app.DB().Create(&instance).Error
		} else {
			updateMap := map[string]any{
				"sync_time": time.Now(),
			}
			if instance.Http2Enable != exist.Http2Enable {
				updateMap["http2_enable"] = instance.Http2Enable
			}
			if instance.ProxyTypes != exist.ProxyTypes {
				updateMap["proxy_types"] = instance.ProxyTypes
			}
			if !bytes.Equal(instance.RealServers, exist.RealServers) {
				updateMap["real_servers"] = instance.RealServers
			}
			if instance.Cname != exist.Cname {
				updateMap["cname"] = instance.Cname
			}
			err = app.DB().Model(&exist).Updates(updateMap).Error
		}
		if err != nil {
			return
		}
	}
	return
}

func (a Account) cleanDDoSDomains(syncTime time.Time) (err error) {
	err = app.DB().Where("account_id = ? AND sync_time < ?", a.ID, syncTime.Add(time.Minute*-5)).Delete(&asset.CloudDDosDomain{}).Error
	return
}
