package auth

import "errors"

var (
	ErrAuthFail      = errors.New("认证失败")
	ErrUserDisabled  = errors.New("用户被禁用")
	ErrOACode        = errors.New("OA 返回状态码异常")
	ErrOAData        = errors.New("OA 返回数据异常")
	ErrCreateUser    = errors.New("创建用户失败")
	ErrDBOP          = errors.New("操作数据失败")
	ErrGroupExist    = errors.New("用户分组已经存在")
	ErrUserNotExists = errors.New("用户不存在")
)

const (
	// 公司
	COMPANYFAMILY = "company"
	// 事业部
	BUSINESSUNITFAMILY = "business_unit"
	// 事业部虚线
	UNITFAMILY = "unit"
	// 应用中心
	CENTERFAMILY = "center"
	// 团队
	TEAMFAMILY = "team"
)

var FAMILIES = map[string]string{
	COMPANYFAMILY:      "公司",
	BUSINESSUNITFAMILY: "事业部",
	UNITFAMILY:         "事业部虚线",
	CENTERFAMILY:       "中心",
	TEAMFAMILY:         "团队",
}

const (
	ENABLE = uint(iota)
	DISABLE
)
