package slowlog

import (
	"time"
)

// MySQL slow query log structure
type MySQLSlowLog struct {
	ID             int64     `gorm:"column:id;primaryKey;autoIncrement;comment:'自增字段'" json:"id"`
	DBInstanceID   string    `gorm:"column:db_instance_id;not null;default:'';comment:'数据库实例id'" json:"db_instance_id"`
	DBPrivateIP    string    `gorm:"column:db_private_ip;not null;default:'';comment:'数据库内网ip'" json:"db_private_ip"`
	DBPort         int       `gorm:"column:db_port;not null;default:3306;comment:'数据库端口'" json:"db_port"`
	DBTitle        string    `gorm:"column:db_title;not null;default:'';comment:'数据库标签'" json:"db_title"`
	DBProjectName  string    `gorm:"column:db_project_name;not null;default:'';comment:'数据库实例企业项目名称'" json:"db_project_name"`
	CloudType      string    `gorm:"column:cloud_type;not null;default:'';comment:'所属cloud,如huaweiyun,aliyun'" json:"cloud_type"`
	DBName         string    `gorm:"column:db_name;not null;default:'';comment:'库名'" json:"db_name"`
	QueryType      string    `gorm:"column:query_type;not null;default:'';comment:'慢sql类型'" json:"query_type"`
	QueryCount     int       `gorm:"column:query_count;not null;default:0;comment:'慢sql数量'" json:"query_count"`
	QueryUser      string    `gorm:"column:query_user;not null;default:'';comment:'慢sql用户'" json:"query_user"`
	QueryStartTime string    `gorm:"column:query_start_time;not null;default:'';comment:'慢sql开始时间'" json:"query_start_time"`
	QueryClientIP  string    `gorm:"column:query_client_ip;not null;default:'';comment:'慢sql客户端ip'" json:"query_client_ip"`
	QueryTime      float64   `gorm:"column:query_time;not null;default:0;comment:'慢sql执行时间'" json:"query_time"`
	QueryLockTime  string    `gorm:"column:query_lock_time;not null;default:'0';comment:'慢sql锁时间'" json:"query_lock_time"`
	RowsSent       int       `gorm:"column:rows_sent;not null;default:0;comment:'慢sql发送行数'" json:"rows_sent"`
	RowsExamined   int       `gorm:"column:rows_examined;not null;default:0;comment:'慢sql扫描行数'" json:"rows_examined"`
	QueryTemplate  string    `gorm:"column:query_template;type:mediumtext;not null;comment:'慢sql模板'" json:"query_template"`
	QuerySample    string    `gorm:"column:query_sample;type:mediumtext;not null;comment:'慢sql语句'" json:"query_sample"`
	CreateTime     time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP;comment:'当前时间'" json:"create_time"`
	UpdateTime     time.Time `gorm:"column:update_time;not null;default:CURRENT_TIMESTAMP;comment:'修改时间'" json:"update_time"`
}

// TableName returns the table name for the MySQL slow log structure
func (m *MySQLSlowLog) TableName() string {
	return "t_prod_mysql_slowlog"
}

// MySQLSlowLogStatistics 生产环境mysql慢查询统计表结构
type MySQLSlowLogStatistics struct {
	ID            int64     `gorm:"column:id;primaryKey;autoIncrement;comment:'自增字段'" json:"id"`
	DBInstanceID  string    `gorm:"column:db_instance_id;not null;default:'';comment:'数据库实例id'" json:"db_instance_id"`
	DBPrivateIP   string    `gorm:"column:db_private_ip;not null;default:'';comment:'数据库内网ip'" json:"db_private_ip"`
	DBPort        int       `gorm:"column:db_port;not null;default:3306;comment:'数据库端口'" json:"db_port"`
	DBTitle       string    `gorm:"column:db_title;not null;default:'';comment:'数据库标签'" json:"db_title"`
	DBProjectName string    `gorm:"column:db_project_name;not null;default:'';comment:'数据库实例企业项目名称'" json:"db_project_name"`
	TotalCount    int       `gorm:"column:total_count;not null;default:0;comment:'总慢查询数'" json:"total_count"`
	CreateTime    time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP;comment:'当前时间'" json:"create_time"`
}

// TableName 设置表名
func (m *MySQLSlowLogStatistics) TableName() string {
	return "t_prod_slowlog_statistics"
}

// MySQLDMLLog MySQL DML记录表结构
type MySQLDMLLog struct {
	ID             int64     `gorm:"column:id;primaryKey;autoIncrement;comment:'自增字段'" json:"id"`
	DBInstanceID   string    `gorm:"column:db_instance_id;not null;default:'';comment:'数据库实例id'" json:"db_instance_id"`
	DBPrivateIP    string    `gorm:"column:db_private_ip;not null;default:'';comment:'数据库内网ip'" json:"db_private_ip"`
	DBPort         int       `gorm:"column:db_port;not null;default:3306;comment:'数据库port'" json:"db_port"`
	DBTitle        string    `gorm:"column:db_title;not null;default:'';comment:'数据库标签'" json:"db_title"`
	DBProjectName  string    `gorm:"column:db_project_name;not null;default:'';comment:'数据库实例企业项目名称'" json:"db_project_name"`
	IDCType        string    `gorm:"column:idc_type;not null;default:'';comment:'云厂商'" json:"idc_type"`
	OfflineDBIP    string    `gorm:"column:offlinedb_ip;not null;default:'';comment:'离线库ip'" json:"offlinedb_ip"`
	OfflineDBPort  int       `gorm:"column:offlinedb_port;not null;default:0;comment:'离线库port'" json:"offlinedb_port"`
	DBName         string    `gorm:"column:db_name;not null;default:'';comment:'库名'" json:"db_name"`
	TBName         string    `gorm:"column:tb_name;not null;default:'';comment:'表名'" json:"tb_name"`
	QueryType      string    `gorm:"column:query_type;not null;default:'';comment:'慢sql类型'" json:"query_type"`
	QueryCount     int       `gorm:"column:query_count;not null;default:0;comment:'慢sql数量'" json:"query_count"`
	QueryUser      string    `gorm:"column:query_user;not null;default:'';comment:'慢sql用户'" json:"query_user"`
	QueryStartTime string    `gorm:"column:query_start_time;not null;default:'';comment:'慢sql开始时间'" json:"query_start_time"`
	QueryClientIP  string    `gorm:"column:query_client_ip;not null;default:'';comment:'慢sql客户端ip'" json:"query_client_ip"`
	QueryTime      float64   `gorm:"column:query_time;not null;default:0;comment:'慢sql执行时间'" json:"query_time"`
	QueryLockTime  string    `gorm:"column:query_lock_time;not null;default:'0';comment:'慢sql锁时间'" json:"query_lock_time"`
	RowsExamined   int       `gorm:"column:rows_examined;not null;default:0;comment:'慢sql扫描行数'" json:"rows_examined"`
	QueryCommand   string    `gorm:"column:query_command;type:text;not null;comment:'慢sql语句'" json:"query_command"`
	SQLOptimizer   string    `gorm:"column:sql_optimizer;type:text;not null;comment:'sql优化建议'" json:"sql_optimizer"`
	CreateTime     time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP;comment:'当前时间'" json:"create_time"`
	UpdateTime     time.Time `gorm:"column:update_time;not null;default:CURRENT_TIMESTAMP;comment:'修改时间'" json:"update_time"`
}

// TableName 设置表名
func (m *MySQLDMLLog) TableName() string {
	return "t_prod_mysql_dmllog"
}
