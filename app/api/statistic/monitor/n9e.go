package monitor

import (
	"cmdb/app"
	"cmdb/app/service/statistic/monitor/n9e"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// GetAlertStatistics 获取告警统计数据
// @Summary 获取告警统计数据
// @Description 获取指定时间范围内的告警统计数据，支持多维度统计分析
// @Tags 告警统计
// @Accept json
// @Produce json
// @Param start_time query string true "开始时间，格式为 2006-01-02 15:04:05"
// @Param end_time query string true "结束时间，格式为 2006-01-02 15:04:05"
// @Param page query int false "页码，默认为1"
// @Param limit query int false "每页数量，默认为10"
// @Success 200 {object} app.ResponseData
// @Router /api/v1/statistic/monitor/n9e/alerts [get]
func GetAlertStatistics(c *gin.Context) {
	// 获取时间参数
	startTimeStr := c.Query("start_time")
	endTimeStr := c.Query("end_time")

	if startTimeStr == "" || endTimeStr == "" {
		app.FailedResponseMsg(c, "开始时间和结束时间不能为空")
		return
	}

	// 解析时间参数
	startTime, err := time.ParseInLocation("2006-01-02 15:04:05", startTimeStr, time.Local)
	if err != nil {
		app.Log().Error("解析开始时间失败", "startTime", startTimeStr, "err", err)
		app.FailedResponseMsg(c, "开始时间格式错误，正确格式为：YYYY-MM-DD HH:MM:SS")
		return
	}

	endTime, err := time.ParseInLocation("2006-01-02 15:04:05", endTimeStr, time.Local)
	if err != nil {
		app.Log().Error("解析结束时间失败", "endTime", endTimeStr, "err", err)
		app.FailedResponseMsg(c, "结束时间格式错误，正确格式为：YYYY-MM-DD HH:MM:SS")
		return
	}

	// 验证时间范围
	if startTime.After(endTime) {
		app.FailedResponseMsg(c, "开始时间不能大于结束时间")
		return
	}

	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	offset := (page - 1) * limit

	// 调用服务层获取统计数据
	statistics, err := n9e.GetAlertStatistics(startTime, endTime, offset, limit)
	if err != nil {
		app.Log().Error("获取告警统计数据失败", "err", err)
		app.FailedResponseMsg(c, "获取告警统计数据失败")
		return
	}

	app.SuccessResponseData(c, statistics)
}

// GetAlertStatisticsByInstance 按实例维度获取告警统计
// @Summary 按实例维度获取告警统计
// @Description 按target_ident字段分组，统计每个实例的告警数量、告警级别分布
// @Tags 告警统计
// @Accept json
// @Produce json
// @Param start_time query string true "开始时间，格式为 2006-01-02 15:04:05"
// @Param end_time query string true "结束时间，格式为 2006-01-02 15:04:05"
// @Param page query int false "页码，默认为1"
// @Param limit query int false "每页数量，默认为10"
// @Success 200 {object} app.ResponseData
// @Router /api/v1/statistic/monitor/n9e/alerts/by-instance [get]
func GetAlertStatisticsByInstance(c *gin.Context) {
	// 获取时间参数
	startTimeStr := c.Query("start_time")
	endTimeStr := c.Query("end_time")

	if startTimeStr == "" || endTimeStr == "" {
		app.FailedResponseMsg(c, "开始时间和结束时间不能为空")
		return
	}

	// 解析时间参数
	startTime, err := time.ParseInLocation("2006-01-02 15:04:05", startTimeStr, time.Local)
	if err != nil {
		app.Log().Error("解析开始时间失败", "startTime", startTimeStr, "err", err)
		app.FailedResponseMsg(c, "开始时间格式错误，正确格式为：YYYY-MM-DD HH:MM:SS")
		return
	}

	endTime, err := time.ParseInLocation("2006-01-02 15:04:05", endTimeStr, time.Local)
	if err != nil {
		app.Log().Error("解析结束时间失败", "endTime", endTimeStr, "err", err)
		app.FailedResponseMsg(c, "结束时间格式错误，正确格式为：YYYY-MM-DD HH:MM:SS")
		return
	}

	// 验证时间范围
	if startTime.After(endTime) {
		app.FailedResponseMsg(c, "开始时间不能大于结束时间")
		return
	}

	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	offset := (page - 1) * limit

	// 调用服务层获取实例维度统计数据
	count, statistics, err := n9e.GetAlertStatisticsByInstance(startTime, endTime, offset, limit)
	if err != nil {
		app.Log().Error("获取实例维度告警统计数据失败", "err", err)
		app.FailedResponseMsg(c, "获取实例维度告警统计数据失败")
		return
	}

	app.SuccessResponseDataCount(c, statistics, count)
}

// GetAlertStatisticsByRule 按告警规则维度获取告警统计
// @Summary 按告警规则维度获取告警统计
// @Description 按rule_id和rule_name分组，统计每个规则的触发次数、影响的实例数
// @Tags 告警统计
// @Accept json
// @Produce json
// @Param start_time query string true "开始时间，格式为 2006-01-02 15:04:05"
// @Param end_time query string true "结束时间，格式为 2006-01-02 15:04:05"
// @Param page query int false "页码，默认为1"
// @Param limit query int false "每页数量，默认为10"
// @Success 200 {object} app.ResponseData
// @Router /api/v1/statistic/monitor/n9e/alerts/by-rule [get]
func GetAlertStatisticsByRule(c *gin.Context) {
	// 获取时间参数
	startTimeStr := c.Query("start_time")
	endTimeStr := c.Query("end_time")

	if startTimeStr == "" || endTimeStr == "" {
		app.FailedResponseMsg(c, "开始时间和结束时间不能为空")
		return
	}

	// 解析时间参数
	startTime, err := time.ParseInLocation("2006-01-02 15:04:05", startTimeStr, time.Local)
	if err != nil {
		app.Log().Error("解析开始时间失败", "startTime", startTimeStr, "err", err)
		app.FailedResponseMsg(c, "开始时间格式错误，正确格式为：YYYY-MM-DD HH:MM:SS")
		return
	}

	endTime, err := time.ParseInLocation("2006-01-02 15:04:05", endTimeStr, time.Local)
	if err != nil {
		app.Log().Error("解析结束时间失败", "endTime", endTimeStr, "err", err)
		app.FailedResponseMsg(c, "结束时间格式错误，正确格式为：YYYY-MM-DD HH:MM:SS")
		return
	}

	// 验证时间范围
	if startTime.After(endTime) {
		app.FailedResponseMsg(c, "开始时间不能大于结束时间")
		return
	}

	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	offset := (page - 1) * limit

	// 调用服务层获取规则维度统计数据
	count, statistics, err := n9e.GetAlertStatisticsByRule(startTime, endTime, offset, limit)
	if err != nil {
		app.Log().Error("获取规则维度告警统计数据失败", "err", err)
		app.FailedResponseMsg(c, "获取规则维度告警统计数据失败")
		return
	}

	app.SuccessResponseDataCount(c, statistics, count)
}

// GetAlertStatisticsByCluster 按集群维度获取告警统计
// @Summary 按集群维度获取告警统计
// @Description 按cluster字段分组，统计每个集群的告警总数、严重程度分布
// @Tags 告警统计
// @Accept json
// @Produce json
// @Param start_time query string true "开始时间，格式为 2006-01-02 15:04:05"
// @Param end_time query string true "结束时间，格式为 2006-01-02 15:04:05"
// @Param page query int false "页码，默认为1"
// @Param limit query int false "每页数量，默认为10"
// @Success 200 {object} app.ResponseData
// @Router /api/v1/statistic/monitor/n9e/alerts/by-cluster [get]
func GetAlertStatisticsByCluster(c *gin.Context) {
	// 获取时间参数
	startTimeStr := c.Query("start_time")
	endTimeStr := c.Query("end_time")

	if startTimeStr == "" || endTimeStr == "" {
		app.FailedResponseMsg(c, "开始时间和结束时间不能为空")
		return
	}

	// 解析时间参数
	startTime, err := time.ParseInLocation("2006-01-02 15:04:05", startTimeStr, time.Local)
	if err != nil {
		app.Log().Error("解析开始时间失败", "startTime", startTimeStr, "err", err)
		app.FailedResponseMsg(c, "开始时间格式错误，正确格式为：YYYY-MM-DD HH:MM:SS")
		return
	}

	endTime, err := time.ParseInLocation("2006-01-02 15:04:05", endTimeStr, time.Local)
	if err != nil {
		app.Log().Error("解析结束时间失败", "endTime", endTimeStr, "err", err)
		app.FailedResponseMsg(c, "结束时间格式错误，正确格式为：YYYY-MM-DD HH:MM:SS")
		return
	}

	// 验证时间范围
	if startTime.After(endTime) {
		app.FailedResponseMsg(c, "开始时间不能大于结束时间")
		return
	}

	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	offset := (page - 1) * limit

	// 调用服务层获取集群维度统计数据
	count, statistics, err := n9e.GetAlertStatisticsByCluster(startTime, endTime, offset, limit)
	if err != nil {
		app.Log().Error("获取集群维度告警统计数据失败", "err", err)
		app.FailedResponseMsg(c, "获取集群维度告警统计数据失败")
		return
	}

	app.SuccessResponseDataCount(c, statistics, count)
}
