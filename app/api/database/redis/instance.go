package redis

import (
	"cmdb/app"
	"cmdb/app/service/audit"
	"cmdb/app/service/database/redis"
	"cmdb/app/service/monitor/prometheus"
	"errors"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// CreateRedisInstance 创建 Redis 实例
func CreateRedisInstance(c *gin.Context) {
	var form redis.InstanceForm
	if err := c.ShouldBindJSON(&form); err != nil {
		app.Log().Debug("添加Redis实例，验证表单失败", "err", err)
		app.FailedResponseMsg(c, "表单验证失败")
		return
	}
	if err := form.Create(); err != nil {
		msg := "添加失败"
		if errors.Is(err, redis.ErrInstanceExist) {
			msg = err.Error()
		} else {
			app.Log().Error("添加Redis实例失败", "err", err)
		}
		app.FailedResponseMsg(c, msg)
		return
	}
	audit.LogAPIOP(c, "数据库管理", "添加Redis实例："+form.Host)
	app.SuccessResponseMsg(c, "添加成功")
}

// UpdateInstance 更新 Redis 实例
func UpdateInstance(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.Log().Debug("更新Redis实例，参数错误", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	var form redis.InstanceForm
	if err := c.ShouldBindJSON(&form); err != nil {
		app.Log().Debug("更新Redis实例，验证表单失败", "err", err)
		app.FailedResponseMsg(c, "表单验证失败")
		return
	}
	instance, err := redis.GetInstanceByID(id)
	if err != nil {
		app.Log().Error("更新Redis实例失败", "err", err)
		app.FailedResponseMsg(c, "更新失败")
		return
	}
	if err := instance.Update(form); err != nil {
		msg := "更新失败"
		if errors.Is(err, redis.ErrInstanceExist) {
			msg = err.Error()
		} else {
			app.Log().Error("更新Redis实例失败", "err", err)
		}
		app.FailedResponseMsg(c, msg)
		return
	}
	audit.LogAPIOP(c, "数据库管理", "更新Redis实例："+form.Host)
	app.SuccessResponseMsg(c, "更新成功")
}

// DeleteInstance 删除 Redis 实例
func DeleteInstance(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.Log().Debug("删除Redis实例，参数错误", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	instance, err := redis.GetInstanceByID(id)
	if err != nil {
		app.Log().Error("删除Redis实例失败", "err", err)
		app.FailedResponseMsg(c, "删除失败")
		return
	}
	if err := redis.Delete(uint(id)); err != nil {
		app.Log().Error("删除Redis实例失败", "err", err)
		app.FailedResponseMsg(c, "删除失败")
		return
	}
	audit.LogAPIOP(c, "数据库管理", "删除Redis实例："+instance.Host)
	app.SuccessResponseMsg(c, "删除成功")
}

// GetInstances 获取 Redis 实例列表
func GetInstances(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	offset := (page - 1) * limit
	var keyword *string
	if val := c.Query("keyword"); val != "" {
		keyword = &val
	}
	tagIDs := []int{}
	if val := c.Query("tag_ids"); val != "" {
		tempIDStrs := strings.Split(val, ",")
		for _, tempIDStr := range tempIDStrs {
			tempID, err := strconv.Atoi(tempIDStr)
			if err != nil {
				app.Log().Error("转换tag_ids失败", "err", err)
				continue
			}
			tagIDs = append(tagIDs, tempID)
		}
	}
	count, data, err := redis.GetInstances(offset, limit, keyword, tagIDs)
	if err != nil {
		app.Log().Error("获取Redis实例列表失败", "err", err)
		app.FailedResponseMsg(c, "获取失败")
		return
	}
	app.SuccessResponseDataCount(c, data, count)
}

// TestConnection 测试 Redis 连接
func TestConnection(c *gin.Context) {
	var form redis.InstanceForm
	if err := c.ShouldBindJSON(&form); err != nil {
		app.Log().Debug("测试Redis实例连接，验证表单失败", "err", err)
		app.FailedResponseMsg(c, "表单验证失败")
		return
	}
	if err := form.TestConnect(); err != nil {
		app.Log().Error("测试Redis实例连接失败", "err", err)
		app.FailedResponseMsg(c, "连接失败:"+err.Error())
		return
	}
	app.SuccessResponseMsg(c, "连接成功")
}

// SyncRedisInstanceFromPrometheus 从Prometheus同步Redis实例
func SyncRedisInstanceFromPrometheus(c *gin.Context) {
	var err error
	var done bool
	go func() {
		err = prometheus.SyncRedisInstanceFromPrometheus()
		done = true
	}()
	audit.LogAPIOP(c, "数据库管理", "同步prometheus监控的redis实例")
	time.Sleep(100 * time.Millisecond)
	if err != nil {
		app.Log().Error("同步prometheus监控的redis实例失败", "err", err)
		app.FailedResponseMsg(c, "同步失败")
	} else {
		if done {
			app.SuccessResponseMsg(c, "同步成功")
		} else {
			app.SuccessResponseMsg(c, "同步中...")
		}
	}
}
