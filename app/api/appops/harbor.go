package appops

import (
	"cmdb/app"
	"cmdb/app/service/appops"
	"cmdb/app/service/audit"
	"errors"
	"strconv"

	"github.com/gin-gonic/gin"
)

func CreateHarbor(c *gin.Context) {
	var form appops.HarborForm
	if err := c.ShouldBind(&form); err != nil {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	if err := form.Create(); err != nil {
		msg := "创建harbor失败"
		if errors.Is(err, appops.ErrHarborExist) {
			msg = err.Error()
		} else {
			app.Log().Error("创建harbor失败", "err", err)
		}
		app.FailedResponseMsg(c, msg)
		return
	}
	audit.LogAPIOP(c, "资产管理", "创建harbor，请输入名称:"+form.Name)
	app.SuccessResponseMsg(c, "操作成功")
}

func UpdateHarbor(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.Log().Error("参数错误", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	harbor, err := appops.GetHarborByID(id)
	if err != nil {
		app.Log().Error("harbor不存在", "err", err)
		app.FailedResponseMsg(c, "harbor不存在")
		return
	}
	var form appops.HarborForm
	if err := c.ShouldBind(&form); err != nil {
		app.Log().Error("表单验证失败", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	if err := harbor.Update(form); err != nil {
		msg := "更新失败"
		if errors.Is(err, appops.ErrHarborExist) {
			msg = err.Error()
		} else {
			app.Log().Error("更新harbor失败", "err", err)
		}
		app.FailedResponseMsg(c, msg)
		return
	}
	audit.LogAPIOP(c, "资产管理", "更新harbor:"+form.Name)
	app.SuccessResponseMsg(c, "操作成功")
}

func DeleteHarbor(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.Log().Error("参数错误", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	harbor, err := appops.GetHarborByID(id)
	if err != nil {
		app.Log().Error("harbor不存在", "err", err)
		app.FailedResponseMsg(c, "harbor不存在")
		return
	}
	if err := harbor.Delete(); err != nil {
		app.Log().Error("删除harbor失败", "err", err)
		app.FailedResponseMsg(c, "删除失败")
		return
	}
	audit.LogAPIOP(c, "资产管理", "删除harbor:"+harbor.Name)
	app.SuccessResponseMsg(c, "删除成功")
}

func GetHarbors(c *gin.Context) {
	offset, _ := strconv.Atoi(c.DefaultQuery("offset", "0"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	var keyword *string
	if val := c.Query("keyword"); val != "" {
		keyword = &val
	}
	count, data, err := appops.GetHarbors(offset, limit, keyword)
	if err != nil {
		app.Log().Error("获取harbor列表失败", "err", err)
		app.FailedResponseMsg(c, "获取失败")
		return
	}
	app.SuccessResponseDataCount(c, data, count)
}

func GetAllHarbors(c *gin.Context) {
	data, err := appops.GetAllHarbors()
	if err != nil {
		app.Log().Error("获取harbor列表失败", "err", err)
		app.FailedResponseMsg(c, "获取失败")
		return
	}
	app.SuccessResponseData(c, data)
}
