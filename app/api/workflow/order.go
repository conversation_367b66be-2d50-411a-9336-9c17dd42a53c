package workflow

import (
	"cmdb/app"
	"cmdb/app/service/auth"
	"cmdb/app/service/workflow"
	"strconv"

	"github.com/gin-gonic/gin"
)

type OrderDetailData struct {
	Access     bool                      `json:"access"`
	CanApprove bool                      `json:"can_approve"`
	CanEdit    bool                      `json:"can_edit"`
	CanEval    bool                      `json:"can_eval"`
	IsLast     bool                      `json:"is_last"`
	Order      any                       `json:"order"`
	Evaluation *workflow.OrderEvaluation `json:"evaluation"`
	Comments   []workflow.OrderComment   `json:"comments"`
}

func GetOrderDetail(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.FailedResponseMsg(c, "获取工单详情失败")
		return
	}
	loginUser, err := auth.GetLoginUser(c)
	if err != nil {
		app.FailedResponseMsg(c, "获取用户信息失败")
		return
	}
	o, err := workflow.GetOrderByID(id)
	if err != nil {
		app.FailedResponseMsg(c, "获取工单详情失败")
		return
	}
	data := OrderDetailData{
		Access:     false,
		CanApprove: false,
		CanEdit:    false,
	}
	if loginUser.IsAdmin {
		data.Access = true
	} else {
		data.Access = o.CheckOrderPrivilege(loginUser.ID)
	}
	if !data.Access {
		app.SuccessResponseData(c, data)
		return
	}
	if loginUser.ID == o.ApplicantID {
		ok, err := o.CanUpdate()
		if err == nil {
			data.CanEdit = ok
		}
	}
	approver, err := workflow.NewApprover(loginUser)
	if err != nil {
		app.Log().Error("获取审批人信息失败", "err", err)
		app.FailedResponseMsg(c, "获取审批人信息失败")
		return
	}
	applicant, err := workflow.GetApplicantByID(o.ApplicantID)
	if err != nil {
		app.Log().Error("获取申请人信息失败", "err", err)
		app.FailedResponseMsg(c, "获取申请人信息失败")
		return
	}
	o.Applicant = &applicant
	if ok, err := o.CanApproveByApprover(approver); err == nil {
		data.CanApprove = ok
	}
	if ok, err := o.IsLastProcess(); err == nil {
		data.IsLast = ok
	}
	data.Order, err = o.Detail()
	if err != nil {
		app.Log().Error("获取工单详情失败", "err", err)
		app.FailedResponseMsg(c, "获取工单详情失败")
		return
	}
	data.Evaluation, _ = o.GetEvaluations()
	// 判断是否可以评价
	if o.Status == workflow.CompletedStatus {
		if o.ApplicantID == loginUser.ID {
			data.CanEval = true
		}
	}
	var err1 error
	data.Comments, err1 = workflow.GetOrderComments(o.SN)
	if err1 != nil {
		app.Log().Error("获取工单评论失败", "err", err1)
	}
	app.SuccessResponseData(c, data)
}
