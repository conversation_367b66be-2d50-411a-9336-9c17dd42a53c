package auth

import (
	"cmdb/app"
	"testing"
)

func TestOAService(t *testing.T) {
	err := app.NewApp("../../../app.ini")
	if err != nil {
		t.Log(err)
		return
	}
	err = app.ConnectDB()
	if err != nil {
		t.Log(err)
		return
	}
	defer func() {
		dbop, err1 := app.DB().DB()
		if err1 == nil {
			dbop.Close()
		}
	}()
	err = UpdateOAUsers()
	if err != nil {
		t.Log(err)
		return
	}
	t.Log("更新OA用户成功")
}
