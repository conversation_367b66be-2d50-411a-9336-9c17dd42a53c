package asset

import (
	"cmdb/app"
	"cmdb/pkg/db"
	"cmdb/pkg/utils"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"
)

var (
	ErrCloudAccountExist = errors.New("账户已经存在")
)

type CloudAccount struct {
	ID           uint           `gorm:"column:id;primaryKey;comment:主键id" json:"id"`
	Name         string         `gorm:"type:varchar(255);column:name;index;comment:名称" json:"name"`
	CloudType    CloudType      `gorm:"column:cloud_type;comment:账户类型" json:"cloud_type"`
	AccessKey    string         `gorm:"column:access_key;type:varchar(255);comment:访问key" json:"access_key"`
	AccessSecret string         `gorm:"column:access_secret;type:varchar(255);comment:访问密码" json:"-"`
	Remark       string         `gorm:"column:remark;type:varchar(255);comment:备注" json:"remark"`
	CreatedAt    time.Time      `gorm:"column:created_at;index;comment:创建时间" json:"created_at"`
	UpdatedAt    time.Time      `gorm:"column:updated_at;index;comment:更新时间" json:"updated_at"`
	DeletedAt    gorm.DeletedAt `gorm:"column:deleted_at;comment:软删除" json:"-"`
}

func (CloudAccount) TableName() string {
	return "asset_cloud_accounts"
}

// CloudAccountForm 账户表单
type CloudAccountForm struct {
	Name         string `json:"name" binding:"required,max=255"`
	CloudType    uint   `json:"cloud_type"`
	AccessKey    string `json:"access_key" binding:"max=255"`
	AccessSecret string `json:"access_secret" binding:"max=255"`
	Remark       string `json:"remark" binding:"max=255"`
}

// Create 通过表单创建账户
func (form CloudAccountForm) Create() (err error) {
	if form.AccessSecret != "" {
		form.AccessSecret = utils.Base64Encode(form.AccessSecret)
	}
	err = app.DB().Select("id").Where("name = ?", form.Name).Take(&CloudAccount{}).Error
	if err == nil {
		err = ErrCloudAccountExist
		return
	}
	if err == gorm.ErrRecordNotFound {
		err = app.DB().Create(&CloudAccount{
			Name:         form.Name,
			CloudType:    CloudType(form.CloudType),
			AccessKey:    form.AccessKey,
			AccessSecret: form.AccessSecret,
			Remark:       form.Remark,
		}).Error
	}
	return
}

// 通过id获取账户对象
func GetCloudAccountByID(id int) (account CloudAccount, err error) {
	err = app.DB().Where("id = ?", id).Take(&account).Error
	return
}
func (a CloudAccount) Delete() error {
	return app.DB().Delete(&a).Error
}

// GetAllAccounts 获取所有账户
func GetAllAccounts() (accounts []CloudAccount, err error) {
	err = app.DB().Order("name").Find(&accounts).Error
	return
}

type GetAccountsParmas struct {
	Offset    int
	Limit     int
	Keyword   string
	CloudType int
}

// GetAccounts 分页获取账户列表
func GetCloudAccounts(params GetAccountsParmas) (count int64, accounts []CloudAccount, err error) {
	dbop := app.DB()
	if params.Keyword != "" {
		dbop = db.MLike(dbop, params.Keyword, "name", "remark")
	}
	if params.CloudType > 0 {
		dbop = dbop.Where("cloud_type = ?", params.CloudType)
	}
	err = dbop.Model(&CloudAccount{}).Count(&count).
		Order("updated_at DESC").
		Offset(params.Offset).Limit(params.Limit).Find(&accounts).Error
	return
}

// 更新账户
func (a CloudAccount) Update(form CloudAccountForm) (err error) {
	if form.AccessSecret != "" {
		form.AccessSecret = utils.Base64Encode(form.AccessSecret)
	}
	updateMap := map[string]any{}
	if a.Name != form.Name {
		updateMap["name"] = form.Name
	}
	if a.Remark != form.Remark {
		updateMap["remark"] = form.Remark
	}
	if a.CloudType != CloudType(form.CloudType) {
		updateMap["cloud_type"] = form.CloudType
	}
	if a.AccessKey != form.AccessKey {
		updateMap["access_key"] = form.AccessKey
	}
	if form.AccessSecret != "" && a.AccessSecret != form.AccessSecret {
		updateMap["access_secret"] = form.AccessSecret
	}
	if len(updateMap) == 0 {
		// 不需要更新就直接返回
		return
	}
	exist := CloudAccount{}
	err = app.DB().Where("name = ?", form.Name).Take(&exist).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		fmt.Println(err)
	}
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		// 查询出现错误
		return err
	} else if err == nil && a.ID != exist.ID {
		err = ErrCloudAccountExist
		return
	}
	fmt.Println(err)
	err = app.DB().Model(&a).Updates(updateMap).Error
	return
}

// 通过ID获取账户名称
func GetAccountNameByID(id int) (name string, err error) {
	err = app.DB().Model(&CloudAccount{}).Select("name").Where("id = ?", id).Scan(&name).Error
	return
}

func GetAllCloudAccounts() (accounts []CloudAccount, err error) {
	err = app.DB().Find(&accounts).Error
	return
}

func (a CloudAccount) CountHost() (count int64, err error) {
	err = app.DB().Model(&Host{}).Where("account_id = ?", a.ID).Count(&count).Error
	return
}

func accountTypeByID(id int) (cloudType CloudType, err error) {
	err = app.DB().Model(&CloudAccount{}).Select("cloud_type").Where("id = ?", id).Scan(&cloudType).Error
	return
}
