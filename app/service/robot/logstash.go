package robot

import (
	"bytes"
	"cmdb/app/service/note"
	"fmt"
	"strconv"
	"time"
)

func Get5xxLogs(usage string, params ...string) (msg HTTPBodyMarkdown) {
	msg.MsgType = "markdown"
	msg.Markdown.Title = "查询域名5xx"
	hoststring := ""
	var contents bytes.Buffer
	endTime := time.Now()
	duration := 5 * time.Minute
	if len(params) > 0 {
		duration1, _ := strconv.ParseInt(params[0], 10, 64)
		if duration1 > 0 && duration1 <= 100 {
			duration = time.Duration(duration1) * time.Minute
		}
	}
	if len(params) > 1 {
		hoststring = params[1]
	}
	startTime := endTime.Add(-duration)
	contents.WriteString(fmt.Sprintf("#### 时间周期：%s ~ %s \n", startTime.Format("2006-01-02 15:04:05"), endTime.Format("2006-01-02 15:04:05")))
	logs, err := note.Get5xxLogs(hoststring, startTime, endTime)
	if err != nil {
		contents.WriteString("查询失败")
	} else {
		if len(logs) == 0 {
			contents.WriteString(">无异常日志\n")
		} else {
			for i := range logs {
				contents.WriteString(fmt.Sprintf("- 域名：%s \n", logs[i].HttpHost))
				for j := range logs[i].StatusCounts {
					contents.WriteString(fmt.Sprintf("> 状态码 %d 转发地址(数量)：", logs[i].StatusCounts[j].Status))
					for k := range logs[i].StatusCounts[j].UpstreamAddrCounts {
						contents.WriteString(fmt.Sprintf("%s (%d) ；", logs[i].StatusCounts[j].UpstreamAddrCounts[k].UpstreamAddr, logs[i].StatusCounts[j].UpstreamAddrCounts[k].Count))
					}
				}
				contents.WriteString("\n---\n")
			}
		}
	}
	if contents.String() == "" {
		msg.Markdown.Text = "未查询到结果"
	} else {
		msg.Markdown.Text = contents.String()
	}
	return
}
