package hwcloud

import (
	"errors"

	"github.com/huaweicloud/huaweicloud-sdk-go-v3/core/auth/basic"
	dns "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/dns/v2"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/dns/v2/model"
	region "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/dns/v2/region"
	"gorm.io/gorm"

	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/asset/cloud"
	"cmdb/pkg/utils"
	"time"
)

func (a Account) syncPublicDomains() (result cloud.SyncResults, err error) {
	sr := cloud.SyncResult{
		Key: "账号：" + a.Name,
	}
	r, err := a.syncRegionPublicDomains(0)
	if err == nil {
		sr.Result = "同步成功"
		// 回收过期公网域名
		err = a.ExpiredPublicDomains(time.Now().Add(-10 * time.Hour))
		if err != nil {
			sr.Result = "同步成功，但是回收公网域名失败" + err.Error()
		}
	} else {
		sr.Result = r + " " + err.Error()
	}
	result = append(result, sr)
	return
}

func (a Account) syncRegionPublicDomains(offset int32) (result string, err error) {
	accesesKeyID := a.AccessKey
	accessSecret, _ := utils.Base64Decode(a.AccessSecret)
	auth, err := basic.NewCredentialsBuilder().
		WithAk(accesesKeyID).
		WithSk(accessSecret).
		SafeBuild()
	if err != nil {
		return
	}
	r, err := region.SafeValueOf("cn-north-4")
	if err != nil {
		return
	}
	b, err := dns.DnsClientBuilder().
		WithRegion(r).
		WithCredential(auth).
		SafeBuild()
	if err != nil {
		return
	}
	client := dns.NewDnsClient(b)
	request := &model.ListPublicZonesRequest{}
	limitRequest := int32(500)
	typeRequest := "public"
	request.Type = &typeRequest
	request.Limit = &limitRequest
	request.Offset = &offset
	response, err := client.ListPublicZones(request)
	if err != nil {
		return
	}

	if response.Zones != nil {
		now := time.Now()
		for _, zone := range *response.Zones {
			exist := asset.PublicDomain{}
			createTime, err2 := time.Parse("2006-01-02T15:04:05", *zone.CreatedAt)
			if err2 != nil {
				createTime = now
			}
			UpdateTime, err2 := time.Parse("2006-01-02T15:04:05", *zone.UpdatedAt)
			if err2 != nil {
				createTime = now
			}
			err1 := app.DB().Where("domain_id = ? AND account_id = ?", zone.Id, a.ID).Take(&exist).Error
			if errors.Is(err1, gorm.ErrRecordNotFound) {
				err = app.DB().Create(&asset.PublicDomain{
					AccountID:       a.ID,
					Name:            *zone.Name,
					RecordCount:     uint64(*zone.RecordNum),
					ResourceGroupID: *zone.EnterpriseProjectId,
					CloudType:       a.CloudType,
					DomainID:        *zone.Id,
					CreatedAt:       createTime,
					UpdatedAt:       UpdateTime,
					SyncTime:        now,
				}).Error
				if err != nil {
					return
				}
			}

		}
	}
	if len(*response.Zones) > 0 && *response.Metadata.TotalCount > offset+limitRequest {
		result, err = a.syncRegionPublicDomains(offset + limitRequest)
	}
	return
}
