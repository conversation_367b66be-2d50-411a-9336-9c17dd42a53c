package slowlog

import (
	"cmdb/app"
	"cmdb/app/service/database/slowlog"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/now"
)

func GetProjectStatistics(c *gin.Context) {
	id, err := strconv.Atoi(c.<PERSON>m("id"))
	if err != nil || id <= 0 {
		app.Log().Error("获取慢日志统计失败", "err", err)
		app.FailedResponseMsg(c, "id参数错误")
		return
	}
	dbType := c.Param("db_type")
	if dbType != "mysql" && dbType != "redis" && dbType != "mongodb" {
		app.FailedResponseMsg(c, "db_type参数错误")
		return
	}
	s, err := slowlog.GetSlowlogStoreByID(id)
	if err != nil {
		app.Log().Error("获取慢日志统计失败", "err", err)
		app.FailedResponseMsg(c, "获取失败")
		return
	}
	tomorrow := time.Now().AddDate(0, 0, 1)
	endTime := now.With(tomorrow).BeginningOfDay()
	startTime := endTime.AddDate(0, 0, -8)
	if val := c.Query("start_time"); val != "" {
		startTime, err = time.ParseInLocation("2006-01-02", val, time.Local)
		if err != nil {
			app.FailedResponseMsg(c, "参数错误, start_time 格式为:YYYY-mm-dd")
			return
		}
	}
	if val := c.Query("end_time"); val != "" {
		endTime, err = time.ParseInLocation("2006-01-02", val, time.Local)
		if err != nil {
			app.FailedResponseMsg(c, "参数错误, end_time 格式为:YYYY-mm-dd")
			return
		}
	}
	var data []slowlog.ProjectStatistics
	var err1 error
	switch dbType {
	case "mysql":
		data, err1 = s.GetMySQLProjectStatistics(startTime, endTime)
	case "redis":
		data, err1 = s.GetRedisProjectStatistics(startTime, endTime)
	case "mongodb":
		data, err1 = s.GetMongoDBProjectStatistics(startTime, endTime)
	default:
		app.FailedResponseMsg(c, "db_type参数错误")
		return
	}
	if err1 != nil {
		app.Log().Error("获取慢日志统计失败", "err", err1)
		app.FailedResponseMsg(c, "获取失败")
		return
	}
	app.SuccessResponseData(c, data)
}
