package aliyun

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/asset/cloud"
	"cmdb/pkg/utils"
	"errors"

	"github.com/aliyun/alibaba-cloud-sdk-go/services/ecs"
	"gorm.io/gorm"
)

// syncRegions 同步帐号的region和ecs信息
func (a Account) syncRegions() (results cloud.SyncResults, err error) {
	accessSecret, _ := utils.Base64Decode(a.AccessSecret)
	client, err := ecs.NewClientWithAccessKey("cn-beijing", a.AccessKey, accessSecret)
	if err != nil {
		results = append(results, cloud.SyncResult{Key: "", Result: "创建请求失败，" + err.Error()})
		return
	}
	request := ecs.CreateDescribeRegionsRequest()
	request.Scheme = "https"
	response, err := client.DescribeRegions(request)
	if err != nil {
		results = append(results, cloud.SyncResult{Key: "", Result: "请求接口失败，" + err.<PERSON>rror()})
		return
	}
	regions := response.Regions.Region
	for i := range regions {
		exist := asset.Datacenter{}
		err1 := app.DB().Where("cloud_type = ? AND code = ?", asset.AliyunCloudType, regions[i].RegionId).Take(&exist).Error
		if errors.Is(err1, gorm.ErrRecordNotFound) {
			err1 = app.DB().Create(&asset.Datacenter{
				Name:      regions[i].LocalName,
				Code:      regions[i].RegionId,
				CloudType: asset.AliyunCloudType,
				Remark:    regions[i].RegionEndpoint,
			}).Error
			if err1 != nil {
				results = append(results, cloud.SyncResult{Key: regions[i].LocalName, Result: "创建失败：" + err1.Error()})
			}
			continue
		}
		if err1 != nil {
			results = append(results, cloud.SyncResult{Key: regions[i].LocalName, Result: "查询失败：" + err1.Error()})
			continue
		}
		updateMap := map[string]any{}
		if exist.Name != regions[i].LocalName {
			updateMap["name"] = regions[i].LocalName
		}
		if exist.Remark != regions[i].RegionEndpoint {
			updateMap["remark"] = regions[i].RegionEndpoint
		}
		if len(updateMap) > 0 {
			err1 = app.DB().Model(&exist).Updates(updateMap).Error
			if err1 != nil {
				results = append(results, cloud.SyncResult{Key: regions[i].LocalName, Result: "更新失败：" + err1.Error()})
				continue
			}
		}
		results = append(results, cloud.SyncResult{Key: regions[i].LocalName, Result: "同步成功"})
	}
	return
}
