package hwcloud

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/asset/cloud"
	"cmdb/pkg/utils"
	"errors"
	"strings"
	"time"

	"github.com/huaweicloud/huaweicloud-sdk-go-v3/core/auth/basic"
	dns "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/dns/v2"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/dns/v2/model"
	region "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/dns/v2/region"
	"gorm.io/gorm"
)

func (a Account) syncPublicDomainsRecords() (result cloud.SyncResults, err error) {
	app.Log().Info("开始同步内网域名解析记录", "account", a.Name)
	r, err := a.syncPublicDomainRecords(0)
	sr := cloud.SyncResult{
		Key: "账号：" + a.Name,
	}
	if err == nil {
		sr.Result = "同步成功"
		// 回收过期域名解析记录
		zones := []asset.PublicDomain{}
		err = app.DB().Where("account_id = ?", a.ID).Find(&zones).Error
		if err != nil {
			sr.Result = "同步成功，但是回收域名解析记录失败" + err.Error()
		}
		for _, zone := range zones {
			err = asset.ExpiredPublicDomainRecords(zone.DomainID, time.Now().Add(-10*time.Minute))
			if err != nil {
				sr.Result = "同步成功，但是回收域名解析记录失败" + err.Error()
			}
		}
	} else {
		sr.Result = r + " " + err.Error()
	}
	result = append(result, sr)
	return
}

func (a Account) syncPublicDomainRecords(offset int32) (result string, err error) {
	accesesKeyID := a.AccessKey
	accessSecret, _ := utils.Base64Decode(a.AccessSecret)
	auth, err := basic.NewCredentialsBuilder().WithAk(accesesKeyID).WithSk(accessSecret).SafeBuild()
	if err != nil {
		result = "认证失败"
		return
	}
	r, err := region.SafeValueOf("cn-north-4")
	if err != nil {
		result = "区域编码错误"
		return
	}
	b, err := dns.DnsClientBuilder().WithRegion(r).WithCredential(auth).SafeBuild()
	if err != nil {
		result = "创建客户端失败" + err.Error()
		return
	}
	client := dns.NewDnsClient(b)
	request := &model.ListRecordSetsRequest{}
	limitRequest := int32(500)
	zoneType := "public"
	request.Limit = &limitRequest
	request.Offset = &offset
	request.ZoneType = &zoneType
	response, err := client.ListRecordSets(request)
	if err != nil {
		result = "获取域名解析失败" + err.Error()
		return
	}
	if response.Recordsets != nil {
		now := time.Now()
		for _, record := range *response.Recordsets {
			name := ""
			if record.Name != nil {
				name = *record.Name
			}
			values := ""
			if record.Records != nil {
				values = strings.Join(*record.Records, "\n")
			}
			recordID := ""
			if record.Id != nil {
				recordID = *record.Id
			}
			createTime, err2 := time.Parse("2006-01-02T15:04:05", *record.CreateAt)
			if err2 != nil {
				createTime = now
			}
			UpdateTime, err2 := time.Parse("2006-01-02T15:04:05", *record.UpdateAt)
			if err2 != nil {
				createTime = now
			}
			remark := ""
			if record.Description != nil {
				remark = *record.Description
			}
			ttl := 60
			if record.Ttl != nil {
				ttl = int(*record.Ttl)
			}
			recordType := ""
			if record.Type != nil {
				recordType = *record.Type
			}
			status := ""
			if record.Status != nil {
				status = *record.Status
			}
			zoneID := ""
			if record.ZoneId != nil {
				zoneID = *record.ZoneId
			}
			exist := asset.PublicDomainRecord{}
			err1 := app.DB().Where("account_id = ? AND domain_id = ? AND record_id = ?", a.ID, zoneID, recordID).Take(&exist).Error
			if errors.Is(err1, gorm.ErrRecordNotFound) {
				err = app.DB().Create(&asset.PublicDomainRecord{
					AccountID: a.ID,
					Rr:        name,
					Status:    status,
					Value:     values,
					RecordID:  recordID,
					Remark:    remark,
					CreatedAt: createTime,
					UpdatedAt: UpdateTime,
					TTL:       ttl,
					Type:      recordType,
					DomainID:  zoneID,
					SyncTime:  now,
				}).Error
			} else if err1 == nil {
				updateMap := map[string]any{"sync_time": now}
				if exist.Status != status {
					updateMap["status"] = status
				}
				if exist.Rr != name {
					updateMap["rr"] = name
				}
				if exist.Value != values {
					updateMap["value"] = values
				}
				if exist.Remark != remark {
					updateMap["remark"] = remark
				}
				if exist.TTL != ttl {
					updateMap["ttl"] = ttl
				}
				if exist.Type != recordType {
					updateMap["type"] = recordType
				}
				if exist.UpdatedAt != UpdateTime {
					updateMap["updated_at"] = UpdateTime
				}
				// if exist.CreatedAt != createTime {
				// 	updateMap["created_at"] = createTime
				// }
				err = app.DB().Model(&exist).Updates(updateMap).Error
			} else {
				err = err1
			}
			if err != nil {
				return
			}
		}
	}
	if len(*response.Recordsets) > 0 && *response.Metadata.TotalCount > offset+500 {
		result, err = a.syncPublicDomainRecords(offset + 500)
	}
	return
}
