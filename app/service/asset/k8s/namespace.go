package k8s

import (
	"cmdb/app"
	"context"
	"encoding/base64"
	"time"

	"gorm.io/gorm"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/clientcmd"
)

type Namespace struct {
	ID        uint           `gorm:"column:id;primaryKey;comment:主键" json:"id"`
	ClusterID uint           `gorm:"column:cluster_id;index;comment:集群ID" json:"cluster_id"`
	Name      string         `gorm:"column:name;type:varchar(255);index;comment:名称" json:"name"`
	Status    string         `gorm:"column:status;type:varchar(255);comment:状态" json:"status"`
	Labels    []Label        `gorm:"many2many:asset_k8s_namespace_labels;joinForeignKey:workload_id;joinReferences:label_id" json:"labels"`
	SyncTime  time.Time      `gorm:"column:sync_time;index;comment:同步时间" json:"sync_time"`
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at;index;comment:删除时间" json:"-"`
}

func (Namespace) TableName() string {
	return "asset_k8s_namespaces"
}

func (space Namespace) SetLabels(labels []Label) (err error) {
	ls := []Label{}
	for i := range labels {
		var l Label
		l, err = GenLabelKey(labels[i].LabelKey, labels[i].LabelValue)
		if err != nil {
			return
		}
		ls = append(ls, l)
	}
	err = app.DB().Model(&space).Association("Labels").Replace(ls)
	return
}

func (c Cluster) getNamespaces() (namespaces []Namespace, err error) {
	kubeconfigBytes, err := base64.StdEncoding.DecodeString(c.Token)
	if err != nil {
		return
	}
	config, err := clientcmd.Load(kubeconfigBytes)
	if err != nil {
		return
	}
	client, err := clientcmd.NewDefaultClientConfig(*config, &clientcmd.ConfigOverrides{}).ClientConfig()
	if err != nil {
		return
	}
	clientset, err := kubernetes.NewForConfig(client)
	if err != nil {
		return
	}
	namespaceList, err := clientset.CoreV1().Namespaces().List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		return
	}
	for i := range namespaceList.Items {
		labels := []Label{}
		for k, v := range namespaceList.Items[i].Labels {
			labels = append(labels, Label{
				LabelKey:   k,
				LabelValue: v,
			})
		}
		namespaces = append(namespaces, Namespace{
			Name:      namespaceList.Items[i].Name,
			ClusterID: c.ID,
			Status:    string(namespaceList.Items[i].Status.Phase),
			Labels:    labels,
			SyncTime:  time.Now(),
		})
	}
	return
}

func (c Cluster) SyncNamespaces() (err error) {
	namespaces, err := c.getNamespaces()
	if err != nil {
		return
	}
	for _, ns := range namespaces {
		var existingNamespace Namespace
		if err = app.DB().Where("name = ? AND cluster_id = ?", ns.Name, ns.ClusterID).Take(&existingNamespace).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				if err = app.DB().Create(&ns).Error; err != nil {
					return
				}
			} else {
				return // 其他错误
			}
		} else {
			updateMap := map[string]any{
				"sync_time": time.Now(),
			}
			if ns.Status != existingNamespace.Status {
				updateMap["status"] = ns.Status
			}
			if err = app.DB().Model(&existingNamespace).Updates(updateMap).Error; err != nil {
				return
			}
			if err = existingNamespace.SetLabels(ns.Labels); err != nil {
				return
			}
		}
	}
	err = app.DB().Where("sync_time < ? AND cluster_id = ? ", time.Now().Add(-10*time.Minute), c.ID).Delete(&Namespace{}).Error
	return
}

func (c Cluster) GetAllNamespaces() (namespaces []Namespace, err error) {
	err = app.DB().Model(&Namespace{}).Where("cluster_id = ?", c.ID).Order("name").Find(&namespaces).Error
	return
}
