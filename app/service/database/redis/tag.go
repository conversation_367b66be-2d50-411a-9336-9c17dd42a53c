package redis

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"errors"
)

func TagInstaces(instances []Instance, tags []asset.Tag) (err error) {
	tx := app.DB().Begin()
	// 遍历实例列表
	for i := range instances {
		// 获取标签
		if err = tx.Model(&instances[i]).Association("Tags").Replace(&tags); err != nil {
			tx.Rollback()
			return
		}
	}
	err = tx.Commit().Error
	return
}

type TagInstanceForm struct {
	TagIDs      []int  `json:"tag_ids"`
	InstanceIDs []int  `json:"instance_ids"`
	OP          string `json:"op"`
}

func (form TagInstanceForm) Do() (err error) {
	switch form.OP {
	case "append":
		tags := []asset.Tag{}
		err = app.DB().Where("id IN (?)", form.TagIDs).Find(&tags).Error
		if err != nil {
			return
		}
		if len(tags) == 0 {
			err = errors.New("tag not found")
		}
		for i := range form.InstanceIDs {
			exist := Instance{}
			err = app.DB().Where("id = ?", form.InstanceIDs[i]).Take(&exist).Error
			if err != nil {
				return
			}
			err = app.DB().Model(&exist).Association("Tags").Append(&tags)
			if err != nil {
				return
			}
		}
	case "delete":
		tags := []asset.Tag{}
		err = app.DB().Where("id IN (?)", form.TagIDs).Find(&tags).Error
		if err != nil {
			return
		}
		if len(tags) == 0 {
			err = errors.New("tag not found")
		}
		for i := range form.InstanceIDs {
			exist := Instance{}
			err = app.DB().Where("id = ?", form.InstanceIDs[i]).Take(&exist).Error
			if err != nil {
				return
			}
			err = app.DB().Model(&exist).Association("Tags").Delete(&tags)
			if err != nil {
				return
			}
		}
	case "replace":
		tags := []asset.Tag{}
		err = app.DB().Where("id IN (?)", form.TagIDs).Find(&tags).Error
		if err != nil {
			return
		}
		if len(tags) == 0 {
			err = errors.New("tag not found")
		}
		for i := range form.InstanceIDs {
			exist := Instance{}
			err = app.DB().Where("id = ?", form.InstanceIDs[i]).Take(&exist).Error
			if err != nil {
				return
			}
			err = app.DB().Model(&exist).Association("Tags").Replace(&tags)
			if err != nil {
				return
			}
		}
	case "clear":
		for i := range form.InstanceIDs {
			exist := Instance{}
			err = app.DB().Where("id = ?", form.InstanceIDs[i]).Take(&exist).Error
			if err != nil {
				return
			}
			err = app.DB().Model(&exist).Association("Tags").Clear()
			if err != nil {
				return
			}
		}
	default:
		err = errors.New("invalid operation")
	}
	return
}
