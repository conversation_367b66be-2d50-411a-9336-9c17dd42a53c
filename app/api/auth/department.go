package auth

import (
	"cmdb/app"
	"cmdb/app/service/auth"
	"strconv"

	"github.com/gin-gonic/gin"
)

func GetDepartments(c *gin.Context) {
	page, _ := strconv.Atoi(c<PERSON>("page", "1"))
	limit, _ := strconv.Atoi(c<PERSON>("limit", "10"))
	offset := (page - 1) * limit
	var keyword, family *string
	if val := c.Query("keyword"); val != "" {
		keyword = &val
	}
	if val := c.<PERSON>("family"); val != "" {
		family = &val
	}
	count, data, err := auth.GetDepartments(offset, limit, family, keyword)
	if err != nil {
		app.Log().Error("获取部分列表失败", "err", err)
		app.FailedResponseMsg(c, "获取部分列表失败")
		return
	}
	app.SuccessResponseDataCount(c, data, count)
}
