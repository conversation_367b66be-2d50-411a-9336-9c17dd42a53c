package note

import (
	"cmdb/app"
	"cmdb/app/service/audit"
	"cmdb/app/service/note"
	"errors"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

func GetBookmarks(c *gin.Context) {
	//TODO
	var limit, offset int
	var keyword *string
	var err error
	limit, err = strconv.<PERSON>oi(c<PERSON>("limit", "10"))
	if err != nil {
		limit = 10
	}
	page, err := strconv.Atoi(c<PERSON>("page", "1"))
	if err != nil {
		page = 1
	}
	offset = (page - 1) * limit
	if val := strings.TrimSpace((c.<PERSON>("keyword"))); val != "" {
		keyword = &val
	}
	count, data, err := note.GetBookmarks(offset, limit, keyword)
	if err != nil {
		app.Log().Error("获取书签列表数据失败", "err", err)
		app.FailedResponseMsg(c, "获取书签列表数据失败")
		return
	}
	app.SuccessResponseDataCount(c, data, count)
}

func CreateBookmark(c *gin.Context) {
	var form note.BookmarkForm
	if err := c.ShouldBindJ<PERSON>N(&form); err != nil {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	if err := form.Create(); err != nil {
		msg := "创建书签失败"
		if errors.Is(err, note.ErrBookmarkExist) {
			msg = err.Error()
		} else {
			app.Log().Error("创建书签失败", "err", err)
		}
		app.FailedResponseMsg(c, msg)
		return
	}
	audit.LogAPIOP(c, "事件管理", "创建书签："+form.Title)
	app.SuccessResponseMsg(c, "创建书签成功")
}

func UpdateBookmark(c *gin.Context) {
	var form note.BookmarkForm
	if err := c.ShouldBindJSON(&form); err != nil {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	var id int
	if id, _ = strconv.Atoi(c.Param("id")); id == 0 {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	bookmark, err := note.GetBookmarkByID(id)
	if err != nil {
		app.FailedResponseMsg(c, "获取书签失败")
		return
	}
	if err := bookmark.Update(form); err != nil {
		msg := "更新书签失败"
		if errors.Is(err, note.ErrBookmarkExist) {
			msg = err.Error()
		} else {
			app.Log().Error("更新书签失败", "err", err)
		}
		app.FailedResponseMsg(c, msg)
		return
	}
	audit.LogAPIOP(c, "事件管理", "更新书签："+form.Title)
	app.SuccessResponseMsg(c, "更新书签成功")
}

func DeleteBookmark(c *gin.Context) {
	var id int
	if id, _ = strconv.Atoi(c.Param("id")); id == 0 {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	bookmark, err := note.GetBookmarkByID(id)
	if err != nil {
		app.FailedResponseMsg(c, "获取书签失败")
		return
	}
	if err := bookmark.Delete(); err != nil {
		app.FailedResponseMsg(c, "删除书签失败")
		return
	}
	audit.LogAPIOP(c, "事件管理", "删除书签："+bookmark.Title)
}
