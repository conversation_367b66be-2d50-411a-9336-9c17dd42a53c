package tencentcloud

import (
	"cmdb/app"
	"cmdb/pkg/utils"

	billing "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/billing/v20180709"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
)

type MonthlyBill struct {
	// ID        uint   `gorm:"column:id;primary_key;auto_increment;comment:ID" json:"id"`
	AccountID uint   `gorm:"column:account_id;index;comment:账户ID" json:"account_id"`
	BillCycle string `gorm:"column:bill_cycle;type:varchar(255);index;comment:账单周期" json:"bill_cycle"`
	// 产品名称：用户所采购的各类云产品，例如：云服务器 CVM
	BusinessCodeName *string `gorm:"column:business_code_name;type:varchar(255);comment:产品名称" json:"business_code_name,omitempty"`
	// 子产品名称：用户采购的具体产品细分类型，例如：云服务器 CVM-标准型 S1
	ProductCodeName *string `gorm:"column:product_code_name;type:varchar(255);comment:子产品名称" json:"product_code_name,omitempty"`
	// 计费模式：资源的计费模式，区分为包年包月和按量计费
	PayModeName *string `gorm:"column:pay_mode_name;type:varchar(255);comment:计费模式" json:"pay_mode_name,omitempty"`
	// 项目名称：资源归属的项目，用户在控制台给资源自主分配项目，未分配则是默认项目
	ProjectName *string `gorm:"column:project_name;type:varchar(255);index;comment:项目名称" json:"project_name,omitempty"`
	// 地域：资源所属地域，如华南地区（广州）
	RegionName *string `gorm:"column:region_name;type:varchar(255);comment:地域" json:"region_name,omitempty"`
	// 可用区：资源所属可用区，如广州三区
	ZoneName *string `gorm:"column:zone_name;type:varchar(255);comment:可用区" json:"zone_name,omitempty"`
	// 资源 ID：账单中出账对象 ID，不同产品因资源形态不同，资源内容不完全相同，如云服务器 CVM 为对应的实例 ID
	ResourceId *string `gorm:"column:resource_id;type:varchar(255);comment:资源ID" json:"resource_id,omitempty"`
	// 资源别名：用户在控制台为资源设置的名称，如果未设置，则默认为空
	ResourceName *string `gorm:"column:resource_name;type:varchar(255);comment:资源别名" json:"resource_name,omitempty"`
	// 交易类型：如包年包月新购、包年包月续费、按量计费扣费等类型
	ActionTypeName *string `gorm:"column:action_type_name;type:varchar(255);comment:交易类型" json:"action_type_name,omitempty"`
	// 订单ID：包年包月计费模式下订购的订单号
	OrderId *string `gorm:"column:order_id;type:varchar(255);comment:订单ID" json:"order_id,omitempty"`
	// 扣费时间：结算扣费时间
	PayTime *string `gorm:"column:pay_time;type:varchar(255);comment:扣费时间" json:"pay_time,omitempty"`
	// 开始使用时间：产品服务开始使用时间
	FeeBeginTime *string `gorm:"column:fee_begin_time;type:varchar(255);comment:开始使用时间" json:"fee_begin_time,omitempty"`
	// 结束使用时间：产品服务结束使用时间
	FeeEndTime *string `gorm:"column:fee_end_time;type:varchar(255);comment:结束使用时间" json:"fee_end_time,omitempty"`
	// 配置描述：该资源下的计费项名称和用量合并展示，仅在资源账单体现
	ConfigDesc *string `gorm:"column:config_desc;type:varchar(255);comment:配置描述" json:"config_desc,omitempty"`
	// 扩展字段1：产品对应的扩展属性信息，仅在资源账单体现
	ExtendField1 *string `gorm:"column:extend_field1;type:varchar(255);comment:扩展字段1" json:"extend_field1,omitempty"`
	// 扩展字段2：产品对应的扩展属性信息，仅在资源账单体现
	ExtendField2 *string `gorm:"column:extend_field2;type:varchar(255);comment:扩展字段2" json:"extend_field2,omitempty"`
	// 原价：原价 = 组件刊例价 * 组件用量 * 使用时长（如果客户享受一口价/合同价则默认不展示，退费类场景也默认不展示）
	TotalCost *string `gorm:"column:total_cost;type:varchar(255);comment:原价" json:"total_cost,omitempty"`
	// 折扣率：本资源享受的折扣率（如果客户享受一口价/合同价则默认不展示，退费场景也默认不展示）
	Discount *string `gorm:"column:discount;type:varchar(255);comment:折扣率" json:"discount,omitempty"`
	// 优惠类型
	ReduceType *string `gorm:"column:reduce_type;type:varchar(255);comment:优惠类型" json:"reduce_type,omitempty"`
	// 优惠后总价
	RealTotalCost *string `gorm:"column:real_total_cost;type:varchar(255);comment:优惠后总价" json:"real_total_cost,omitempty"`
	// 优惠券支出：使用各类优惠券（如代金券、现金券等）支付的金额
	VoucherPayAmount *string `gorm:"column:voucher_pay_amount;type:varchar(255);comment:优惠券支出" json:"voucher_pay_amount,omitempty"`
	// 现金账户支出：通过现金账户支付的金额
	CashPayAmount *string `gorm:"column:cash_pay_amount;type:varchar(255);comment:现金账户支出" json:"cash_pay_amount,omitempty"`
	// 赠送账户支出：使用赠送金支付的金额
	IncentivePayAmount *string `gorm:"column:incentive_pay_amount;type:varchar(255);comment:赠送账户支出" json:"incentive_pay_amount,omitempty"`
	// 分成金账户支出：通过分成金账户支付的金额
	// 注意：此字段可能返回 null，表示取不到有效值。
	TransferPayAmount *string `gorm:"column:transfer_pay_amount;type:varchar(255);comment:分成金账户支出" json:"transfer_pay_amount,omitempty"`
	// 扩展字段3：产品对应的扩展属性信息，仅在资源账单体现
	ExtendField3 *string `gorm:"column:extend_field3;type:varchar(255);comment:扩展字段3" json:"extend_field3,omitempty"`
	// 扩展字段4：产品对应的扩展属性信息，仅在资源账单体现
	ExtendField4 *string `gorm:"column:extend_field4;type:varchar(255);comment:扩展字段4" json:"extend_field4,omitempty"`
	// 扩展字段5：产品对应的扩展属性信息，仅在资源账单体现
	ExtendField5 *string `gorm:"column:extend_field5;type:varchar(255);comment:扩展字段5" json:"extend_field5,omitempty"`
	// 支付者UIN：支付者的账号 ID，账号 ID 是用户在腾讯云的唯一账号标识
	PayerUin *string `gorm:"column:payer_uin;type:varchar(255);comment:支付者UIN" json:"payer_uin,omitempty"`
	// 使用者UIN：实际使用资源的账号 ID
	OwnerUin *string `gorm:"column:owner_uin;type:varchar(255);comment:使用者UIN" json:"owner_uin,omitempty"`
	// 操作者UIN：操作者账号 ID（预付费资源下单或后付费操作开通资源账号的 ID 或者角色 ID ）
	OperateUin *string `gorm:"column:operate_uin;type:varchar(255);comment:操作者UIN" json:"operate_uin,omitempty"`
	// 产品编码
	BusinessCode *string `gorm:"column:business_code;type:varchar(255);comment:产品编码" json:"business_code,omitempty"`
	// 子产品编码
	ProductCode *string `gorm:"column:product_code;type:varchar(255);comment:子产品编码" json:"product_code,omitempty"`
	// 地域ID
	RegionId *int64 `gorm:"column:region_id;type:bigint;comment:地域ID" json:"region_id,omitempty"`
	// 实例类型：购买的产品服务对应的实例类型，包括资源包、RI、SP、竞价实例。正常的实例展示默认为不展示
	InstanceType *string `gorm:"column:instance_type;type:varchar(255);comment:实例类型" json:"instance_type,omitempty"`
	// 预留实例抵扣组件原价：本产品或服务使用预留实例抵扣的组件原价金额
	OriginalCostWithRI *string `gorm:"column:original_cost_with_ri;type:varchar(255);comment:预留实例抵扣组件原价" json:"original_cost_with_ri,omitempty"`
	// 节省计划抵扣组件原价：节省计划抵扣原价=节省计划包抵扣金额/节省计划抵扣率
	OriginalCostWithSP *string `gorm:"column:original_cost_with_sp;type:varchar(255);comment:节省计划抵扣组件原价" json:"original_cost_with_sp,omitempty"`
	// 账单归属月
	// 注意：此字段可能返回 null，表示取不到有效值。
	BillMonth *string `gorm:"column:bill_month;type:varchar(255);comment:账单归属月" json:"bill_month,omitempty"`
}

func (MonthlyBill) TableName() string {
	return "asset_monthly_bills_tencentcloud"
}

func (a Account) syncMonthlyBills(billCycle string) (result string, err error) {
	result, monthlyBills, err := a.syncPageMonthlyBills(billCycle, 1)
	if err != nil {
		return
	}
	dbop := app.DB().Begin()
	err = dbop.Model(&MonthlyBill{}).Where("account_id = ? AND bill_cycle = ?", a.ID, billCycle).Delete(&MonthlyBill{}).Error
	if err != nil {
		dbop.Rollback()
		result += "删除失败:" + err.Error()
		return
	}
	err = dbop.CreateInBatches(&monthlyBills, 1000).Error
	if err != nil {
		dbop.Rollback()
		result += "插入失败:" + err.Error()
		return
	}
	err = dbop.Commit().Error
	if err != nil {
		result += "提交失败:" + err.Error()
		return
	}
	result += "同步账单成功"
	return
}

func (a Account) syncPageMonthlyBills(cycle string, page int) (result string, monthlyBills []MonthlyBill, err error) {
	secret, _ := utils.Base64Decode(a.AccessSecret)
	credential := common.NewCredential(
		a.AccessKey,
		secret,
	)
	// 实例化一个client选项，可选的，没有特殊需求可以跳过
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = "billing.tencentcloudapi.com"
	// 实例化要请求产品的client对象,clientProfile是可选的
	client, _ := billing.NewClient(credential, "", cpf)

	// 实例化一个请求对象,每个接口都会对应一个request对象
	request := billing.NewDescribeBillResourceSummaryRequest()
	limit := 100
	if page < 1 {
		page = 1
	}
	offset := (page - 1) * limit
	request.Month = common.StringPtr(cycle)
	request.Offset = common.Uint64Ptr(uint64(offset))
	request.Limit = common.Uint64Ptr(uint64(limit))
	// 返回的resp是一个BillResourceSummary的实例，与请求对象对应
	response, err := client.DescribeBillResourceSummary(request)
	if err != nil {
		return
	}
	monthlyBills = a.transformMonthlyBills(cycle, response.Response.ResourceSummarySet...)
	if len(monthlyBills) > 0 || response.Response.Total != nil && *response.Response.Total > int64(offset+limit) {
		rs1, pageBills, err1 := a.syncPageMonthlyBills(cycle, page+1)
		if err1 != nil {
			result += rs1
			err = err1
			return
		}
		monthlyBills = append(monthlyBills, pageBills...)
	}
	return
}

func (a Account) transformMonthlyBills(billCycle string, bills ...*billing.BillResourceSummary) (monthlyBills []MonthlyBill) {
	for _, bill := range bills {
		monthlyBills = append(monthlyBills, MonthlyBill{
			AccountID:          a.ID,
			BillCycle:          billCycle,
			BusinessCodeName:   bill.BusinessCodeName,
			ProductCodeName:    bill.ProductCodeName,
			PayModeName:        bill.PayModeName,
			ProjectName:        bill.ProjectName,
			RegionName:         bill.RegionName,
			ZoneName:           bill.ZoneName,
			ResourceId:         bill.ResourceId,
			ResourceName:       bill.ResourceName,
			ActionTypeName:     bill.ActionTypeName,
			OrderId:            bill.OrderId,
			PayTime:            bill.PayTime,
			FeeBeginTime:       bill.FeeBeginTime,
			FeeEndTime:         bill.FeeEndTime,
			ConfigDesc:         bill.ConfigDesc,
			ExtendField1:       bill.ExtendField1,
			ExtendField2:       bill.ExtendField2,
			TotalCost:          bill.TotalCost,
			Discount:           bill.Discount,
			ReduceType:         bill.ReduceType,
			RealTotalCost:      bill.RealTotalCost,
			VoucherPayAmount:   bill.VoucherPayAmount,
			CashPayAmount:      bill.CashPayAmount,
			IncentivePayAmount: bill.IncentivePayAmount,
			TransferPayAmount:  bill.TransferPayAmount,
			ExtendField3:       bill.ExtendField3,
			ExtendField4:       bill.ExtendField4,
			ExtendField5:       bill.ExtendField5,
			PayerUin:           bill.PayerUin,
			OwnerUin:           bill.OwnerUin,
			OperateUin:         bill.OperateUin,
			BusinessCode:       bill.BusinessCode,
			ProductCode:        bill.ProductCode,
			RegionId:           bill.RegionId,
			InstanceType:       bill.InstanceType,
			OriginalCostWithRI: bill.OriginalCostWithRI,
			OriginalCostWithSP: bill.OriginalCostWithSP,
			BillMonth:          bill.BillMonth,
		})
	}
	return
}
