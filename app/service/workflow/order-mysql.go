package workflow

import (
	"bytes"
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/auth"
	"cmdb/app/service/database/mysql"
	"encoding/json"
	"errors"
	"io"
	"os"
	"path"
	"strings"
	"time"

	"gorm.io/datatypes"
)

var (
	ErrSaveAttachmentFail = errors.New("附件保存失败")
	ErrPermissionNeed     = errors.New("权限不能为空")
	ErrProdNeedOrder      = errors.New("生产环境 需要关联 测试工单 或者 开启紧急工单选项")
)

type OrderMySQL struct {
	ID               uint           `gorm:"column:id;primarykey;comment:id" json:"id"`
	SN               string         `gorm:"type:varchar(128);unique;column:sn;comment:工单单号" json:"sn"`
	OPTypes          datatypes.JSON `gorm:"column:op_types;comment:操作类型" json:"op_types"`
	ProjectID        uint           `gorm:"column:project_id;comment:关联项目" json:"project_id"`
	DBName           string         `gorm:"type:varchar(255);column:db_name;comment:数据库名称" json:"db_name"`
	Content          string         `gorm:"type:longtext;column:content;comment:工单内容" json:"content"`
	SQLContent       string         `gorm:"type:longtext;column:sql_content;comment:sql语句" json:"sql_content"`
	Attachments      datatypes.JSON `gorm:"column:attachments;comment:附件记录" json:"attachments"`
	Permission       string         `gorm:"column:permission;comment:数据库帐号开通权限" json:"permission"`
	IterationVersion string         `gorm:"column:iteration_version;comment:迭代版本" json:"iteration_version"`
}

// 设置表名
func (OrderMySQL) TableName() string {
	return "workflow_order_mysqls"
}

type OrderMySQLDetail struct {
	Order            `gorm:"-"`
	OPTypes          datatypes.JSON `gorm:"column:op_types;comment:操作类型" json:"op_types"`
	ProjectID        uint           `gorm:"column:project_id;comment:关联项目" json:"project_id"`
	Project          *mysql.Project `gorm:"-" json:"project,omitempty"`
	DBName           string         `gorm:"type:varchar(255);column:db_name;comment:数据库名称" json:"db_name"`
	Content          string         `gorm:"type:longtext;column:content;comment:工单内容" json:"content"`
	SQLContent       string         `gorm:"type:longtext;column:sql_content;comment:sql语句" json:"sql_content"`
	Attachments      datatypes.JSON `gorm:"column:attachments;comment:附件记录" json:"attachments"`
	Permission       string         `gorm:"column:permission;comment:数据库帐号开通权限" json:"permission"`
	IterationVersion string         `gorm:"column:iteration_version;comment:迭代版本" json:"iteration_version"`
}

// 设置表名
func (OrderMySQLDetail) TableName() string {
	return "workflow_order_mysqls"
}

func GetOrderMySQLDetail(o Order) (detail OrderMySQLDetail, err error) {
	detail = OrderMySQLDetail{}
	err = app.DB().Where("sn = ?", o.SN).Take(&detail).Error
	if err != nil {
		return
	}
	if detail.ProjectID > 0 {
		detail.Project = &mysql.Project{}
		err1 := app.DB().Where("id = ?", detail.ProjectID).Take(detail.Project).Error
		if err1 != nil {
			app.Log().Error("获取项目信息失败", "err", err1)
		}
	}
	o.RelatedOrders, err = o.GetRelatedOrders()
	if err != nil {
		app.Log().Error("获取关联工单失败", "err", err)
	}
	detail.Order = o
	return
}

type OrderMySQLForm struct {
	Title            string       `json:"title" binding:"required,max=255"`
	OrderType        string       `json:"order_type" binding:"required,max=255"`
	Content          string       `json:"content" binding:"required"`
	SQLContent       string       `json:"sql_content"`
	DBName           string       `json:"db_name" binding:"max=255"`
	ProjectID        uint         `json:"project_id"`
	Attachments      []Attachment `json:"attachments"`
	PlanTime         time.Time    `json:"plan_time" binding:"required"`
	Orders           []string     `json:"orders"`
	Env              asset.ENV    `json:"env"`
	Permission       string       `json:"permission"`
	Critical         bool         `json:"critical"`
	IterationVersion string       `json:"iteration_version"`
}

func (form OrderMySQLForm) GenProcesses(sn string, a Applicant, admin *auth.User) (processes []Process, err error) {
	var flow Flow
	err = app.DB().Where("order_type = ?", MySQLOrderType).Take(&flow).Error
	if err != nil {
		return
	}
	flow.Nodes, err = flow.GetFlowNodes()
	if err != nil {
		return
	}
	processes, err = flow.genProcesses(sn, a, admin)
	return
}

func (form OrderMySQLForm) Create(a Applicant, flow Flow, admin *auth.User) (err error) {
	err = form.check()
	if err != nil {
		return
	}
	if form.ProjectID > 0 {
		var project mysql.Project
		// 如果是敏感库，需要走敏感库的流程
		project, err = mysql.GetProjectByID(int(form.ProjectID))
		if err != nil {
			return
		}
		if project.IsSensitive {
			flow, err = GetFlowByCode(MySQLSensitiveOderType.Code())
			if err != nil {
				return
			}
		}
	}
	sn := genSN("mysql")
	processes, err := flow.genProcesses(sn, a, admin)
	if err != nil {
		return
	}
	status := ApprovingStatus
	var index uint
	if len(processes) == 0 {
		err = errors.New("生成流程失败")
		return
	} else if len(processes) <= 1 {
		// 如果流程没有配置就直接结束流程
		status = CompletedStatus
	} else {
		// 如果有流程就获取下一个节点的索引
		index = processes[1].ProcessIndex
		processes[1].Status = ApprovingProcessStatus
	}
	snsByte, _ := json.Marshal(&form.Orders)
	// 过滤重复的附件
	attachments := map[string]Attachment{}
	for i := range form.Attachments {
		attachments[form.Attachments[i].URL] = form.Attachments[i]
	}
	form.Attachments = []Attachment{}
	for _, attachment := range attachments {
		form.Attachments = append(form.Attachments, attachment)
	}
	// 移动保存附件
	for _, attatchment := range form.Attachments {
		if attatchment.URL != "" && !strings.HasPrefix(attatchment.URL, "/api/v1/workflow/order/") {
			return ErrSaveAttachmentFail
		}
		destFile := strings.Replace(attatchment.URL, "/api/v1/workflow/order/", "", 1)
		sourceFile := path.Join("tmp", strings.Replace(attatchment.URL, "/api/v1/workflow/order/", "", 1))
		err = os.MkdirAll(path.Dir(destFile), os.ModePerm|os.ModeAppend|os.ModeDir)
		if err != nil {
			return ErrSaveAttachmentFail
		}
		_, err = os.Stat(destFile)
		if err == nil {
			continue
		}
		var src, dst *os.File
		src, err = os.Open(sourceFile)
		if err != nil {
			return ErrSaveAttachmentFail
		}
		dst, err = os.Create(destFile)
		if err != nil {
			return ErrSaveAttachmentFail
		}
		_, err = io.Copy(dst, src)
		if err != nil {
			return ErrSaveAttachmentFail
		}
	}

	order := Order{
		Title:        form.Title + " - " + a.Name,
		OrderType:    form.OrderType,
		Env:          form.Env,
		ExtType:      DatabaseExtType,
		SN:           sn,
		Critical:     form.Critical,
		ApplicantID:  a.ID,
		Status:       status,
		PlanTime:     &form.PlanTime,
		ProcessIndex: index,
		Orders:       snsByte,
	}
	attachmentsByte, _ := json.Marshal(&form.Attachments)
	extOrder := OrderMySQL{
		SN:               sn,
		Attachments:      attachmentsByte,
		Content:          form.Content,
		ProjectID:        form.ProjectID,
		DBName:           form.DBName,
		SQLContent:       form.SQLContent,
		Permission:       form.Permission,
		IterationVersion: form.IterationVersion,
	}
	dbop := app.DB().Begin()
	err = dbop.Create(&order).Error
	if err != nil {
		dbop.Rollback()
		return
	}
	err = dbop.Create(&extOrder).Error
	if err != nil {
		dbop.Rollback()
		return
	}
	err = dbop.Create(&processes).Error
	if err != nil {
		dbop.Rollback()
		return
	}
	err = dbop.Commit().Error
	if err != nil {
		dbop.Rollback()
	}
	go order.notifyNextProcess()
	return
}

func (form OrderMySQLForm) Update(o Order) (err error) {
	extOrder := OrderMySQL{}
	err = app.DB().Where("sn = ?", o.SN).Take(&extOrder).Error
	if err != nil {
		return
	}
	for _, attatchment := range form.Attachments {
		if attatchment.URL != "" && !strings.HasPrefix(attatchment.URL, "/api/v1/workflow/order/") {
			return
		}
		destFile := strings.Replace(attatchment.URL, "/api/v1/workflow/order/", "", 1)
		sourceFile := path.Join("tmp", strings.Replace(attatchment.URL, "/api/v1/workflow/order/", "", 1))
		err = os.MkdirAll(path.Dir(destFile), os.ModePerm|os.ModeAppend|os.ModeDir)
		if err != nil {
			return ErrSaveAttachmentFail
		}
		var src, dst *os.File
		_, err = os.Stat(destFile)
		if err == nil {
			continue
		}
		dst, err = os.Create(destFile)
		if err != nil {
			return ErrSaveAttachmentFail
		}
		src, err = os.Open(sourceFile)
		if err != nil {
			return ErrSaveAttachmentFail
		}
		_, err = io.Copy(dst, src)
		if err != nil {
			return ErrSaveAttachmentFail
		}
	}
	updateOrderMap := map[string]interface{}{}
	if o.Title != form.Title {
		updateOrderMap["title"] = form.Title
	}
	if o.OrderType != form.OrderType {
		updateOrderMap["order_type"] = form.OrderType
	}
	updateMySQLMap := map[string]interface{}{}
	if extOrder.Content != form.Content {
		updateMySQLMap["content"] = form.Content
	}
	if extOrder.DBName != form.DBName {
		updateMySQLMap["db_name"] = form.DBName
	}
	if extOrder.Permission != form.Permission {
		updateMySQLMap["permission"] = form.Permission
	}
	if extOrder.SQLContent != form.SQLContent {
		updateMySQLMap["sql_content"] = form.SQLContent
	}
	attachments, _ := json.Marshal(&form.Attachments)
	if !bytes.Equal(extOrder.Attachments, attachments) {
		updateMySQLMap["attachments"] = attachments
	}
	if o.Env != form.Env {
		updateOrderMap["env"] = form.Env
	}
	snsByte, _ := json.Marshal(&form.Orders)
	if !bytes.Equal(o.Orders, snsByte) {
		updateOrderMap["orders"] = snsByte
	}
	if o.Critical != form.Critical {
		updateOrderMap["critical"] = form.Critical
	}
	if !o.PlanTime.Equal(form.PlanTime) {
		updateOrderMap["plan_time"] = form.PlanTime
	}
	dbop := app.DB().Begin()
	if len(updateOrderMap) > 0 {
		err = app.DB().Model(&Order{}).Where("sn = ?", o.SN).Updates(updateOrderMap).Error
		if err != nil {
			dbop.Rollback()
			return
		}
	}
	if len(updateMySQLMap) > 0 {
		err = app.DB().Model(&OrderMySQL{}).Where("sn = ?", o.SN).Updates(updateMySQLMap).Error
		if err != nil {
			dbop.Rollback()
			return
		}
	}
	err = dbop.Commit().Error
	if err != nil {
		dbop.Rollback()
	}
	return
}

func (form OrderMySQLForm) check() (err error) {
	if form.OrderType == "数据库应用账号开通" && form.Permission == "" {
		return ErrPermissionNeed
	}
	if !form.Critical && form.Env == asset.PRODENV && len(form.Orders) == 0 {
		return ErrProdNeedOrder
	}
	return
}

// 通过SN获取数据库工单的附件信息
func GetMySQLOrderAttachements(sn string) (attachments []Attachment, err error) {
	m := OrderMySQL{}
	err = app.DB().Select("attachments").Where("sn = ?", sn).Take(&m).Error
	if err == nil {
		err = json.Unmarshal(m.Attachments, &attachments)
	}
	return
}

func GetOrderMySQLBySN(sn string) (o OrderMySQL, err error) {
	err = app.DB().Where("sn =?", sn).Take(&o).Error
	return
}

func (o OrderMySQL) GetProjectTableMetaData() (tables []mysql.Table, err error) {
	if o.SQLContent == "" || o.DBName == "" || o.ProjectID == 0 {
		return
	}

	sqls := strings.Split(o.SQLContent, ";")
	tableNames := []string{}
	for _, sql := range sqls {
		sql = strings.TrimSpace(sql)
		if sql == "" {
			continue
		}
		tableName := ""
		if strings.Contains(strings.ToLower(sql), "alter table") {
			tableName = getAlterTableTableName(strings.ToLower(sql))
		} else if strings.Contains(strings.ReplaceAll(strings.ToLower(sql), " ", ""), "altettable") {
			tableName = getAlterTableTableName(strings.ToLower(sql))
		}
		if tableName != "" {
			tableNames = append(tableNames, tableName)
		}
	}
	project, err := mysql.GetProjectByID(int(o.ProjectID))
	if err != nil {
		return
	}
	tables, err = project.GetProjectTableMetaData(o.DBName, tableNames)
	return
}

func getAlterTableTableName(sql string) string {
	tableName := ""
	parts := strings.Split(sql, "table")
	if len(parts) < 2 {
		return tableName
	}
	tablePart := strings.TrimSpace(parts[1])
	fields := strings.Fields(tablePart)
	if len(fields) < 1 {
		return tableName
	}
	tableName = fields[0]
	tableName = strings.ReplaceAll(tableName, "`", "")
	tableName = strings.TrimSpace(tableName)
	if strings.Contains(tableName, ".") {
		parts := strings.Split(tableName, ".")
		if len(parts) > 1 {
			tableName = parts[1]
		}
	}
	return tableName
}
