package asset

import (
	"cmdb/app"
	"testing"

	"github.com/jinzhu/now"
)

func TestStat(t *testing.T) {
	err := app.NewApp("../../../../app.ini")
	if err != nil {
		t.<PERSON>al(err)
	}
	err = app.ConnectDB()
	if err != nil {
		t.<PERSON><PERSON>(err)
	}
	defer func() {
		dbop, err := app.DB().DB()
		if err != nil {
			t.<PERSON><PERSON>(err)
		}
		err = dbop.Close()
		if err != nil {
			t.<PERSON><PERSON>(err)
		}
	}()
	err = Stat(now.BeginningOfDay())
	if err != nil {
		t.<PERSON><PERSON>(err)
	}
}
