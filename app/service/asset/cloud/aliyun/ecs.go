package aliyun

import (
	"bytes"
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/asset/cloud"
	"cmdb/pkg/utils"
	"errors"
	"strconv"
	"strings"
	"time"

	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/requests"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/ecs"
	"gorm.io/gorm"
)

var amdSpecPres = []string{
	"ecs.g8a",
	"ecs.c8a",
	"ecs.r8a",
	"ecs.g8ae",
	"ecs.c8ae",
	"ecs.r8ae",
	"ecs.g7a",
	"ecs.c7a",
	"ecs.r7a",
	"ecs.g6a",
	"ecs.c6a",
	"ecs.r6a",
}

func (a Account) syncECSs() (result cloud.SyncResults, err error) {
	dcs, err := asset.GetDatacentersByCloudType(asset.AliyunCloudType)
	if err != nil {
		return
	}
	for _, dc := range dcs {
		r, err := a.syncRegionECSs(dc, 1)
		sr := cloud.SyncResult{
			Key: dc.Name,
		}
		if err == nil {
			sr.Result = "同步成功"
			// 回收过期主机
			err = a.expiredHosts(time.Now().Add(-10*time.Minute), dc)
			if err != nil {
				sr.Result = "同步成功，但是回收主机失败" + err.Error()
			}
		} else {
			sr.Result = r + " " + err.Error()
		}
		result = append(result, sr)
	}
	return
}

func (a Account) syncRegionECSs(dc asset.Datacenter, page int) (result string, err error) {
	accessSecret, _ := utils.Base64Decode(a.AccessSecret)
	client, err := ecs.NewClientWithAccessKey(dc.Code, a.AccessKey, accessSecret)
	if err != nil {
		result = "创建客户端失败"
		return
	}
	if page < 1 {
		page = 1
	}
	request := ecs.CreateDescribeInstancesRequest()
	request.Scheme = "https"
	pageSize := 100
	request.PageNumber = requests.NewInteger(page)
	request.PageSize = requests.NewInteger(pageSize)
	response, err := client.DescribeInstances(request)
	if err != nil {
		result = "请求失败"
		return
	}
	total := response.TotalCount
	err = a.saveECS(dc, response.Instances.Instance)
	if err != nil {
		result = "保存同步记录失败"
		return
	}
	if total < pageSize {
		return
	}
	if page*pageSize < total {
		// 下一页
		result, err = a.syncRegionECSs(dc, page+1)
		if err != nil {
			return
		}
	}
	return
}

func (a Account) saveECS(dc asset.Datacenter, instances []ecs.Instance) (err error) {
	for _, instance := range instances {
		now := time.Now()
		createTime, _ := time.Parse("2006-01-02T15:04Z", instance.CreationTime)
		ip := ""
		publicIP := ""
		if len(instance.VpcAttributes.PrivateIpAddress.IpAddress) > 0 {
			ip = instance.VpcAttributes.PrivateIpAddress.IpAddress[0]
		} else if len(instance.InnerIpAddress.IpAddress) > 0 {
			ip = instance.InnerIpAddress.IpAddress[0]
		}
		if len(instance.PublicIpAddress.IpAddress) > 0 {
			publicIP = instance.PublicIpAddress.IpAddress[0]
		} else if len(instance.EipAddress.IpAddress) > 0 {
			publicIP = instance.EipAddress.IpAddress
		}
		var status asset.HostStatus
		if instance.Status == "Stopped" {
			status = asset.StoppedHostStatus
		} else if instance.Status == "Running" {
			status = asset.RunningHostStatus
		} else if instance.Status == "Stopping" {
			status = asset.StoppedHostStatus
		} else if instance.Status == "Starting" {
			status = asset.StoppedHostStatus
		}
		expiredTime, _ := time.Parse("2006-01-02T15:04Z", instance.ExpiredTime)
		var isMonitor bool = true
		chargeType := asset.PrepaidChargeType
		if instance.InstanceChargeType == "PostPaid" {
			chargeType = asset.PostpaidChargeType
		}
		if chargeType == asset.PostpaidChargeType {
			isMonitor = false
		}
		var remotePort uint = 22
		if instance.OSType == "windows" {
			remotePort = 3389
		}
		isAMD := false
		for _, spec := range amdSpecPres {
			if strings.Contains(instance.InstanceType, spec) {
				isAMD = true
				break
			}
		}
		exist := asset.Host{}
		err = app.DB().Where("sn = ? AND host_type = ?", instance.InstanceId, asset.AliyunHostType).Take(&exist).Error
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 新增
			err = app.DB().Create(&asset.Host{
				IP:              ip,
				PublicIP:        publicIP,
				OS:              instance.OSName,
				SN:              instance.InstanceId,
				Name:            instance.InstanceName,
				Position:        instance.VpcAttributes.VpcId,
				HostType:        asset.AliyunHostType,
				ChargeType:      chargeType,
				DatacenterID:    dc.ID,
				AccountID:       a.ID,
				Cabinet:         instance.ZoneId,
				CreatedAt:       createTime,
				Memory:          uint(instance.Memory),
				CPUThread:       uint(instance.Cpu),
				IsAMD:           isAMD,
				OSType:          instance.OSType,
				Status:          status,
				SyncTime:        &now,
				PingMonitor:     isMonitor,
				ExpiredTime:     &expiredTime,
				RemotePort:      remotePort,
				Model:           instance.InstanceType,
				GPUAmount:       uint(instance.GPUAmount),
				GPUSpec:         instance.GPUSpec,
				Remark:          instance.Description,
				ResourceGroupID: instance.ResourceGroupId,
			}).Error
			if err == nil {
				TagECSBySN(instance.ActivationId, instance.Tags.Tag)
			}
		} else if err == nil {
			updateMap := map[string]interface{}{"sync_time": time.Now()}
			var changeLog bytes.Buffer
			if exist.IP != ip {
				changeLog.WriteString("IP：" + exist.IP + " => " + ip + "\n")
				updateMap["ip"] = ip
			}
			if exist.IsAMD != isAMD {
				changeLog.WriteString("是否AMD：" + strconv.FormatBool(exist.IsAMD) + " => " + strconv.FormatBool(isAMD) + "\n")
				updateMap["is_amd"] = isAMD
			}
			if exist.PublicIP != publicIP {
				changeLog.WriteString("公网IP：" + exist.PublicIP + " => " + publicIP + "\n")
				updateMap["public_ip"] = publicIP
			}
			if exist.Name != instance.InstanceName {
				changeLog.WriteString("名称：" + exist.Name + " => " + instance.InstanceName + "\n")
				updateMap["name"] = instance.InstanceName
			}
			if exist.GPUAmount != uint(instance.GPUAmount) {
				changeLog.WriteString("GPU数量：" + strconv.Itoa(int(exist.GPUAmount)) + " => " + strconv.Itoa(int(instance.GPUAmount)) + "\n")
				updateMap["gpu_amount"] = instance.GPUAmount
			}
			if exist.GPUSpec != instance.GPUSpec {
				changeLog.WriteString("GPU规格：" + exist.GPUSpec + " => " + instance.GPUSpec + "\n")
				updateMap["gpu_spec"] = instance.GPUSpec
			}
			if exist.Status != status {
				changeLog.WriteString("主机状态：" + exist.Status.String() +
					" => " + status.String() + "\n")
				updateMap["status"] = status
			}
			if exist.Cabinet != instance.ZoneId {
				changeLog.WriteString("机柜变更：" + exist.Cabinet + " => " + instance.ZoneId + "\n")
				updateMap["cabinet"] = instance.ZoneId
			}
			if exist.ExpiredTime != nil && !exist.ExpiredTime.Equal(expiredTime) {
				changeLog.WriteString("过期时间：" + exist.ExpiredTime.Format("2006-01-02 15:04:05") + " => " + expiredTime.Format("2006-01-02 15:04:05") + "\n")
				updateMap["expired_time"] = &expiredTime
			} else if exist.ExpiredTime == nil {
				changeLog.WriteString("过期时间：" + expiredTime.Format("2006-01-02 15:04:05") + "\n")
				updateMap["expired_time"] = &expiredTime
			}
			if exist.OSType != instance.OSType {
				changeLog.WriteString("系统类型：" + exist.OSType + " => " + instance.OSType + "\n")
				updateMap["os_type"] = instance.OSType
			}
			if instance.VpcAttributes.VpcId != exist.Position {
				changeLog.WriteString("机柜柜位/vpc：" + exist.Position + " => " + instance.VpcAttributes.VpcId + "\n")
				updateMap["position"] = instance.VpcAttributes.VpcId
			}
			if instance.ResourceGroupId != exist.ResourceGroupID {
				changeLog.WriteString("资源组：" + exist.ResourceGroupID + " => " + instance.ResourceGroupId + "\n")
				updateMap["resource_group_id"] = instance.ResourceGroupId
			}
			if exist.Model != instance.InstanceType {
				changeLog.WriteString("型号：" + exist.Model + " => " + instance.InstanceType + "\n")
				updateMap["model"] = instance.InstanceType
			}
			if exist.ChargeType != chargeType {
				updateMap["charge_type"] = chargeType
				changeLog.WriteString("付费类型：" + exist.ChargeType.String() + " => " + chargeType.String() + "\n")
			}
			if exist.AccountID != a.ID {
				updateMap["account_id"] = a.ID
			}
			if exist.Remark != instance.Description {
				updateMap["remark"] = instance.Description
				changeLog.WriteString("备注：" + exist.Remark + " => " + instance.Description + "\n")
			}
			err = app.DB().Model(&exist).Updates(updateMap).Error
			if err == nil {
				if len(changeLog.String()) > 0 {
					exist.LogChanges("同步更新", changeLog.String())
				}
				TagECSByID(exist.ID, instance.Tags.Tag)
			}
		}
		if err != nil {
			return
		}
	}
	return
}

func (a Account) expiredHosts(expiredTime time.Time, dc asset.Datacenter) (err error) {
	hosts := []asset.Host{}
	err = app.DB().Where("account_id = ? and sync_time < ? and datacenter_id = ?", a.ID, expiredTime, dc.ID).Find(&hosts).Error
	if err != nil {
		return
	}
	for i := range hosts {
		err = app.DB().Model(&hosts[i]).Updates(map[string]any{
			"status":     asset.RecycleHostStatus,
			"deleted_at": time.Now(),
		}).Error
		if err != nil {
			return
		}
		hosts[i].LogChanges("同步更新", "回收主机")
	}
	return
}

func TagECSByID(id uint, tags []ecs.Tag) (err error) {
	if len(tags) == 0 {
		return
	}
	instance := asset.Host{}
	err = app.DB().Where("id = ?", id).Take(&instance).Error
	if err != nil {
		return
	}
	opTags := []asset.Tag{}
	var t asset.Tag
	for i := range tags {
		if strings.HasPrefix(tags[i].TagKey, "ack.") || strings.HasPrefix(tags[i].TagKey, "acs:") || strings.HasPrefix(tags[i].TagKey, "k8s.") {
			continue
		}
		t, err = asset.GenTag(tags[i].TagKey, tags[i].TagValue)
		if err != nil {
			return
		}
		opTags = append(opTags, t)
	}
	err = app.DB().Model(&instance).Association("Tags").Append(opTags)
	return
}

func TagECSBySN(sn string, tags []ecs.Tag) (err error) {
	if len(tags) == 0 {
		return
	}
	instance := asset.Host{}
	err = app.DB().Where("sn = ?", sn).Take(&instance).Error
	if err != nil {
		return
	}
	opTags := []asset.Tag{}
	var t asset.Tag
	for i := range tags {
		if strings.HasPrefix(tags[i].TagKey, "ack.") || strings.HasPrefix(tags[i].TagKey, "acs:") || strings.HasPrefix(tags[i].TagKey, "k8s.") {
			continue
		}
		t, err = asset.GenTag(tags[i].TagKey, tags[i].TagValue)
		if err != nil {
			return
		}
		opTags = append(opTags, t)
	}
	err = app.DB().Model(&instance).Association("Tags").Append(opTags)
	return
}
