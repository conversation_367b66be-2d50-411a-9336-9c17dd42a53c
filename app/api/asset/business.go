package asset

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/audit"
	"errors"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

type BusinessRow struct {
	asset.Business
	HostCount int64 `json:"host_count"`
}

func GetBusinesses(c *gin.Context) {
	// 获取查询参数
	keyword := c.Query("keyword")
	// 获取分页参数
	page, _ := strconv.Atoi(c.<PERSON>("page", "1"))
	limit, _ := strconv.Atoi(c.<PERSON>ult<PERSON>("limit", "10"))
	// 计算偏移量
	offset := (page - 1) * limit
	// 查询业务列表
	count, data, err := asset.GetBusinesses(offset, limit, keyword)
	if err != nil {
		app.Log().Error("获取业务列表失败", "err", err)
		app.FailedResponseMsg(c, "获取业务列表失败")
		return
	}
	newData := make([]BusinessRow, len(data))
	for i, v := range data {
		count, _ := v.GetHostCount()
		newData[i] = BusinessRow{Business: v, HostCount: count}
	}
	app.SuccessResponseDataCount(c, newData, count)
}

func AddBusiness(c *gin.Context) {
	var form asset.BusinessForm
	if err := c.ShouldBind(&form); err != nil {
		app.Log().Error("添加业务是，验证表单失败", "err", err)
		app.FailedResponseMsg(c, "表单验证失败")
		return
	}
	if err := form.Create(); err != nil {
		msg := "添加失败"
		if errors.Is(err, asset.ErrBusinessExist) {
			msg = err.Error()
		} else {
			app.Log().Error("添加业务失败", "err", err)
		}
		app.FailedResponseMsg(c, msg)
		return
	}
	audit.LogAPIOP(c, "资产管理", "添加业务："+form.Name)
	app.SuccessResponseMsg(c, "添加成功")
}

func UpdateBusiness(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.Log().Error("更新业务失败，参数错误", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	var form asset.BusinessForm
	if err := c.ShouldBind(&form); err != nil {
		app.Log().Error("更新业务是，验证表单失败", "err", err)
		app.FailedResponseMsg(c, "表单验证失败")
		return
	}
	business, err := asset.GetBusinessByID(id)
	if err != nil {
		app.Log().Error("更新业务失败，获取业务失败", "err", err)
		app.FailedResponseMsg(c, "获取业务失败")
		return
	}
	if err := business.Update(form); err != nil {
		msg := "更新失败"
		if errors.Is(err, asset.ErrBusinessExist) {
			msg = err.Error()
		} else {
			app.Log().Error("更新业务失败", "err", err)
		}
		app.FailedResponseMsg(c, msg)
		return
	}
	audit.LogAPIOP(c, "资产管理", "更新业务："+form.Name)
	app.SuccessResponseMsg(c, "更新成功")
}

func DeleteBusiness(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.Log().Error("删除业务失败，参数错误", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	business, err := asset.GetBusinessByID(id)
	if err != nil {
		app.Log().Error("删除业务失败，获取业务失败", "err", err)
		app.FailedResponseMsg(c, "获取业务失败")
		return
	}
	if err := business.Delete(); err != nil {
		app.Log().Error("删除业务失败", "err", err)
		app.FailedResponseMsg(c, "删除失败")
		return
	}
	audit.LogAPIOP(c, "资产管理", "删除业务："+business.Name)
	app.SuccessResponseMsg(c, "删除成功")
}

func GetAllBusinesses(c *gin.Context) {
	data, err := asset.GetAllBusinesses()
	if err != nil {
		app.Log().Error("获取业务失败", "err", err)
		app.FailedResponseMsg(c, "获取业务失败")
		return
	}
	app.SuccessResponseData(c, data)
}

func UpdateBusinessResourceGroups(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.Log().Error("更新业务资源失败，参数错误", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	var form asset.BusinessSetResourceGroupForm
	if err := c.ShouldBind(&form); err != nil {
		app.Log().Error("更新业务资源失败，验证表单失败", "err", err)
		app.FailedResponseMsg(c, "表单验证失败")
		return
	}
	business, err := asset.GetBusinessByID(id)
	if err != nil {
		app.Log().Error("更新业务资源失败，获取业务失败", "err", err)
		app.FailedResponseMsg(c, "获取业务失败")
		return
	}
	if err := business.SetResourceGroups(form); err != nil {
		app.Log().Error("更新业务资源失败", "err", err)
		app.FailedResponseMsg(c, "更新失败")
		return
	}
	audit.LogAPIOP(c, "资产管理", "更新业务资源组："+business.Name)
	app.SuccessResponseMsg(c, "更新成功")
}

func GetBusinessMonthlyBills(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.Log().Error("获取业务账单失败，参数错误", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	now := time.Now()
	endTime, err := time.ParseInLocation("2006-01", c.Query("end_time"), time.Local)
	if err != nil {
		endTime = now
	}
	startTime, err := time.ParseInLocation("2006-01", c.Query("start_time"), time.Local)
	if err != nil {
		startTime = endTime.AddDate(0, -6, 0)
	}
	business, err := asset.GetBusinessByID(id)
	if err != nil {
		app.Log().Error("获取业务账单失败，获取业务失败", "err", err)
		app.FailedResponseMsg(c, "获取业务失败")
		return
	}
	amount, err := business.GetMonthlyBillAmounts(startTime, endTime)
	if err != nil {
		app.Log().Error("获取业务账单失败", "err", err)
		app.FailedResponseMsg(c, "获取账单失败")
		return
	}
	app.SuccessResponseData(c, amount)
}
