package slowlog

import (
	"cmdb/app"
	"cmdb/app/service/database/slowlog"
	"strconv"

	"github.com/gin-gonic/gin"
)

func GetRedisBigKeys(c *gin.Context) {
	id, err := strconv.Atoi(c.<PERSON>m("id"))
	if err != nil || id <= 0 {
		app.Log().Error("获取Redis慢日志失败", "err", err)
		app.FailedResponseMsg(c, "id参数错误")
		return
	}
	s, err := slowlog.GetSlowlogStoreByID(id)
	if err != nil {
		app.Log().Error("获取Redis慢日志失败", "err", err)
		app.FailedResponseMsg(c, "获取失败")
		return
	}
	page, _ := strconv.Atoi(c.<PERSON>("page", "1"))
	limit, _ := strconv.Atoi(c.<PERSON>("limit", "10"))
	var keyword *string
	if val := c.Que<PERSON>("keyword"); val != "" {
		keyword = &val
	}
	offset := (page - 1) * limit
	count, data, err := s.GetRedisBigKeys(offset, limit, keyword)
	if err != nil {
		app.Log().Error("获取Redis慢日志失败", "err", err)
		app.FailedResponseMsg(c, "获取失败")
		return
	}
	app.SuccessResponseDataCount(c, data, count)
}

func GetRedisBigKeysStringStatistics(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil || id <= 0 {
		app.Log().Error("获取Redis慢日志统计失败", "err", err)
		app.FailedResponseMsg(c, "id参数错误")
		return
	}
	s, err := slowlog.GetSlowlogStoreByID(id)
	if err != nil {
		app.Log().Error("获取Redis慢日志统计失败", "err", err)
		app.FailedResponseMsg(c, "获取失败")
		return
	}
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	var keyword *string
	if val := c.Query("keyword"); val != "" {
		keyword = &val
	}
	offset := (page - 1) * limit
	count, data, err := s.GetRedisBigKeysStringStatistics(offset, limit, keyword)
	if err != nil {
		app.Log().Error("获取Redis慢日志统计失败", "err", err)
		app.FailedResponseMsg(c, "获取失败")
		return
	}
	app.SuccessResponseDataCount(c, data, count)
}
