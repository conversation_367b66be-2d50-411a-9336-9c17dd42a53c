package mysql

import (
	"cmdb/app"
	"cmdb/app/service/audit"
	"cmdb/app/service/database/mysql"

	"github.com/gin-gonic/gin"
)

func TagInstaces(c *gin.Context) {
	var form mysql.TagInstancesForm
	if err := c.ShouldBindJSON(&form); err != nil {
		app.Log().Debug("MySQL实例标签绑定失败", "err", err)
		app.FailedResponseMsg(c, "表单验证失败")
		return
	}

	if err := form.Do(); err != nil {
		app.Log().Error("MySQL实例标签绑定失败", "err", err)
		app.FailedResponseMsg(c, "操作失败")
		return
	}
	audit.LogAPIOP(c, "数据库管理", "批量设置实例标签")
	app.SuccessResponseMsg(c, "操作成功")
}
