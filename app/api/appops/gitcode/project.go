package gitcode

import (
	"cmdb/app"
	"cmdb/app/service/appops/gitcode"
	"strconv"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
)

type ProjectRow struct {
	gitcode.Project
	GitlabName string `json:"gitlab_name"`
}

func GetProjects(c *gin.Context) {
	page, _ := strconv.Atoi(c<PERSON>("page", "1"))
	limit, _ := strconv.Atoi(c<PERSON>("limit", "10"))
	offset := (page - 1) * limit
	var keyword *string
	if val := c.Query("keyword"); val != "" {
		keyword = &val
	}
	var groupID *int
	if val, err := strconv.Atoi(c.<PERSON>("group_id")); err == nil {
		groupID = &val
	}
	var gitlabID *int
	if val, err := strconv.Atoi(c.Query("gitlab_id")); err == nil {
		gitlabID = &val
	}
	count, data, err := gitcode.GetProjects(offset, limit, keyword, groupID, gitlabID)
	if err != nil {
		app.Log().Error("获取项目列表失败", "err", err)
		app.FailedResponseMsg(c, "获取项目列表失败")
		return
	}
	newData := make([]ProjectRow, len(data))
	gitlabNameMap := sync.Map{}
	for i := range data {
		newData[i].Project = data[i]
		if val, ok := gitlabNameMap.Load(data[i].GitlabID); ok {
			newData[i].GitlabName = val.(string)
		} else {
			gitlabName, err := data[i].GetGitlabName()
			if err == nil {
				gitlabNameMap.Store(data[i].GitlabID, gitlabName)
				newData[i].GitlabName = gitlabName
			}
		}
	}
	app.SuccessResponseDataCount(c, newData, count)
}

func SyncProjects(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.Log().Error("同步GitCode项目失败", "err", err)
		app.FailedResponseMsg(c, "同步GitCode项目失败")
		return
	}
	gitlabObject, err := gitcode.GetGitlabByID(id)
	if err != nil {
		app.Log().Error("同步GitCode项目失败", "err", err)
		app.FailedResponseMsg(c, "同步GitCode项目失败")
		return
	}
	// 创建一个done通道
	done := make(chan struct{}, 1)
	// 启动一个goroutine进行同步
	go func() {

		err = gitlabObject.SyncProjects()
		if err != nil {
			// 记录日志
			app.Log().Error("同步GitCode项目时，获取项目信息失败：", "err", err)
		}
		// 发送完成信号
		done <- struct{}{}
	}()
	ticker := time.NewTicker(500 * time.Millisecond)
	select {
	case <-done:
		if err != nil {
			app.FailedResponseMsg(c, err.Error())
		} else {
			app.SuccessResponseMsg(c, "同步成功")
		}
		break
	case <-ticker.C:
		app.SuccessResponseMsg(c, "任务提交成功,正在同步中，请稍后...")
		break
	}

}

func GetAllProjects(c *gin.Context) {
	projects, err := gitcode.GetAllProjects()
	if err != nil {
		app.Log().Error("获取项目列表失败", "err", err)
		app.FailedResponseMsg(c, "获取项目列表失败")
		return
	}
	app.SuccessResponseData(c, projects)
}
