package setting

import (
	"cmdb/app"
	"cmdb/app/service/setting"

	"github.com/gin-gonic/gin"
)

func UpdateSetting(c *gin.Context) {
	var form []setting.SettingForm
	if err := c.ShouldBindJSON(&form); err != nil {
		app.Log().Error("更新配置失败", "err", err)
		app.FailedResponseMsg(c, "更新配置失败")
		return
	}
	if err := setting.UpdateSetting(form); err != nil {
		app.Log().Error("更新配置失败", "err", err)
		app.FailedResponseMsg(c, "更新配置失败")
		return
	}
	app.SuccessResponseMsg(c, "更新配置成功")
}

func GetSettingSections(c *gin.Context) {
	data, err := setting.GetSettingSections()
	if err != nil {
		app.Log().Error("获取配置失败", "err", err)
		app.FailedResponseMsg(c, "获取配置失败")
		return
	}
	app.SuccessResponseData(c, data)
}

func GetSettingSectionItems(c *gin.Context) {
	name := c.Param("section")
	section, err := setting.GetSettingSectionByName(name)
	if err != nil {
		app.Log().Error("获取配置失败", "err", err)
		app.FailedResponseMsg(c, "获取配置失败")
		return
	}
	if err := section.GetItems(); err != nil {
		app.Log().Error("获取配置失败", "err", err)
		app.FailedResponseMsg(c, "获取配置失败")
		return
	}
	app.SuccessResponseData(c, section.Items)
}
