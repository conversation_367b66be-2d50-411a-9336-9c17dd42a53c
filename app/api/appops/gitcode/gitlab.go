package gitcode

import (
	"cmdb/app"
	"cmdb/app/service/appops/gitcode"
	"cmdb/app/service/audit"
	"errors"
	"strconv"

	"github.com/gin-gonic/gin"
)

func CreateGitlab(c *gin.Context) {
	var form gitcode.GitlabForm
	if err := c.ShouldBindJSON(&form); err != nil {
		app.Log().Debug("添加GitLab失败", "err", err)
		app.FailedResponseMsg(c, "表单验证失败")
		return
	}
	if err := form.Create(); err != nil {
		msg := "添加失败"
		if errors.Is(err, gitcode.ErrGitlabExist) {
			msg = err.Error()
		} else {
			app.Log().Error("添加GitLab失败", "err", err)
		}
		app.FailedResponseMsg(c, msg)
		return
	}
	audit.LogAPIOP(c, "GitCode管理", "添加GitLab："+form.Name)
	app.SuccessResponseMsg(c, "添加成功")
}

func GetGitlabs(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	offset := (page - 1) * limit
	var keyword *string
	if val := c.Query("keyword"); val != "" {
		keyword = &val
	}
	count, data, err := gitcode.GetGitlabs(offset, limit, keyword)
	if err != nil {
		app.Log().Error("获取GitLab列表失败", "err", err)
		app.FailedResponseMsg(c, "获取GitLab列表失败")
		return
	}
	app.SuccessResponseDataCount(c, data, count)
}

func DeleteGitlab(c *gin.Context) {
	id, _ := strconv.Atoi(c.Param("id"))
	gitlabObject, err := gitcode.GetGitlabByID(id)
	if err != nil {
		app.Log().Error("删除GitLab失败", "err", err)
		app.FailedResponseMsg(c, "删除GitLab失败")
		return
	}
	if err := gitlabObject.Delete(); err != nil {
		app.Log().Error("删除GitLab失败", "err", err)
		app.FailedResponseMsg(c, "删除GitLab失败")
		return
	}
	audit.LogAPIOP(c, "GitCode管理", "删除GitLab:"+gitlabObject.Name)
	app.SuccessResponseMsg(c, "删除成功")
}

func UpdateGitlab(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.Log().Debug("更新GitLab失败", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	var form gitcode.GitlabForm
	if err := c.ShouldBindJSON(&form); err != nil {
		app.Log().Debug("更新GitLab失败", "err", err)
		app.FailedResponseMsg(c, "表单验证失败")
		return
	}
	gitlabObject, err := gitcode.GetGitlabByID(id)
	if err != nil {
		app.Log().Error("更新GitLab失败", "err", err)
		app.FailedResponseMsg(c, "GitLab不存在")
		return
	}
	if err := gitlabObject.Update(form); err != nil {
		msg := "更新失败"
		if errors.Is(err, gitcode.ErrGitlabExist) {
			msg = err.Error()
		} else {
			app.Log().Error("更新GitLab失败", "err", err)
		}
		app.FailedResponseMsg(c, msg)
		return
	}
	audit.LogAPIOP(c, "GitCode管理", "更新GitLab:"+gitlabObject.Name)
	app.SuccessResponseMsg(c, "更新成功")
}

func GetAllGitlabs(c *gin.Context) {
	gitlabs, err := gitcode.GetAllGitlabs()
	if err != nil {
		app.Log().Error("获取GitLab列表失败", "err", err)
		app.FailedResponseMsg(c, "获取GitLab列表失败")
		return
	}
	app.SuccessResponseData(c, gitlabs)
}

func GetGitlabGroups(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	gitlabObject, err := gitcode.GetGitlabByID(id)
	if err != nil {
		app.FailedResponseMsg(c, "GitLab不存在")
		return
	}
	groups, err := gitlabObject.GetGroups()
	if err != nil {
		app.Log().Error("获取GitLab组失败", "err", err)
		app.FailedResponseMsg(c, "获取GitLab组失败")
		return
	}
	app.SuccessResponseData(c, groups)
}

func GetGitlabProjects(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	gitlabObject, err := gitcode.GetGitlabByID(id)
	if err != nil {
		app.FailedResponseMsg(c, "GitLab不存在")
		return
	}
	projects, err := gitlabObject.GetProjects()
	if err != nil {
		app.Log().Error("获取GitLab项目失败", "err", err)
		app.FailedResponseMsg(c, "获取GitLab项目失败")
		return
	}
	app.SuccessResponseData(c, projects)
}
