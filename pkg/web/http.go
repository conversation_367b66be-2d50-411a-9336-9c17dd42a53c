package web

import (
	"context"
	"fmt"
	"log/slog"
	"net"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"

	"github.com/gogf/greuse"
)

// server http服务监听
type Server struct {
	srv      http.Server
	listener net.Listener
}

// StartServer 启动http服务
func StartServer(host string, port uint, route *gin.Engine) (s Server, err error) {
	s = Server{}
	s.listener, err = greuse.Listen("tcp", fmt.Sprintf("%s:%d", host, port))
	if err != nil {
		return
	}
	s.srv = http.Server{
		Addr:    fmt.Sprintf("%s:%d", host, port),
		Handler: route,
	}
	go func() {
		// 服务连接
		slog.Info(fmt.Sprintf("Listening and serving HTTP on %s:%d", host, port))
		err := s.srv.Serve(s.listener)
		// if err != nil && err != http.ErrServerClosed {
		if err != nil {
			slog.Error("listen error", slog.Any("err", err))
		}
		s.listener.Close()
	}()
	return
}

// Stop 停止http服务清理
func (s *Server) Stop() (err error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	err = s.srv.Shutdown(ctx)
	return
}
