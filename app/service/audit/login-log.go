package audit

import (
	"cmdb/app"
	"cmdb/pkg/db"
	"time"
)

// 登录记录
type LoginLog struct {
	ID        uint      `gorm:"primaryKey;column:id;comment:ID" json:"id"`
	User      string    `gorm:"type:varchar(255);index;column:user;comment:用户" json:"user"`
	Username  string    `gorm:"type:varchar(255);index;column:username;comment:用户名" json:"username"`
	IP        string    `gorm:"type:varchar(255);column:ip;comment:登陆IP" json:"ip"`
	Agent     string    `gorm:"type:varchar(500);column:agent;comment:客户端" json:"agent"`
	Result    string    `gorm:"type:varchar(255);column:result;comment:结果" json:"result"`
	Mode      string    `gorm:"type:varchar(255);column:mode;comment:登陆方式" json:"mode"`
	CreatedAt time.Time `gorm:"column:created_at;index;comment:创建时间" json:"created_at"`
}

// 设置表名
func (*LoginLog) TableName() string {
	return "audit_login_logs"
}

// 记录登录日志
func LogLogin(user, username, ip, agent, result, mode string) (err error) {
	err = app.DB().Create(&LoginLog{
		User:     user,
		Username: username,
		IP:       ip,
		Agent:    agent,
		Result:   result,
		Mode:     mode,
	}).Error
	return
}

// 分页获取登录记录
func GetLoginLogs(offset, limit int, ip, user string, startTime, endTime *time.Time) (count int64, logs []LoginLog, err error) {
	dbop := app.DB()
	if ip != "" {
		dbop = dbop.Where("ip = ?", ip)
	}
	if startTime != nil {
		dbop = dbop.Where("created_at >= ?", startTime)
	}
	if endTime != nil {
		dbop = dbop.Where("created_at <= ?", endTime)
	}
	if user != "" {
		dbop = db.MLike(dbop, user, "user", "username")
	}
	err = dbop.Model(&LoginLog{}).Count(&count).Order("created_at DESC ,id DESC").
		Offset(offset).Limit(limit).Find(&logs).Error
	return
}
