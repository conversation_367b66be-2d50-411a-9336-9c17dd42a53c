package notice

import (
	"cmdb/app"
	"cmdb/app/service/notice"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

func GetMessages(c *gin.Context) {
	page, _ := strconv.Atoi(c<PERSON><PERSON><PERSON>("page", "1"))
	// 获取每页显示数量，默认为10
	limit, _ := strconv.Atoi(c<PERSON>("limit", "10"))
	// 计算偏移量
	offset := (page - 1) * limit
	var contact *string
	if val := c.Query("contact"); val != "" {
		contact = &val
	}
	var messageType *string
	if val := c.Query("message_type"); val != "" {
		messageType = &val
	}
	var status *int
	if val, err := strconv.Atoi(c.Query("status")); err == nil {
		status = &val
	}
	var startTime, endTime *time.Time
	startTimeStr := strings.TrimSpace(c.<PERSON>("start_time"))
	if value, err1 := time.Parse(time.RFC3339, startTimeStr); err1 == nil {
		startTime = &value
	}
	endTimeStr := strings.TrimSpace(c.Query("end_time"))
	if value, err2 := time.Parse(time.RFC3339, endTimeStr); err2 == nil {
		endTime = &value
	}
	count, data, err := notice.GetMessages(offset, limit, contact, messageType, status, startTime, endTime)
	if err != nil {
		app.Log().Error("获取消息列表失败", "err", err)
		app.FailedResponseMsg(c, "获取数据失败")
		return
	}
	app.SuccessResponseDataCount(c, data, count)
}

func GetMessage(c *gin.Context) {
	var id int
	var err error
	if id, err = strconv.Atoi(c.Param("id")); err != nil {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	data, err := notice.GetMessageDetailByID(id)
	if err != nil {
		app.Log().Error("获取消息失败", "err", err)
		app.FailedResponseMsg(c, "获取数据失败")
		return
	}
	app.SuccessResponseData(c, data)
}
