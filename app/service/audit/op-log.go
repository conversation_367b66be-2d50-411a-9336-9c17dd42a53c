package audit

import (
	"cmdb/app"
	"cmdb/app/service/auth"
	"cmdb/pkg/db"
	"time"

	"github.com/gin-gonic/gin"
)

// 审计记录
type OPLog struct {
	ID        uint      `gorm:"primaryKey;column:id;comment:主键" json:"id"`
	User      string    `gorm:"type:varchar(255);index;column:user;comment:姓名" json:"user"`
	Username  string    `gorm:"type:varchar(255);index;column:username;comment:用户名" json:"username"`
	IP        string    `gorm:"type:varchar(255);column:ip;comment:登陆IP" json:"ip"`
	Module    string    `gorm:"type:varchar(255);column:module;comment:模块" json:"module"`
	Content   string    `gorm:"type:text;column:content;comment:操作内容" json:"content"`
	CreatedAt time.Time `gorm:"column:created_at;index;comment:创建时间" json:"created_at"`
}

// 设置表名
func (*OPLog) TableName() string {
	return "audit_op_logs"
}

// 记录操作日志
func logOP(user, username, ip, module, content string) error {
	return app.DB().Create(&OPLog{
		User:     user,
		Username: username,
		IP:       ip,
		Module:   module,
		Content:  content,
	}).Error
}

func LogOP(user, username, ip, module, content string) error {
	return logOP(user, username, ip, module, content)
}

// 通过API进来的审计
func LogAPIOP(c *gin.Context, module, content string) error {
	token := app.GetToken(c)
	u, err := auth.ParseToken(token)
	if err != nil {
		return err
	}
	clientIP := c.GetHeader("X-Forwarded-For")
	if clientIP == "" {
		clientIP = c.ClientIP()
	}
	return logOP(u.Name, u.Username, clientIP, module, content)
}

// 分页获取操作记录
func GetOPLogs(offset, limit int, ip, user, module string, startTime, endTime *time.Time) (count int64, opLogs []OPLog, err error) {
	dbop := app.DB()
	if ip != "" {
		dbop = dbop.Where("ip = ?", ip)
	}
	if user != "" {
		dbop = db.MLike(dbop, user, "user", "username")
	}
	if module != "" {
		dbop = dbop.Where("module = ?", module)
	}
	if startTime != nil {
		dbop = dbop.Where("created_at >= ?", startTime)
	}
	if endTime != nil {
		dbop = dbop.Where("created_at <= ?", endTime)
	}
	err = dbop.Model(&OPLog{}).Count(&count).Order("created_at DESC, id DESC").
		Offset(offset).Limit(limit).Find(&opLogs).Error
	return
}
