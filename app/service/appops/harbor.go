package appops

import (
	"cmdb/app"
	"cmdb/pkg/db"
	"cmdb/pkg/utils"
	"encoding/base64"
	"errors"
	"time"

	"gorm.io/gorm"
)

var (
	ErrHarborExist = errors.New("harbor已经存在")
)

type Harbor struct {
	ID        uint           `gorm:"column:id;primaryKey;comment:主键" json:"id"`
	Name      string         `gorm:"type:varchar(255);index;column:name;comment:名称" json:"name"`
	Address   string         `gorm:"type:varchar(255);column:address;comment:地址" json:"address"`
	Version   string         `gorm:"type:varchar(255);column:version;comment:版本" json:"version"`
	Username  string         `gorm:"type:varchar(255);column:username;comment:用户名" json:"username"`
	Password  string         `gorm:"type:varchar(255);column:password;comment:密码" json:"-"`
	Remark    string         `gorm:"type:varchar(255);column:remark;comment:备注" json:"remark"`
	CreatedAt time.Time      `gorm:"column:created_at;comment:创建时间" json:"created_at"`
	UpdatedAt time.Time      `gorm:"column:updated_at;comment:更新时间" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at;comment:删除时间" json:"-"`
}

func (Harbor) TableName() string {
	return "asset_harbors"
}

type HarborForm struct {
	Name     string `json:"name" binding:"required,max=255"`
	Address  string `json:"address" binding:"max=255"`
	Version  string `json:"version" binding:"max=255"`
	Username string `json:"username" binding:"required,max=255"`
	Password string `json:"password" binding:"max=255"`
	Remark   string `json:"remark" binding:"max=255"`
}

func (form HarborForm) Create() (err error) {
	// 通过name判断是否已经存在
	if err1 := app.DB().Select("id").Where("name = ?", form.Name).Take(&Harbor{}).Error; err1 == nil {
		return ErrHarborExist
	} else if !errors.Is(err1, gorm.ErrRecordNotFound) {
		err = err1
		return
	}
	// 创建harbor
	err = app.DB().Create(&Harbor{
		Name:     form.Name,
		Address:  form.Address,
		Version:  form.Version,
		Username: form.Username,
		Password: utils.Base64Encode(form.Password),
		Remark:   form.Remark,
	}).Error
	return
}

func (harbor Harbor) Update(form HarborForm) (err error) {
	updateMap := map[string]any{}
	if form.Name != harbor.Name {
		exist := Harbor{}
		err = app.DB().Select("id").Where("name = ?", form.Name).Take(&exist).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		} else if err == nil && exist.ID != harbor.ID {
			err = ErrHarborExist
			return
		}
		updateMap["name"] = form.Name
	}
	if form.Remark != harbor.Remark {
		updateMap["remark"] = form.Remark
	}
	if base64.RawStdEncoding.EncodeToString([]byte(form.Password)) != harbor.Password {
		updateMap["password"] = base64.RawStdEncoding.EncodeToString([]byte(form.Password))
	}
	if len(updateMap) > 0 {
		err = app.DB().Model(&harbor).Updates(updateMap).Error
	}
	return
}

func (harbor Harbor) Delete() (err error) {
	err = app.DB().Delete(&harbor).Error
	return
}

func GetHarborByID(id int) (harbor Harbor, err error) {
	err = app.DB().Where("id = ?", id).Take(&harbor).Error
	return
}

func GetHarbors(offset, limit int, keyword *string) (count int64, data []Harbor, err error) {
	dbop := app.DB()
	if keyword != nil {
		dbop = db.MLike(dbop, *keyword, "name", "remark", "address")
	}
	err = dbop.Model(&Harbor{}).Count(&count).Order("name").Offset(offset).Limit(limit).Find(&data).Error
	return
}

func GetAllHarbors() (data []Harbor, err error) {
	err = app.DB().Order("name").Find(&data).Error
	return
}
