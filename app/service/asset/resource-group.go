package asset

import (
	"cmdb/app"
	"cmdb/pkg/db"
	"time"

	"gorm.io/gorm"
)

type ResourceGroup struct {
	ID               uint           `gorm:"column:id;primaryKey;comment:主键" json:"id"`
	AccountID        uint           `gorm:"column:account_id;index;comment:账户ID" json:"account_id"`
	GroupID          string         `gorm:"column:group_id;type:varchar(255);index;comment:资源组ID" json:"group_id"`
	GroupName        string         `gorm:"column:group_name;type:varchar(255);comment:资源组名称" json:"group_name"`
	GroupDisplayName string         `gorm:"column:group_display_name;type:varchar(255);index;comment:资源组显示名称" json:"group_display_name"`
	CreatedAt        time.Time      `gorm:"column:created_at;comment:创建时间" json:"created_at"`
	UpdatedAt        time.Time      `gorm:"column:updated_at;comment:更新时间" json:"updated_at"`
	DeletedAt        gorm.DeletedAt `gorm:"column:deleted_at;comment:删除时间" json:"-"`
}

func (ResourceGroup) TableName() string {
	return "asset_resource_groups"
}

func GetResourceGroups(offset, limit int, accountID *int, keyword *string) (count int64, list []ResourceGroup, err error) {
	dbop := app.DB()
	if keyword != nil {
		dbop = db.MLike(dbop, *keyword, "group_name", "group_display_name", "group_id")
	}
	if accountID != nil {
		dbop = dbop.Where("account_id = ?", *accountID)
	}
	err = dbop.Model(&ResourceGroup{}).Count(&count).Order("group_display_name").Offset(offset).Limit(limit).Find(&list).Error
	return
}

func (group ResourceGroup) GetHostTotal() (count int64, err error) {
	err = app.DB().Model(&Host{}).Where("resource_group_id = ? AND account_id = ?", group.GroupID, group.AccountID).Count(&count).Error
	return
}

func GetAllResourceGroups() (list []ResourceGroup, err error) {
	err = app.DB().Model(&ResourceGroup{}).Order("group_display_name").Find(&list).Error
	return
}

func GetResourceGroupNameByID(groupID string) (groupDisplayName string, err error) {
	err = app.DB().Model(&ResourceGroup{}).Select("group_display_name").Where("group_id =?", groupID).Limit(1).Scan(&groupDisplayName).Error
	// 如果资源组名称为空，则使用默认值
	if groupDisplayName == "" {
		groupDisplayName = "（未设置资源组）"
	}
	return
}
func (rs ResourceGroup) GetCloudAccounts() (account CloudAccount, err error) {
	err = app.DB().Model(&CloudAccount{}).Where("id = ?", rs.AccountID).Take(&account).Error
	return
}

func GetResourceGroupByID(id int) (rs ResourceGroup, err error) {
	err = app.DB().Where("id = ?", id).Take(&rs).Error
	return
}
