package aliyun

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/pkg/utils"

	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/requests"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/nas"
)

func StatsAllAccountsNasStorage() (storageTotal int64, err error) {
	accounts, err := GetAllAccounts()
	if err != nil {
		app.Log().Error("统计nas存储容量时，获取阿里云账户列表异常", "err", err.Error())
		return
	}
	for _, account := range accounts {
		var storage int64
		var err1 error
		storage, err1 = account.getNasFilesystemsStorage()
		if err1 != nil {
			app.Log().Error("获取阿里云NAS存储容量异常", "account", account.Name, "err", err1.Error())
			continue
		}
		storageTotal += storage
	}
	return
}

func (a Account) getNasFilesystemsStorage() (storageTotal int64, err error) {
	regions, err := asset.GetDatacentersByCloudType(asset.AliyunCloudType)
	if err != nil {
		app.Log().Error("统计nas存储容量时，获取阿里云区域列表异常", "account", a.Name, "err", err.Error())
		return
	}
	for _, region := range regions {
		var storage int64
		var err1 error
		storage, err1 = a.getNasFilesystemsStorageByRegion(region.Code, 1)
		if err1 != nil {
			app.Log().Error("获取阿里云NAS存储容量异常", "account", a.Name, "region", region.Code, "err", err1.Error())
			continue
		}
		storageTotal += storage
	}
	return
}

func (a Account) getNasFilesystemsStorageByRegion(regionCode string, page int) (storage int64, err error) {
	secret, err := utils.Base64Decode(a.AccessSecret)
	if err != nil {
		app.Log().Error("统计nas存储容量时，解密阿里云NAS密钥异常", "account", a.Name, "region", regionCode, "err", err.Error())
		return
	}
	client, err := nas.NewClientWithAccessKey(regionCode, a.AccessKey, secret)
	if err != nil {
		app.Log().Error("统计nas存储容量时，创建阿里云NAS客户端异常", "account", a.Name, "region", regionCode, "err", err.Error())
		return
	}

	request := nas.CreateDescribeFileSystemsRequest()
	request.Scheme = "https"
	request.PageSize = requests.NewInteger(100)
	if page < 1 {
		page = 1
	}
	request.PageNumber = requests.NewInteger(page)
	response, err := client.DescribeFileSystems(request)
	if err != nil {
		app.Log().Error("统计nas存储容量时，获取阿里云NAS文件系统列表异常", "account", a.Name, "region", regionCode, "err", err.Error())
		return
	}
	for _, fs := range response.FileSystems.FileSystem {
		storage += fs.MeteredSize
	}
	if response.TotalCount > page*100 {
		var subStorage int64
		subStorage, err = a.getNasFilesystemsStorageByRegion(regionCode, page+1)
		if err != nil {
			app.Log().Error("统计nas存储容量时，获取子页阿里云NAS存储容量异常", "account", a.Name, "region", regionCode, "err", err.Error())
			return
		}
		storage += subStorage
	}
	return
}
