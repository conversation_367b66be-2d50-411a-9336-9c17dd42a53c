package notice

import (
	"cmdb/app"
	"cmdb/app/service/audit"
	"cmdb/app/service/notice"
	"errors"
	"strconv"

	"github.com/gin-gonic/gin"
)

// 获取联系人列表
func GetContacts(c *gin.Context) {
	// 获取页码，默认为1
	page, _ := strconv.Atoi(c.<PERSON>("page", "1"))
	// 获取每页显示数量，默认为10
	limit, _ := strconv.Atoi(c.<PERSON>("limit", "10"))
	// 计算偏移量
	offset := (page - 1) * limit
	var keyword *string
	if val := c.Query("keyword"); val != "" {
		keyword = &val
	}
	// 调用GetContacts方法获取联系人列表
	count, data, err := notice.GetContacts(offset, limit, keyword)
	if err != nil {
		app.Log().Error("获取联系人列表失败", "err", err)
		app.FailedResponseMsg(c, "获取联系人列表失败")
		return
	}
	app.SuccessResponseDataCount(c, data, count)
}

// 添加联系人
func CreateContact(c *gin.Context) {
	var form notice.ContactForm
	if err := c.ShouldBindJSON(&form); err != nil {
		app.Log().Error("绑定数据失败", "err", err)
		app.FailedResponseMsg(c, "绑定数据失败")
		return
	}
	if err := form.Create(); err != nil {
		msg := "添加失败"
		if errors.Is(err, notice.ErrContactExist) {
			msg = err.Error()
		} else {
			app.Log().Error("添加联系人失败", "err", err)
		}
		app.FailedResponseMsg(c, msg)
		return
	}
	audit.LogAPIOP(c, "通知管理", "添加联系人："+form.Name)
	app.SuccessResponseMsg(c, "添加成功")
}

// 更新联系人
func UpdateContact(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.Log().Error("更新通知联系人，获取ID非法", "err", err)
		app.FailedResponseMsg(c, "非法参数")
		return
	}
	var form notice.ContactForm
	if err := c.ShouldBindJSON(&form); err != nil {
		app.Log().Error("绑定数据失败", "err", err)
		app.FailedResponseMsg(c, "非法字段")
		return
	}
	contact, err := notice.GetContactByID(id)
	if err != nil {
		msg := "更新失败"
		if errors.Is(err, notice.ErrContactExist) {
			msg = err.Error()
		} else {
			app.Log().Error("更新通知联系人，获取联系人失败", "err", err)
		}
		app.FailedResponseMsg(c, msg)
		return
	}
	if err := contact.Update(form); err != nil {
		app.Log().Error("更新通知联系人失败", "err", err)
		app.FailedResponseMsg(c, "更新失败")
		return
	}
	audit.LogAPIOP(c, "通知管理", "更新联系人："+form.Name)
	app.SuccessResponseMsg(c, "更新成功")
}

// 删除联系人
func DeleteContact(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.Log().Error("删除通知联系人，获取ID非法", "err", err)
		app.FailedResponseMsg(c, "非法参数")
		return
	}
	contact, err := notice.GetContactByID(id)
	if err != nil {
		app.Log().Error("删除通知联系人，获取联系人失败", "err", err)
		app.FailedResponseMsg(c, "删除失败")
		return
	}
	if err := contact.Delete(); err != nil {
		app.Log().Error("删除通知联系人失败", "err", err)
		app.FailedResponseMsg(c, "删除失败")
		return
	}
	audit.LogAPIOP(c, "通知管理", "删除联系人："+c.Param("id"))
	app.SuccessResponseMsg(c, "删除成功")
}

// 获取所有联系人
func GetAllContacts(c *gin.Context) {
	contacts, err := notice.GetAllContacts()
	if err != nil {
		app.Log().Error("获取通知联系人失败", "err", err)
		app.FailedResponseMsg(c, "获取失败")
		return
	}
	app.SuccessResponseData(c, contacts)
}
