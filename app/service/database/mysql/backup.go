package mysql

import (
	"bytes"
	"cmdb/app"
	"cmdb/app/service/notice"
	"cmdb/app/service/setting"
	"cmdb/pkg/db"
	"errors"
	"strconv"
	"strings"
	"time"

	"gorm.io/gorm"
)

var (
	ErrBackupExist = errors.New("备份记录已存在，创建失败")
	//  备份记录插入失败
	ErrInsertBackupData = errors.New("备份记录插入失败")
)

// Backup mysql备份记录
type Backup struct {
	ID         uint       `gorm:"column:id;primaryKey;comment:ID" json:"id"`
	Host       string     `gorm:"type:varchar(200);column:host;comment:主机" json:"host"`
	Port       uint       `gorm:"column:port;comment:端口" json:"port"`
	Status     string     `gorm:"type:varchar(200);column:status;comment:状态" json:"status"`
	Project    string     `gorm:"type:varchar(200);column:project;comment:项目" json:"project"`
	Filename   string     `gorm:"type:varchar(200);column:filename;comment:文件名" json:"filename"`
	Filesize   int64      `gorm:"type:bigint(20);column:filesize;comment:文件大小" json:"filesize"`
	Remark     string     `gorm:"type:varchar(1000);column:remark;comment:备注" json:"remark" `
	BackupTime *time.Time `gorm:"type:datetime;default:null;column:backup_time;comment:备份时间" json:"backup_time"`
	CreatedAt  time.Time  `gorm:"column:created_at;comment:创建时间" json:"created_at"`
}

// TableName 设置表名
func (Backup) TableName() string {
	return "asset_db_mysql_backups"
}

// RecordForm 备份记录
type RecordForm struct {
	Host       string `json:"host"  form:"host"`
	Port       uint   `json:"port"  form:"port"`
	Filename   string `json:"filename" form:"filename"`
	Status     string `json:"status" form:"backup_status"`
	Project    string `json:"project" form:"project"`
	Filesize   int64  `json:"filesize" form:"filesize"`
	Remark     string `json:"remark" form:"comments"`
	BackupTime string `json:"backup_time" form:"backup_time" `
}

func CreateBackup(recordForm RecordForm, backupTime time.Time) (err error) {
	err = app.DB().Select("id").Where(
		"host = ? AND port = ? AND backup_time = ? AND filesize = ?",
		recordForm.Host, recordForm.Port, backupTime, recordForm.Filesize).Take(&Backup{}).Error
	if err == nil {
		err = ErrBackupExist
		return
	} else if err == gorm.ErrRecordNotFound {
		err = app.DB().Create(&Backup{
			Host:       recordForm.Host,
			Port:       recordForm.Port,
			Filename:   recordForm.Filename,
			Status:     recordForm.Status,
			Project:    recordForm.Project,
			Filesize:   recordForm.Filesize,
			Remark:     recordForm.Remark,
			BackupTime: &backupTime,
		}).Error
		if err != nil {
			err = ErrInsertBackupData
			return
		}
	}
	return nil
}

func GetBackups(offset, limit int, host, keyword *string) (count int64, backups []Backup, err error) {
	dbop := app.DB()
	if keyword != nil {
		dbop = db.MLike(dbop, *keyword, "project")
	}
	if host != nil {
		dbop = dbop.Where("host = ?", host)
	}
	err = dbop.Model(&Backup{}).Count(&count).
		Order("created_at desc").Offset(offset).Limit(limit).Find(&backups).Error
	return
}

// GetBackupData 获取项目主机端口的备份数据
func GetBackupData(host string, port int) (data []Backup, err error) {
	err = app.DB().Where("host = ? AND port = ? AND filesize != 0 ", host, port).Order("backup_time ASC").Find(&data).Error
	return
}

// 对比最后两条记录
func CompatWithLastRecord(host string, port uint, filename string, filesize int64) (ok bool, contents string, err error) {
	record := Backup{}
	err = app.DB().Where("host = ? AND port = ? AND filename = ? AND filesize = ?", host, port, filename, filesize).
		Take(&record).Error
	if err != nil {
		return
	}
	lastRecord := Backup{}
	err = app.DB().Where("host = ? AND port = ?", host, port).
		Where("backup_time <= ? ", record.BackupTime).
		Order("backup_time DESC").Take(&lastRecord).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	var contentBytes bytes.Buffer
	var backupFileInfoBytes bytes.Buffer
	var noOK bool
	if !strings.Contains(strings.ToLower(record.Status), "ok") {
		contentBytes.WriteString("备份状态异常：" + record.Status + "\n")
		noOK = true
	}
	if record.Filesize == 0 {
		contentBytes.WriteString("备份文件大小：0\n")
		noOK = true
	}
	if err == nil && strings.ToLower(lastRecord.Status) == "ok" {
		diffSize := float64(lastRecord.Filesize-record.Filesize) / 1024 / 1024
		if diffSize > 1024*5 {
			contentBytes.WriteString("备份比上次备份小：" + strconv.FormatFloat(diffSize, 'f', 2, 32) + " MB\n")
			noOK = true
		}
		backupFileInfoBytes.WriteString("本次备份时间：" + record.BackupTime.Format(time.DateTime) + " （上次备份文件：" + lastRecord.BackupTime.Format(time.DateTime) + ")\n")
		backupFileInfoBytes.WriteString("本次备份大小：" + record.Filename + " （上次备份文件：" + lastRecord.Filename + ")\n")
		backupFileInfoBytes.WriteString("本次备份文件：" + strconv.FormatFloat(float64(record.Filesize)/1024/1024, 'f', 4, 64) + " MB  （上次备份文件：" + strconv.FormatFloat(float64(lastRecord.Filesize)/1024/1024, 'f', 4, 64) + " MB)\n")
	} else {
		backupFileInfoBytes.WriteString("本次备份时间：" + record.BackupTime.Format(time.DateTime) + "\n")
		backupFileInfoBytes.WriteString("本次备份大小：" + record.Filename + "\n")
		backupFileInfoBytes.WriteString("本次备份文件：" + strconv.FormatFloat(float64(record.Filesize)/1024/1024, 'f', 4, 64) + " MB\n")
	}
	ok = !noOK
	if ok {
		err = nil
		return
	}
	contents = "========异常信息===========\n" +
		contentBytes.String() +
		"========备份信息===========\n" +
		"IP：" + record.Host +
		"\n端口：" + strconv.FormatUint(uint64(record.Port), 10) +
		"\n项目：" + record.Project + "\n" +
		backupFileInfoBytes.String()
	return
}

func CheckTodyBackup() (err error) {
	now := time.Now()
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)
	yestoday := today.AddDate(0, 0, -1)
	projects := []string{}
	err = app.DB().Model(&Backup{}).Distinct("project").
		Group("project").Where("backup_time >= ? AND backup_time < ? ", yestoday, today).Scan(&projects).Error
	if err != nil {
		return
	}
	noBackupProjects := []string{}
	for i := range projects {
		err1 := app.DB().Select("id").Where("project = ? AND backup_time > ?", projects[i], today).Take(&Backup{}).Error
		if errors.Is(err1, gorm.ErrRecordNotFound) {
			noBackupProjects = append(noBackupProjects, projects[i])
		}
	}

	if len(noBackupProjects) > 0 {
		err = notice.CreateMessage("数据库无备份提醒", ""+now.Format(time.DateTime)+" 无备份项目："+strings.Join(noBackupProjects, "、"), notice.MessageDingtalkRobot, "数据库备份通知", setting.GetMySQLBackupSetting().DingtalkRobotURL, "all")
		if err != nil {
			app.Log().Error("添加通知信息异常 ", "err", err)
		}
		return
	}
	err = notice.CreateMessage("数据库无异常", now.Format(time.DateTime)+" 备份检查，"+"无异常", notice.MessageDingtalkRobot, "数据库备份通知", setting.GetMySQLBackupSetting().DingtalkRobotURL)
	if err != nil {
		app.Log().Error("添加通知信息异常 ", "err", err)
	}
	return
}
