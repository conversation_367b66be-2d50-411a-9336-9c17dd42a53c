package k8s

import (
	"cmdb/app"
	"context"
	"encoding/base64"
	"errors"
	"strings"
	"sync"
	"time"

	"gorm.io/gorm"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/clientcmd"
)

type Node struct {
	ID                      uint           `gorm:"column:id;primaryKey;comment:主键" json:"id"`
	Name                    string         `gorm:"column:name;type:varchar(255);index" json:"name"`
	Status                  string         `gorm:"column:status;type:varchar(255);index" json:"status"`
	Role                    string         `gorm:"column:role;type:varchar(255);index" json:"role"`
	Version                 string         `gorm:"column:version;type:varchar(255);index" json:"version"`
	InternalIP              string         `gorm:"column:internal_ip;type:varchar(255);index" json:"internal_ip"`
	Hostname                string         `gorm:"column:hostname;type:varchar(255)" json:"hostname"`
	ExternalIP              string         `gorm:"column:external_ip;type:varchar(255);index" json:"external_ip"`
	OSImage                 string         `gorm:"column:os_image;internal_ip:varchar(255);index" json:"os_image"`
	KernelVersion           string         `gorm:"column:kernel_version;type:varchar(255);index" json:"kernel_version"`
	ContainerRuntimeVersion string         `gorm:"column:container_runtime_version;type:varchar(255);index" json:"container_runtime_version"`
	Labels                  []Label        `gorm:"many2many:asset_k8s_node_labels;joinForeignKey:node_id;joinReferences:label_id" json:"labels"`
	ClusterID               uint           `gorm:"column:cluster_id;index;comment:集群ID" json:"cluster_id"`
	CapacityCPU             int64          `gorm:"column:capacity_cpu;comment:总CPU" json:"capacity_cpu"`
	CapacityMemory          int64          `gorm:"column:capacity_memory;comment:总内存" json:"capacity_memory"`
	CreatedAt               time.Time      `gorm:"column:created_at;index" json:"created_at"`
	SyncTime                time.Time      `gorm:"column:sync_time;index" json:"sync_time"`
	DeletedAt               gorm.DeletedAt `gorm:"column:deleted_at;index" json:"-"`
}

func (Node) TableName() string {
	return "asset_k8s_nodes"
}

func GetNodes(offset, limit int, clusterID *int, keyword *string) (count int64, data []Node, err error) {
	dbop := app.DB()
	if keyword != nil && *keyword != "" {
		if strings.Contains(*keyword, "=") {
			kv := strings.Split(*keyword, "=")
			if len(kv) == 2 {
				dbop = dbop.Where("internal_ip LIKE ? OR external_ip LIKE ? OR hostname LIKE  ? OR id IN (select node_id from asset_k8s_node_labels where label_id in (?))", "%"+*keyword+"%", "%"+*keyword+"%", "%"+*keyword+"%", app.DB().Model(&Label{}).Select("id").Where("label_key = ? AND label_value = ? ", kv[0], kv[1]))

			}
		} else {
			dbop = dbop.Where("internal_ip LIKE ? OR external_ip LIKE ? OR hostname LIKE  ? OR id IN (select node_id from asset_k8s_node_labels where label_id in (?))", "%"+*keyword+"%", "%"+*keyword+"%", "%"+*keyword+"%", app.DB().Model(&Label{}).Select("id").Where("label_key LIKE ? OR label_value LIKE ?", "%"+*keyword+"%", "%"+*keyword+"%"))
		}
	}
	if clusterID != nil {
		dbop = dbop.Where("cluster_id = ?", clusterID)
	}
	err = dbop.Model(Node{}).Count(&count).Order("internal_ip").Offset(offset).Limit(limit).Find(&data).Error
	return
}

func (c Cluster) getNodes() (nodes []Node, err error) {
	kubeconfigBytes, err := base64.StdEncoding.DecodeString(c.Token)
	if err != nil {
		return
	}
	config, err := clientcmd.Load(kubeconfigBytes)
	if err != nil {
		return
	}
	client, err := clientcmd.NewDefaultClientConfig(*config, &clientcmd.ConfigOverrides{}).ClientConfig()
	if err != nil {
		return
	}
	clientset, err := kubernetes.NewForConfig(client)
	if err != nil {
		return
	}
	// 获取所有节点
	nodeList, err := clientset.CoreV1().Nodes().List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		return
	}
	now := time.Now()
	for i := range nodeList.Items {
		tnode := Node{
			Name:                    nodeList.Items[i].Name,
			CreatedAt:               nodeList.Items[i].CreationTimestamp.Time,
			OSImage:                 nodeList.Items[i].Status.NodeInfo.OSImage,
			KernelVersion:           nodeList.Items[i].Status.NodeInfo.KernelVersion,
			ContainerRuntimeVersion: nodeList.Items[i].Status.NodeInfo.ContainerRuntimeVersion,
			Version:                 nodeList.Items[i].Status.NodeInfo.KubeletVersion,
			CapacityCPU:             nodeList.Items[i].Status.Capacity.Cpu().MilliValue(),
			CapacityMemory:          nodeList.Items[i].Status.Capacity.Memory().Value(),
			SyncTime:                now,
			ClusterID:               c.ID,
		}
		for j := range nodeList.Items[i].Status.Addresses {
			switch nodeList.Items[i].Status.Addresses[j].Type {
			case "Hostname":
				tnode.Hostname = nodeList.Items[i].Status.Addresses[j].Address
			case "InternalIP":
				tnode.InternalIP = nodeList.Items[i].Status.Addresses[j].Address
			case "ExternalIP":
				tnode.ExternalIP = nodeList.Items[i].Status.Addresses[j].Address
			}
		}
		tnode.Status = "not ready"
		for j := range nodeList.Items[i].Status.Conditions {
			if nodeList.Items[i].Status.Conditions[j].Type == "Ready" && nodeList.Items[i].Status.Conditions[j].Status == "True" {
				tnode.Status = "ready"
				break
			}
		}
		for k, v := range nodeList.Items[i].Labels {
			if k == "node-role.kubernetes.io/control-plane" {
				tnode.Role = "master"
			}
			tnode.Labels = append(tnode.Labels, Label{
				LabelKey: k, LabelValue: v,
			})
		}
		nodes = append(nodes, tnode)
	}
	return
}

func (node Node) GetLabels() (labels []Label, err error) {
	err = app.DB().Model(&node).Association("Labels").Find(&labels)
	return
}

func (node Node) SetLabels(labels []Label) (err error) {
	ls := []Label{}
	for i := range labels {
		var l Label
		l, err = GenLabelKey(labels[i].LabelKey, labels[i].LabelValue)
		if err != nil {
			return
		}
		ls = append(ls, l)
	}
	err = app.DB().Model(&node).Association("Labels").Replace(ls)
	return
}

func (c Cluster) SyncNodes() (err error) {
	nodes, err := c.getNodes()
	if err != nil {
		return
	}
	for i := range nodes {
		exist := Node{}
		err = app.DB().Where("name = ? AND cluster_id = ?", nodes[i].Name, c.ID).Take(&exist).Error
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = app.DB().Create(&Node{
				Name:                    nodes[i].Name,
				ClusterID:               nodes[i].ClusterID,
				InternalIP:              nodes[i].InternalIP,
				ExternalIP:              nodes[i].ExternalIP,
				Hostname:                nodes[i].Hostname,
				Status:                  nodes[i].Status,
				Role:                    nodes[i].Role,
				Version:                 nodes[i].Version,
				KernelVersion:           nodes[i].KernelVersion,
				OSImage:                 nodes[i].OSImage,
				ContainerRuntimeVersion: nodes[i].ContainerRuntimeVersion,
				CapacityCPU:             nodes[i].CapacityCPU,
				CapacityMemory:          nodes[i].CapacityMemory,
				SyncTime:                nodes[i].SyncTime,
				// CreatedAt:               nodes[i].CreatedAt,
			}).Error
			if err != nil {
				return
			}
			err = app.DB().Where("name = ? AND cluster_id = ?", nodes[i].Name, c.ID).Take(&exist).Error
			if err != nil {
				return
			}
			err = exist.SetLabels(nodes[i].Labels)
		} else if err == nil {
			updateMap := map[string]any{
				"sync_time": nodes[i].SyncTime,
			}
			if nodes[i].ExternalIP != exist.ExternalIP {
				updateMap["external_ip"] = nodes[i].ExternalIP
			}
			if nodes[i].InternalIP != exist.InternalIP {
				updateMap["internal_ip"] = nodes[i].InternalIP
			}
			if nodes[i].Status != exist.Status {
				updateMap["status"] = nodes[i].Status
			}
			if nodes[i].Role != exist.Role {
				updateMap["role"] = nodes[i].Role
			}
			if nodes[i].KernelVersion != exist.KernelVersion {
				updateMap["kernel_version"] = nodes[i].KernelVersion
			}
			if nodes[i].OSImage != exist.OSImage {
				updateMap["os_image"] = nodes[i].OSImage
			}
			if nodes[i].Version != exist.Version {
				updateMap["version"] = nodes[i].Version
			}
			if nodes[i].Hostname != exist.Hostname {
				updateMap["hostname"] = nodes[i].Hostname
			}
			if nodes[i].CapacityCPU != exist.CapacityCPU {
				updateMap["capacity_cpu"] = nodes[i].CapacityCPU
			}
			if nodes[i].CapacityMemory != exist.CapacityMemory {
				updateMap["capacity_memory"] = nodes[i].CapacityMemory
			}
			if nodes[i].ContainerRuntimeVersion != exist.ContainerRuntimeVersion {
				updateMap["container_runtime_version"] = nodes[i].ContainerRuntimeVersion
			}
			err = app.DB().Model(&exist).Updates(updateMap).Error
			if err != nil {
				return
			}
			err = exist.SetLabels(nodes[i].Labels)
		}
		if err != nil {
			return
		}
	}
	err = app.DB().Where("sync_time < ? AND cluster_id = ?", time.Now().Add(-10*time.Minute), c.ID).Delete(&Node{}).Error
	return
}

type NodeResource struct {
	RequestCPU    int64 `gorm:"column:request_cpu" json:"request_cpu"`
	RequestMemory int64 `gorm:"column:request_memory" json:"request_memory"`
	LimitCPU      int64 `gorm:"column:limit_cpu" json:"limit_cpu"`
	LimitMemory   int64 `gorm:"column:limit_memory" json:"limit_memory"`
}

func (node Node) GetNodeResources() (resource NodeResource, err error) {
	resource = NodeResource{
		RequestCPU:    0,
		RequestMemory: 0,
		LimitCPU:      0,
		LimitMemory:   0,
	}
	pods := []Pod{}
	err = app.DB().Model(&Pod{}).Where("host_ip = ?", node.InternalIP).Find(&pods).Error
	if err != nil && len(pods) == 0 {
		return
	}
	wg := sync.WaitGroup{}
	maxChannel := make(chan struct{}, 30)
	for i := range pods {
		wg.Add(1)
		go func(i int) {
			defer wg.Done()
			maxChannel <- struct{}{}
			podResource := NodeResource{}
			err = app.DB().Model(&Container{}).Where("pod = ?  AND namespace = ? AND cluster_id = ?", pods[i].Name, pods[i].Namespace, pods[i].ClusterID).Select("SUM(request_cpu) AS request_cpu, SUM(limit_cpu) AS limit_cpu, SUM(request_memory) AS request_memory, SUM(limit_memory) AS limit_memory").Scan(&podResource).Error
			if err != nil {
				return
			}
			resource.RequestCPU += podResource.RequestCPU
			resource.LimitCPU += podResource.LimitCPU
			resource.RequestMemory += podResource.RequestMemory
			resource.LimitMemory += podResource.LimitMemory
			<-maxChannel
		}(i)
	}
	wg.Wait()
	close(maxChannel)
	return
}
