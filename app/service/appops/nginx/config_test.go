package nginx

import (
	"cmdb/app"
	"testing"
)

func TestConfig(t *testing.T) {
	err := app.NewApp("../../../../app.ini")
	if err != nil {
		t.Error(err)
		return
	}
	err = app.ConnectDB()
	if err != nil {
		t.Error(err)
		return
	}
	defer func() {
		dbop, err1 := app.DB().DB()
		if err1 == nil {
			dbop.Close()
		}
	}()
	err = SyncConfig("10.120.16.92", 22, "root", "/home/<USER>/.ssh/id_ed25519", "test")
	if err != nil {
		t.Error(err)
		return
	}
}
