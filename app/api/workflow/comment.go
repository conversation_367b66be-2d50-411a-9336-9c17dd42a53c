package workflow

import (
	"cmdb/app"
	"cmdb/app/service/auth"
	"cmdb/app/service/workflow"
	"strconv"

	"github.com/gin-gonic/gin"
)

func AddOrderComment(c *gin.Context) {
	var form workflow.OrderCommentForm
	if err := c.ShouldBindJSON(&form); err != nil {
		app.Log().Debug("获取评论信息失败", "err", err)
		app.FailedResponseMsg(c, "参数异常")
		return
	}
	loginUser, err := auth.GetLoginUser(c)
	if err != nil {
		app.Log().Debug("获取用户信息失败", "err", err)
		app.FailedResponseMsg(c, "获取用户信息失败")
		return
	}
	approver, err := workflow.NewApprover(loginUser)
	if err != nil {
		app.Log().Error("获取评论人信息失败", "err", err)
		app.FailedResponseMsg(c, "获取评论人信息失败")
		return
	}
	err = approver.Comment(form)
	if err != nil {
		app.Log().Error("评论失败", "err", err)
		app.FailedResponseMsg(c, "评论失败")
		return
	}
	app.SuccessResponseMsg(c, "评论成功")
}

func UpdateOrderComment(c *gin.Context) {
	var form workflow.OrderCommentForm
	if err := c.ShouldBindJSON(&form); err != nil {
		app.Log().Debug("获取评论信息失败", "err", err)
		app.FailedResponseMsg(c, "参数异常")
		return
	}
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.FailedResponseMsg(c, "参数异常")
		return
	}
	comment, err := workflow.GetOrderCommentByID(id)
	if err != nil {
		app.FailedResponseMsg(c, "获取评论信息失败")
		return
	}
	loginUser, err := auth.GetLoginUser(c)
	if err != nil {
		app.Log().Debug("获取用户信息失败", "err", err)
		app.FailedResponseMsg(c, "获取用户信息失败")
		return
	}
	if loginUser.ID != comment.UserID {
		app.FailedResponseMsg(c, "无权限操作")
		return
	}
	err = comment.Update(form)
	if err != nil {
		app.Log().Error("更新评论失败", "err", err)
		app.FailedResponseMsg(c, "更新评论失败")
		return
	}
	app.SuccessResponseMsg(c, "更新评论成功")
}
