package k8s

import (
	"cmdb/app"
	"cmdb/app/service/asset/k8s"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

type WorkloadRow struct {
	k8s.Workload
	ClusterName string        `json:"cluster_name"`
	Pods        []k8s.Pod     `json:"pods"`
	Services    []k8s.Service `json:"services"`
}

func GetWorkLoads(c *gin.Context) {
	var keyword, namespace *string
	// 获取查询参数
	if val := strings.TrimSpace(c.Query("keyword")); val != "" {
		keyword = &val
	}
	if val := strings.TrimSpace(c.Query("namespace")); val != "" {
		namespace = &val
	}
	// 获取分页参数
	page, _ := strconv.Atoi(c.<PERSON>fault<PERSON>("page", "1"))
	limit, _ := strconv.Atoi(c.<PERSON>fault<PERSON>("limit", "10"))
	// 计算偏移量
	offset := (page - 1) * limit
	workloadTypeStr := c.Query("workload_type")
	workloadType := k8s.GetWorkloadType(workloadTypeStr)
	clusterIDStr := c.Que<PERSON>("cluster_id")
	var clusterID *int
	if val, err1 := strconv.Atoi(clusterIDStr); err1 == nil && val > 0 {
		clusterID = &val
	}
	count, data, err := k8s.GetWorkloads(offset, limit, clusterID, workloadType, keyword, namespace)
	if err != nil {
		app.Log().Error("获取数据失败", "err", err)
		app.FailedResponseMsg(c, "获取数据失败")
		return
	}
	newData := []WorkloadRow{}
	tmpStore := map[uint]string{}
	for _, v := range data {
		v.Labels, _ = v.GetLabels()
		v.SelectorLabels, _ = v.GetSelectorLabels()
		if val, ok := tmpStore[v.ClusterID]; ok {
			newData = append(newData, WorkloadRow{Workload: v, ClusterName: val})
		} else {
			if name, err := k8s.GetClusterName(v.ClusterID); err == nil {
				tmpStore[v.ClusterID] = name
				newData = append(newData, WorkloadRow{Workload: v, ClusterName: name})
			} else {
				newData = append(newData, WorkloadRow{Workload: v, ClusterName: ""})
			}
		}
	}
	app.SuccessResponseDataCount(c, newData, count)
}

func GetWorkloadDetail(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.Log().Error("参数错误", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	workload, err := k8s.GetWorkloadByID(id)
	if err != nil {
		app.Log().Error("获取数据失败", "err", err)
		app.FailedResponseMsg(c, "获取数据失败")
		return
	}
	data := WorkloadRow{Workload: workload}
	data.ClusterName, err = k8s.GetClusterName(workload.ClusterID)
	if err != nil {
		app.Log().Error("获取数据失败", "err", err)
		app.FailedResponseMsg(c, "获取数据失败")
		return
	}
	data.Labels, err = workload.GetLabels()
	if err != nil {
		app.Log().Error("获取数据失败", "err", err)
		app.FailedResponseMsg(c, "获取数据失败")
		return
	}
	data.SelectorLabels, err = workload.GetSelectorLabels()
	if err != nil {
		app.Log().Error("获取数据失败", "err", err)
		app.FailedResponseMsg(c, "获取数据失败")
		return
	}
	data.Pods, err = workload.GetPods()
	if err != nil {
		app.Log().Error("获取数据失败", "err", err)
		app.FailedResponseMsg(c, "获取数据失败")
		return
	}
	for i := range data.Pods {
		data.Pods[i].Containers, err = data.Pods[i].GetContainers()
		if err != nil {
			app.Log().Error("获取数据失败", "err", err)
			app.FailedResponseMsg(c, "获取数据失败")
			return
		}
		for _, container := range data.Pods[i].Containers {
			data.Pods[i].RequestCPU += container.RequestCPU
			data.Pods[i].RequestMemory += container.RequestMemory
			data.Pods[i].LimitCPU += container.LimitCPU
			data.Pods[i].LimitMemory += container.LimitMemory
		}
	}
	data.Services, err = workload.GetServices()
	if err != nil {
		app.Log().Error("获取数据失败", "err", err)
		app.FailedResponseMsg(c, "获取数据失败")
		return
	}
	app.SuccessResponseData(c, data)
}
