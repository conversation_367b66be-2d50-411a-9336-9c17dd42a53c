package mysql

import (
	"time"

	"gorm.io/gorm"
)

type Column struct {
	ID        uint           `gorm:"column:id;primaryKey;comment:主键" json:"id"`
	TableID   uint           `gorm:"column:table_id;index" json:"table_id"`
	Name      string         `gorm:"column:name;type:varchar(255);index" json:"name"`
	UpdatedAt time.Time      `gorm:"column:updated_at;index" json:"updatedAt"`
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at;index" json:"-"`
}

func (Column) TableName() string {
	return "asset_db_mysql_columns"
}
