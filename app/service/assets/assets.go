package assets

// 定义资产类型
type AssetType uint

// 定义资产类型常量
const (
	// 系统
	SystemAssetType AssetType = iota + 1
	// 域名
	DomainAssetType
	// 负载均衡/高防
	LoadbalancerAssetType
	// 主机
	HostAssetType
	// APP
	AppAssetType
	// k8s workload
	K8SAppAssetType
	// MySQL
	MySQLAssetType
	// Mongodb
	MongoDBAssetType
	// redis
	RedisAssetType
	// 消息队列服务：kafka、rocketMQ等
	MQAssetType
	// 公网IP
	PublicIPAssetType
	// k8s pod
	K8SPodAssetType
	// 私有域名
	PrivateDomainAssetType
	// 公有域名
	PublicDomainAssetType
	// 高防防护
	CloudDDosProtectionAssetType
	// 高防域名
	CloudDDosDomainAssetType
	// k8s service
	K8SSVCAssetType
	// proxysql
	ProxySQLAssetType
	// codis-proxy
	CodisProxyAssetType
)

func (assetType AssetType) String() string {
	switch assetType {
	case SystemAssetType:
		return "系统"
	case DomainAssetType:
		return "域名"
	case LoadbalancerAssetType:
		return "负载均衡/高防"
	case HostAssetType:
		return "主机"
	case AppAssetType:
		return "APP"
	case K8SAppAssetType:
		return "k8s workload"
	case MySQLAssetType:
		return "MySQL"
	case MongoDBAssetType:
		return "Mongodb"
	case RedisAssetType:
		return "redis"
	case MQAssetType:
		return "消息队列服务"
	case PublicIPAssetType:
		return "公网IP"
	case K8SPodAssetType:
		return "k8s pod"
	case PrivateDomainAssetType:
		return "私有域名"
	case PublicDomainAssetType:
		return "公有域名"
	case CloudDDosProtectionAssetType:
		return "高防防护"
	case CloudDDosDomainAssetType:
		return "高防域名"
	case K8SSVCAssetType:
		return "k8s service"
	case ProxySQLAssetType:
		return "proxysql"
	case CodisProxyAssetType:
		return "codis-proxy"
	default:
		return "未知资产类型"
	}
}
