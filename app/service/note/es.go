package note

import (
	"cmdb/app/service/setting"
	"net"
	"net/http"
	"strings"
	"time"

	"gopkg.in/olivere/elastic.v6"
)

func initClient() (client *elastic.Client, err error) {
	var options []elastic.ClientOptionFunc
	setting := setting.GetELKSetting()
	url := setting.EsUrl
	options = append(options, elastic.SetURL(strings.Split(url, ",")...))
	password := setting.EsPassword
	user := setting.EsUser
	if user != "" && password != "" {
		options = append(options, elastic.SetBasicAuth(user, password))
	}
	if !setting.SetSniff {
		options = append(options, elastic.SetSniff(setting.SetSniff))
	}
	options = append(options, elastic.SetHttpClient(&http.Client{
		Transport: &http.Transport{
			MaxIdleConns:        10,
			IdleConnTimeout:     30 * time.Second,
			MaxConnsPerHost:     200,
			MaxIdleConnsPerHost: 100,
			DialContext: (&net.Dialer{
				Timeout:   30 * time.Second,
				KeepAlive: 30 * time.Second,
			}).DialContext,
		},
	}))
	client, err = elastic.NewClient(options...)
	return
}
