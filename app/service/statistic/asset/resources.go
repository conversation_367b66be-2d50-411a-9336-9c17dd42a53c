package asset

import (
	"cmdb/app/service/asset"
	"time"
)

// CloudComputerResource 云计算机资源统计
type CloudComputerResource struct {
	ID            uint            `gorm:"column:id;primaryKey;comment:主键id" json:"id"`
	StatDate      time.Time       `gorm:"column:stat_date;type:date;index;comment:统计日期" json:"stat_date"`
	CloudType     asset.CloudType `gorm:"column:cloud_type;index;comment:云类型" json:"cloud_type"`
	CloudTypeName string          `gorm:"column:cloud_type_name;comment:云类型名称" json:"cloud_type_name"`
	HostTotal     uint64          `gorm:"column:host_total;comment:主机总数" json:"host_total"`
	CPUTotal      uint64          `gorm:"column:cpu_total;comment:CPU总数" json:"cpu_total"`
	MemoryMBTotal uint64          `gorm:"column:memory_mb_total;comment:内存总数(MB)" json:"memory_mb_total"`
	GPUTotal      uint64          `gorm:"column:gpu_total;comment:GPU总数" json:"gpu_total"`
}

func (CloudComputerResource) TableName() string {
	return "statistic_daily_cloud_computer_resources"
}

// RegionComputerResource 区域计算机资源统计
type RegionComputerResource struct {
	ID            uint      `gorm:"column:id;primaryKey;comment:主键id" json:"id"`
	StatDate      time.Time `gorm:"column:stat_date;type:date;index;comment:统计日期" json:"stat_date"`
	RegionName    string    `gorm:"column:region_name;comment:区域名称" json:"region_name"`
	HostTotal     uint64    `gorm:"column:host_total;comment:主机总数" json:"host_total"`
	CPUTotal      uint64    `gorm:"column:cpu_total;comment:CPU总数" json:"cpu_total"`
	MemoryMBTotal uint64    `gorm:"column:memory_mb_total;comment:内存总数(MB)" json:"memory_mb_total"`
	GPUTotal      uint64    `gorm:"column:gpu_total;comment:GPU总数" json:"gpu_total"`
}

func (RegionComputerResource) TableName() string {
	return "statistic_daily_region_computer_resources"
}

// ComputerResource 计算机资源统计
type ComputerResource struct {
	ID            uint      `gorm:"column:id;primaryKey;comment:主键id" json:"id"`
	StatDate      time.Time `gorm:"column:stat_date;type:date;index;comment:统计日期" json:"stat_date"`
	HostTotal     uint64    `gorm:"column:host_total;comment:主机总数" json:"host_total"`
	CPUTotal      uint64    `gorm:"column:cpu_total;comment:CPU总数" json:"cpu_total"`
	MemoryMBTotal uint64    `gorm:"column:memory_mb_total;comment:内存总数(MB)" json:"memory_mb_total"`
	GPUTotal      uint64    `gorm:"column:gpu_total;comment:GPU总数" json:"gpu_total"`
	DLICUTotal    uint64    `gorm:"column:dli_cu_total;comment:DLI CU 总数" json:"dli_cu_total"`
}

func (ComputerResource) TableName() string {
	return "statistic_daily_computer_resources"
}

// DataResource 数据资源统计
type DataResource struct {
	ID                     uint      `gorm:"column:id;primaryKey;comment:主键id" json:"id"`
	StatDate               time.Time `gorm:"column:stat_date;type:date;index;comment:统计日期" json:"stat_date"`
	MySQLStorageTotal      int64     `gorm:"column:mysql_storage_total;comment:MYSQL 总存储大小" json:"mysql_storage_total"`
	OSSStorageTotal        int64     `gorm:"column:oss_storage_total;comment:OSS 总存储大小" json:"oss_storage_total"`
	OBSStorageTotal        int64     `gorm:"column:obs_storage_total;comment:OBS 总存储大小" json:"obs_storage_total"`
	SFSStorageTotal        int64     `gorm:"column:sfs_storage_total;comment:SFS 总存储大小" json:"sfs_storage_total"`
	NASStorageTotal        int64     `gorm:"column:nas_storage_total;comment:NAS 总存储大小" json:"nas_storage_total"`
	MongodbStorageTotal    int64     `gorm:"column:mongodb_storage_total;comment:Mongodb 总存储大小" json:"mongodb_storage_total"`
	StarrocksStorageTotal  int64     `gorm:"column:starrocks_storage_total;comment:Starrocks 总存储大小" json:"starrocks_storage_total"`
	ClickhouseStorageTotal int64     `gorm:"column:clickhouse_storage_total;comment:Clickhouse 总存储大小" json:"clickhouse_storage_total"`
	TidbStorageTotal       int64     `gorm:"column:tidb_storage_total;comment:Tidb 总存储大小" json:"tidb_storage_total"`
	DliStorageTotal        int64     `gorm:"column:dli_storage_total;comment:Dli 总存储大小" json:"dli_storage_total"`
}

func (DataResource) TableName() string {
	return "statistic_daily_data_resources"
}

// 可优化资源
type OptimizableStat struct {
	ID          uint      `gorm:"column:id;primaryKey;comment:主键id" json:"id"`
	StatDate    time.Time `gorm:"column:stat_date;type:date;index;comment:统计日期" json:"stat_date"`
	AMDPercent  float64   `gorm:"column:amd_percent;comment:AMD百分比" json:"amd_percent"`
	CPUUsage    float64   `gorm:"column:cpu_usage;comment:CPU使用率" json:"cpu_usage"`
	MemoryUsage float64   `gorm:"column:memory_usage;comment:内存使用率" json:"memory_usage"`
}

func (OptimizableStat) TableName() string {
	return "statistic_daily_optimizable_assets"
}
