package bill

import (
	"cmdb/app"
	"errors"
	"time"
)

// ResourceGroupBill 表示资源组级别的账单统计
type ResourceGroupBill struct {
	Month             string  `json:"month"`               // 账单周期，格式为 2021-01
	ResourceGroupName string  `json:"resource_group_name"` // 资源组名称
	Amount            float64 `json:"amount"`              // 账单金额
}

// 获取资源组名称，如果为空则返回默认值
func getResourceGroupDisplayName(name string) string {
	if name == "" {
		return "（未设置资源组）"
	}
	return name
}

// GetResourceGroupBills 获取指定账单周期内所有资源组的账单统计
func GetResourceGroupBills(billCycle string) (rgbs []ResourceGroupBill, err error) {
	rgbs = make([]ResourceGroupBill, 0)

	// 从阿里云账单表中直接查询资源组数据
	var aliyunRgbs []ResourceGroupBill
	err = app.DB().Table("asset_monthly_bills_aliyun a").
		Select("? as month, a.resource_group as resource_group_name, SUM(a.pretax_amount) as amount", billCycle).
		Where("a.bill_cycle = ? AND a.pretax_amount > 0", billCycle).
		Group("a.resource_group").
		Scan(&aliyunRgbs).Error
	if err != nil {
		app.Log().Error("获取阿里云资源组账单统计失败", "billCycle", billCycle, "err", err)
	} else {
		// 处理资源组名称为空的情况
		for i := range aliyunRgbs {
			aliyunRgbs[i].ResourceGroupName = getResourceGroupDisplayName(aliyunRgbs[i].ResourceGroupName)
		}
		rgbs = append(rgbs, aliyunRgbs...)
	}

	// 从华为云账单表中直接查询资源组数据
	var hwcloudRgbs []ResourceGroupBill
	err = app.DB().Table("asset_monthly_bills_hwcloud h").
		Select("? as month, h.enterprise_project_name as resource_group_name, SUM(h.consume_amount) as amount", billCycle).
		Where("h.cycle = ? AND h.consume_amount > 0", billCycle).
		Group("h.enterprise_project_name").
		Scan(&hwcloudRgbs).Error
	if err != nil {
		app.Log().Error("获取华为云资源组账单统计失败", "billCycle", billCycle, "err", err)
	} else {
		// 处理资源组名称为空的情况
		for i := range hwcloudRgbs {
			hwcloudRgbs[i].ResourceGroupName = getResourceGroupDisplayName(hwcloudRgbs[i].ResourceGroupName)
		}
		rgbs = append(rgbs, hwcloudRgbs...)
	}

	// 从腾讯云账单表中直接查询资源组数据
	var tencentRgbs []ResourceGroupBill
	err = app.DB().Table("asset_monthly_bills_tencentcloud t").
		Select("? as month, t.project_name as resource_group_name, SUM(t.real_total_cost) as amount", billCycle).
		Where("t.bill_cycle = ? AND t.real_total_cost > 0", billCycle).
		Group("t.project_name").
		Scan(&tencentRgbs).Error
	if err != nil {
		app.Log().Error("获取腾讯云资源组账单统计失败", "billCycle", billCycle, "err", err)
	} else {
		// 处理资源组名称为空的情况
		for i := range tencentRgbs {
			tencentRgbs[i].ResourceGroupName = getResourceGroupDisplayName(tencentRgbs[i].ResourceGroupName)
		}
		rgbs = append(rgbs, tencentRgbs...)
	}

	// 按资源组名称合并统计金额（处理多云环境下资源组名称相同的情况）
	mergedRgbs := mergeResourceGroupBillsByName(rgbs)

	return mergedRgbs, nil
}

// mergeResourceGroupBillsByName 按资源组名称合并统计金额
func mergeResourceGroupBillsByName(rgbs []ResourceGroupBill) []ResourceGroupBill {
	// 按资源组名称分组统计
	groupMap := make(map[string]map[string]*ResourceGroupBill)

	for _, rgb := range rgbs {
		// 确保资源组名称不为空
		groupName := getResourceGroupDisplayName(rgb.ResourceGroupName)

		// 如果该资源组名称不存在，则创建一个新的map
		if _, ok := groupMap[groupName]; !ok {
			groupMap[groupName] = make(map[string]*ResourceGroupBill)
		}

		// 如果该资源组名称和月份的组合不存在，则创建一个新的ResourceGroupBill
		if _, ok := groupMap[groupName][rgb.Month]; !ok {
			groupMap[groupName][rgb.Month] = &ResourceGroupBill{
				Month:             rgb.Month,
				ResourceGroupName: groupName,
				Amount:            0,
			}
		}

		// 累加金额
		groupMap[groupName][rgb.Month].Amount += rgb.Amount
	}

	// 转换为切片
	result := make([]ResourceGroupBill, 0)
	for _, monthMap := range groupMap {
		for _, bill := range monthMap {
			result = append(result, *bill)
		}
	}

	return result
}

// GetResourceGroupBillsByDateRange 获取指定时间范围内所有资源组的账单统计
func GetResourceGroupBillsByDateRange(startTime, endTime string) (rgbs []ResourceGroupBill, err error) {
	rgbs = make([]ResourceGroupBill, 0)

	// 计算时间范围内的所有月份
	billCycles := []string{}
	if startTime == endTime {
		billCycles = append(billCycles, startTime)
	} else {
		// 解析开始和结束时间
		start, err1 := time.Parse("2006-01", startTime)
		end, err2 := time.Parse("2006-01", endTime)
		if err1 != nil || err2 != nil {
			app.Log().Error("解析时间失败", "startTime", startTime, "endTime", endTime)
			return rgbs, errors.New("解析时间失败")
		}

		// 确保开始时间不晚于结束时间
		if start.After(end) {
			start, end = end, start
		}

		// 生成时间范围内的所有月份
		for d := start; !d.After(end); d = d.AddDate(0, 1, 0) {
			billCycles = append(billCycles, d.Format("2006-01"))
		}
	}

	// 从阿里云账单表中直接查询资源组数据
	var aliyunRgbs []ResourceGroupBill
	err = app.DB().Table("asset_monthly_bills_aliyun a").
		Select("a.bill_cycle as month, a.resource_group as resource_group_name, SUM(a.pretax_amount) as amount").
		Where("a.bill_cycle IN (?) AND a.pretax_amount > 0", billCycles).
		Group("a.bill_cycle, a.resource_group").
		Scan(&aliyunRgbs).Error
	if err != nil {
		app.Log().Error("获取阿里云资源组账单统计失败", "billCycles", billCycles, "err", err)
	} else {
		// 处理资源组名称为空的情况
		for i := range aliyunRgbs {
			aliyunRgbs[i].ResourceGroupName = getResourceGroupDisplayName(aliyunRgbs[i].ResourceGroupName)
		}
		rgbs = append(rgbs, aliyunRgbs...)
	}

	// 从华为云账单表中直接查询资源组数据
	var hwcloudRgbs []ResourceGroupBill
	err = app.DB().Table("asset_monthly_bills_hwcloud h").
		Select("h.cycle as month, h.enterprise_project_name as resource_group_name, SUM(h.consume_amount) as amount").
		Where("h.cycle IN (?) AND h.consume_amount > 0", billCycles).
		Group("h.cycle, h.enterprise_project_name").
		Scan(&hwcloudRgbs).Error
	if err != nil {
		app.Log().Error("获取华为云资源组账单统计失败", "billCycles", billCycles, "err", err)
	} else {
		// 处理资源组名称为空的情况
		for i := range hwcloudRgbs {
			hwcloudRgbs[i].ResourceGroupName = getResourceGroupDisplayName(hwcloudRgbs[i].ResourceGroupName)
		}
		rgbs = append(rgbs, hwcloudRgbs...)
	}

	// 从腾讯云账单表中直接查询资源组数据
	var tencentRgbs []ResourceGroupBill
	err = app.DB().Table("asset_monthly_bills_tencentcloud t").
		Select("t.bill_cycle as month, t.project_name as resource_group_name, SUM(t.real_total_cost) as amount").
		Where("t.bill_cycle IN (?) AND t.real_total_cost > 0", billCycles).
		Group("t.bill_cycle, t.project_name").
		Scan(&tencentRgbs).Error
	if err != nil {
		app.Log().Error("获取腾讯云资源组账单统计失败", "billCycles", billCycles, "err", err)
	} else {
		// 处理资源组名称为空的情况
		for i := range tencentRgbs {
			tencentRgbs[i].ResourceGroupName = getResourceGroupDisplayName(tencentRgbs[i].ResourceGroupName)
		}
		rgbs = append(rgbs, tencentRgbs...)
	}

	// 按资源组名称合并统计金额（处理多云环境下资源组名称相同的情况）
	rgbs = mergeResourceGroupBillsByName(rgbs)

	return
}

// ResourceGroupBillSummary 资源组账单汇总统计
type ResourceGroupBillSummary struct {
	Month  string  `json:"month"`  // 账单周期，格式为 2021-01
	Count  int     `json:"count"`  // 资源组数量
	Amount float64 `json:"amount"` // 账单总金额
}

// GetResourceGroupBillsByCloudType 获取指定账单周期内资源组账单汇总统计
// 注意：函数名保持不变以保持API兼容性，但实际上不再按云服务商类型分组
func GetResourceGroupBillsByCloudType(billCycle string) (summary ResourceGroupBillSummary, err error) {
	summary = ResourceGroupBillSummary{
		Month:  billCycle,
		Count:  0,
		Amount: 0,
	}

	// 获取所有资源组账单
	bills, err := GetResourceGroupBills(billCycle)
	if err != nil {
		app.Log().Error("获取资源组账单失败", "billCycle", billCycle, "err", err)
		return
	}

	// 统计不重复的资源组名称
	resourceGroupNames := make(map[string]struct{})
	for _, bill := range bills {
		resourceGroupNames[bill.ResourceGroupName] = struct{}{}
		summary.Amount += bill.Amount
	}
	summary.Count = len(resourceGroupNames)

	return
}
