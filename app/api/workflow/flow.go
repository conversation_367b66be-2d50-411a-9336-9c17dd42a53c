package workflow

import (
	"cmdb/app"
	"cmdb/app/service/audit"
	"cmdb/app/service/workflow"

	"github.com/gin-gonic/gin"
)

func GetAllFlow(c *gin.Context) {
	data, err := workflow.GetAllFlows()
	if err != nil {
		app.Log().Error("获取所有工单流程失败", "err", err)
		app.FailedResponseMsg(c, "获取所有工单流程失败")
		return
	}
	app.SuccessResponseData(c, data)
}

func UpdateFlow(c *gin.Context) {
	var form workflow.FlowNodesForm
	if err := c.ShouldBindJSON(&form); err != nil || form.FlowID == 0 {
		app.Log().Error("更新工单流程失败", "err", err)
		app.FailedResponseMsg(c, "非法字段")
		return
	}
	flow, err := workflow.GetFlowByID(form.FlowID)
	if err != nil {
		app.Log().Error("更新工单流程失败", "err", err)
		app.FailedResponseMsg(c, "更新失败")
		return
	}
	err = flow.UpdateNodes(form)
	if err != nil {
		app.Log().Error("更新工单流程失败", "err", err)
		app.FailedResponseMsg(c, "更新失败")
		return
	}
	audit.LogAPIOP(c, "工单管理", "更新工单流程")
	app.SuccessResponseMsg(c, "更新成功")
}
