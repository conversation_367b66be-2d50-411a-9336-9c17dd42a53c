package workflow

import (
	"cmdb/app"
	"cmdb/app/service/auth"
	"time"
)

type OrderComment struct {
	ID        uint      `gorm:"column:id;primaryKey;comment:主键" json:"id"`
	UserID    uint      `gorm:"column:user_id;index;comment:用户ID" json:"user_id"`
	User      auth.User `gorm:"-" json:"user"`
	SN        string    `gorm:"type:varchar(128);column:sn;index;comment:关联工单单号" json:"sn"`
	Content   string    `gorm:"column:content;type:text;comment:内容" json:"content"`
	CreatedAt time.Time `gorm:"column:created_at;index" json:"created_at"`
}

func (OrderComment) TableName() string {
	return "workflow_order_comments"
}

type OrderCommentForm struct {
	SN      string `json:"sn" binding:"required"`
	Content string `json:"content" binding:"required"`
}

func (approver Approver) Comment(form OrderCommentForm) (err error) {
	err = app.DB().Create(&OrderComment{
		UserID:  approver.ID,
		SN:      form.SN,
		Content: form.Content,
	}).Error
	return
}

func GetOrderComments(sn string) (comments []OrderComment, err error) {
	err = app.DB().Where("sn = ?", sn).Order("created_at DESC").Find(&comments).Error
	if err != nil {
		return
	}
	var err1 error
	for i, comment := range comments {
		comments[i].User, err1 = auth.GetUserByID(int(comment.UserID))
		if err1 != nil {
			app.Log().Error("获取用户失败", "err", err1)
		}
	}
	return
}

func GetOrderCommentByID(id int) (comment OrderComment, err error) {
	err = app.DB().Where("id = ?", id).Take(&comment).Error
	return
}

func (comment OrderComment) Update(form OrderCommentForm) (err error) {
	if form.Content != comment.Content {
		err = app.DB().Model(&comment).Update("content", form.Content).Error
	}
	return
}
