package upload

import (
	"cmdb/app"
	"cmdb/app/service/audit"
	"cmdb/app/service/auth"
	"cmdb/app/service/workflow"
	"cmdb/pkg/utils"
	"fmt"
	"io"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// 上传的临时目录
var UploadTmpDir = "tmp/uploads/database/"

// 上传目录
func DatabaseOrderUpLoad(c *gin.Context) {
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		app.Log().Error("获取文件失败", "err", err)
		app.FailedResponseMsg(c, "获取文件失败")
		return
	}
	suffixs := []string{".rar", ".zip", ".txt", ".sql", "tar.gz", ".tar", ".gz"}
	legalFileFlag := false
	for i := range suffixs {
		if strings.HasSuffix(header.Filename, suffixs[i]) {
			legalFileFlag = true
			break
		}
	}
	if !legalFileFlag {
		app.FailedResponseMsg(c, "非法文件！合法文件后缀："+strings.Join(suffixs, ","))
		return
	}
	urlDir := time.Now().Format("2006-01")
	err = os.MkdirAll(UploadTmpDir+urlDir, os.ModeAppend|os.ModeDir|os.ModePerm)
	if err != nil {
		app.Log().Error("目录创建失败", "err", err)
		app.FailedResponseMsg(c, "目录创建失败")
		return
	}
	filename := urlDir + string(os.PathSeparator) + strings.ReplaceAll(utils.NewUUID(header.Filename), "-", "")
	tmpfile := UploadTmpDir + string(os.PathSeparator) + filename
	out, err := os.Create(tmpfile)
	if err != nil {
		app.Log().Error("创建文件失败", "err", err)
		app.FailedResponseMsg(c, "创建文件失败")
		return
	}
	defer out.Close()
	_, err = io.Copy(out, file)
	if err != nil {
		app.Log().Error("保存文件失败", "err", err)
		app.FailedResponseMsg(c, "保存文件失败")
		return
	}
	fileInfo, err := os.Stat(tmpfile)
	if err == nil {
		audit.LogAPIOP(c, "文件上传", fmt.Sprintf("上传数据库工单附件：%s(%s)，大小：%d 字节", filename, fileInfo.Name(), fileInfo.Size()))
	} else {
		app.Log().Error("获取文件信息失败", "err", err)
	}
	app.SuccessResponseData(c, map[string]string{
		"url":      "/api/v1/workflow/order/uploads/database/" + filename,
		"filename": header.Filename,
	})
}

// 下载
func DatabaseOrderDownload(c *gin.Context) {
	attachmentURL := c.Request.URL.Path
	filename := c.Param("filename")
	sn := c.DefaultQuery("sn", "")
	if filename == "" || sn == "" {
		c.AbortWithStatus(http.StatusNotFound)
		return
	}
	loginUser, err := auth.GetLoginUser(c)
	o, err := workflow.GetOrderBySN(sn)
	if err != nil {
		c.AbortWithStatus(http.StatusInternalServerError)
		return
	}
	if !loginUser.IsAdmin && !o.CheckOrderPrivilege(loginUser.ID) {
		c.AbortWithStatus(http.StatusForbidden)
		return
	}
	attachements, err := workflow.GetMySQLOrderAttachements(sn)
	if err != nil {
		app.Log().Error("获取工单附件失败", "err", err)
		c.AbortWithStatus(http.StatusInternalServerError)
		return
	}
	attachementName := "attachment"
	for i := range attachements {
		if attachements[i].URL == attachmentURL {
			attachementName = attachements[i].Filename
			break
		}
	}
	c.Writer.Header().Add("Content-Disposition", fmt.Sprintf("attachment; filename=%s", attachementName))
	if strings.HasSuffix(filename, ".txt") || strings.HasSuffix(filename, ".sql") {
		c.Writer.Header().Add("Content-Type", "text/plain")
	} else {
		c.Writer.Header().Add("Content-Type", "application/octet-stream")
	}
	c.Status(http.StatusOK)
	c.File("uploads" + filename)
}
