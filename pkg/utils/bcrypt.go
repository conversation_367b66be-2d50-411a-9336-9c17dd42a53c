package utils

import "golang.org/x/crypto/bcrypt"

// ComparePassword : 对比密码
func ComparePassword(comPwd, pwdHash string) bool {
	return bcrypt.CompareHashAndPassword([]byte(pwdHash), []byte(comPwd)) == nil
}

// EncodePassword ： 加密密码
func EncodePassword(pwd string) string {
	hash, err := bcrypt.GenerateFromPassword([]byte(pwd), bcrypt.DefaultCost)
	if err != nil {
		return ""
	}
	return string(hash)
}
