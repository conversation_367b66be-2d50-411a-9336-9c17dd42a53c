package api

import (
	"cmdb/app"
	"cmdb/app/service/auth"
	"encoding/json"
	"net/http"

	"github.com/gin-gonic/gin"
)

// Cors 跨域设置
func Cors() gin.HandlerFunc {
	return func(c *gin.Context) {
		method := c.Request.Method
		c.Header("Access-Control-Allow-Origin", "*")
		c.<PERSON>("Access-Control-Allow-Methods", "POST, GET, OPTIONS, PUT, DELETE, UPDATE")
		c.<PERSON><PERSON>("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept, Authorization")
		c.<PERSON><PERSON>("Access-Control-Expose-Headers", "Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Cache-Control, Content-Language, Content-Type")
		c.<PERSON>er("Access-Control-Allow-Credentials", "true")
		if method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
		}
		c.Next()
	}
}

// 需要验证登录
func loginRequired() gin.HandlerFunc {
	return func(c *gin.Context) {
		_, err := auth.ParseToken(app.GetToken(c))
		if err != nil {
			app.Log().Info("登陆认证中间件认证失败", "err", err)
			c.JSON(http.StatusUnauthorized, gin.H{"success": false, "msg": "验证失败"})
			c.Abort()
			return
		}
		c.Next()
	}
}

// AdminRequired 管理员权限
func adminRequired() gin.HandlerFunc {
	return func(c *gin.Context) {
		token := app.GetToken(c)
		u, err := auth.ParseToken(token)
		if err != nil {
			app.Log().Debug("登陆认证中间件认证失败", "err", err)
			c.JSON(http.StatusUnauthorized, gin.H{"success": false, "msg": "验证失败"})
			c.Abort()
			return
		}
		if !u.IsAdmin {
			c.JSON(http.StatusForbidden, gin.H{"success": false, "msg": "权限不足"})
			c.Abort()
			return
		}
		c.Next()
	}
}

func qaRequired() gin.HandlerFunc {
	return rolesRequiredOne(auth.RoleQA)
}

func workflowAdminRequired() gin.HandlerFunc {
	return rolesRequiredOne(auth.RoleWorkflowAdmin)
}

func rolesRequiredOne(roles ...auth.Role) gin.HandlerFunc {
	return func(c *gin.Context) {
		token := app.GetToken(c)
		u, err := auth.ParseToken(token)
		if err != nil {
			app.Log().Debug("登陆认证中间件认证失败", "err", err)
			c.JSON(http.StatusUnauthorized, gin.H{"success": false, "msg": "验证失败"})
			c.Abort()
			return
		}
		if u.IsAdmin {
			c.Next()
			return
		}
		uRoles := []auth.Role{}
		json.Unmarshal(u.Roles, &uRoles)
		for _, role := range roles {
			for i := range uRoles {
				if uRoles[i] == role {
					c.Next()
					return
				}
			}
		}
		c.Next()
	}
}

// 验证登录
func KeyRequired() gin.HandlerFunc {
	return func(c *gin.Context) {
		token := c.GetHeader("token")
		uri := c.Request.URL.Path
		ok := auth.CheckKey(token, uri)
		if ok != nil {
			c.JSON(http.StatusForbidden, gin.H{"success": false, "msg": "权限不足"})
			c.Abort()
		}
		c.Next()
	}
}

// TokenRequired token验证
func TokenRequired() gin.HandlerFunc {
	return func(c *gin.Context) {
		if c.GetHeader("Token") != "VrUnpgN5vx8vNkLHqqsPGsxvkNZHeXYi" {
			c.JSON(http.StatusForbidden, gin.H{
				"success": false,
				"msg":     "权限不足",
			})
			c.Abort()
			return
		}
		c.Next()
	}
}
