package asset

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/now"
)

type MonthlyBillRow struct {
	asset.MonthlyBill
	AccountName string `gorm:"-" json:"account_name"`
}

func GetMonthlyBills(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	account, err := asset.GetCloudAccountByID(id)
	if err != nil {
		app.FailedResponseMsg(c, "获取账户信息失败")
		return
	}
	page, _ := strconv.Atoi(c.Default<PERSON>uery("page", "1"))
	limit, _ := strconv.Atoi(c.Default<PERSON>("limit", "10"))
	// 计算偏移量
	offset := (page - 1) * limit
	var resourceID, billCycle, resourceName *string
	if val := strings.TrimSpace(c.Query("bill_cycle")); val != "" {
		billCycle = &val
	}
	if val := strings.TrimSpace(c.Query("resource_id")); val != "" {
		resourceID = &val
	}
	if val := strings.TrimSpace(c.Query("resource_name")); val != "" {
		resourceName = &val
	}
	count, bills, err := account.GetMonthlyBills(offset, limit, billCycle, resourceID, resourceName)
	if err != nil {
		app.Log().Error("获取账单数据失败", "err", err)
		app.FailedResponseMsg(c, "获取账单数据失败")
		return
	}
	newData := make([]MonthlyBillRow, len(bills))
	if len(bills) > 0 {
		accountMap := sync.Map{}
		for i, bill := range bills {
			newData[i] = MonthlyBillRow{MonthlyBill: bill}
			if val, ok := accountMap.Load(bill.AccountID); ok {
				newData[i].AccountName = val.(string)
			} else {
				accountName, err1 := asset.GetAccountNameByID(int(bill.AccountID))
				if err1 == nil && accountName != "" {
					accountMap.Store(bill.AccountID, accountName)
					newData[i].AccountName = accountName
				}
			}
		}
	}
	app.SuccessResponseDataCount(c, newData, count)
}

func GetMonthlyBillAmounts(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.Log().Debug("参数错误", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	account, err := asset.GetCloudAccountByID(id)
	if err != nil {
		app.Log().Error("获取账户信息失败", "err", err)
		app.FailedResponseMsg(c, "获取账户信息失败")
		return
	}
	now := time.Now()
	endTime, err := time.ParseInLocation("2006-01", c.Query("end_time"), time.Local)
	if err != nil {
		endTime = now
	}
	startTime, err := time.ParseInLocation("2006-01", c.Query("start_time"), time.Local)
	if err != nil {
		startTime = endTime.AddDate(0, -6, 0)
	}
	var resourceID *string
	if val := strings.TrimSpace(c.Query("resource_id")); val != "" {
		resourceID = &val
	}
	data, err := account.GetMonthlyBillAmounts(startTime, endTime, resourceID)
	if err != nil {
		app.Log().Error("获取账单金额失败", "err", err)
		app.FailedResponseMsg(c, "获取账单金额失败")
		return
	}
	app.SuccessResponseData(c, data)
}

func GetMonthlyBillResourceTypeAmount(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.Log().Debug("参数错误", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	var billCycle string
	bTime, err := time.ParseInLocation("2006-01", c.Query("bill_cycle"), time.Local)
	if err != nil {
		billCycle = time.Now().Format("2006-01")
	} else {
		billCycle = bTime.Format("2006-01")
	}
	account, err := asset.GetCloudAccountByID(id)
	if err != nil {
		app.Log().Error("获取账户信息失败", "err", err)
		app.FailedResponseMsg(c, "获取账户信息失败")
		return
	}
	data, err := account.GetMonthlyBillResourceTypeAmount(billCycle)
	if err != nil {
		app.Log().Error("获取账单资源类型金额失败", "err", err)
		app.FailedResponseMsg(c, "获取账单资源类型金额失败")
		return
	}
	app.SuccessResponseData(c, data)
}

// 获取指定时间范围内的账单资源类型金额
func GetMonthlyBillResourceTypeAmounts(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.Log().Debug("参数错误", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	now := time.Now()
	endTime, err := time.ParseInLocation("2006-01", c.Query("end_time"), time.Local)
	if err != nil {
		endTime = now
	}
	startTime, err := time.ParseInLocation("2006-01", c.Query("start_time"), time.Local)
	if err != nil {
		startTime = endTime.AddDate(0, -6, 0)
	}
	// 获取云账户
	account, err := asset.GetCloudAccountByID(id)
	if err != nil {
		app.Log().Error("获取账户信息失败", "err", err)
		app.FailedResponseMsg(c, "获取账户信息失败")
		return
	}
	// 获取账单资源类型金额
	data, err := account.GetMonthlyBillResourceTypeAmountsByCycles(startTime, endTime)
	if err != nil {
		app.Log().Error("获取账单资源类型金额失败", "err", err)
		app.FailedResponseMsg(c, "获取账单资源类型金额失败")
		return
	}
	app.SuccessResponseData(c, data)
}

// 资源组月份账单
func GetResourceGroupMonthlyBill(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.Log().Debug("参数错误", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	now := time.Now()
	endTime, err := time.ParseInLocation("2006-01", c.Query("end_time"), time.Local)
	if err != nil {
		endTime = now
	}
	startTime, err := time.ParseInLocation("2006-01", c.Query("start_time"), time.Local)
	if err != nil {
		startTime = endTime.AddDate(0, -6, 0)
	}
	// 获取云账户
	rs, err := asset.GetResourceGroupByID(id)
	if err != nil {
		app.Log().Error("获取账户信息失败", "err", err)
		app.FailedResponseMsg(c, "获取账户信息失败")
		return
	}
	// 获取账单资源类型金额
	data, err := rs.GetMonthlyBillAmounts(startTime, endTime)
	if err != nil {
		app.Log().Error("获取账单资源组类型金额失败", "err", err)
		app.FailedResponseMsg(c, "获取账单资源组类型金额失败")
		return
	}
	app.SuccessResponseData(c, data)
}

// 获取指定时间范围内的云服务商月度账单
func GetCloudTypeMonthlyBills(c *gin.Context) {
	defaultNow := now.BeginningOfMonth()
	endTime, err := time.ParseInLocation("2006-01", c.Query("end_time"), time.Local)
	if err != nil {
		endTime = defaultNow
	}
	startTime, err := time.ParseInLocation("2006-01", c.Query("start_time"), time.Local)
	if err != nil {
		startTime = endTime.AddDate(0, -6, 0)
	}
	// 获取云服务商月度账单
	data, err := asset.GetCloudTypeMonthlyBills(startTime, endTime)
	if err != nil {
		app.Log().Error("获取云服务商月度账单失败", "err", err)
		app.FailedResponseMsg(c, "获取云服务商月度账单失败")
		return
	}
	app.SuccessResponseData(c, data)
}
