package assets

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/assets"
	"cmdb/app/service/audit"
	"errors"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

type AssetSystemList struct {
	assets.AssetSystem
	Business string `json:"business"`
}

// 获取系统列表
func GetAssetSystems(c *gin.Context) {
	page, _ := strconv.Atoi(c.<PERSON><PERSON>("page", "1"))
	limit, _ := strconv.Atoi(c.<PERSON>("limit", "10"))
	offset := (page - 1) * limit
	var keyword *string
	if val := strings.TrimSpace(c.Query("keyword")); val != "" {
		keyword = &val
	}
	var businessID *int
	if val, err := strconv.Atoi(c.Query("business_id")); err == nil {
		businessID = &val
	}
	count, data, err := assets.GetAssetSystems(offset, limit, businessID, keyword)
	if err != nil {
		app.Log().Error("获取系统列表失败", "err", err)
		app.FailedResponseMsg(c, "获取系统列表失败")
		return
	}
	newData := make([]AssetSystemList, 0, len(data))
	for _, item := range data {
		businessName, _ := asset.GetBusinessNameByID(int(item.BusinessID))
		newData = append(newData, AssetSystemList{
			AssetSystem: item,
			Business:    businessName,
		})
	}
	app.SuccessResponseDataCount(c, newData, count)
}

// 创建系统
func CreateAssetSystem(c *gin.Context) {
	var form assets.AssetSystemForm
	if err := c.ShouldBindJSON(&form); err != nil {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	err := form.Create()
	if err != nil {
		msg := "创建系统失败"
		if errors.Is(err, assets.ErrAssetSystemExist) {
			msg = "系统名称已存在"
		} else {
			app.Log().Error(msg, "err", err)
		}
		app.FailedResponseMsg(c, msg)
		return
	}
	audit.LogAPIOP(c, "资产管理", "创建系统："+form.Name)
	app.SuccessResponseMsg(c, "创建成功")
}

// 更新系统
func UpdateAssetSystem(c *gin.Context) {
	var form assets.AssetSystemForm
	if err := c.ShouldBindJSON(&form); err != nil {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	system, err := assets.GetAssetSystemByID(id)
	if err != nil {
		app.Log().Error("更新系统失败", "err", err)
		app.FailedResponseMsg(c, "更新系统失败")
		return
	}
	err = system.Update(form)
	if err != nil {
		msg := "更新系统失败"
		if errors.Is(err, assets.ErrAssetSystemExist) {
			msg = "系统名称已存在"
		} else {
			app.Log().Error(msg, "err", err)
		}
		app.FailedResponseMsg(c, msg)
		return
	}
	audit.LogAPIOP(c, "资产管理", "更新系统:"+form.Name)
	app.SuccessResponseMsg(c, "更新成功")
}

// 删除系统
func DeleteAssetSystem(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	system, err := assets.GetAssetSystemByID(id)
	if err != nil {
		app.Log().Error("删除系统失败", "err", err)
		app.FailedResponseMsg(c, "删除系统失败")
		return
	}
	err = system.Delete()
	if err != nil {
		app.Log().Error("删除系统失败", "err", err)
		app.FailedResponseMsg(c, "删除系统失败")
		return
	}
	audit.LogAPIOP(c, "资产管理", "删除系统:"+system.Name)
	app.SuccessResponseMsg(c, "删除成功")
}

func GetAssetSystem(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	system, err := assets.GetAssetSystemByID(id)
	if err != nil {
		app.Log().Error("获取系统失败", "err", err)
		app.FailedResponseMsg(c, "获取系统失败")
		return
	}
	businessName, _ := asset.GetBusinessNameByID(int(system.BusinessID))
	app.SuccessResponseData(c, AssetSystemList{
		AssetSystem: system,
		Business:    businessName,
	})
}
