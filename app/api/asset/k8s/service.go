package k8s

import (
	"cmdb/app"
	"cmdb/app/service/asset/k8s"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

type ServiceRow struct {
	k8s.Service
	ClusterName string `json:"cluster_name"`
}

func GetServices(c *gin.Context) {
	page, _ := strconv.Atoi(c.Query("page"))
	limit, _ := strconv.Atoi(c.Query("limit"))
	offset := (page - 1) * limit
	var clusterID *int
	if c.Query("cluster_id") != "" {
		clusterIDInt, _ := strconv.Atoi(c.Query("cluster_id"))
		clusterID = &clusterIDInt
	}
	var namespace *string
	if val := strings.TrimSpace(c.Query("namespace")); val != "" {
		namespace = &val
	}
	var keyword *string
	if val := strings.TrimSpace(c.Query("keyword")); val != "" {
		keyword = &val
	}
	data, total, err := k8s.GetServices(offset, limit, clusterID, namespace, keyword)
	if err != nil {
		app.Log().Error("获取k8s服务失败", "err", err)
		app.FailedResponseMsg(c, "获取k8s服务失败")
		return
	}
	newData := []ServiceRow{}
	tmpStore := map[uint]string{}
	for _, v := range data {
		v.Labels, _ = v.GetLabels()
		v.SelectorLabels, _ = v.GetSelectorLabels()
		if val, ok := tmpStore[v.ClusterID]; ok {
			newData = append(newData, ServiceRow{Service: v, ClusterName: val})
		} else {
			if name, err := k8s.GetClusterName(v.ClusterID); err == nil {
				tmpStore[v.ClusterID] = name
				newData = append(newData, ServiceRow{Service: v, ClusterName: name})
			} else {
				newData = append(newData, ServiceRow{Service: v, ClusterName: ""})
			}
		}
	}
	app.SuccessResponseDataCount(c, newData, total)
}
