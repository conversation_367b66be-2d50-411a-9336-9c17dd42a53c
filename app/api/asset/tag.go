package asset

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/audit"
	"errors"
	"strconv"

	"github.com/gin-gonic/gin"
)

// 添加标签
func AddTag(c *gin.Context) {
	// 定义标签表单
	var form asset.TagForm
	// 绑定表单数据
	err := c.ShouldBind(&form)
	if err != nil {
		// 记录错误日志
		app.Log().Error("添加标签失败", "err", err)
		// 返回失败响应
		app.FailedResponseMsg(c, "添加失败，存在非法字段")
		return
	}
	// 创建标签
	if err := form.Create(); err != nil {
		// 定义失败消息
		msg := "添加失败"
		// 判断错误类型
		if errors.Is(err, asset.ErrTagExist) {
			// 如果标签已存在，返回错误消息
			msg = err.Error()
		} else {
			// 记录错误日志
			app.Log().Error("添加标签失败", "err", err)
		}
		// 返回失败响应
		app.FailedResponseMsg(c, msg)
		return
	}
	// 记录操作日志
	audit.LogAPIOP(c, "资产管理", "添加标签:"+form.Key+":"+form.Value)
	// 返回成功响应
	app.SuccessResponseMsg(c, "添加成功")
}

func UpdateTag(c *gin.Context) {
	id := c.Param("id")
	idInt, err := strconv.Atoi(id)
	if err != nil {
		app.Log().Debug("参数错误", "id", id, "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	var form asset.TagForm
	err = c.ShouldBind(&form)
	if err != nil {
		app.Log().Debug("参数错误", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	tag, err := asset.GetTagByID(idInt)
	if err != nil {
		app.Log().Debug("查询标签对象异常", "err", err)
		app.FailedResponseMsg(c, "标签不存在")
		return
	}
	err = tag.Update(form)
	if err != nil {
		msg := "更新失败"
		if errors.Is(err, asset.ErrTagExist) {
			msg = err.Error()
		} else {
			app.Log().Error("更新标签失败", "err", err)
		}
		app.FailedResponseMsg(c, msg)
		return
	}
	app.SuccessResponseMsg(c, "更新成功")
}

// 获取标签键
func GetTagKeys(c *gin.Context) {
	// 获取标签键
	data, err := asset.GetTagKeys()
	if err != nil {
		// 记录错误日志
		app.Log().Error("获取标签键失败", "err", err)
		// 返回失败响应
		app.FailedResponseMsg(c, "获取标签键失败")
		return
	}
	// 返回成功响应
	app.SuccessResponseData(c, data)
}

type TagRow struct {
	asset.Tag
	HostTotal int64 `json:"host_total"`
}

func GetTags(c *gin.Context) {
	// 获取查询参数
	keyword := c.Query("keyword")
	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	// 计算偏移量
	offset := (page - 1) * limit
	// 获取标签列表
	count, data, err := asset.GetTags(offset, limit, keyword)
	if err != nil {
		// 记录错误日志
		app.Log().Error("获取标签列表失败", "err", err)
		// 返回失败响应
		app.FailedResponseMsg(c, "获取标签列表失败")
		return
	}
	newData := make([]TagRow, len(data))
	if len(data) > 0 {
		for i, tag := range data {
			newData[i] = TagRow{tag, tag.CountHost()}
		}
	}
	// 返回成功响应
	app.SuccessResponseDataCount(c, newData, count)
}

func DeleteTag(c *gin.Context) {
	// 获取标签ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		// 记录错误日志
		app.Log().Error("删除标签失败", "err", err)
		// 返回失败响应
		app.FailedResponseMsg(c, "删除失败")
		return
	}
	tag, err := asset.GetTagByID(id)
	if err != nil {
		// 记录错误日志
		app.Log().Error("删除标签失败", "err", err)
		// 返回失败响应
		app.FailedResponseMsg(c, "删除失败")
		return
	}
	// 删除标签
	err = tag.Delete()
	if err != nil {
		// 记录错误日志
		app.Log().Error("删除标签失败", "err", err)
		// 返回失败响应
		app.FailedResponseMsg(c, "删除失败")
		return
	}
	// 返回成功响应
	app.SuccessResponseMsg(c, "删除成功")
}

func GetAllTags(c *gin.Context) {
	data, err := asset.GetAllTags()
	if err != nil {
		// 记录错误日志
		app.Log().Error("获取标签列表失败", "err", err)
		// 返回失败响应
		app.FailedResponseMsg(c, "获取标签列表失败")
		return
	}
	app.SuccessResponseData(c, data)
}
