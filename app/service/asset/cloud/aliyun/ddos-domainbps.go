package aliyun

import (
	"errors"
	"time"

	ddoscoo******** "github.com/alibabacloud-go/ddoscoo-********/v3/client"
	"github.com/jinzhu/now"
	"gorm.io/gorm"

	openapi "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	"github.com/alibabacloud-go/tea/tea"

	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/asset/cloud"
	"cmdb/pkg/utils"
)

func (a Account) syncDDoSDomainBps(statDate time.Time) (result cloud.SyncResults, err error) {
	var domains []asset.CloudDDosDomain
	err = app.DB().Where("account_id = ?", a.ID).Find(&domains).Error
	if err != nil {
		return
	}
	startTime := now.With(statDate).BeginningOfDay()
	endTime := startTime.AddDate(0, 0, 1)
	for _, domain := range domains {
		// 同步昨天的数据
		err := a.syncDDosDomainBps(domain.Domain, startTime, endTime)
		if err != nil {
			result = append(result, cloud.SyncResult{
				Key:    "DDoS Domain " + domain.Domain + " Bps",
				Result: "同步失败" + err.Error(),
			})
		}
	}
	result = append(result, cloud.SyncResult{
		Key:    "DDoS Domain Bps",
		Result: "同步成功",
	})
	return
}

func SyncDDosDomainBps(domain asset.CloudDDosDomain, statDate time.Time) (err error) {
	a := Account{}
	err = app.DB().Where("id = ?", domain.AccountID).Take(&a).Error
	if err != nil {
		return err
	}
	startTime := now.With(statDate).BeginningOfDay()
	endTime := startTime.AddDate(0, 0, 1)
	err = a.syncDDosDomainBps(domain.Domain, startTime, endTime)
	return
}

// SyncInstances synchronizes DDoS instances from Alibaba Cloud
func (a Account) syncDDosDomainBps(domain string, startTime, endTime time.Time) (err error) {
	secret, _ := utils.Base64Decode(a.AccessSecret)
	config := &openapi.Config{
		AccessKeyId:     tea.String(a.AccessKey),
		AccessKeySecret: tea.String(secret),
	}
	config.Endpoint = tea.String("ddoscoo.cn-hangzhou.aliyuncs.com")
	client, err := ddoscoo********.NewClient(config)
	if err != nil {
		return err
	}
	describeDomainBpsRequest := &ddoscoo********.DescribeDomainBpsRequest{}
	describeDomainBpsRequest.Region = tea.String("cn")
	describeDomainBpsRequest.StartTime = tea.Int64(startTime.Unix())
	describeDomainBpsRequest.EndTime = tea.Int64(endTime.Unix())
	if domain != "" {
		describeDomainBpsRequest.Domain = tea.String(domain)
	}
	describeDomainBpsRequest.Interval = tea.Int64(int64(endTime.Sub(startTime).Seconds()) + 1)
	response, err := client.DescribeDomainBps(describeDomainBpsRequest)
	if err != nil {
		return err
	}
	var InBps, OutBps int64
	for _, item := range response.Body.DomainBps {
		if item.InBps != nil {
			InBps += *item.InBps
		}
		if item.OutBps != nil {
			OutBps += *item.OutBps
		}
	}
	if len(response.Body.DomainBps) > 1 {
		InBps = InBps / int64(len(response.Body.DomainBps))
		OutBps = OutBps / int64(len(response.Body.DomainBps))
	}
	exist := asset.CloudDDosDomainDailyBps{}
	err = app.DB().Where("domain = ? AND stat_date = ? AND account_id = ?", domain, startTime.Format("2006-01-02"), a.ID).Take(&exist).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = app.DB().Create(&asset.CloudDDosDomainDailyBps{
			Domain:    domain,
			AccountID: a.ID,
			StatDate:  startTime,
			InBps:     InBps,
			OutBps:    OutBps,
		}).Error
	} else {
		updateMap := map[string]any{}
		if exist.InBps != InBps {
			updateMap["in_bps"] = InBps
		}
		if exist.OutBps != OutBps {
			updateMap["out_bps"] = OutBps
		}
		if len(updateMap) > 0 {
			err = app.DB().Model(&exist).Updates(updateMap).Error
		}
	}
	if err != nil {
		return
	}
	return nil
}
