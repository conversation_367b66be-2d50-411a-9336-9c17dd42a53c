package elasticsearch

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"sort"
	"strings"
	"time"

	"gopkg.in/olivere/elastic.v6"
)

type LogCenterErrorType struct {
	Index         string
	ErrorType     string
	SearchFilters []elastic.Query
}

var LogCenterErrorTypes = []LogCenterErrorType{
	{Index: "logs-center-*", ErrorType: "mysql invalid connection", SearchFilters: []elastic.Query{
		elastic.NewMatchQuery("msg", "invalid connection").Operator("AND"),
		elastic.NewTermQuery("error_type", "mysql"),
		elastic.NewTermQuery("level", "error"),
		elastic.NewTermQuery("env", "online"),
	}},
	{Index: "logs-center*", ErrorType: "mysql i/o timeout", SearchFilters: []elastic.Query{
		elastic.NewMatchQuery("msg", ": i/o timeout").Operator("AND"),
		elastic.NewTermQuery("error_type", "mysql"),
		elastic.NewTermQuery("level", "error"),
		elastic.NewTermQuery("env", "online"),
	}},
	{Index: "logs-center*", ErrorType: "mysql refuse", SearchFilters: []elastic.Query{
		elastic.NewMatchQuery("msg", "mysql refuse").Operator("AND"),
		elastic.NewTermQuery("error_type", "mysql"),
		elastic.NewTermQuery("level", "error"),
		elastic.NewTermQuery("env", "online"),
	}},
	{Index: "logs-center*", ErrorType: "redis invalid connection", SearchFilters: []elastic.Query{
		elastic.NewMatchQuery("msg", "invalid connection").Operator("AND"),
		elastic.NewTermQuery("error_type", "redis"),
		elastic.NewTermQuery("level", "error"),
		elastic.NewTermQuery("env", "online"),
	}},
	{Index: "logs-center*", ErrorType: "redis i/o timeout", SearchFilters: []elastic.Query{
		elastic.NewMatchQuery("msg", ": i/o timeout").Operator("AND"),
		elastic.NewTermQuery("error_type", "redis"),
		elastic.NewTermQuery("level", "error"),
		elastic.NewTermQuery("env", "online"),
	}},
	{Index: "logs-center*", ErrorType: "redis refuse", SearchFilters: []elastic.Query{
		elastic.NewMatchQuery("msg", "redis refuse").Operator("AND"),
		elastic.NewTermQuery("error_type", "redis"),
		elastic.NewTermQuery("level", "error"),
		elastic.NewTermQuery("env", "online"),
	}},
	{Index: "logs-center*", ErrorType: "Connection timed out", SearchFilters: []elastic.Query{
		elastic.NewMatchQuery("msg", "Connection timed out").Operator("AND"),
		elastic.NewTermQuery("level", "error"),
		elastic.NewTermQuery("env", "online"),
	}},
}

type LogcenterErrorStat struct {
	StatSummary string
	Domain      string
	ErrorType   string
	Count       int64
}

type LogcenterErrorStats []LogcenterErrorStat

func (l LogcenterErrorStats) Len() int {
	return len(l)
}
func (l LogcenterErrorStats) Less(i, j int) bool {
	return l[i].Count < l[j].Count
}
func (l LogcenterErrorStats) Swap(i, j int) {
	l[i], l[j] = l[j], l[i]

}

type LogcenterErrorAddrStat struct {
	StatSummary string
	Domain      string
	Addr        string
	ErrorType   string `json:"error_type"`
	Count       int64
}

type LogcenterErrorAddrStats []LogcenterErrorAddrStat

func (l LogcenterErrorAddrStats) Len() int {
	return len(l)
}
func (l LogcenterErrorAddrStats) Less(i, j int) bool {
	return l[i].Count < l[j].Count
}
func (l LogcenterErrorAddrStats) Swap(i, j int) {
	l[i], l[j] = l[j], l[i]

}

type LogsCenterRecord struct {
	Message   string `json:"msg"`
	Domain    string `json:"domain"`
	ErrorType string `json:"error_type"`
	Level     string `json:"level"`
}

func (sp LogCenterErrorType) CheckError(startTime, endTime time.Time) (stats LogcenterErrorStats, addrStats LogcenterErrorAddrStats, err error) {
	client, err := initClient()
	if err != nil {
		return
	}
	defer client.Stop()
	query := elastic.NewBoolQuery()
	qs := []elastic.Query{elastic.NewRangeQuery("@timestamp").Gte(startTime).Lt(endTime)}
	qs = append(qs, sp.SearchFilters...)
	query.Must(qs...)
	result, err := client.Search(sp.Index).Query(query).Size(10000).Do(context.Background())
	if err != nil {
		return
	}
	if result.Hits.TotalHits > 0 {
		errorType := ""
		// 应用统计
		appCount := map[string]int64{}
		addrCount := map[string]int64{}
		for _, hit := range result.Hits.Hits {
			record := LogsCenterRecord{}
			err = json.Unmarshal(*hit.Source, &record)
			if err == nil {
				if errorType == "" {
					errorType = record.ErrorType
				}
				if v, ok := appCount[record.Domain]; ok {
					appCount[record.Domain] = v + 1
				} else {
					appCount[record.Domain] = 1
				}
				matches := regexp.MustCompile(`read tcp.*->(.*?):(\d+)`).FindStringSubmatch(record.Message)
				if len(matches) > 0 {
					if len(matches) > 3 {
						k := record.Domain + "||" + fmt.Sprintf("%s:%s", matches[1], matches[2])
						if v, ok := addrCount[k]; ok {
							addrCount[k] = v + 1
						} else {
							addrCount[k] = 1
						}
					}
				}
				matches = regexp.MustCompile(`mysql:host=(.*?);port=(.*?);`).FindStringSubmatch(record.Message)
				if len(matches) >= 3 {
					k := record.Domain + "||" + fmt.Sprintf("%s:%s", matches[1], matches[2])
					if v, ok := addrCount[k]; ok {
						addrCount[k] = v + 1
					} else {
						addrCount[k] = 1
					}
				}
			}
		}
		for app, count := range appCount {
			if count < 10 {
				// 只统计大于10
				continue
			}
			stats = append(stats, LogcenterErrorStat{
				StatSummary: sp.ErrorType,
				Domain:      app,
				Count:       count,
				ErrorType:   errorType,
			})
		}
		if len(stats) > 0 {
			sort.Sort(stats)
		}
		for addr, count := range addrCount {
			if count < 2 {
				// 只统计大于2
				continue
			}
			fileds := strings.Split(addr, "||")
			if len(fileds) > 1 {
				addrStats = append(addrStats, LogcenterErrorAddrStat{
					StatSummary: sp.ErrorType,
					Addr:        fileds[1],
					Count:       count,
					ErrorType:   errorType,
					Domain:      fileds[0],
				})
			}
		}
		if len(addrStats) > 0 {
			sort.Sort(addrStats)
		}
	}

	return
}
