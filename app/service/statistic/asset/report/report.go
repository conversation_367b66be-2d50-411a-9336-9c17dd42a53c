package report

import (
	"cmdb/app"
	"encoding/json"
	"errors"
	"time"

	"github.com/jinzhu/now"
	"gorm.io/gorm"
)

// 注意：DailyReport、MonthlyReport、ComputerResource、DataResource、OptimizableAsset、
// ComputerResourceMonthly、DataResourceMonthly、OptimizableAssetMonthly 和 DomainBpsMonthly 结构体
// 已在 daily.go 和 month.go 文件中定义

// DomainBps 域名带宽统计
type DomainBps struct {
	Domain       string `json:"domain"`
	InBps        int64  `json:"in_bps"`
	InBpsChange  *int64 `json:"in_bps_change"`
	OutBps       int64  `json:"out_bps"`
	OutBpsChange *int64 `json:"out_bps_change"`
}

// DailyReportResponse 日报响应结构体
type DailyReportResponse struct {
	ID               uint             `json:"id"`
	StatDate         string           `json:"stat_date"`
	ComputerResource ComputerResource `json:"computer_resource"`
	DataResource     DataResource     `json:"data_resource"`
	OptimizableAsset OptimizableAsset `json:"optimizable_asset"`
	DomainBpsStats   []DomainBps      `json:"domain_bps_stats"`
}

// MonthlyReportResponse 月报响应结构体
type MonthlyReportResponse struct {
	ID               uint               `json:"id"`
	Month            string             `json:"month"`
	ComputerResource ComputerResource   `json:"computer_resource"`
	DataResource     DataResource       `json:"data_resource"`
	OptimizableAsset OptimizableAsset   `json:"optimizable_asset"`
	DomainBpsStats   []DomainBpsMonthly `json:"domain_bps_stats"`
}

// GetDailyReport 获取指定日期的日报
// 参数：
// - statDate: 统计日期
// 返回值：
// - *DailyReportResponse: 日报数据
// - error: 错误信息
func GetDailyReport(statDate time.Time) (*DailyReportResponse, error) {
	// 确保是一天的开始时间
	statDate = now.With(statDate).BeginningOfDay()

	// 从数据库中查询日报
	var report DailyReport
	err := app.DB().Where("stat_date = ?", statDate.Format("2006-01-02")).Take(&report).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("未找到指定日期的日报")
		}
		app.Log().Error("获取日报失败", "err", err, "date", statDate.Format("2006-01-02"))
		return nil, err
	}

	// 解析JSON数据
	response := &DailyReportResponse{
		ID:       report.ID,
		StatDate: report.StatDate.Format("2006-01-02"),
	}

	// 解析计算机资源统计数据
	if len(report.ComputerResourceStats) > 0 {
		var computerResource ComputerResource
		err = json.Unmarshal(report.ComputerResourceStats, &computerResource)
		if err != nil {
			app.Log().Error("解析计算机资源统计数据失败", "err", err)
			return nil, err
		}
		response.ComputerResource = computerResource
	}

	// 解析数据资源统计数据
	if len(report.DataResourceStats) > 0 {
		var dataResource DataResource
		err = json.Unmarshal(report.DataResourceStats, &dataResource)
		if err != nil {
			app.Log().Error("解析数据资源统计数据失败", "err", err)
			return nil, err
		}
		response.DataResource = dataResource
	}

	// 解析可优化资源统计数据
	if len(report.OptimizableAssetStats) > 0 {
		var optimizableAsset OptimizableAsset
		err = json.Unmarshal(report.OptimizableAssetStats, &optimizableAsset)
		if err != nil {
			app.Log().Error("解析可优化资源统计数据失败", "err", err)
			return nil, err
		}
		response.OptimizableAsset = optimizableAsset
	}

	// 解析域名带宽统计数据
	if len(report.DomainBpsStats) > 0 {
		var domainBpsStats []DomainBps
		err = json.Unmarshal(report.DomainBpsStats, &domainBpsStats)
		if err != nil {
			app.Log().Error("解析域名带宽统计数据失败", "err", err)
			return nil, err
		}
		response.DomainBpsStats = domainBpsStats
	}

	return response, nil
}

// GetDailyReportByDateStr 根据日期字符串获取日报
// 参数：
// - dateStr: 日期字符串，格式为 "2006-01-02"
// 返回值：
// - *DailyReportResponse: 日报数据
// - error: 错误信息
func GetDailyReportByDateStr(dateStr string) (*DailyReportResponse, error) {
	// 解析日期字符串
	statDate, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		return nil, errors.New("日期格式错误，正确格式为：YYYY-MM-DD")
	}

	return GetDailyReport(statDate)
}

// GetMonthlyReport 获取指定月份的月报
// 参数：
// - reportMonth: 统计月份
// 返回值：
// - *MonthlyReportResponse: 月报数据
// - error: 错误信息
func GetMonthlyReport(reportMonth time.Time) (*MonthlyReportResponse, error) {
	// 确保是月初第一天
	reportMonth = time.Date(reportMonth.Year(), reportMonth.Month(), 1, 0, 0, 0, 0, reportMonth.Location())
	monthStr := reportMonth.Format("2006-01")

	// 从数据库中查询月报
	var report MonthlyReport
	err := app.DB().Where("month = ?", monthStr).Take(&report).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("未找到指定月份的月报")
		}
		app.Log().Error("获取月报失败", "err", err, "month", monthStr)
		return nil, err
	}

	// 解析JSON数据
	response := &MonthlyReportResponse{
		ID:    report.ID,
		Month: report.Month,
	}

	// 解析计算机资源统计数据
	if len(report.ComputerResourceStats) > 0 {
		var computerResource ComputerResource
		err = json.Unmarshal(report.ComputerResourceStats, &computerResource)
		if err != nil {
			app.Log().Error("解析计算机资源统计数据失败", "err", err)
			return nil, err
		}
		response.ComputerResource = computerResource
	}

	// 解析数据资源统计数据
	if len(report.DataResourceStats) > 0 {
		var dataResource DataResource
		err = json.Unmarshal(report.DataResourceStats, &dataResource)
		if err != nil {
			app.Log().Error("解析数据资源统计数据失败", "err", err)
			return nil, err
		}
		response.DataResource = dataResource
	}

	// 解析可优化资源统计数据
	if len(report.OptimizableAssetStats) > 0 {
		var optimizableAsset OptimizableAsset
		err = json.Unmarshal(report.OptimizableAssetStats, &optimizableAsset)
		if err != nil {
			app.Log().Error("解析可优化资源统计数据失败", "err", err)
			return nil, err
		}
		response.OptimizableAsset = optimizableAsset
	}

	// 解析域名带宽统计数据
	if len(report.DomainBpsStats) > 0 {
		var domainBpsStats []DomainBpsMonthly
		err = json.Unmarshal(report.DomainBpsStats, &domainBpsStats)
		if err != nil {
			app.Log().Error("解析域名带宽统计数据失败", "err", err)
			return nil, err
		}
		response.DomainBpsStats = domainBpsStats
	}

	return response, nil
}

// GetMonthlyReportByMonthStr 根据月份字符串获取月报
// 参数：
// - monthStr: 月份字符串，格式为 "2006-01"
// 返回值：
// - *MonthlyReportResponse: 月报数据
// - error: 错误信息
func GetMonthlyReportByMonthStr(monthStr string) (*MonthlyReportResponse, error) {
	// 解析月份字符串
	reportMonth, err := time.Parse("2006-01", monthStr)
	if err != nil {
		return nil, errors.New("月份格式错误，正确格式为：YYYY-MM")
	}

	return GetMonthlyReport(reportMonth)
}

// GetLatestDailyReport 获取最新的日报
// 返回值：
// - *DailyReportResponse: 日报数据
// - error: 错误信息
func GetLatestDailyReport() (*DailyReportResponse, error) {
	// 查询最新的日报
	var report DailyReport
	err := app.DB().Order("stat_date DESC").Take(&report).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("未找到任何日报")
		}
		app.Log().Error("获取最新日报失败", "err", err)
		return nil, err
	}

	return GetDailyReport(report.StatDate)
}

// GetLatestMonthlyReport 获取最新的月报
// 返回值：
// - *MonthlyReportResponse: 月报数据
// - error: 错误信息
func GetLatestMonthlyReport() (*MonthlyReportResponse, error) {
	// 查询最新的月报
	var report MonthlyReport
	err := app.DB().Order("month DESC").Take(&report).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("未找到任何月报")
		}
		app.Log().Error("获取最新月报失败", "err", err)
		return nil, err
	}

	// 解析月份字符串
	reportMonth, err := time.Parse("2006-01", report.Month)
	if err != nil {
		app.Log().Error("解析月份字符串失败", "err", err, "month", report.Month)
		return nil, err
	}

	return GetMonthlyReport(reportMonth)
}
