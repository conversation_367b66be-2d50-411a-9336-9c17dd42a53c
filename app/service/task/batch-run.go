package task

import (
	"cmdb/app"
	"time"
)

type BatchRunForm struct {
	TaskIds []uint `json:"task_ids"`
}

func (form BatchRunForm) BatRun(opUser string) (err error) {
	if len(form.TaskIds) == 0 {
		return
	}
	tasks := []BatchTask{}
	err = app.DB().Where("id IN (?)", form.TaskIds).Find(&tasks).Error
	if err != nil {
		return
	}
	if len(tasks) == 0 {
		return
	}
	for _, task := range tasks {
		err = task.Run(opUser)
		if err != nil {
			return
		}
	}
	return
}

func AsyncExecTask() (err error) {
	readytasks := []BatchTask{}
	err = app.DB().Where("status = ?", StatusTaskReady).Find(&readytasks).Error
	if err != nil {
		return
	}
	for i := range readytasks {
		go func(t BatchTask) {
			result := app.DB().Model(&t).Where("status = ?", StatusTaskReady).Updates(map[string]any{"status": StatusTaskRunning, "run_time": time.Now()})
			if result.Error == nil && result.RowsAffected > 0 {
				output, err := t.run()
				updateMap := map[string]any{
					"result":        output,
					"finished_time": time.Now(),
				}
				if err != nil {
					updateMap["status"] = StatusTaskFailed
					updateMap["result"] = output + "\n" + err.Error()
				} else {
					updateMap["status"] = StatusTaskSuccess
				}
				err = app.DB().Model(&t).Updates(updateMap).Error
				if err != nil {
					app.Log().Error("任务更新失败", "err", err)
				}
			}
		}(readytasks[i])
		time.Sleep(500 * time.Millisecond)
	}
	return
}
