package task

import (
	"cmdb/app"
	"encoding/json"
	"errors"
	"os/exec"
	"strings"
)

func (task BatchTask) run() (output string, err error) {
	switch task.TaskType {
	case TaskTypeAnsible:
		varMap := map[string]interface{}{
			"hosts": task.IP,
		}
		varMapStr, _ := json.Marshal(varMap)
		params := []string{"7200", app.Conf().Ansible.AnsiblePlaybookBinary, "--extra-vars", string(varMapStr), "--inventory", app.Conf().Ansible.AnsibleInventoryFile, "--ssh-common-args='-o StrictHostKeyChecking=no'", task.FilePath}
		cmd := exec.Command("timeout", params...)
		var cmdOutput []byte
		cmdOutput, err = cmd.CombinedOutput()
		output = cmd.String()
		if err != nil {
			output += "error:" + err.Error() + "\n"
		}
		output += "\n" + string(cmdOutput)
		ok := false
		lines := strings.Split(string(cmdOutput), "\n")
		var next bool
		for _, line := range lines {
			if strings.HasPrefix(line, "PLAY RECAP *") {
				next = true
			}
			if next && (strings.Contains(line, "unreachable=0") &&
				strings.Contains(line, "failed=0")) {
				ok = true
				next = false
			}
		}
		if !ok {
			err = errors.New("批量任务结果存在失败")
		}
		return
	case TaskTypeShell:
		params := []string{"7200", app.Conf().Ansible.AnsibleBinary, task.IP, "--inventory", app.Conf().Ansible.AnsibleInventoryFile, "--ssh-common-args='-o StrictHostKeyChecking=no'", "-m", "script", "-a", task.FilePath}
		cmd := exec.Command("timeout", params...)
		var cmdOutput []byte
		cmdOutput, err = cmd.CombinedOutput()
		output = cmd.String() + "\n" + string(cmdOutput)
		lines := strings.Split(string(cmdOutput), "\n")
		ok := true
		for _, line := range lines {
			if strings.Contains(line, "No hosts matched, nothing to do") {
				ok = false
				break
			}
		}
		if !ok {
			err = errors.New("批量任务结果存在失败")
		}
		return
	default:
		return "不支持的任务类型", errors.New("task type not support")
	}
}
