package k8s

import (
	"cmdb/app"
	"errors"
	"time"

	"gorm.io/gorm"
)

type Label struct {
	ID         uint           `gorm:"column:id;primaryKey;comment:主键" json:"id"`
	LabelKey   string         `gorm:"column:label_key;type:varchar(255);index:laebl_index;comment:标签key" json:"label_key"`
	LabelValue string         `gorm:"column:label_value;type:varchar(255);index:laebl_index;comment:标签值" json:"label_value"`
	UpdatedAt  time.Time      `gorm:"column:updated_at;index" json:"updated_at"`
	DeletedAt  gorm.DeletedAt `gorm:"column:deleted_at;index" json:"-"`
}

func (Label) TableName() string {
	return "asset_k8s_labels"
}

func GenLabelKey(labelKey, labelValue string) (label Label, err error) {
	err = app.DB().Where("label_key = ? and label_value = ?", labelKey, labelValue).Take(&label).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = app.DB().Create(&Label{
			LabelKey:   labelKey,
			LabelValue: labelValue,
		}).Error
		if err != nil {
			return
		}
		err = app.DB().Where("label_key = ? and label_value = ?", labelKey, labelValue).Take(&label).Error
	}
	return
}

func GetLabelsByKeyword(keyword string) (labels []Label, err error) {
	err = app.DB().Where("label_key LIKE ? OR label_value LIKE ?", "%"+keyword+"%", "%"+keyword+"%").Find(&labels).Error
	return
}
