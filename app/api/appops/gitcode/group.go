package gitcode

import (
	"cmdb/app"
	"cmdb/app/service/appops/gitcode"
	"strconv"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
)

type GroupRow struct {
	gitcode.Group
	GitlabName string `json:"gitlab_name"`
}

func GetGroups(c *gin.Context) {
	page, _ := strconv.Atoi(c<PERSON>("page", "1"))
	limit, _ := strconv.Atoi(<PERSON><PERSON>("limit", "10"))
	offset := (page - 1) * limit
	var keyword *string
	if val := c.Query("keyword"); val != "" {
		keyword = &val
	}
	var gitlabID *int
	if val, err := strconv.Atoi(c.<PERSON>("gitlab_id")); err == nil {
		gitlabID = &val
	}
	count, data, err := gitcode.GetGroups(offset, limit, keyword, gitlabID)
	if err != nil {
		app.Log().Error("获取组列表失败", "err", err)
		app.FailedResponseMsg(c, "获取组列表失败")
		return
	}
	newData := make([]GroupRow, len(data))
	gitlabNameMap := sync.Map{}
	for i, v := range data {
		newData[i].Group = v
		if val, ok := gitlabNameMap.Load(v.GitlabID); ok {
			newData[i].GitlabName = val.(string)
		} else {
			gitlabName, err := v.GetGitlabName()
			if err == nil {
				gitlabNameMap.Store(v.GitlabID, gitlabName)
				newData[i].GitlabName = gitlabName
			}
		}
	}
	app.SuccessResponseDataCount(c, newData, count)
}

func SyncGroups(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.Log().Error("同步组失败", "err", err)
		app.FailedResponseMsg(c, "同步组失败")
		return
	}
	gitlabObject, err := gitcode.GetGitlabByID(id)
	if err != nil {
		app.Log().Error("同步组失败", "err", err)
		app.FailedResponseMsg(c, "同步组失败")
		return
	}
	// 创建一个done通道
	done := make(chan struct{}, 1)
	// 启动一个goroutine进行同步
	go func() {

		err = gitlabObject.SyncGroups()
		if err != nil {
			// 记录日志
			app.Log().Error("同步GitCode组时，获取组信息失败：", "err", err)
		}
		// 发送完成信号
		done <- struct{}{}
	}()
	ticker := time.NewTicker(500 * time.Millisecond)
	select {
	case <-done:
		if err != nil {
			app.FailedResponseMsg(c, err.Error())
		} else {
			app.SuccessResponseMsg(c, "同步成功")
		}
		break
	case <-ticker.C:
		app.SuccessResponseMsg(c, "任务提交成功,正在同步中，请稍后...")
		break
	}
}
