package auth

import (
	"cmdb/app"
	"cmdb/app/service/audit"
	"cmdb/app/service/auth"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

func GetKeys(c *gin.Context) {
	var keyword *string
	if val := strings.TrimSpace(c.Query("keyword")); val != "" {
		keyword = &val
	}
	page, _ := strconv.Atoi(c.<PERSON>("page", "1"))
	limit, _ := strconv.Atoi(c.<PERSON>("limit", "10"))
	offset := (page - 1) * limit
	count, data, err := auth.GetKeys(offset, limit, keyword)
	if err != nil {
		app.Log().Error("获取key列表失败", "err", err)
		return
	}
	app.SuccessResponseDataCount(c, data, count)
}

func CreateKey(c *gin.Context) {
	var form auth.KeyForm
	err := c.<PERSON>ind<PERSON>(&form)
	if err != nil {
		app.Log().Error("添加key，验证表单失败", "err", err)
		app.FailedResponseMsgData(c, "字段非法", nil)
		return
	}
	err = form.Create()
	if err != nil {
		msg := "添加失败"
		if err == auth.ErrKeyExists {
			msg = err.Error()
		} else {
			app.Log().Error("添加key失败", "err", err)
		}
		app.FailedResponseMsgData(c, msg, nil)
		return
	}
	audit.LogAPIOP(c, "验证模块", "添加key："+form.Name)
	app.SuccessResponseMsgData(c, "添加成功", nil)

}

// GetKeyInfo 获取key信息
func GetKeyInfo(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.FailedResponseMsgData(c, "非法参数", nil)
		return
	}
	key, err := auth.GetKeyByID(id)
	if err != nil {
		app.FailedResponseMsgData(c, "key不存在", nil)
		return
	}
	app.SuccessResponseData(c, key)
}

// UpdateKey 更新key
func UpdateKey(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.FailedResponseMsg(c, "非法参数")
		return
	}
	k, err := auth.GetKeyByID(id)
	if err != nil {
		msg := "获取key记录失败"
		if err != gorm.ErrRecordNotFound {
			msg = "key不存在"
		} else {
			app.Log().Error("获取key记录失败", "err", err)
		}
		app.FailedResponseMsg(c, msg)
		return
	}
	var form auth.KeyForm
	err = c.ShouldBind(&form)
	if err != nil {
		app.Log().Error("更新key，验证表单失败", "err", err)
		app.FailedResponseMsg(c, "字段非法")
		return
	}
	contents := ""
	if k.Name != form.Name {
		contents += " 名称：" + k.Name + "=>" + form.Name + ";"
	}
	if k.Remark != form.Remark {
		contents += " 备注：" + k.Remark + "=>" + form.Remark
	}
	if k.Secret != form.Secret {
		contents += "Secret变动"
	}
	if contents != "" {
		// 根据变更内容判断是否需要更新数据库
		err = k.Update(&form)
		if err != nil {
			msg := "修改失败"
			if err == auth.ErrKeyExists {
				msg = err.Error()
			} else {
				app.Log().Error("更新key失败", "err", err)
			}
			app.FailedResponseMsg(c, msg)
			return
		}
		audit.LogAPIOP(c, "验证模块", "修改「"+k.Name+"」:「 "+contents+"」")
	}
	app.SuccessResponseMsg(c, "修改成功")
}

// DeleteKey 删除key
func DeleteKey(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.FailedResponseMsg(c, "非法参数")
		return
	}
	k, err := auth.GetKeyByID(id)
	if err != nil {
		msg := "获取key记录失败"
		if err != gorm.ErrRecordNotFound {
			msg = "key不存在"
		} else {
			app.Log().Error("获取key记录失败", "err", err)
		}
		app.FailedResponseMsg(c, msg)
		return
	}
	err = k.Delete()
	if err != nil {
		app.Log().Error("删除key失败", "err", err)
		app.FailedResponseMsg(c, "删除失败")
		return
	}
	audit.LogAPIOP(c, "验证模块", "删除key："+k.Name)
	app.SuccessResponseMsg(c, "删除成功")
}

// GetAllAPIs 霍索所有api
func GetAllAPIs(c *gin.Context) {
	apis, err := auth.GetAllAPIs()
	if err != nil {
		app.Log().Error("获取所有api失败", "err", err)
		app.FailedResponseMsg(c, "获取所有api失败")
		return
	}
	app.SuccessResponseData(c, apis)
}

// GrantKeyAPIs 授权API
func GrantKeyAPIs(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.FailedResponseMsg(c, "非法参数")
		return
	}
	var form auth.GrantKeyAPIsForm
	err = c.ShouldBindJSON(&form)
	if err != nil {
		app.FailedResponseMsg(c, "非法字段")
		return
	}
	k, err := auth.GetKeyByID(id)
	if err != nil {
		msg := "获取key失败"
		if err == gorm.ErrRecordNotFound {
			msg = "key不存在"
		} else {
			app.Log().Error("获取key对象失败", "err", err)
		}
		app.FailedResponseMsg(c, msg)
		return
	}
	apis, err := auth.GetAPIsByIDs(form.IDs)
	if err != nil {
		app.Log().Error("获取api失败", "err", err)
		app.FailedResponseMsg(c, "获取api失败")
		return
	}
	err = k.GrantAPIs(&apis)
	if err != nil {
		app.Log().Error("授权失败", "err", err)
		app.FailedResponseMsg(c, "授权失败")
		return
	}
	apisName := []string{}
	for i := range apis {
		apisName = append(apisName, apis[i].Name)
	}
	audit.LogAPIOP(c, "验证模块", "授权key「"+k.Name+"」权限 ：「"+strings.Join(apisName, ",")+"」")
	app.SuccessResponseMsg(c, "授权成功")

}

// key登录
func KeyAuth(c *gin.Context) {
	var keyAuthForm auth.KeyAuthForm
	err := c.ShouldBind(&keyAuthForm)
	if err != nil {
		app.Log().Debug("key登录验证表单失败", "err", err)
		app.V2FailedResponseMsg(c, "非法字段")
		return
	}
	k, err := auth.KeyAuth(keyAuthForm.Key, keyAuthForm.Secret)
	if err != nil {
		app.Log().Error("key验证失败", "err", err)
		app.V2FailedResponseMsg(c, "key验证失败")
		return
	}
	token, err := k.GenerateToken(time.Hour * 7 * 24)
	if err != nil {
		app.Log().Error("生成token失败", "err", err)
		app.V2FailedResponseMsg(c, "生成token失败")
		return
	}
	app.Log().Info(c.ClientIP() + " 使用 " + keyAuthForm.Key + " 进行登录")
	c.JSON(http.StatusOK, gin.H{"token": token, "code": 0, "msg": "登录成功"})
}
