package setting

import (
	"cmdb/app"
	"errors"

	"gorm.io/gorm"
)

var defaultSettings = []SettingSection{
	{
		Name: "smtp", Alias: "SMTP",
		Items: []SettingItem{
			{Section: "smtp", Name: "name", Alias: "邮箱名称", SettingType: NormalSettingType, Content: "<EMAIL>", OrderIndex: 0},
			{Section: "smtp", Name: "host", Alias: "地址", SettingType: NormalSettingType, Content: "xxxxxx", OrderIndex: 1},
			{Section: "smtp", Name: "port", Alias: "端口", SettingType: NormalSettingType, Content: "25", OrderIndex: 2},
			{Section: "smtp", Name: "user", Alias: "用户名", SettingType: NormalSettingType, Content: "<EMAIL>", OrderIndex: 3},
			{Section: "smtp", Name: "password", Alias: "密码", SettingType: PasswordSettingType, Content: "xxxxxx", OrderIndex: 4},
		},
	},
	{
		Name: "goInception", Alias: "SQL审核",
		Items: []SettingItem{
			{Section: "goInception", Name: "host", Alias: "地址", SettingType: NormalSettingType, Content: "127.0.0.1", OrderIndex: 0},
			{Section: "goInception", Name: "port", Alias: "端口", SettingType: NormalSettingType, Content: "4000", OrderIndex: 1},
			{Section: "goInception", Name: "user", Alias: "用户名", SettingType: NormalSettingType, Content: "root", OrderIndex: 2},
		},
	},
	{
		Name: "gitlab", Alias: "gitlab仓库",
		Items: []SettingItem{
			{Section: "gitlab", Name: "url", Alias: "地址", SettingType: NormalSettingType, Content: "https://gitlab-ops.meiyou.com", OrderIndex: 0},
			{Section: "gitlab", Name: "token", Alias: "Token", SettingType: PasswordSettingType, Content: "xxxxxx", OrderIndex: 1},
		},
	},
	{
		Name: "domain_https", Alias: "域名证书",
		Items: []SettingItem{
			{Section: "domain_https", Name: "dingtalk_robot_url", Alias: "钉钉机器人", SettingType: NormalSettingType, Content: "", OrderIndex: 0},
		},
	},
	{
		Name: "duty", Alias: "值班",
		Items: []SettingItem{
			{Section: "duty", Name: "dingtalk_robot_url", Alias: "钉钉机器人", SettingType: NormalSettingType, Content: "", OrderIndex: 0},
		},
	},
	{
		Name: "mysql_backup", Alias: "MySQL备份",
		Items: []SettingItem{
			{Section: "mysql_backup", Name: "dingtalk_robot_url", Alias: "钉钉机器人", SettingType: NormalSettingType, Content: "", OrderIndex: 0},
		},
	},
	{
		Name: "elk", Alias: "ELK",
		Items: []SettingItem{
			{Section: "elk", Name: "es_url", Alias: "ES地址", SettingType: NormalSettingType, Content: "", OrderIndex: 0},
			{Section: "elk", Name: "es_user", Alias: "ES用户名", SettingType: NormalSettingType, Content: "", OrderIndex: 1},
			{Section: "elk", Name: "es_password", Alias: "ES密码", SettingType: PasswordSettingType, Content: "", OrderIndex: 2},
			{Section: "elk", Name: "set_sniff", Alias: "是否启用", SettingType: BoolSettingType, Content: "false", OrderIndex: 3},
		},
	},
	{
		Name: "dingtalk_robot", Alias: "钉钉应用机器人",
		Items: []SettingItem{
			{Section: "dingtalk_robot", Name: "app_secret", Alias: "应用密钥", SettingType: PasswordSettingType, Content: "", OrderIndex: 0},
		},
	},
	{
		Name: "ansible", Alias: "Ansible",
		Items: []SettingItem{
			{Section: "ansible", Name: "inventory", Alias: "资产清单", SettingType: NormalSettingType, Content: "/etc/ansible/hosts", OrderIndex: 0},
			{Section: "ansible", Name: "playbook", Alias: "Playbook路径", SettingType: NormalSettingType, Content: "/usr/local/bin/ansible-playbook", OrderIndex: 1},
			{Section: "ansible", Name: "bin", Alias: "ansible路径", SettingType: NormalSettingType, Content: "/usr/local/bin/ansible", OrderIndex: 2},
		},
	},
}

// 获取默认的设置
func GetDefaultSetting() []SettingSection {
	return defaultSettings
}

func SyncDefaultSettingToDB() (err error) {
	for _, section := range defaultSettings {
		exist := SettingSection{}
		err := app.DB().Model(&SettingSection{}).Where("name = ?", section.Name).Take(&exist).Error
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = app.DB().Create(&section).Error
			if err != nil {
				app.Log().Error(err.Error())
			}
		} else if err != nil {
			app.Log().Error(err.Error())
		} else {
			if exist.Alias != section.Alias {
				err = app.DB().Model(&SettingSection{}).Where("name = ?", section.Name).Update("alias", section.Alias).Error
				if err != nil {
					app.Log().Error(err.Error())
				}
			}
		}
		for _, item := range section.Items {
			exist := SettingItem{}
			err := app.DB().Where("section = ? AND name = ?", item.Section, item.Name).Take(&exist).Error
			if err == nil {
				updateMap := map[string]any{}
				if exist.Alias != item.Alias {
					updateMap["alias"] = item.Alias
				}
				if exist.OrderIndex != item.OrderIndex {
					updateMap["order_index"] = item.OrderIndex
				}
				if len(updateMap) > 0 {
					err = app.DB().Model(&SettingItem{}).Where("section = ? AND name = ?", item.Section, item.Name).Updates(updateMap).Error
					if err != nil {
						app.Log().Error(err.Error())
					}
				}
			} else if errors.Is(err, gorm.ErrRecordNotFound) {
				err = app.DB().Create(&item).Error
				if err != nil {
					app.Log().Error(err.Error())
				}
			} else {
				app.Log().Error(err.Error())
			}
		}
	}
	return
}
