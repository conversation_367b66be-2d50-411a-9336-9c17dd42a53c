package dingtalk

import (
	"bytes"
	"encoding/json"
	"errors"
	"io"
	"net/http"
	"strings"
	"time"
)

// SendMessage 发送钉钉机器人消息
func SendMessage(url, message string, atMobiles ...string) (respContent string, err error) {
	c := &http.Client{
		Timeout: time.Second * 30,
	}
	messages := []rune(message)
	errs := []error{}
	respContents := []string{}
	for len(messages) > 1 {
		text := ""
		if len(messages) > 2000 {
			text = string(messages[:2000]) + "\n下面还有...\n"
			messages = append([]rune("...接上一条\n"), messages[2000:]...)
		} else {
			text = string(messages)
			messages = []rune{}
		}
		text += "\n --from cmdb"
		atMap := map[string]interface{}{"isAtAll": false}
		if strings.Join(atMobiles, "") == "all" {
			atMap["isAtAll"] = true
			atMap["atMobiles"] = []string{}
		} else {
			atMap["atMobiles"] = atMobiles
		}
		data := map[string]interface{}{
			"msgtype": "text",
			"text":    map[string]string{"content": text},
			"at":      atMap,
		}
		b, _ := json.Marshal(data)
		resp, err1 := c.Post(url, "application/json", bytes.NewReader(b))
		if err1 != nil {
			respContents = append(respContents, "post请求失败")
			errs = append(errs, err1)
			continue
		}
		defer resp.Body.Close()
		body, _ := io.ReadAll(resp.Body)
		respContents = append(respContents, string(body))
		time.Sleep(3 * time.Second)
	}
	if len(errs) != 0 {
		var errStrings bytes.Buffer
		for i := range errs {
			errStrings.WriteString(errs[i].Error() + " ; ")
		}
		err = errors.New(errStrings.String())
	}
	respContent = strings.Join(respContents, " ; ")
	return
}

// SendMessage 发送钉钉机器人markdown消息
func SendMessageWithMarkdown(url, title, message string, atMobiles ...string) (respContent string, err error) {
	c := &http.Client{
		Timeout: time.Second * 30,
	}
	messages := []rune(message)
	errs := []error{}
	respContents := []string{}
	for len(messages) > 1 {
		text := ""
		if len(messages) > 2000 {
			text = string(messages[:2000]) + "\n下面还有...\n"
			messages = append([]rune("...接上一条\n"), messages[2000:]...)
		} else {
			text = string(messages)
			messages = []rune{}
		}
		text += "\n --from cmdb"
		atMap := map[string]interface{}{"isAtAll": false}
		if strings.Join(atMobiles, "") == "all" {
			atMap["isAtAll"] = true
			atMap["atMobiles"] = []string{}
		} else {
			atMap["atMobiles"] = atMobiles
		}
		data := map[string]interface{}{
			"msgtype": "markdown",
			"markdown": map[string]interface{}{
				"title": title,
				"text":  text,
			},
			"at": atMap,
		}
		b, _ := json.Marshal(data)
		resp, err1 := c.Post(url, "application/json", bytes.NewReader(b))
		if err1 != nil {
			respContents = append(respContents, "post请求失败")
			errs = append(errs, err1)
			continue
		}
		defer resp.Body.Close()
		body, _ := io.ReadAll(resp.Body)
		respContents = append(respContents, string(body))
		time.Sleep(3 * time.Second)
	}
	if len(errs) != 0 {
		var errStrings bytes.Buffer
		for i := range errs {
			errStrings.WriteString(errs[i].Error() + " ; ")
		}
		err = errors.New(errStrings.String())
	}
	respContent = strings.Join(respContents, " ; ")
	return
}
