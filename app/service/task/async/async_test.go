package async

import (
	"cmdb/app"
	"os"
	"os/signal"
	"testing"
	"time"
)

func TestTask(t *testing.T) {
	err := app.NewApp("../../../../app.ini")
	if err != nil {
		t.Fatal(err)
	}
	intervalTasks := IntervalTasks{
		{
			Interval: 1 * time.Second,
			Func: func() (err error) {
				// do something
				app.Log().Info("定时任务测试1")
				return
			},
			Name: "测试任务标题1",
		}, {
			Interval: 2 * time.Second,
			Func: func() (err error) {
				// do something
				app.Log().Info("定时任务测试2")
				return
			},
			Name: "测试任务标题2",
		},
	}
	intervalTasks.Run()
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, os.Interrupt)
	<-quit
	intervalTasks.Stop()
	// time.Sleep(10 * time.Second)
}
