package bill

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"errors"
	"time"
)

// AccountBill 表示账号级别的账单统计
type AccountBill struct {
	Month       string  `json:"month"`        // 账单周期，格式为 2021-01
	AccountID   uint    `json:"account_id"`   // 账号ID
	AccountName string  `json:"account_name"` // 账号名称
	CloudType   string  `json:"cloud_type"`   // 云服务商类型
	Amount      float64 `json:"amount"`       // 账单金额
}

// GetAccountBills 获取指定账单周期内所有账号的账单统计
func GetAccountBills(billCycle string) (abs []AccountBill, err error) {
	abs = make([]AccountBill, 0)

	// 获取所有云账号
	accounts, err := asset.GetAllAccounts()
	if err != nil {
		app.Log().Error("获取所有云账号失败", "err", err)
		return
	}

	// 遍历所有账号，获取每个账号的账单金额
	for _, account := range accounts {
		var amount *float64
		amount, err = account.GetMonthlyBillAmount(billCycle)
		if err != nil {
			app.Log().Error("获取账号账单金额失败", "account", account.Name, "err", err)
			continue // 如果获取某个账号的账单失败，继续处理下一个账号
		}

		// 如果金额为空或为0，跳过
		if amount == nil || *amount == 0 {
			continue
		}

		// 获取云服务商类型名称
		cloudTypeName := account.CloudType.String()

		// 添加到结果集
		abs = append(abs, AccountBill{
			Month:       billCycle,
			AccountID:   account.ID,
			AccountName: account.Name,
			CloudType:   cloudTypeName,
			Amount:      *amount,
		})
	}

	return
}

// GetAccountBillsByDateRange 获取指定时间范围内所有账号的账单统计
func GetAccountBillsByDateRange(startTime, endTime string) (abs []AccountBill, err error) {
	abs = make([]AccountBill, 0)

	// 获取所有云账号
	accounts, err := asset.GetAllAccounts()
	if err != nil {
		app.Log().Error("获取所有云账号失败", "err", err)
		return
	}

	// 计算时间范围内的所有月份
	billCycles := []string{}
	if startTime == endTime {
		billCycles = append(billCycles, startTime)
	} else {
		// 解析开始和结束时间
		start, err1 := time.Parse("2006-01", startTime)
		end, err2 := time.Parse("2006-01", endTime)
		if err1 != nil || err2 != nil {
			app.Log().Error("解析时间失败", "startTime", startTime, "endTime", endTime)
			return abs, errors.New("解析时间失败")
		}

		// 确保开始时间不晚于结束时间
		if start.After(end) {
			start, end = end, start
		}

		// 生成时间范围内的所有月份
		for d := start; !d.After(end); d = d.AddDate(0, 1, 0) {
			billCycles = append(billCycles, d.Format("2006-01"))
		}
	}

	// 遍历所有账号和月份，获取账单金额
	for _, account := range accounts {
		for _, billCycle := range billCycles {
			var amount *float64
			amount, err = account.GetMonthlyBillAmount(billCycle)
			if err != nil {
				app.Log().Error("获取账号账单金额失败", "account", account.Name, "billCycle", billCycle, "err", err)
				continue // 如果获取某个账号的账单失败，继续处理下一个账号
			}

			// 如果金额为空或为0，跳过
			if amount == nil || *amount == 0 {
				continue
			}

			// 获取云服务商类型名称
			cloudTypeName := account.CloudType.String()

			// 添加到结果集
			abs = append(abs, AccountBill{
				Month:       billCycle,
				AccountID:   account.ID,
				AccountName: account.Name,
				CloudType:   cloudTypeName,
				Amount:      *amount,
			})
		}
	}

	return
}

// AccountBillByCloudType 按云服务商类型分组的账号账单统计
type AccountBillByCloudType struct {
	Month     string  `json:"month"`      // 账单周期，格式为 2021-01
	CloudType string  `json:"cloud_type"` // 云服务商类型
	Count     int     `json:"count"`      // 账号数量
	Amount    float64 `json:"amount"`     // 账单总金额
}

// GetAccountBillsByCloudType 获取指定账单周期内按云服务商类型分组的账号账单统计
func GetAccountBillsByCloudType(billCycle string) (abcts []AccountBillByCloudType, err error) {
	// 先获取所有账号的账单
	accountBills, err := GetAccountBills(billCycle)
	if err != nil {
		return
	}

	// 按云服务商类型分组统计
	cloudTypeMap := make(map[string]*AccountBillByCloudType)
	for _, ab := range accountBills {
		if _, ok := cloudTypeMap[ab.CloudType]; !ok {
			cloudTypeMap[ab.CloudType] = &AccountBillByCloudType{
				Month:     billCycle,
				CloudType: ab.CloudType,
				Count:     0,
				Amount:    0,
			}
		}
		cloudTypeMap[ab.CloudType].Count++
		cloudTypeMap[ab.CloudType].Amount += ab.Amount
	}

	// 转换为切片
	for _, abct := range cloudTypeMap {
		abcts = append(abcts, *abct)
	}

	return
}
