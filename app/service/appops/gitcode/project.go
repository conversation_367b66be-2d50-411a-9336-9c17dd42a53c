package gitcode

import (
	"cmdb/app"
	"cmdb/pkg/db"
	"errors"
	"time"

	goGitlab "github.com/xanzy/go-gitlab"
	"gorm.io/gorm"
)

type Project struct {
	ID          uint           `gorm:"column:id;primaryKey;comment:ID" json:"id"`
	GitlabID    uint           `gorm:"type:varchar(255);column:gitlab_id;index;comment:GitLab ID" json:"gitlab_id"`
	ProjectID   int            `gorm:"type:varchar(255);column:project_id;index;comment:项目 ID" json:"project_id"`
	Name        string         `gorm:"type:varchar(255);column:name;index;comment:名称" json:"name"`
	Description string         `gorm:"type:varchar(255);column:description;index;comment:描述" json:"description"`
	HttpUrl     string         `gorm:"type:varchar(255);column:http_url;index;comment:HTTP URL" json:"http_url"`
	SshUrl      string         `gorm:"type:varchar(255);column:ssh_url;index;comment:SSH URL" json:"ssh_url"`
	CreatedAt   time.Time      `gorm:"column:created_at;index;comment:创建时间" json:"created_at"`
	DeletedAt   gorm.DeletedAt `gorm:"column:deleted_at;index;comment:删除时间" json:"deleted_at"`
	SyncTime    time.Time      `gorm:"column:sync_time;index;comment:同步时间" json:"sync_time"`
}

func (Project) TableName() string {
	return "asset_gitcode_projects"
}

func (gitlab Gitlab) SyncProjects() (err error) {
	syncTime := time.Now()
	// 初始化 GitLab 客户端
	client, err := gitlab.GetClient()
	if err != nil {
		return
	}

	// 获取所有项目
	opt := &goGitlab.ListProjectsOptions{
		ListOptions: goGitlab.ListOptions{
			PerPage: 100,
			Page:    1,
		},
	}

	for {
		projects, resp, err := client.Projects.ListProjects(opt)
		if err != nil {
			return err
		}

		// 遍历项目并同步到数据库
		for _, p := range projects {
			exist := Project{}
			// 检查数据库中是否已存在该项目
			err = app.DB().Where("gitlab_id = ? AND project_id = ?", gitlab.ID, p.ID).Take(&exist).Error
			if errors.Is(err, gorm.ErrRecordNotFound) {
				// 如果不存在，则创建新的项目记录
				err = app.DB().Create(&Project{
					GitlabID:    gitlab.ID,
					ProjectID:   p.ID,
					Name:        p.Name,
					Description: p.Description,
					HttpUrl:     p.WebURL,
					SshUrl:      p.SSHURLToRepo,
					SyncTime:    syncTime,
				}).Error
			} else if err == nil {
				updateMap := map[string]any{
					"sync_time": syncTime,
				}
				if exist.Name != p.Name {
					updateMap["name"] = p.Name
				}
				if exist.Description != p.Description {
					updateMap["description"] = p.Description
				}
				if exist.HttpUrl != p.WebURL {
					updateMap["http_url"] = p.WebURL
				}
				if exist.SshUrl != p.SSHURLToRepo {
					updateMap["ssh_url"] = p.SSHURLToRepo
				}
				err = app.DB().Model(&exist).Updates(updateMap).Error
			}
			if err != nil {
				return err
			}
		}

		// 检查是否还有下一页
		if resp.CurrentPage >= resp.TotalPages {
			break
		}
		opt.Page = resp.NextPage
	}

	// 删除已删除的项目，通过sync_time来判断
	err = app.DB().Where("gitlab_id = ? AND sync_time < ?", gitlab.ID, syncTime.Add(-1*time.Hour)).
		Delete(&Project{}).Error
	return
}

func GetProjects(offset, limit int, keyword *string, groupID *int, gitlabID *int) (count int64, projects []Project, err error) {
	dbop := app.DB()
	if keyword != nil {
		dbop = db.MLike(dbop, *keyword, "name", "http_url", "ssh_url", "description")
	}
	if groupID != nil {
		dbop = dbop.Where("group_id = ?", *groupID)
	}
	if gitlabID != nil {
		dbop = dbop.Where("gitlab_id = ?", *gitlabID)
	}
	err = dbop.Model(&Project{}).Count(&count).Order("created_at DESC , name").Offset(offset).Limit(limit).Find(&projects).Error
	return
}

func (git Gitlab) GetProjects() (projects []Project, err error) {
	err = app.DB().Where("gitlab_id = ?", git.ID).Order("name").Find(&projects).Error
	return
}

func (p Project) GetGitlabName() (name string, err error) {
	err = app.DB().Model(&Gitlab{}).Where("id = ?", p.GitlabID).Select("name").Scan(&name).Error
	return
}
func GetAllProjects() (projects []Project, err error) {
	err = app.DB().Order("name").Find(&projects).Error
	return
}
