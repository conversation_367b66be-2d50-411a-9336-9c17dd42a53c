package tencentcloud

import (
	"bytes"
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/asset/cloud"
	"cmdb/pkg/utils"
	"errors"
	"strconv"
	"strings"
	"time"

	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	cvm "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/cvm/v20170312"
	"gorm.io/gorm"
)

func (a Account) expiredHosts(expiredTime time.Time, dc asset.Datacenter) (err error) {
	hosts := []asset.Host{}
	err = app.DB().Where("account_id = ? and sync_time < ? and datacenter_id = ?", a.ID, expiredTime, dc.ID).Find(&hosts).Error
	if err != nil {
		return
	}
	for i := range hosts {
		err = app.DB().Model(&hosts[i]).Updates(map[string]any{
			"status":     asset.RecycleHostStatus,
			"deleted_at": time.Now(),
		}).Error
		if err != nil {
			return
		}
		hosts[i].LogChanges("同步更新", "回收主机")
	}
	return
}
func (a Account) syncECSs() (result cloud.SyncResults, err error) {
	dcs, err := asset.GetDatacentersByCloudType(asset.AliyunCloudType)
	if err != nil {
		return
	}
	for _, dc := range dcs {
		r, err := a.syncRegionECSs(dc, 1)
		sr := cloud.SyncResult{
			Key: dc.Name,
		}
		if err == nil {
			sr.Result = "同步成功"
			// 回收过期主机
			err = a.expiredHosts(time.Now().Add(-10*time.Minute), dc)
			if err != nil {
				sr.Result = "同步成功，但是回收主机失败" + err.Error()
			}
		} else {
			sr.Result = r + " " + err.Error()
		}
		result = append(result, sr)
	}
	return
}

func (a Account) syncRegionECSs(dc asset.Datacenter, offset int64) (result string, err error) {
	accessSecret, _ := utils.Base64Decode(a.AccessSecret)
	credential := common.NewCredential(
		a.AccessKey,
		accessSecret,
	)
	// 实例化一个client选项，可选的，没有特殊需求可以跳过
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = "cvm.tencentcloudapi.com"
	// 实例化要请求产品的client对象,clientProfile是可选的
	client, _ := cvm.NewClient(credential, "", cpf)

	// 实例化一个请求对象,每个接口都会对应一个request对象
	request := cvm.NewDescribeInstancesRequest()

	request.Offset = common.Int64Ptr(offset)
	request.Limit = common.Int64Ptr(100)

	response, err := client.DescribeInstances(request)
	if err != nil {
		result = "获取主机列表失败"
		return
	}
	total := *response.Response.TotalCount
	err = a.saveECS(dc, response.Response.InstanceSet)
	if err != nil {
		result = "保存同步记录失败"
		return
	}
	if offset+100 < total {
		// 下一页
		result, err = a.syncRegionECSs(dc, offset+100)
	}
	return
}

func (a Account) saveECS(dc asset.Datacenter, instances []*cvm.Instance) (err error) {
	for _, instance := range instances {
		now := time.Now()
		createTime, _ := time.ParseInLocation("2006-01-02T15:04Z", *instance.CreatedTime, time.Local)
		ip := ""
		if len(instance.PrivateIpAddresses) > 0 {
			ip = *instance.PrivateIpAddresses[0]
		}
		publicIP := ""
		if len(instance.PublicIpAddresses) > 0 {
			publicIP = *instance.PublicIpAddresses[0]
		}
		var status asset.HostStatus
		if instance.InstanceState != nil {
			switch *instance.InstanceState {
			case "PENDING":
				status = asset.CreatingHostStatus
			case "SHUTDOWN":
				status = asset.StoppedHostStatus
			case "REBOOTING":
				status = asset.StoppedHostStatus
			case "RUNNING":
				status = asset.RunningHostStatus
			case "STARTING":
				status = asset.RunningHostStatus
			case "STOPPED":
				status = asset.StoppedHostStatus
			case "TERMINATING":
				status = asset.DeletingHostStatus
			default:
				status = asset.StoppedHostStatus
			}
		}
		var expiredTime *time.Time
		if instance.ExpiredTime != nil {
			expiredTimet, errTime := time.ParseInLocation("2006-01-02T15:04Z", *instance.ExpiredTime, time.Local)
			if errTime == nil {
				expiredTime = &expiredTimet
			}
		}
		var isMonitor bool = true
		chargeType := asset.PrepaidChargeType
		if instance.InstanceChargeType != nil && *instance.InstanceChargeType != "PREPAID" {
			chargeType = asset.PostpaidChargeType
		}
		if chargeType == asset.PostpaidChargeType {
			isMonitor = false
		}
		var remotePort uint = 22
		if instance.OsName != nil && strings.Contains(strings.ToLower(*instance.OsName), "windows") {
			remotePort = 3389
		}
		osType := "linux"
		if instance.OsName != nil && strings.Contains(strings.ToLower(*instance.OsName), "windows") {
			osType = "windows"
		}

		exist := asset.Host{}
		err = app.DB().Where("sn = ? AND host_type = ?", instance.InstanceId, asset.AliyunHostType).Take(&exist).Error
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 新增
			err = app.DB().Create(&asset.Host{
				IP:           ip,
				PublicIP:     publicIP,
				OS:           *instance.OsName,
				SN:           *instance.InstanceId,
				Name:         *instance.InstanceName,
				Position:     *instance.VirtualPrivateCloud.SubnetId,
				HostType:     asset.AliyunHostType,
				ChargeType:   chargeType,
				DatacenterID: dc.ID,
				AccountID:    a.ID,
				Cabinet:      *instance.Placement.Zone,
				CreatedAt:    createTime,
				Memory:       uint(*instance.Memory),
				CPUThread:    uint(*instance.CPU),
				OSType:       osType,
				Status:       status,
				SyncTime:     &now,
				PingMonitor:  isMonitor,
				ExpiredTime:  expiredTime,
				RemotePort:   remotePort,
				Model:        *instance.InstanceType,
				GPUAmount:    uint(*instance.GPUInfo.GPUCount),
			}).Error
		} else if err == nil {
			updateMap := map[string]interface{}{"sync_time": time.Now()}
			var changeLog bytes.Buffer
			if exist.IP != ip {
				changeLog.WriteString("IP：" + exist.IP + " => " + ip + "\n")
				updateMap["ip"] = ip
			}
			if exist.PublicIP != publicIP {
				changeLog.WriteString("公网IP：" + exist.PublicIP + " => " + publicIP + "\n")
				updateMap["public_ip"] = publicIP
			}
			if exist.Name != *instance.InstanceName {
				changeLog.WriteString("名称：" + exist.Name + " => " + *instance.InstanceName + "\n")
				updateMap["name"] = instance.InstanceName
			}
			if exist.GPUAmount != uint(*instance.GPUInfo.GPUCount) {
				changeLog.WriteString("GPU数量：" + strconv.Itoa(int(exist.GPUAmount)) + " => " + strconv.Itoa(int(*instance.GPUInfo.GPUCount)) + "\n")
				updateMap["gpu_amount"] = instance.GPUInfo.GPUCount
			}
			if exist.Status != status {
				changeLog.WriteString("主机状态：" + exist.Status.String() +
					" => " + status.String() + "\n")
				updateMap["status"] = status
			}
			if exist.Cabinet != *instance.Placement.Zone {
				changeLog.WriteString("机柜变更：" + exist.Cabinet + " => " + *instance.Placement.Zone + "\n")
				updateMap["cabinet"] = *instance.Placement.Zone
			}
			if exist.ExpiredTime != nil && !exist.ExpiredTime.Equal(*expiredTime) {
				changeLog.WriteString("过期时间：" + exist.ExpiredTime.Format("2006-01-02 15:04:05") + " => " + expiredTime.Format("2006-01-02 15:04:05") + "\n")
				updateMap["expired_time"] = expiredTime
			} else if exist.ExpiredTime == nil {
				changeLog.WriteString("过期时间：" + expiredTime.Format("2006-01-02 15:04:05") + "\n")
				updateMap["expired_time"] = &expiredTime
			}
			if exist.OSType != osType {
				changeLog.WriteString("系统类型：" + exist.OSType + " => " + osType + "\n")
				updateMap["os_type"] = osType
			}
			if *instance.VirtualPrivateCloud.SubnetId != exist.Position {
				changeLog.WriteString("机柜柜位/vpc：" + exist.Position + " => " + *instance.VirtualPrivateCloud.SubnetId + "\n")
				updateMap["position"] = *instance.VirtualPrivateCloud.SubnetId
			}
			if exist.Model != *instance.InstanceType {
				changeLog.WriteString("型号：" + exist.Model + " => " + *instance.InstanceType + "\n")
				updateMap["model"] = instance.InstanceType
			}
			if exist.ChargeType != chargeType {
				updateMap["charge_type"] = chargeType
				changeLog.WriteString("付费类型：" + exist.ChargeType.String() + " => " + chargeType.String() + "\n")
			}
			if exist.AccountID != a.ID {
				updateMap["account_id"] = a.ID
			}
			err = app.DB().Model(&exist).Updates(updateMap).Error
			if err == nil {
				if len(changeLog.String()) > 0 {
					exist.LogChanges("同步更新", changeLog.String())
				}
			}
		}
		if err != nil {
			return
		}
	}
	return
}
