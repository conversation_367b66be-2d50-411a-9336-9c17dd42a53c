package asset

import (
	"cmdb/app"
	"cmdb/pkg/db"
	"time"

	"gorm.io/gorm"
)

type SubNet struct {
	ID              uint           `gorm:"column:id;primaryKey;comment:主键" json:"id"`
	Name            string         `gorm:"column:name;type:varchar(255);index;comment:名称" json:"name"`
	AccountID       uint           `gorm:"column:account_id;index;comment:关联云账户id" json:"account_id"`
	ResourceGroupID string         `gorm:"column:resource_group_id;type:varchar(255);index;comment:资源组ID" json:"resource_group_id"`
	DatacenterID    uint           `gorm:"column:datacenter_id;index;comment:关联dc id" json:"datacenter_id"`
	ZoneID          string         `gorm:"column:zone_id;type:varchar(255);index;comment:区域ID" json:"zone_id"`
	IPv4CIDR        string         `gorm:"column:ipv4_cidr;type:varchar(255);index;comment:网段" json:"ipv4_cidr"`
	IPv6CIDR        string         `gorm:"column:ipv6_cidr;type:varchar(255);index;comment:网段" json:"ipv6_cidr"`
	VSwitchID       string         `gorm:"column:v_switch_id;type:varchar(255);index;comment:VPC ID" json:"v_switch_id"`
	VPCID           string         `gorm:"column:vpc_id;type:varchar(255);index;comment:VPC ID" json:"vpc_id"`
	Description     string         `gorm:"column:description;type:varchar(255);comment:描述" json:"description"`
	CreatedAt       time.Time      `gorm:"column:created_at;comment:创建时间" json:"created_at"`
	SyncTime        *time.Time     `gorm:"column:sync_time;index;comment:同步时间" json:"sync_time"`
	DeletedAt       gorm.DeletedAt `gorm:"column:deleted_at;comment:删除时间" json:"-"`
}

func (SubNet) TableName() string {
	return "asset_subnets"
}

func (s SubNet) Delete() error {
	return app.DB().Delete(&s).Error
}

func GetSubNets(offset, limit int, accountID, dataCenterID *int, ip, cidr, keyword *string) (count int64, subnets []SubNet, err error) {
	dbop := app.DB()
	if accountID != nil && *accountID > 0 {
		dbop = dbop.Where("account_id = ?", *accountID)
	}
	if dataCenterID != nil && *dataCenterID > 0 {
		dbop = dbop.Where("datacenter_id = ?", *dataCenterID)
	}
	if keyword != nil && *keyword != "" {
		dbop = db.MLike(dbop, *keyword, "name", "description", "zone_id", "vpc_id", "v_switch_id", "ipv4_cidr", "ipv6_cidr", "resource_group_id")
	}
	if ip != nil && *ip != "" {
		dbop = dbop.Where(`
			INET_ATON(?) BETWEEN 
				(INET_ATON(SUBSTRING_INDEX(ipv4_cidr, '/', 1)) & (0xFFFFFFFF << (32 - SUBSTRING_INDEX(ipv4_cidr, '/', -1)))) 
				AND 
				(INET_ATON(SUBSTRING_INDEX(ipv4_cidr, '/', 1)) | (0xFFFFFFFF >> SUBSTRING_INDEX(ipv4_cidr, '/', -1))) `, *ip)
	}
	if cidr != nil && *cidr != "" {
		dbop = dbop.Where("ipv4_cidr = ? OR ipv6_cidr = ?", *cidr, *cidr)
	}
	err = dbop.Model(&SubNet{}).Count(&count).Order("created_at desc").Offset(offset).Limit(limit).Find(&subnets).Error
	return
}
