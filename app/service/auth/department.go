package auth

import (
	"cmdb/app"
	"cmdb/pkg/db"

	"gorm.io/gorm"
)

// Department 部门
type Department struct {
	ID        uint           `gorm:"column:id;primaryKey;comment:主键id" json:"id"`
	Name      string         `gorm:"column:name;type:varchar(255);index;comment:部门名称" json:"name"`
	Family    string         `gorm:"column:family;type:varchar(255);index;comment:组织类型" json:"family"`
	Remark    string         `gorm:"type:varchar(255);column:remark;comment:备注" json:"remark"`
	Users     []User         `gorm:"many2many:auth_user_departments" json:"users,omitempty"`
	DeletedAt gorm.DeletedAt `grom:"column:deleted_at" json:"-"`
}

// TableName 设置表名
func (Department) TableName() string {
	return "auth_departments"
}

// GetDepartments 分页获取部门列表
func GetDepartments(offset, limit int, family, keyword *string) (count int64, deparments []Department, err error) {
	dbop := app.DB()
	if family != nil {
		dbop = dbop.Where("family = ?", *family)
	}
	if keyword != nil {
		dbop = db.M<PERSON>ike(dbop, *keyword, "name", "remark")
	}
	err = dbop.Model(&Department{}).Order("name DESC").Count(&count).
		Offset(offset).Limit(limit).Find(&deparments).Error
	return
}

// Gen 生成部门记录
func (d *Department) Gen() (err error) {
	exist := Department{}
	err = app.DB().Where("name = ?", d.Name).Take(&exist).Error
	if err == nil {
		d.ID = exist.ID
		updateMap := map[string]any{}
		if d.Remark != exist.Remark {
			updateMap["remark"] = d.Remark
		}
		if d.Family != exist.Family {
			updateMap["family"] = d.Family
		}
		if len(updateMap) > 0 {
			err = app.DB().Model(&exist).Updates(updateMap).Error
		}
	}
	if err == gorm.ErrRecordNotFound {
		err = app.DB().Create(d).Error
		if err == nil {
			err = app.DB().Where("name = ?", d.Name).Take(d).Error
		}
	}
	return
}
