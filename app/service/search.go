package service

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/asset/k8s"
	"cmdb/app/service/assets"
	"encoding/json"
	"strings"

	"gorm.io/gorm/clause"
)

type AssetInfo struct {
	IP            string           `json:"ip"`
	AccountName   string           `json:"account_name"`
	ExtInfo       string           `json:"ext_info"`
	AssetCloudURL string           `json:"asset_cloud_url"`
	Name          string           `json:"name"`
	AssetType     assets.AssetType `json:"asset_type"`
	AssetTypeName string           `json:"asset_type_name"`
}

func Search(ip string, assetTypes ...string) (data []AssetInfo, err error) {
	var hostFlag, loadbalancerFlag, publicIPFlag, privateDomainFlag, publicDomainFlag, podFlag, ddosFlag, svcFlag bool
	for _, assetType := range assetTypes {
		switch assetType {
		case "host":
			hostFlag = true
		case "loadbalancer":
			loadbalancerFlag = true
		case "eip":
			publicIPFlag = true
		case "private_domain":
			privateDomainFlag = true
		case "public_domain":
			publicDomainFlag = true
		case "pod":
			podFlag = true
		case "ddos":
			ddosFlag = true
		case "svc":
			svcFlag = true
		}
	}
	if !hostFlag && !loadbalancerFlag && !publicIPFlag &&
		!privateDomainFlag && !publicDomainFlag && !podFlag && !ddosFlag && !svcFlag {
		hostFlag, loadbalancerFlag, publicIPFlag, privateDomainFlag, publicDomainFlag, podFlag, ddosFlag, svcFlag = true, true, true, true, true, true, true, true
	}
	if hostFlag {
		// 查询主机
		var hosts []asset.Host
		err = app.DB().Where("ip =? OR public_ip = ?", ip, ip).Find(&hosts).Error
		if err == nil {
			for i := range hosts {
				accountName, _ := asset.GetAccountNameByID(int(hosts[i].AccountID))
				data = append(data, AssetInfo{
					IP:            hosts[i].IP,
					ExtInfo:       "公网IP:" + hosts[i].PublicIP,
					AccountName:   accountName,
					Name:          hosts[i].Name,
					AssetType:     assets.HostAssetType,
					AssetTypeName: assets.HostAssetType.String(),
					AssetCloudURL: hosts[i].GetCloudURL(),
				})
			}
		}
	}
	if loadbalancerFlag {
		// 查询负载均衡
		var slb []asset.Loadbalancer
		err = app.DB().Where("host = ?", ip).Find(&slb).Error
		if err == nil {
			for i := range slb {
				accountName, _ := asset.GetAccountNameByID(int(slb[i].AccountID))
				data = append(data, AssetInfo{
					IP:            slb[i].Host,
					ExtInfo:       "负载均衡ID:" + slb[i].LoadbalancerID,
					Name:          slb[i].Name,
					AccountName:   accountName,
					AssetType:     assets.LoadbalancerAssetType,
					AssetTypeName: assets.LoadbalancerAssetType.String(),
					AssetCloudURL: slb[i].GetCloudURL(),
				})
			}
		}
	}
	if publicIPFlag {
		// 查询公网IP
		var publicIPs []asset.PublicIP
		err = app.DB().Where("ip = ? OR private_ip = ?", ip, ip).Find(&publicIPs).Error
		if err == nil {
			for i := range publicIPs {
				accountName, _ := asset.GetAccountNameByID(int(publicIPs[i].AccountID))
				data = append(data, AssetInfo{
					IP:            publicIPs[i].IP,
					ExtInfo:       "内网IP:" + publicIPs[i].PrivateIP,
					Name:          publicIPs[i].Name,
					AccountName:   accountName,
					AssetType:     assets.PublicIPAssetType,
					AssetTypeName: assets.PublicIPAssetType.String(),
					AssetCloudURL: publicIPs[i].GetCloudURL(),
				})
			}
		}
	}
	if privateDomainFlag {
		// 查询内网域名
		domains, _ := asset.GetPrivateDomainRecordsByValue(ip)
		if len(domains) > 0 {
			for _, domain := range domains {
				data = append(data, AssetInfo{
					IP:            ip,
					ExtInfo:       "域名:" + domain.Rr,
					Name:          domain.Domain,
					AccountName:   domain.AccountName,
					AssetType:     assets.PrivateDomainAssetType,
					AssetTypeName: assets.PrivateDomainAssetType.String(),
				})
			}
		}
	}
	if publicDomainFlag {
		// 查询公网域名
		publicDomains, _ := asset.GetPublicDomainRecordsByValue(ip)
		if len(publicDomains) > 0 {
			for _, domain := range publicDomains {
				data = append(data, AssetInfo{
					IP:            ip,
					ExtInfo:       "域名:" + domain.Rr,
					Name:          domain.Domain,
					AccountName:   domain.AccountName,
					AssetType:     assets.PublicDomainAssetType,
					AssetTypeName: assets.PublicDomainAssetType.String(),
				})
			}
		}
	}
	if podFlag {
		// 查询k8s pod
		var pods []k8s.Pod
		err = app.DB().Where("pod_ip =?", ip).Find(&pods).Error
		if err == nil {
			for i := range pods {
				clusterName, _ := k8s.GetClusterNamebyID(int(pods[i].ClusterID))
				data = append(data, AssetInfo{
					IP:            pods[i].PodIP,
					ExtInfo:       "节点IP:" + pods[i].HostIP,
					Name:          pods[i].Name,
					AccountName:   clusterName,
					AssetType:     assets.K8SPodAssetType,
					AssetTypeName: assets.K8SPodAssetType.String(),
				})
			}
		}
	}
	if ddosFlag {
		// 查询ddos
		var ddos []asset.CloudDDosProtection
		err = app.DB().Where("ip = ? ", ip).Find(&ddos).Error
		if err == nil {
			for i := range ddos {
				accountName, _ := asset.GetAccountNameByID(int(ddos[i].AccountID))
				data = append(data, AssetInfo{
					IP:            ddos[i].IP,
					ExtInfo:       "高防实例:" + ddos[i].InstanceID,
					Name:          ddos[i].Remark,
					AccountName:   accountName,
					AssetType:     assets.CloudDDosProtectionAssetType,
					AssetTypeName: assets.CloudDDosProtectionAssetType.String(),
				})
			}
		}
		var ddosDomains []asset.CloudDDosDomain
		err = app.DB().Model(&asset.CloudDDosDomain{}).Clauses(clause.Expr{
			SQL:  "JSON_CONTAINS(real_servers,JSON_ARRAY(?))",
			Vars: []interface{}{ip},
		}).Find(&ddosDomains).Error
		if err == nil {
			for i := range ddosDomains {
				ips := []string{}
				json.Unmarshal(ddosDomains[i].RealServers, &ips)
				accountName, _ := asset.GetAccountNameByID(int(ddosDomains[i].AccountID))
				data = append(data, AssetInfo{
					IP:            strings.Join(ips, ","),
					ExtInfo:       "高防域名:" + ddosDomains[i].Domain,
					Name:          "高防后端IP",
					AccountName:   accountName,
					AssetType:     assets.CloudDDosDomainAssetType,
					AssetTypeName: assets.CloudDDosDomainAssetType.String(),
				})
			}
		}
	}
	if svcFlag {
		var svcs []k8s.Service
		err = app.DB().Where("cluster_ip = ?", ip).Find(&svcs).Error
		if err == nil {
			for i := range svcs {
				clusterName, _ := k8s.GetClusterNamebyID(int(svcs[i].ClusterID))
				data = append(data, AssetInfo{
					IP:            svcs[i].ClusterIP,
					ExtInfo:       "服务名称:" + svcs[i].Name,
					Name:          svcs[i].Name,
					AccountName:   clusterName,
					AssetType:     assets.K8SSVCAssetType,
					AssetTypeName: assets.K8SSVCAssetType.String(),
				})
			}
		}
	}
	return
}

func MachineUse(ip string) (useFor string, err error) {
	err = app.DB().Model(&asset.Host{}).Select("name").Where("ip = ? ", ip).Limit(1).Scan(&useFor).Error
	return
}
