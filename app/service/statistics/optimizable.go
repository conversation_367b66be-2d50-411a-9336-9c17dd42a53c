package statistics

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/monitor/prometheus"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"
)

type OptimizableAsset struct {
	ID                uint    `gorm:"column:id;primaryKey;comment:主键id" json:"id"`
	Month             string  `gorm:"column:month;index;comment:统计月份" json:"month"`
	AMDPercent        float64 `gorm:"column:amd_percent;comment:AMD百分比" json:"amd_percent"`
	AMDPercentChange  float64 `gorm:"column:amd_percent_change;comment:AMD百分比变化" json:"amd_percent_change"`
	CPUUsage          float64 `gorm:"column:cpu_usage;comment:CPU使用率" json:"cpu_usage"`
	CPUUsageChange    float64 `gorm:"column:cpu_usage_change;comment:CPU使用率变化" json:"cpu_usage_change"`
	MemoryUsage       float64 `gorm:"column:memory_usage;comment:内存使用率" json:"memory_usage"`
	MemoryUsageChange float64 `gorm:"column:memory_usage_change;comment:内存使用率变化" json:"memory_usage_change"`
}

func (OptimizableAsset) TableName() string {
	return "statistics_optimizable_assets"
}

func GetOptimizableMetrics(start, end, optimizableType string) (data []MetricsItem, err error) {
	if optimizableType != "amd_percent" && optimizableType != "cpu_usage" && optimizableType != "memory_usage" {
		return
	}
	sql := fmt.Sprintf("select %s AS metric, month from statistics_optimizable_assets where month >= ? and month <= ? ", optimizableType)
	err = app.DB().Raw(sql, start, end).Scan(&data).Error
	return
}
func (o *OptimizableAsset) Compute() {
	monthDate, err := time.ParseInLocation("2006-01", o.Month, time.Local)
	if err != nil {
		app.Log().Error("对比月份可优化资产指标，解析月份时间失败", "err", err)
		return
	}
	lastMonthDate := monthDate.AddDate(0, -1, 0).Format("2006-01")
	var exist OptimizableAsset
	err = app.DB().Model(&OptimizableAsset{}).Where("month = ? ", lastMonthDate).Take(&exist).Error
	if err != nil {
		app.Log().Error("对比月份可优化资产指标，查询上月数据失败", "err", err)
		return
	}
	o.AMDPercentChange = o.AMDPercent - exist.AMDPercent
	o.CPUUsageChange = o.CPUUsage - exist.CPUUsage
	o.MemoryUsageChange = o.MemoryUsage - exist.MemoryUsage
}

type HostTypeAMDHost struct {
	HostType asset.CloudType `gorm:"column:host_type" json:"host_type"`
	AMDCount int64           `gorm:"column:amd_count" json:"amd_count"`
	Count    int64           `gorm:"column:count" json:"count"`
}

func statOptimizableAssets(month string) (err error) {
	data := OptimizableAsset{Month: month}
	// 使用SQL直接计算AMD服务器占比
	var amdPercentage float64
	err = app.DB().Raw(`select (select count(*) from asset_hosts ah  where ah.is_amd  = true and ah.deleted_at is null)/(select count(*) from asset_hosts ah  where ah.deleted_at is null) * 100 AS amd_percentage`).Scan(&amdPercentage).Error

	if err != nil {
		return
	}
	data.AMDPercent = amdPercentage

	// 通过prometheus获取主机的CPU和内存的使用率
	promInstances, err := prometheus.GetAllInstances()
	if err != nil {
		app.Log().Error("获取Prometheus实例失败", "err", err)
		return
	}
	nowTime := time.Now()
	duration := 24 * 30 * time.Hour
	var avgCPUValueTotal, avgMemoryValueTotal float64
	var avgCPUCount, avgMemoryCount int64
	// 如果有Prometheus实例，获取主机CPU和内存使用率
	if len(promInstances) > 0 {
		for i := range promInstances {
			cpuql := ""
			memoryql := ""
			switch promInstances[i].InstanceType {
			case prometheus.InstanceTypeN9e:
				cpuql = ` avg(max_over_time(100-cpu_usage_idle[30d:5m]))`
				memoryql = `avg(max_over_time(mem_used_percent[30d:5m]))`
			default:
				cpuql = `avg(avg by (instance) (max_over_time(((1 - sum(rate(node_cpu_seconds_total{mode="idle"}[5m])) by (instance)/sum(rate(node_cpu_seconds_total[5m])) by (instance)) * 100)[30d:5m])))`
				memoryql = `avg(max_over_time(((node_memory_MemTotal_bytes - ode_memory_MemAvailable_bytes)/node_memory_MemTotal_bytes* 100)[30d:5m]) by (instance))`
			}
			result, err := prometheus.QueryInstant(promInstances[i].Datasource, cpuql, &nowTime, &duration)
			if err == nil {
				v, err := result.GetVectorValue()
				if err == nil {
					avgCPUValueTotal += v
					avgCPUCount += 1
				}
			}

			result, err = prometheus.QueryInstant(promInstances[i].Datasource, memoryql, &nowTime, &duration)
			if err == nil {
				v, err := result.GetVectorValue()
				if err == nil {
					avgMemoryValueTotal += v
					avgMemoryCount += 1
				}
			}
		}
	}

	data.CPUUsage = 0
	data.MemoryUsage = 0
	if avgCPUCount > 0 {
		data.CPUUsage = avgCPUValueTotal / float64(avgCPUCount)
	}
	if avgMemoryCount > 0 {
		data.MemoryUsage = avgMemoryValueTotal / float64(avgMemoryCount)
	}
	exist := OptimizableAsset{}
	err = app.DB().Where("month = ? ", month).Take(&exist).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = app.DB().Create(&data).Error
		if err != nil {
			return
		}
	} else {
		updateMap := map[string]any{}
		if exist.CPUUsage != data.CPUUsage {
			updateMap["cpu_usage"] = data.CPUUsage
		}
		if exist.MemoryUsage != data.MemoryUsage {
			updateMap["memory_usage"] = data.MemoryUsage
		}
		if len(updateMap) > 0 {
			err = app.DB().Model(&exist).Updates(updateMap).Error
			if err != nil {
				return
			}
		}
	}
	return
}
