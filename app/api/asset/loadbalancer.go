package asset

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"strconv"
	"strings"
	"sync"

	"github.com/gin-gonic/gin"
)

// 定义主机行结构体
type LoadbalancerRow struct {
	asset.Loadbalancer
	Datacenter        string `json:"datacenter"`
	Account           string `json:"account"`
	RegionID          string `json:"region_id"`
	ResourceGroupName string `json:"resource_group_name"`
}

// 获取负载均衡器列表
func GetLoadbalancers(c *gin.Context) {
	params := asset.GetLoadbalancerParams{}
	//  获取查询参数
	keyword := c.Query("keyword")
	// 获取分页参数
	page, _ := strconv.Atoi(c.<PERSON>("page", "1"))
	limit, _ := strconv.Atoi(c.<PERSON>("limit", "10"))
	// 计算偏移量
	params.Offset = (page - 1) * limit
	params.Limit = limit
	if keyword != "" {
		params.Keyword = &keyword
	}
	host := strings.TrimSpace(c.Query("host"))
	if host != "" {
		params.Host = &host
	}
	resourceGroupID := strings.TrimSpace(c.Query("resource_group_id"))
	if resourceGroupID != "" {
		params.ResourceGroupID = &resourceGroupID
	}
	accountID := strings.TrimSpace(c.Query("account_id"))
	if val, err := strconv.Atoi(accountID); err == nil {
		params.AccountID = &val
	}
	datacenterId := strings.TrimSpace(c.Query("datacenter_id"))
	if val, err := strconv.Atoi(datacenterId); err == nil {
		params.DatacenterID = &val
	}
	loadbalancerType := strings.TrimSpace(c.Query("loadbalancer_type"))
	if loadbalancerType != "" {
		params.LoadbalancerType = &loadbalancerType
	}
	loadbalancerID := strings.TrimSpace(c.Query("loadbalancer_id"))
	if loadbalancerID != "" {
		params.LoadbalancerID = &loadbalancerID
	}
	// 获取负载均衡器列表
	count, data, err := asset.GetLoadbalancers(params)
	if err != nil {
		app.Log().Error("获取负载均衡器列表失败", "err", err)
		app.FailedResponseMsg(c, "获取负载均衡器列表失败")
		return
	}
	// 将数据转换为新的结构体
	newData := make([]LoadbalancerRow, len(data))
	if len(data) > 0 {
		tmpCloudAccountStore := sync.Map{}
		tmpDatacenterStore := sync.Map{}
		tmpDatacenterCodeStore := sync.Map{}
		tmpResourceGroupStore := sync.Map{}
		// 并发获取数据中心名称和云账户名称
		wg := sync.WaitGroup{}
		wg.Add(len(data))
		for i := range data {
			go func() {
				newData[i].Loadbalancer = data[i]
				// 获取云账户名称
				if val, ok := tmpCloudAccountStore.Load(data[i].AccountID); ok {
					newData[i].Account, _ = val.(string)
				} else {
					account, err1 := asset.GetAccountNameByID(int(data[i].AccountID))
					if err1 == nil {
						newData[i].Account = account
						tmpCloudAccountStore.Store(data[i].AccountID, account)
					}
				}
				// 获取数据中心名称
				if val, ok := tmpDatacenterStore.Load(data[i].DatacenterID); ok {
					newData[i].Datacenter, _ = val.(string)
				} else {
					datacenter, err1 := asset.GetDatacenterNameByID(int(data[i].DatacenterID))
					if err1 == nil {
						newData[i].Datacenter = datacenter
						tmpDatacenterStore.Store(data[i].DatacenterID, datacenter)
					}
				}
				// 获取数据中心code
				if val, ok := tmpDatacenterCodeStore.Load(data[i].DatacenterID); ok {
					newData[i].RegionID, _ = val.(string)
				} else {
					code, err1 := asset.GetDatacenterCodeByID(int(data[i].DatacenterID))
					if err1 == nil {
						newData[i].RegionID = code
						tmpDatacenterCodeStore.Store(data[i].DatacenterID, code)
					}
				}
				// 获取资源组名称
				if val, ok := tmpResourceGroupStore.Load(data[i].ResourceGroupID); ok {
					newData[i].ResourceGroupName, _ = val.(string)
				} else {
					resourceGroup, err1 := asset.GetResourceGroupNameByID(data[i].ResourceGroupID)
					if err1 == nil {
						newData[i].ResourceGroupName = resourceGroup
						tmpResourceGroupStore.Store(data[i].ResourceGroupID, resourceGroup)
					}
				}
				wg.Done()
			}()
		}
		wg.Wait()
	}
	// 返回数据
	app.SuccessResponseDataCount(c, newData, count)
}
