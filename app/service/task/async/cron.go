package async

import (
	"github.com/robfig/cron"
)

type CronTask struct {
	Name     string
	CronSpec string
	Func     func()
}

type CronTasks struct {
	Tasks []CronTask
	Cron  *cron.Cron
}

func (tasks *CronTasks) Run() {
	tasks.Cron = cron.New()
	for i := range tasks.Tasks {
		tasks.Cron.AddFunc(tasks.Tasks[i].CronSpec, tasks.Tasks[i].Func)
	}
	tasks.Cron.Start()
}

func (tasks *CronTasks) Stop() {
	if tasks.Cron != nil {
		tasks.Cron.Stop()
	}
}
