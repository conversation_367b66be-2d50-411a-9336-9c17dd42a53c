# 夜莺(N9E)告警统计接口

本模块提供了基于夜莺(N9E)告警数据的多维度统计分析功能，支持时间范围查询、分页查询和多种统计维度。

## 功能特性

- **时间范围告警统计**：支持指定开始时间和结束时间参数
- **多维度统计分析**：
  - 实例维度统计（按 `target_ident` 分组）
  - 告警规则维度统计（按 `rule_id` 和 `rule_name` 分组）
  - 集群维度统计（按 `cluster` 分组）
- **丰富的统计指标**：
  - 告警总数
  - 按严重程度分类统计（Emergency/Warning/Notice）
  - 恢复率统计
  - 平均告警持续时间（已优化计算逻辑）
- **数据库NULL值处理**：使用 `sql.NullFloat64` 安全处理NULL值
- **资源组默认值处理**：空的group_name字段自动设置为'(未设置资源组)'

## API接口

### 1. 获取告警统计数据

**接口地址**：`GET /api/v1/statistic/monitor/n9e/alerts`

**请求参数**：
- `start_time` (必填): 开始时间，格式为 `2006-01-02 15:04:05`
- `end_time` (必填): 结束时间，格式为 `2006-01-02 15:04:05`
- `page` (可选): 页码，默认为1
- `limit` (可选): 每页数量，默认为10

**响应示例**：
```json
{
  "success": true,
  "data": {
    "total_count": 1500,
    "emergency_count": 50,
    "warning_count": 800,
    "notice_count": 650,
    "recovered_count": 1200,
    "recovery_rate": 80.0,
    "avg_duration": 1800.5
  }
}
```

### 2. 按实例维度获取告警统计

**接口地址**：`GET /api/v1/statistic/monitor/n9e/alerts/by-instance`

### 3. 按告警规则维度获取告警统计

**接口地址**：`GET /api/v1/statistic/monitor/n9e/alerts/by-rule`

### 4. 按集群维度获取告警统计

**接口地址**：`GET /api/v1/statistic/monitor/n9e/alerts/by-cluster`

## 平均持续时间计算优化

### 🔧 优化内容

1. **正确的时间字段**：使用 `trigger_time` 而非 `first_trigger_time` 计算持续时间
2. **严格的边界条件**：确保 `recover_time > trigger_time`，避免负数持续时间
3. **异常数据过滤**：自动过滤无效的恢复时间数据
4. **NULL值安全处理**：完善的NULL值和边界情况处理

### 📊 计算逻辑

```sql
-- 优化后的平均持续时间计算
AVG(CASE 
    WHEN is_recovered = 1 
    AND recover_time > 0 
    AND recover_time > trigger_time 
    THEN recover_time - trigger_time 
    ELSE NULL 
END) as avg_duration
```

### ✅ 验证结果

- 所有单元测试通过
- 边界条件处理完善
- 异常数据自动过滤
- 计算结果准确可靠

## 数据模型

### 告警严重程度

- `0`: Emergency (紧急)
- `1`: Warning (警告)  
- `2`: Notice (通知)

### 时间字段说明

- `trigger_time`: 告警触发时间（Unix时间戳）
- `recover_time`: 告警恢复时间（Unix时间戳）
- `first_trigger_time`: 首次触发时间（Unix时间戳）

**重要**：平均持续时间 = `recover_time - trigger_time`

## 使用示例

### 查询最近24小时的告警统计

```bash
curl -X GET "http://localhost:8080/api/v1/statistic/monitor/n9e/alerts?start_time=2024-01-01%2000:00:00&end_time=2024-01-02%2000:00:00" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 注意事项

1. **时间格式**：所有时间参数必须使用 `2006-01-02 15:04:05` 格式
2. **权限要求**：需要管理员权限才能访问这些接口
3. **性能优化**：
   - 建议在 `trigger_time` 字段上创建索引
   - 建议创建复合索引：`(is_recovered, recover_time, trigger_time)`
   - 大时间范围查询时建议使用分页
4. **数据质量**：
   - 系统会自动过滤异常数据（如负数持续时间）
   - 只计算已恢复且有有效恢复时间的告警
   - NULL值和边界情况都有适当处理
5. **平均持续时间**：
   - 单位为秒
   - 只统计已恢复的告警
   - 自动排除异常数据

## 错误处理

接口会返回标准的错误响应格式：

```json
{
  "success": false,
  "msg": "错误信息描述"
}
```

常见错误：
- 时间格式错误
- 开始时间大于结束时间
- 缺少必填参数
- 数据库查询失败

## 相关文档

- [优化总结](./OPTIMIZATION_SUMMARY.md) - 详细的优化过程和技术细节
- [使用示例](./example.sh) - 完整的API调用示例脚本
