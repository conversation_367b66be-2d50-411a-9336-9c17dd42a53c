package setting

type SettingSection struct {
	ID    uint          `gorm:"primaryKey;column:id;comment:ID" json:"id"`
	Name  string        `gorm:"type:varchar(250);unique;column:name;comment:配置节" json:"name"`
	<PERSON><PERSON> string        `gorm:"type:varchar(255);column:alias;comment:配置节别名" json:"alias"`
	Items []SettingItem `gorm:"-" json:"items,omitempty"`
}

func (*SettingSection) TableName() string {
	return "setting_sections"
}
