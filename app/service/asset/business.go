package asset

import (
	"cmdb/app"
	"cmdb/pkg/db"
	"errors"
	"time"

	"gorm.io/gorm"
)

var (
	ErrBusinessExist = errors.New("业务已经存在")
)

// 业务
type Business struct {
	ID             uint            `gorm:"column:id;primaryKey;comment:主键" json:"id"`
	Name           string          `gorm:"column:name;type:varchar(255);index" json:"name"`
	Remark         string          `gorm:"column:remark;type:varchar(255)" json:"remark"`
	ResourceGroups []ResourceGroup `gorm:"many2many:assets_business_resource_groups;" json:"resource_groups,omitempty"`
	CloudAccounts  []CloudAccount  `gorm:"many2many:assets_business_cloud_accounts;" json:"cloud_accounts,omitempty"`
	CreatedAt      time.Time       `gorm:"column:created_at;index" json:"created_at"`
	UpdatedAt      time.Time       `gorm:"column:updated_at;index" json:"updated_at"`
	DeletedAt      gorm.DeletedAt  `gorm:"column:deleted_at;index" json:"-"`
}

func (Business) TableName() string {
	return "assets_businesses"
}

type BusinessForm struct {
	Name   string `json:"name" binding:"required,max=255"`
	Remark string `json:"remark" binding:"max=255"`
}

func (form BusinessForm) Create() (err error) {
	err = app.DB().Where("name = ?", form.Name).Take(&Business{}).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		// 不存在，进行创建
		err = app.DB().Create(&Business{
			Name: form.Name, Remark: form.Remark,
		}).Error
	} else if err == nil {
		// 查询到已经存在
		err = ErrBusinessExist
	}
	return
}

func GetBusinessByID(id int) (b Business, err error) {
	err = app.DB().Where("id = ?", id).Take(&b).Error
	return
}
func GetBusinessNameByID(id int) (name string, err error) {
	err = app.DB().Model(&Business{}).Select("name").Where("id = ?", id).Limit(1).Scan(&name).Error
	return
}

func (b Business) Delete() (err error) {
	err = app.DB().Delete(&b).Error
	return
}

func (b Business) Update(form BusinessForm) (err error) {
	exist := Business{}
	err = app.DB().Select("id").Where("name = ?", form.Name).Take(&exist).Error
	if err == nil && exist.ID != b.ID {
		// 查询存在，但是不是它自己，说明冲突了
		err = ErrBusinessExist
		return
	} else if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		// 查询错误，但是不包含记录不存在
		return
	}
	updateMap := map[string]any{}
	if b.Name != form.Name {
		updateMap["name"] = form.Name
	}
	if b.Remark != form.Remark {
		updateMap["remark"] = form.Remark
	}
	if len(updateMap) > 0 {
		err = app.DB().Model(&b).Updates(updateMap).Error
	} else {
		// 没有需要更新的，直接判定是成功的操作
		err = nil
	}
	return
}

func GetBusinesses(offset, limit int, keyword string) (count int64, businesses []Business, err error) {
	dbop := app.DB()
	if keyword != "" {
		dbop = db.MLike(dbop, keyword, "name", "remark")
	}
	err = dbop.Model(&Business{}).Count(&count).Order("name").Offset(offset).Limit(limit).Find(&businesses).Error
	if err != nil {
		return
	}
	for i := range businesses {
		app.DB().Model(&businesses[i]).Association("ResourceGroups").Find(&businesses[i].ResourceGroups)
		app.DB().Model(&businesses[i]).Association("CloudAccounts").Find(&businesses[i].CloudAccounts)
	}
	return
}

func GetAllBusinesses() (data []Business, err error) {
	err = app.DB().Order("name").Find(&data).Error
	return
}

// 获取主机计数
func (b Business) GetHostCount() (count int64, err error) {
	// 查找关联的资源组
	rs := []ResourceGroup{}
	app.DB().Model(&b).Association("ResourceGroups").Find(&rs)
	groupIds := []uint{}
	for _, v := range rs {
		groupIds = append(groupIds, v.ID)
	}
	// 构建数据库操作对象
	dbop := app.DB().Or("resource_group_id IN (?)", app.DB().Model(&ResourceGroup{}).Select("group_id").Where("group_name = ? OR group_display_name = ?", b.Name, b.Name))
	// 如果有关联的资源组，则添加条件
	if len(groupIds) > 0 {
		dbop = dbop.Or("resource_group_id IN (?)", groupIds)
	}
	// 查找关联的云账户
	cas := []CloudAccount{}
	app.DB().Model(&b).Association("CloudAccounts").Find(&cas)
	cloudAccountIds := []uint{}
	for _, v := range cas {
		cloudAccountIds = append(cloudAccountIds, v.ID)
	}
	if len(cloudAccountIds) > 0 {
		dbop = dbop.Or("account_id IN (?)", cloudAccountIds)
	}
	// 计算符合条件的主机总数
	err = dbop.Model(&Host{}).Count(&count).Error
	return
}

type BusinessSetResourceGroupForm struct {
	ResourceGroupIDs []uint `json:"resource_group_ids"`
	CloudAccountIDs  []uint `json:"cloud_account_ids"`
}

func (b Business) SetResourceGroups(form BusinessSetResourceGroupForm) (err error) {
	rs := []ResourceGroup{}
	err = app.DB().Where("id IN (?)", form.ResourceGroupIDs).Find(&rs).Error
	if err != nil {
		return
	}
	err = app.DB().Model(&b).Association("ResourceGroups").Replace(&rs)
	if err != nil {
		return
	}
	cas := []CloudAccount{}
	err = app.DB().Where("id IN (?)", form.CloudAccountIDs).Find(&cas).Error
	if err != nil {
		return
	}
	err = app.DB().Model(&b).Association("CloudAccounts").Replace(&cas)
	return
}

func (b Business) GetMonthlyBillAmounts(startTime, endTime time.Time) (data []MonthlyBillAmount, err error) {
	billCycles := []string{}
	for d := startTime; d.Before(endTime); d = d.AddDate(0, 1, 0) {
		billCycles = append(billCycles, d.Format("2006-01"))
	}
	billCycles = append(billCycles, endTime.Format("2006-01"))
	cas := []CloudAccount{}
	app.DB().Model(&b).Association("CloudAccounts").Find(&cas)
	rs := []ResourceGroup{}
	app.DB().Model(&b).Association("ResourceGroups").Find(&rs)
	nameRS := []ResourceGroup{}
	app.DB().Model(&ResourceGroup{}).Where("group_display_name = ? OR group_name = ?", b.Name, b.Name).Find(&nameRS)
	// 去重
	for _, v := range nameRS {
		exist := false
		for _, vv := range rs {
			if v.ID == vv.ID {
				exist = true
				break
			}
		}
		if !exist {
			rs = append(rs, v)
		}
	}
	// 去重，如果资源组关联的云账户和业务关联的云账户相同，保留账号
	newRs := []ResourceGroup{}
	for _, vv := range rs {
		exist := false
		for _, v := range cas {
			if v.ID == vv.AccountID {
				exist = true
				break
			}
		}
		if !exist {
			newRs = append(newRs, vv)
		}
	}
	rs = newRs
	// 计算每个月的账单金额
	for i := range billCycles {
		monthlyAmount := 0.0
		if len(cas) > 0 {
			var amount *float64
			amount, _ = cas[0].GetMonthlyBillAmount(billCycles[i])
			if amount != nil {
				monthlyAmount += *amount
			}
		}
		for _, v := range rs {
			var amount *float64
			amount, _ = v.GetMonthlyBillAmount(billCycles[i])
			if amount != nil {
				monthlyAmount += *amount
			}
		}
		data = append(data, MonthlyBillAmount{BillCycle: billCycles[i], Amount: &monthlyAmount})
	}
	return
}
