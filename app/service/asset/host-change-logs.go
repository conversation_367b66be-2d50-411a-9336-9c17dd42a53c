package asset

import (
	"cmdb/app"
	"time"
)

type HostChangeLog struct {
	ID        uint      `gorm:"column:id;primaryKey;comment:主键" json:"id"`
	HostID    uint      `gorm:"column:host_id;index;comment:关联主机ID" json:"host_id"`
	Operator  string    `gorm:"column:operator;comment:操作人" json:"operator"`
	Content   string    `gorm:"column:content;type:text;comment:变更内容" json:"content"`
	CreatedAt time.Time `gorm:"column:created_at;comment:创建时间" json:"created_at"`
}

func (HostChangeLog) TableName() string {
	return "asset_host_change_logs"
}

func (h Host) LogChanges(oper, content string) (err error) {
	err = app.DB().Create(&HostChangeLog{
		HostID: h.ID, Operator: oper,
		Content: content, CreatedAt: time.Now(),
	}).Error
	return
}

type HostChangeLogForm struct {
	Operator string `json:"operator"`
	Content  string `json:"content"  binding:"required"`
}

func (h Host) GetChangeLogs(offset, limit int, startTime, endTIme *time.Time) (count int64, logs []HostChangeLog, err error) {
	dbop := app.DB().Where("host_id =?", h.ID)
	if startTime != nil {
		dbop = dbop.Where("created_at >= ?", startTime)
	}
	if endTIme != nil {
		dbop = dbop.Where("created_at <= ?", endTIme)
	}
	err = dbop.Model(&HostChangeLog{}).Count(&count).Order("created_at DESC").Offset(offset).Limit(limit).Find(&logs).Error
	return
}
