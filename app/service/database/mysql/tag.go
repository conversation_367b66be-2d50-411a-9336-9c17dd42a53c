package mysql

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"errors"

	"gorm.io/gorm"
)

// TagInstancesForm 标签实例操作表单
// 用于批量处理标签与实例的关联关系
type TagInstancesForm struct {
	TagIDs      []int  `json:"tag_ids"`      // 标签ID列表
	InstanceIDs []int  `json:"instance_ids"` // 实例ID列表
	OP          string `json:"op"`           // 操作类型：append(追加)、delete(删除)、replace(替换)、clear(清除)
}

// Do 执行标签实例操作
// 根据操作类型执行不同的批量处理逻辑，所有操作都在事务中执行以确保数据一致性
// 返回错误信息，如果操作成功则返回nil
func (form TagInstancesForm) Do() (err error) {
	switch form.OP {
	case "append":
		// 在事务中执行追加标签操作
		err = app.DB().Transaction(func(tx *gorm.DB) error {
			// 查询指定的标签
			tags := []asset.Tag{}
			err = tx.Where("id IN (?)", form.TagIDs).Find(&tags).Error
			if err != nil {
				return err
			}
			if len(tags) == 0 {
				return errors.New("tag not found")
			}
			// 为每个实例追加标签
			for i := range form.InstanceIDs {
				exist := Instance{}
				err = tx.Where("id = ?", form.InstanceIDs[i]).Take(&exist).Error
				if err != nil {
					return err
				}
				err = tx.Model(&exist).Association("Tags").Append(&tags)
				if err != nil {
					return err
				}
			}
			return nil
		})
	case "delete":
		// 在事务中执行删除标签操作
		err = app.DB().Transaction(func(tx *gorm.DB) error {
			// 查询指定的标签
			tags := []asset.Tag{}
			err = tx.Where("id IN (?)", form.TagIDs).Find(&tags).Error
			if err != nil {
				return err
			}
			if len(tags) == 0 {
				return errors.New("tag not found")
			}
			// 从每个实例中删除指定的标签
			for i := range form.InstanceIDs {
				exist := Instance{}
				err = tx.Where("id = ?", form.InstanceIDs[i]).Take(&exist).Error
				if err != nil {
					return err
				}
				err = tx.Model(&exist).Association("Tags").Delete(&tags)
				if err != nil {
					return err
				}
			}
			return nil
		})
	case "replace":
		// 在事务中执行替换标签操作
		err = app.DB().Transaction(func(tx *gorm.DB) error {
			// 查询指定的标签
			tags := []asset.Tag{}
			err = tx.Where("id IN (?)", form.TagIDs).Find(&tags).Error
			if err != nil {
				return err
			}
			if len(tags) == 0 {
				return errors.New("tag not found")
			}
			// 替换每个实例的所有标签
			for i := range form.InstanceIDs {
				exist := Instance{}
				err = tx.Where("id = ?", form.InstanceIDs[i]).Take(&exist).Error
				if err != nil {
					return err
				}
				err = tx.Model(&exist).Association("Tags").Replace(&tags)
				if err != nil {
					return err
				}
			}
			return nil
		})
	case "clear":
		// 在事务中执行清除标签操作
		err = app.DB().Transaction(func(tx *gorm.DB) error {
			// 清除每个实例的所有标签
			for i := range form.InstanceIDs {
				exist := Instance{}
				err = tx.Where("id = ?", form.InstanceIDs[i]).Take(&exist).Error
				if err != nil {
					return err
				}
				err = tx.Model(&exist).Association("Tags").Clear()
				if err != nil {
					return err
				}
			}
			return nil
		})
	default:
		err = errors.New("invalid operation")
	}
	return
}
