package auth

import (
	"cmdb/app"
	"cmdb/app/service/audit"
	"cmdb/app/service/auth"
	"errors"
	"strconv"

	"github.com/gin-gonic/gin"
)

// 获取用户分组
func GetGroups(c *gin.Context) {
	// 获取页码，默认为1
	page, _ := strconv.Atoi(c.<PERSON>("page", "1"))
	// 获取每页显示数量，默认为10
	limit, _ := strconv.Atoi(c.<PERSON>("limit", "10"))
	// 计算偏移量
	offset := (page - 1) * limit
	var keyword *string
	if val := c.Query("keyword"); val != "" {
		keyword = &val
	}
	count, data, err := auth.GetGroups(offset, limit, keyword)
	if err != nil {
		app.Log().Error("获取用户分组失败", "err", err)
		app.FailedResponseMsg(c, "获取用户分组失败")
		return
	}
	app.SuccessResponseDataCount(c, data, count)
}

// 更新用户分组
func UpdateGroup(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.Log().Debug("更新用户分组时，验证参数失败", "id", id)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	var form auth.GroupForm
	if err := c.ShouldBindJSON(&form); err != nil {
		app.Log().Debug("更新用户分组时，验证表单失败", "err", err)
		app.FailedResponseMsg(c, "表单错误")
		return
	}
	group, err := auth.GetGroupByID(id)
	if err != nil {
		app.Log().Error("更新用户分组时，获取分组失败", "err", err)
		app.FailedResponseMsg(c, "分组不存在")
		return
	}
	err = group.Update(form)
	if err != nil {
		msg := "更新失败"
		if errors.Is(err, auth.ErrGroupExist) {
			msg = err.Error()
		} else {
			app.Log().Error("更新分组失败", "err", err)
		}
		app.FailedResponseMsg(c, msg)
		return
	}
	audit.LogAPIOP(c, "认证管理", "更新分组："+form.Name)
	app.SuccessResponseMsg(c, "更新成功")
}

// 创建用户分组
func CreateGroup(c *gin.Context) {
	var form auth.GroupForm
	if err := c.ShouldBindJSON(&form); err != nil {
		app.Log().Debug("创建用户分组时，验证表单失败", "err", err)
		app.FailedResponseMsg(c, "表单错误")
		return
	}
	if err := form.Create(); err != nil {
		msg := "创建失败"
		if errors.Is(err, auth.ErrGroupExist) {
			msg = err.Error()
		} else {
			app.Log().Error("创建分组失败", "err", err)
		}
		app.FailedResponseMsg(c, msg)
		return
	}
	audit.LogAPIOP(c, "认证管理", "创建分组："+form.Name)
	app.SuccessResponseMsg(c, "创建成功")
}

// 删除用户分组
func DeleteGroup(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.Log().Error("删除用户分组时，参数错误", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	group, err := auth.GetGroupByID(id)
	if err != nil {
		app.Log().Error("删除用户分组时，分组不存在", "err", err)
		app.FailedResponseMsg(c, "分组不存在")
		return
	}
	if err := group.Delete(); err != nil {
		msg := "删除失败"
		if errors.Is(err, auth.ErrGroupExist) {
			msg = err.Error()
		} else {
			app.Log().Error("删除分组失败", "err", err)
		}
		app.FailedResponseMsg(c, msg)
		return
	}
	audit.LogAPIOP(c, "认证管理", "删除分组："+group.Name)
	app.SuccessResponseMsg(c, "删除成功")
}

// 获取所有用户分组
func GetAllGroups(c *gin.Context) {
	data, err := auth.GetAllGroups()
	if err != nil {
		app.Log().Error("获取用户分组失败", "err", err)
		app.FailedResponseMsg(c, "获取用户分组失败")
		return
	}
	app.SuccessResponseData(c, data)
}
