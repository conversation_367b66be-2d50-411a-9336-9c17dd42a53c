package v2

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"strings"

	"github.com/gin-gonic/gin"
)

func GetPVTZRecordsByValue(c *gin.Context) {
	value := strings.TrimSpace(c.Query("host"))
	if value == "" {
		app.V2FailedResponseMsg(c, "host参数不能为空")
		return
	}
	domains, err := asset.GetPVTZRecordsByValue(value)
	if err != nil {
		app.Log().Error("阿里云内网解析，根据解析记录查询解析失败", "err", err)
		app.V2FailedResponseMsg(c, "查询失败")
		return
	}
	app.V2SuccessResponseMsgData(c, "查询成功", domains)
}
