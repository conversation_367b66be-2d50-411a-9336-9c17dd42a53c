package tencentcloud

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/asset/cloud"
	"cmdb/pkg/utils"
	"errors"
	"strconv"
	"time"

	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	dnspod "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/dnspod/v20210323"
	"gorm.io/gorm"
)

// 同步账号下的域名
func (a Account) syncPublicDomains() (result cloud.SyncResults, err error) {
	// 调用同步分页域名的函数
	r, err := a.syncPagePublicDomains(0)
	// 创建同步结果对象
	sr := cloud.SyncResult{
		Key: "账号：" + a.Name,
	}
	if err == nil {
		// 同步成功
		sr.Result = "同步成功"
		// 回收过期主机
		err = a.ExpiredPublicDomains(time.Now().Add(-10 * time.Minute))
		if err != nil {
			// 回收失败
			sr.Result = "同步成功，但是回收Domain失败" + err.Error()
		}
	} else {
		// 同步失败
		sr.Result = r + " " + err.Error()
	}
	// 将同步结果添加到结果集
	result = append(result, sr)
	return
}

// 同步分页域名
func (a Account) syncPagePublicDomains(offset int64) (result string, err error) {
	// 解码AccessSecret
	accessSecret, err := utils.Base64Decode(a.AccessSecret)
	if err != nil {
		err = errors.New("AccessSecret解码失败")
		return
	}
	// 创建凭证
	credential := common.NewCredential(
		a.AccessKey,
		accessSecret,
	)
	// 创建客户端配置
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = "dnspod.tencentcloudapi.com"
	// 创建客户端
	client, err := dnspod.NewClient(credential, "", cpf)
	if err != nil {
		err = errors.New("创建客户端失败")
		return
	}
	// 创建请求
	request := dnspod.NewDescribeDomainListRequest()
	request.Offset = common.Int64Ptr(offset)
	request.Limit = common.Int64Ptr(3000)
	// 发送请求
	response, err := client.DescribeDomainList(request)
	// 判断错误类型
	if err != nil {
		err = errors.New("获取域名列表失败：" + err.Error())
		return
	}
	// 获取当前时间
	now := time.Now()
	// 遍历域名列表
	for _, domain := range response.Response.DomainList {
		domainId := ""
		// 获取域名ID
		if domain.DomainId != nil {
			domainId = strconv.FormatUint(*domain.DomainId, 10)
		}
		name := ""
		// 获取域名名称
		if domain.Name != nil {
			name = *domain.Name
		}
		var recordCount uint64 = 0
		// 获取记录数
		if domain.RecordCount != nil {
			recordCount = *domain.RecordCount
		}
		remark := ""
		// 获取备注
		if domain.Remark != nil {
			remark = *domain.Remark
		}
		groupID := ""
		// 获取分组ID
		if domain.GroupId != nil {
			groupID = strconv.FormatUint(*domain.GroupId, 10)
		}
		// 获取创建时间
		createdOn, _ := time.ParseInLocation("2006-01-02 15:04:05", *domain.CreatedOn, time.Local)
		// 获取更新时间
		updatedOn, _ := time.ParseInLocation("2006-01-02 15:04:05", *domain.UpdatedOn, time.Local)
		// 查询数据库中是否存在该域名
		exist := asset.PublicDomain{}
		genDomainErr := app.DB().Where("account_id = ? AND domain_id = ?", a.ID, domainId).Take(&exist).Error
		// 如果不存在，则创建
		if errors.Is(genDomainErr, gorm.ErrRecordNotFound) {
			err = app.DB().Create(&asset.PublicDomain{
				AccountID:       a.ID,
				DomainID:        domainId,
				Name:            name,
				RecordCount:     recordCount,
				CloudType:       a.CloudType,
				Remark:          remark,
				ResourceGroupID: groupID,
				SyncTime:        now,
				CreatedAt:       createdOn,
				UpdatedAt:       updatedOn,
			}).Error
			if err != nil {
				return
			}
			// 如果存在，则更新
		} else if genDomainErr == nil {
			updateMap := map[string]any{
				"sync_time": now,
			}
			if exist.Name != name {
				updateMap["name"] = name
			}
			if exist.RecordCount != recordCount {
				updateMap["record_count"] = recordCount
			}
			if exist.Remark != remark {
				updateMap["remark"] = remark
			}
			if exist.ResourceGroupID != groupID {
				updateMap["resource_group_id"] = groupID
			}
			if !exist.CreatedAt.Equal(createdOn) {
				updateMap["created_at"] = createdOn
			}
			if !exist.UpdatedAt.Equal(updatedOn) {
				updateMap["updated_at"] = updatedOn
			}
			err = app.DB().Model(&exist).Updates(updateMap).Error
			// 如果查询出错，则返回错误
		} else {
			err = genDomainErr
		}
		if err != nil {
			return
		}
	}
	// 如果还有更多域名，继续同步
	if response.Response.DomainCountInfo.AllTotal != nil &&
		*response.Response.DomainCountInfo.AllTotal > uint64(offset+3000) {
		result, err = a.syncPagePublicDomains(offset + 3000)
	}
	return
}
