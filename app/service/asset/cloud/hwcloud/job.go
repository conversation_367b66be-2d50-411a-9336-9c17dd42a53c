package hwcloud

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/task"
	"cmdb/pkg/utils/mytime"
	"errors"
	"time"
)

type jobFunc func(a Account) (err error)

var jobTypes = map[string]jobFunc{
	"region": func(a Account) (err error) {
		return a.SyncRegions()
	},
	"ecs": func(a Account) (err error) {
		return a.SyncECSs()
	},
	"eip": func(a Account) (err error) {
		return a.SyncEIPs()
	},
	"loadbalancer": func(a Account) (err error) {
		return a.SyncLoadbanlancers()
	},
	"domain": func(a Account) (err error) {
		return a.SyncDomains()
	},
	"resource-group": func(a Account) (err error) {
		return a.SyncResourceGroups()
	},
	"subnet": func(a Account) (err error) {
		return a.SyncSubnets()
	},
}

func SyncAllAccounts() (err error) {
	accounts := []Account{}
	err = app.DB().Where("cloud_type = ?", asset.HuaweiCloudType).Find(&accounts).Error
	if err != nil {
		return
	}
	for _, account := range accounts {
		for jobName, job := range jobTypes {
			err1 := job(account)
			if err1 != nil {
				app.Log().Error("华为云同步资产异常", "account", account.Name, "job_type", jobName, "err", err1.Error())
			}
		}
	}
	return
}

func SyncAllAccountsMonthlyBills() (err error) {
	accounts := []Account{}
	err = app.DB().Where("cloud_type = ?", asset.HuaweiCloudType).Find(&accounts).Error
	if err != nil {
		return
	}
	for _, account := range accounts {
		err = account.SyncMonthlyBills(time.Now().Format("2006-01"))
		if err != nil {
			app.Log().Error("华为云同步账单异常", "account", account.Name, "err", err.Error())
		}
	}
	return
}

// 同步所有账户的上月账单
func SyncAllAccountsLastMonthMonthlyBills() (err error) {
	accounts := []Account{}
	err = app.DB().Where("cloud_type = ?", asset.HuaweiCloudType).Find(&accounts).Error
	if err != nil {
		return
	}
	for _, account := range accounts {
		err = account.SyncMonthlyBills(mytime.GetLastMonth(time.Now()).Format("2006-01"))
		if err != nil {
			app.Log().Error("华为云同步账单异常", "account", account.Name, "err", err.Error())
		}
	}
	return
}
func (a Account) Sync(assetType string) (err error) {
	if job, ok := jobTypes[assetType]; ok {
		return job(a)
	} else {
		return errors.New("不支持同步")
	}
}

func (a Account) SyncRegions() (err error) {
	err = task.RunJob("sync-hwcloud-regions", "同步华为云regions", func() (result string, err error) {
		rs, err := a.syncRegions()
		result = rs.String()
		return
	})
	return
}

func (a Account) SyncECSs() (err error) {
	err = task.RunJob("sync-hwcloud-ecs", "同步华为云ecs", func() (result string, err error) {
		rs, err := a.syncECSs()
		result = rs.String()
		return
	})
	return
}

func (a Account) SyncEIPs() (err error) {
	err = task.RunJob("sync-hwcloud-eip", "同步华为云eip", func() (result string, err error) {
		rs, err := a.syncEIPs()
		result = rs.String()
		return
	})
	return
}

func (a Account) SyncLoadbanlancers() (err error) {
	err = task.RunJob("sync-hwcloud-elb", "同步华为云elb", func() (result string, err error) {
		rs, err := a.syncLoadbalancers()
		result = rs.String()
		return
	})
	return
}

func (a Account) SyncDomains() (err error) {
	err = task.RunJob("sync-hwcloud-domains", "同步华为云DNS", func() (result string, err error) {
		app.Log().Info("开始同步内网域名")
		rs, err := a.syncPrivateDomains()
		result = "内网域名同步：" + rs.String() + "\n"
		if err != nil {
			app.Log().Info("开始同步内网域名", "err", err.Error())
			return
		}
		app.Log().Info("开始同步内网域名解析")
		rs, err = a.syncPrivateDomainsRecords()
		result += "内网域名解析同步：" + rs.String() + "\n"
		if err != nil {
			return
		}
		rs, err = a.syncPublicDomains()
		result += "外网域名同步：" + rs.String() + "\n"
		if err != nil {
			return
		}
		rs, err = a.syncPublicDomainsRecords()
		result += "外网域名解析同步：" + rs.String() + "\n"
		return
	})
	return
}

func (a Account) SyncResourceGroups() (err error) {
	err = task.RunJob("sync-hwcloud-resource-groups", "同步华为云资源组", func() (result string, err error) {
		rs, err := a.syncEnterpriseProjects()
		result = rs
		return
	})
	return
}

func (a Account) SyncSubnets() (err error) {
	err = task.RunJob("sync-hwcloud-subnets", "同步华为云子网", func() (result string, err error) {
		rs, err := a.syncSubnets()
		result = rs.String()
		return
	})
	return
}

func (a Account) SyncMonthlyBills(billCycle string) (err error) {
	err = task.RunJob("sync-hwcloud-monthly-bills", "同步华为云月度账单", func() (result string, err error) {
		rs, err := a.syncMonthlyBill(billCycle)
		result = rs
		return
	})
	return
}
