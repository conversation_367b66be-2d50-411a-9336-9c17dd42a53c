package asset

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"strconv"
	"strings"
	"sync"

	"github.com/gin-gonic/gin"
)

// 定义主机行结构体
type PublicIPRow struct {
	asset.PublicIP
	Datacenter string `json:"datacenter"`
	Account    string `json:"account"`
	RegionID   string `json:"region_id"`
}

func GetPublicIPs(c *gin.Context) {
	params := asset.GetPublicIPsParmas{}
	// 获取分页参数
	page, _ := strconv.Atoi(c.<PERSON><PERSON>ult<PERSON>("page", "1"))
	limit, _ := strconv.Atoi(c.<PERSON>("limit", "10"))
	// 计算偏移量
	params.Offset = (page - 1) * limit
	params.Limit = limit
	//  获取查询参数
	keyword := strings.TrimSpace(c.Query("keyword"))
	if keyword != "" {
		params.Keyword = &keyword
	}
	ip := strings.TrimSpace(c.Query("ip"))
	if ip != "" {
		params.IP = &ip
	}
	cloudType := strings.TrimSpace(c.Query("cloud_type"))
	if val, err := strconv.Atoi(cloudType); err == nil {
		params.CloudType = &val
	}
	accountID := strings.TrimSpace(c.Query("account_id"))
	if val, err := strconv.Atoi(accountID); err == nil {
		params.AccountID = &val
	}
	datacenterID := strings.TrimSpace(c.Query("datacenter_id"))
	if val, err := strconv.Atoi(datacenterID); err == nil {
		params.DatacenterID = &val
	}
	count, data, err := asset.GetPublicIPs(params)
	if err != nil {
		app.Log().Error("获取公网IP列表失败", "err", err)
		app.FailedResponseMsg(c, "获取公网IP列表失败")
		return
	}
	// 将数据转换为新的结构体
	newData := make([]PublicIPRow, len(data))
	if len(data) > 0 {
		tmpCloudAccountStore := sync.Map{}
		tmpDatacenterStore := sync.Map{}
		tmpDatacenterCodeStore := sync.Map{}
		wg := sync.WaitGroup{}
		wg.Add(len(data))
		for i := range data {
			go func() {
				newData[i].PublicIP = data[i]
				// 获取云账户名称
				if val, ok := tmpCloudAccountStore.Load(data[i].AccountID); ok {
					newData[i].Account, _ = val.(string)
				} else {
					account, err1 := asset.GetAccountNameByID(int(data[i].AccountID))
					if err1 == nil {
						newData[i].Account = account
						tmpCloudAccountStore.Store(data[i].AccountID, account)
					}
				}
				// 获取数据中心名称
				if val, ok := tmpDatacenterStore.Load(data[i].DatacenterID); ok {
					newData[i].Datacenter, _ = val.(string)
				} else {
					datacenter, err1 := asset.GetDatacenterNameByID(int(data[i].DatacenterID))
					if err1 == nil {
						newData[i].Datacenter = datacenter
						tmpDatacenterStore.Store(data[i].DatacenterID, datacenter)
					}
				}
				// 获取数据中心code
				if val, ok := tmpDatacenterCodeStore.Load(data[i].DatacenterID); ok {
					newData[i].RegionID, _ = val.(string)
				} else {
					code, err1 := asset.GetDatacenterCodeByID(int(data[i].DatacenterID))
					if err1 == nil {
						newData[i].RegionID = code
						tmpDatacenterCodeStore.Store(data[i].DatacenterID, code)
					}
				}
				wg.Done()
			}()
		}
		wg.Wait()
	}
	app.SuccessResponseDataCount(c, newData, count)
}
