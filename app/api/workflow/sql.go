package workflow

import (
	"cmdb/app"
	"cmdb/app/service/audit"
	"cmdb/app/service/database/mysql"
	"cmdb/app/service/workflow"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

// sql检查
func CheckSQL(c *gin.Context) {
	var sqlCheckForm mysql.SQLCheckForm
	err := c.ShouldBind(&sqlCheckForm)
	if err != nil {
		app.Log().Debug("检查SQL时，非法字段", "err", err)
		app.FailedResponseMsgData(c, "非法字段", []any{})
		return
	}
	result, err := mysql.CheckSQL(&sqlCheckForm)
	if err != nil {
		app.FailedResponseMsgData(c, err.Error(), result)
		return
	}
	app.SuccessResponseMsgData(c, "审核成功", result)
}

// sql执行
func ExecuteSQL(c *gin.Context) {
	var form mysql.SQLExcuteForm
	err := c.ShouldBind(&form)
	if err != nil {
		app.Log().Debug("执行SQL时，非法字段", "err", err)
		app.FailedResponseMsgData(c, "非法字段", []any{})
		return
	}
	result, err := mysql.ExecSQL(form)
	if err != nil {
		app.FailedResponseMsgData(c, err.Error(), result)
		return
	}
	audit.LogAPIOP(c, "工单管理", "SQL执行，关联project ID:"+strconv.Itoa(int(form.ProjectID))+" DB:"+form.DBName)
	app.SuccessResponseMsgData(c, "执行成功", result)
}

func GetOrderMySQLTableMetaData(c *gin.Context) {
	sn := strings.TrimSpace(c.Param("sn"))
	om, err := workflow.GetOrderMySQLBySN(sn)
	if err != nil {
		app.Log().Error("获取工单失败", "err", err)
		app.FailedResponseMsg(c, "工单不存在")
		return
	}
	tables, err := om.GetProjectTableMetaData()
	if err != nil {
		app.Log().Error("获取工单表信息失败", "err", err)
		app.FailedResponseMsg(c, err.Error())
		return
	}
	app.SuccessResponseData(c, tables)

}
