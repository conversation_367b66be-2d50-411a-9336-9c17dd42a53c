package prometheus

import (
	"cmdb/app"
	"testing"
)

func TestFunction(t *testing.T) {
	err := app.NewApp("../../../../app.ini")
	if err != nil {
		t.Error(err)
		return
	}
	err = app.ConnectDB()
	if err != nil {
		t.Error(err)
		return
	}
	defer func() {
		dbop, err1 := app.DB().DB()
		if err1 == nil {
			err = dbop.Close()
			if err != nil {
				t.Error(err)
			}
		}
	}()
	err = SyncRedisInstanceFromPrometheus()
	if err != nil {
		t.<PERSON>rror(err)
		return
	}
}
