package asset

import (
	"cmdb/app"
	"cmdb/pkg/db"
	"strings"
	"time"

	"gorm.io/gorm"
)

type PublicDomain struct {
	ID              uint           `gorm:"column:id;primaryKey;comment:主键" json:"id"`
	AccountID       uint           `gorm:"column:account_id;type:int;index;comment:账户ID" json:"account_id"`
	Name            string         `gorm:"column:name;type:varchar(255);index;comment:名称" json:"name"`
	RecordCount     uint64         `gorm:"column:record_count;type:int;comment:记录数" json:"record_count"`
	Remark          string         `gorm:"column:remark;type:varchar(255);index" json:"remark"`
	ResourceGroupID string         `gorm:"column:resource_group_id;type:varchar(255);comment:资源组ID" json:"resource_group_id"`
	CloudType       CloudType      `gorm:"column:cloud_type;index;comment:类型" json:"cloud_type"`
	DomainID        string         `gorm:"column:domain_id;type:varchar(255);index;comment:域名ID" json:"domain_id"`
	CreatedAt       time.Time      `gorm:"column:created_at;index;comment:创建时间" json:"CreatedAt"`
	UpdatedAt       time.Time      `gorm:"column:updated_at;index;comment:更新时间" json:"updated_at"`
	SyncTime        time.Time      `gorm:"column:sync_time;index" json:"sync_time"`
	DeletedAt       gorm.DeletedAt `gorm:"column:deleted_at;index" json:"-"`
}

func (PublicDomain) TableName() string {
	return "asset_public_domains"
}

func (a CloudAccount) ExpiredPublicDomains(expiredTime time.Time) (err error) {
	err = app.DB().Where("sync_time < ? AND account_id = ?", expiredTime, a.ID).Delete(&PublicDomain{}).Error
	return
}

func GetPublicDomainByDomainID(domainID string) (data PublicDomain, err error) {
	err = app.DB().Where("domain_id = ?", domainID).Take(&data).Error
	return
}

func GetPublicDomains(offset, limit int, accountID *int, keyword, ip *string) (count int64, data []PublicDomain, err error) {
	dbop := app.DB()
	if accountID != nil {
		dbop = dbop.Where("account_id = ?", *accountID)
	}
	if keyword != nil {
		dbop = db.MLike(dbop, *keyword, "name", "remark", "domain_id")
	}
	if ip != nil {
		dbop = dbop.Where("domain_id IN (SELECT  DISTINCT domain_id FROM asset_public_domain_records WHERE value = ?)", *ip)
	}
	err = dbop.Model(&PublicDomain{}).Count(&count).Order("created_at DESC").Offset(offset).Limit(limit).Find(&data).Error
	return
}

type PublicDomainRecord struct {
	ID        uint           `gorm:"column:id;primaryKey;comment:主键" json:"id"`
	AccountID uint           `gorm:"column:account_id;type:int;index;comment:账户ID" json:"account_id"`
	RecordID  string         `gorm:"column:record_id;type:varchar(255);index;comment:记录ID" json:"record_id"`
	DomainID  string         `gorm:"column:domain_id;type:varchar(255);index;comment:域名ID" json:"domain_id"`
	Rr        string         `gorm:"column:rr;type:varchar(255);index;comment:记录" json:"rr"`
	Status    string         `gorm:"column:status;type:varchar(255);index;comment:状态" json:"status"`
	Value     string         `gorm:"column:value;type:varchar(255);index;comment:值" json:"value"`
	Type      string         `gorm:"column:type;type:varchar(255);comment:类型" json:"type"`
	TTL       int            `gorm:"column:ttl;type:int;comment:TTL" json:"ttl"`
	Line      string         `gorm:"column:line;type:varchar(255);comment:线路" json:"line"`
	Remark    string         `gorm:"column:remark;type:varchar(255);comment:备注" json:"remark"`
	CreatedAt time.Time      `gorm:"column:created_at;comment:创建时间" json:"created_at"`
	UpdatedAt time.Time      `gorm:"column:updated_at;comment:更新时间" json:"updated_at"`
	SyncTime  time.Time      `gorm:"column:sync_time;index" json:"sync_time"`
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at;index" json:"-"`
}

func (PublicDomainRecord) TableName() string {
	return "asset_public_domain_records"
}

func ExpiredPublicDomainRecords(domainID string, expiredTime time.Time) (err error) {
	err = app.DB().Where("sync_time < ? AND domain_id = ?", expiredTime, domainID).Delete(&PublicDomainRecord{}).Error
	return
}

func (domain PublicDomain) GetRecords(offset, limit int, keyword *string) (count int64, data []PublicDomainRecord, err error) {
	dbop := app.DB().Where("domain_id = ?", domain.DomainID)
	if keyword != nil {
		if strings.HasPrefix(*keyword, "rr:") {
			dbop = dbop.Where("rr = ?", strings.TrimPrefix(*keyword, "rr:"))
		} else {
			dbop = db.MLike(dbop, *keyword, "rr", "`value`", "remark", "domain_id", "record_id")
		}
	}
	err = dbop.Model(&PublicDomainRecord{}).Count(&count).Order("created_at DESC").Offset(offset).Limit(limit).Find(&data).Error
	return
}

func GetPublicDomainHosts() (hosts []string, err error) {
	err = app.DB().Model(&PublicDomainRecord{}).Distinct(
		`DISTINCT 
		CASE 
			asset_public_domain_records.rr WHEN '@' THEN asset_public_domains.name
			ELSE CONCAT(asset_public_domain_records.rr , '.', asset_public_domains.name)
		END AS host`,
	).Joins("JOIN asset_public_domains ON asset_public_domains.domain_id = asset_public_domain_records.domain_id").Scan(&hosts).Error
	return
}

type DomainAndRecord struct {
	AccountID   uint   `json:"account_id"`
	AccountName string `json:"account_name"`
	Domain      string `json:"domain"`
	Value       string `json:"value"`
	Rr          string `json:"rr"`
}

func GetPublicDomainRecordsByValue(value string) (domains []DomainAndRecord, err error) {
	var rs []PublicDomainRecord
	err = app.DB().Where("`value` = ?", value).Find(&rs).Error
	if len(rs) == 0 {
		return
	}
	for _, r := range rs {
		account, _ := GetCloudAccountByID(int(r.AccountID))

		var name string
		app.DB().Model(&PublicDomain{}).Select("name").Where("domain_id = ?", r.DomainID).Scan(&name)

		if account.CloudType == HuaweiCloudType {
			domains = append(domains, DomainAndRecord{AccountID: r.AccountID, AccountName: account.Name, Domain: name, Value: value, Rr: r.Rr})
		} else {
			domains = append(domains, DomainAndRecord{AccountID: r.AccountID, AccountName: account.Name, Domain: name, Value: value, Rr: r.Rr + "." + name})
		}
	}
	return
}
