package task

import (
	"cmdb/app"
	"cmdb/pkg/db"
	"errors"
	"time"

	"gorm.io/gorm"
)

type TaskType string

const (
	TaskTypeAnsible TaskType = "ansible"
	TaskTypeShell   TaskType = "shell"
)

type BatchTemplate struct {
	ID        uint           `gorm:"primaryKey;column:id;comment:ID" json:"id"`
	Name      string         `gorm:"type:varchar(200);not null;column:name;comment:模板名称" json:"name"`
	FilePath  string         `gorm:"type:varchar(500);column:file_path;comment:文件路径" json:"file_path"`
	TaskType  TaskType       `gorm:"type:varchar(200);not null ;column:task_type;index;comment:任务类型：ansible/shell" json:"task_type"`
	Remark    string         `gorm:"type:varchar(250);column:remark;comment:备注" json:"remark"`
	CreatedAt time.Time      `gorm:"column:created_at;index;comment:创建时间" json:"created_at"`
	UpdatedAt time.Time      `gorm:"column:updated_at;comment:更新时间" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at;index;comment:软删除" json:"-"`
}

func (BatchTemplate) TableName() string {
	return "task_batch_templates"
}

func GetBatchTemplates(offset, limit int, keyword *string) (count int64, templates []BatchTemplate, err error) {
	dbop := app.DB()
	if keyword != nil {
		dbop = db.MLike(dbop, *keyword, "name", "remak")
	}
	err = dbop.Model(&BatchTemplate{}).Count(&count).Order("created_at DESC").Offset(offset).Limit(limit).Find(&templates).Error
	return
}

type BatchTemplateForm struct {
	Name     string   `json:"name" binding:"required,max=255"`
	FilePath string   `json:"file_path" binding:"required,max=500"`
	TaskType TaskType `json:"task_type" binding:"required"`
	Remark   string   `json:"remark" binding:"max=255"`
}

var ErrBatchTemplateExist = errors.New("任务模板已经存在")

func (form BatchTemplateForm) Create() (err error) {
	err = app.DB().Where("name = ?", form.Name).Take(&BatchTemplate{}).Error
	if err == nil {
		err = ErrBatchTemplateExist
		return
	}
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = app.DB().Create(&BatchTemplate{
			Name:     form.Name,
			FilePath: form.FilePath,
			TaskType: form.TaskType,
			Remark:   form.Remark,
		}).Error
	}
	return
}

func GetBatchTemplateByID(id int) (template BatchTemplate, err error) {
	err = app.DB().Where("id = ?", id).Take(&template).Error
	return
}

func (template BatchTemplate) Update(form BatchTemplateForm) (err error) {
	updateMap := map[string]any{}
	if form.Name != template.Name {
		// 检查任务模板是否存在
		exist := BatchTemplate{}
		err = app.DB().Where("id = ?", template.ID).Take(&exist).Error
		if err == nil && exist.ID != template.ID {
			err = ErrBatchTemplateExist
			return
		}
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		err = nil
	}
	if form.FilePath != template.FilePath {
		updateMap["file_path"] = form.FilePath
	}
	if form.TaskType != template.TaskType {
		updateMap["task_type"] = form.TaskType
	}
	if form.Remark != template.Remark {
		updateMap["remark"] = form.Remark
	}
	return
}

func (template BatchTemplate) Delete() (err error) {
	err = app.DB().Delete(&template).Error
	return
}

func GetAllBatchTemplates() (templates []BatchTemplate, err error) {
	err = app.DB().Find(&templates).Error
	return
}
