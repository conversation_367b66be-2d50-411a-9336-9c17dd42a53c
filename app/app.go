package app

import (
	"cmdb/pkg/web"
	"errors"
	"fmt"
	stdlog "log"
	"log/slog"
	"os"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	redis "github.com/redis/go-redis/v9"
	"gopkg.in/ini.v1"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

type application struct {
	dbop         *gorm.DB
	log          *slog.Logger
	config       config
	sessionStore *redis.Client
	httpServer   *web.Server
	PID          string
}

// app 全局变量
var app *application
var (
	ErrGetConfigFile = errors.New("获取配置文件失败")
	ErrNotInitApp    = errors.New("App未初始化")
)

func NewApp(configFile string) (err error) {
	app = &application{}
	app.PID = strconv.Itoa(os.Getpid())
	cfg, err := ini.Load(configFile)
	if err != nil {
		return ErrGetConfigFile
	}
	cfg.BlockMode = false
	err = cfg.MapTo(&app.config)
	if err != nil {
		return
	}
	if app.config.LogLevel == "debug" {
		app.log = slog.New(slog.NewJSONHandler(os.Stdout, &slog.HandlerOptions{
			Level: slog.LevelDebug,
		}))
	} else {
		app.log = slog.New(slog.NewJSONHandler(os.Stdout, &slog.HandlerOptions{
			Level: slog.LevelInfo,
		}))
	}
	if app.config.HTTP.Host == "" {
		app.config.HTTP.Host = "0.0.0.0"
	}
	if app.config.HTTP.Port == 0 {
		app.config.HTTP.Port = 8080
	}
	return
}

// Conf 获取配置对象
func Conf() config {
	if app == nil {
		panic("请先初始化app")
	}
	return app.config
}

// Log 获取日志句柄
func Log() *slog.Logger {
	if app == nil {
		panic("请先初始化app")
	}
	if app.log == nil {
		// 如果日志对象为nil，创建一个默认的日志对象
		app.log = slog.New(slog.NewJSONHandler(os.Stdout, &slog.HandlerOptions{
			Level: slog.LevelInfo,
		}))
	}
	return app.log
}

// PID 获取进程ID
func PID() string {
	if app == nil {
		return strconv.Itoa(os.Getpid())
	}
	return app.PID
}

// DB 获取数据操作句柄
func DB() *gorm.DB {
	if app == nil {
		panic("数据库未连接")
	}
	return app.dbop
}

// SessionStore 会话存储，用于操作session
func SessionStore() *redis.Client {
	if app == nil {
		panic("请先初始化app")
	}
	return app.sessionStore
}

// SessionStore 会话存储，用于操作session
func SetSessionStore(s *redis.Client) {
	app.sessionStore = s
}

// ConnectDB 连接数据库
func ConnectDB() (err error) {
	if app == nil {
		return ErrNotInitApp
	}
	dbConfig := app.config.DB
	startTime := time.Now()
	app.log.Info("数据库 连接 开始")
	gormConfig := &gorm.Config{
		CreateBatchSize:                          1000,
		QueryFields:                              true,
		DisableForeignKeyConstraintWhenMigrating: true,
	}
	loggerConfig := logger.Config{
		SlowThreshold: time.Second,   // 慢 SQL 阈值
		LogLevel:      logger.Silent, // Log level
		Colorful:      false,         // 禁用彩色打印
	}
	if dbConfig.ShowSQL {
		loggerConfig.LogLevel = logger.Info
		loggerConfig.Colorful = true
	}
	gormConfig.Logger = logger.New(stdlog.New(os.Stdout, "\r\n", stdlog.LstdFlags), loggerConfig)
	app.dbop, err = gorm.Open(mysql.New(mysql.Config{
		DriverName: "mysql",
		DSN: fmt.Sprintf("%s:%s@(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local&timeout=10s",
			dbConfig.Username, dbConfig.Password, dbConfig.Host, dbConfig.Port, dbConfig.DBName),
	}), gormConfig)
	if err != nil {
		return
	}
	sqlDB, err := app.dbop.DB()
	if err != nil {
		return
	}
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxIdleTime(30 * time.Minute)
	sqlDB.SetConnMaxLifetime(1 * time.Hour)
	app.log.Info("数据库 连接 成功", slog.String("cost", time.Since(startTime).String()))
	return
}

func StartHttp(router *gin.Engine) (err error) {
	if app == nil {
		return ErrNotInitApp
	}
	err = ConnectDB()
	if err != nil {
		return
	}
	s, err := web.StartServer(app.config.HTTP.Host, app.config.HTTP.Port, router)
	if err != nil {
		return
	}
	app.httpServer = &s
	return
}

func StopHttp() (err error) {
	startTime := time.Now()
	if app.httpServer != nil {
		app.log.Info("开始 关闭 http server")
		err = app.httpServer.Stop()
		if err != nil {
			app.log.Error("关闭 http server失败", slog.Any("err", err))
		}
		app.log.Info("结束 关闭 http server", slog.String("cost", time.Since(startTime).String()))
	}
	// 关闭redis
	startTime = time.Now()
	if app.sessionStore != nil {
		app.log.Info("开始 关闭 redis连接")
		err := app.sessionStore.Close()
		if err != nil {
			app.log.Error("关闭 redis 连接失败", slog.Any("err", err))
		}
		app.log.Info("完成 关闭 redis连接", slog.String("cost", time.Since(startTime).String()))
	}
	// 关闭数据库
	startTime = time.Now()
	if app.dbop != nil {
		app.log.Info("开始 关闭 数据库连接")
		dbop, err := app.dbop.DB()
		if err == nil {
			err = dbop.Close()
			if err != nil {
				app.log.Error("关闭 数据库 连接失败", slog.Any("err", err))
			}
		}
		app.log.Info("完成 关闭 数据库连接", slog.String("cost", time.Since(startTime).String()))
	}
	return

}
