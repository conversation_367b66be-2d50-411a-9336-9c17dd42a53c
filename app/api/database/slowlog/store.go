package slowlog

import (
	"cmdb/app"
	"strconv"

	"cmdb/app/service/audit"
	"cmdb/app/service/database/slowlog"

	"github.com/gin-gonic/gin"
)

func CreateSlowlogStore(c *gin.Context) {
	var form slowlog.SlowlogStoreForm
	if err := c.ShouldBindJSON(&form); err != nil {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	err := form.Create()
	if err != nil {
		app.FailedResponseMsg(c, "添加失败")
		return
	}
	audit.LogAPIOP(c, "慢日志管理", "添加慢日志实例："+form.Name)
	app.SuccessResponseMsg(c, "添加成功")
}

func UpdateSlowlogStore(c *gin.Context) {
	var form slowlog.SlowlogStoreForm
	if err := c.ShouldBindJSON(&form); err != nil {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	store, err := slowlog.GetSlowlogStoreByID(id)
	if err != nil {
		app.FailedResponseMsg(c, "更新失败")
		return
	}
	err = store.Update(form)
	if err != nil {
		app.FailedResponseMsg(c, "更新失败")
		return
	}
	audit.LogAPIOP(c, "慢日志管理", "更新慢日志实例："+form.Name)
	app.SuccessResponseMsg(c, "更新成功")
}

func DeleteSlowlogStore(c *gin.Context) {
	var form slowlog.SlowlogStoreForm
	if err := c.ShouldBindJSON(&form); err != nil {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	store, err := slowlog.GetSlowlogStoreByID(id)
	if err != nil {
		app.FailedResponseMsg(c, "删除失败")
		return
	}
	err = store.Delete()
	if err != nil {
		app.FailedResponseMsg(c, "删除失败")
		return
	}
	audit.LogAPIOP(c, "慢日志管理", "删除慢日志实例："+form.Name)
	app.SuccessResponseMsg(c, "删除成功")
}

func GetSlowlogStores(c *gin.Context) {
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	offset := (page - 1) * limit
	keyword := c.Query("keyword")
	count, data, err := slowlog.GetSlowlogStore(offset, limit, &keyword)
	if err != nil {
		app.Log().Error("获取慢日志实例失败", "err", err)
		app.FailedResponseMsg(c, "获取失败")
		return
	}
	app.SuccessResponseDataCount(c, data, count)
}

func GetAllSlowlogStores(c *gin.Context) {
	data, err := slowlog.GetAllSlowlogStore()
	if err != nil {
		app.FailedResponseMsg(c, "获取失败")
		return
	}
	app.SuccessResponseData(c, data)
}
