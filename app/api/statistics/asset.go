// statistics包提供资产统计相关的API处理函数
package statistics

import (
	"cmdb/app"
	"cmdb/app/service/statistics"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/now"
)

// GetMonthlyAssetStat 处理获取月度资产统计数据的请求
// 如果未指定月份参数，默认返回上个月的统计数据
// @参数 c *gin.Context - Gin的上下文，包含请求数据
// @查询参数 month string - 可选的月份参数，格式为 "2006-01"
func GetMonthlyAssetStat(c *gin.Context) {
	month := c.Query("month")
	if month == "" {
		month = now.BeginningOfMonth().AddDate(0, -1, 0).Format("2006-01")
	}
	data, err := statistics.GetMonthlyAssetStat(month)
	if err != nil {
		app.FailedResponseMsg(c, "获取数据失败")
		return
	}
	app.SuccessResponseData(c, data)
}

// UpdateDataAsset 处理更新特定资产数据记录的请求
// @参数 c *gin.Context - Gin的上下文，包含请求数据
// @参数 id string - 要更新的资产ID
// @请求体 form statistics.DataAsset - 更新的资产数据
func UpdateDataAsset(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	var form statistics.DataAsset
	err = c.ShouldBindJSON(&form)
	if err != nil {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	err = statistics.UpdateDataAsset(id, form)
	if err != nil {
		app.FailedResponseMsg(c, "更新数据失败")
		return
	}
	app.SuccessResponseMsg(c, "更新数据成功")
}

// StatAsssetNow 触发对上个月资产数据的即时统计计算
// 该计算过程是异步执行的，以避免阻塞响应
// @参数 c *gin.Context - Gin的上下文，包含请求数据
func StatAsssetNow(c *gin.Context) {
	month := now.BeginningOfMonth().AddDate(0, -1, 0).Format("2006-01")
	var err error
	go func() {
		err = statistics.StataAssets(month)
	}()
	time.Sleep(300 * time.Millisecond)
	if err != nil {
		app.FailedResponseMsg(c, "统计数据失败")
		return
	}
	app.SuccessResponseMsg(c, "提交成功")
}

func GetAssetMetrics(c *gin.Context) {
	start := c.Query("start")
	if start == "" {
		start = now.BeginningOfMonth().AddDate(0, -12, 0).Format("2006-01")
	}
	end := c.Query("end")
	if end == "" {
		end = now.BeginningOfMonth().Format("2006-01")
	}
	assetType := c.Query("asset_type")
	metricType := c.Query("metric_type")
	name := c.Query("name")

	data, err := statistics.GetAssetMetrics(start, end, assetType, metricType, name)
	if err != nil {
		app.Log().Error("获取数据失败", "err", err)
		app.FailedResponseMsg(c, "获取数据失败")
		return
	}
	app.SuccessResponseData(c, data)
}
