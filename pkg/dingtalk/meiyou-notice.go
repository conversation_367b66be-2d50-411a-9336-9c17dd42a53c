package dingtalk

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha1"
	"crypto/tls"
	"encoding/base64"
	"encoding/json"
	"errors"
	"io"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"
)

// SentMeiyouNotice 工作通知
func SentMeiyouNotice(sn, content string) (resp string, err error) {
	timeFormat := time.Now().Format("2006-01-02T15:04:05.000-07:00")
	id := "0cadba854b744b4f8c20ea7781b00fe7"
	clientSecret := "0cJDsN+0MN76snBTCDtjXxBpntsSGbosvvrAYJ2E"
	host := "https://oa-service.meiyou.com"
	method := "POST"
	path := "/dingtalk-text"
	url := host + path
	header := map[string]string{
		"Scope":        "3",
		"Accept":       "application/json",
		"Content-Type": "application/json",
		"Recipient":    sn,
		"Application":  "38655780",
	}
	headerKeys := []string{}
	for key := range header {
		headerKeys = append(headerKeys, key)
	}
	sort.Strings(headerKeys)
	var stringToSign bytes.Buffer
	stringToSign.WriteString(timeFormat + method + path)
	for _, key := range headerKeys {
		stringToSign.WriteString(header[key])
	}
	body := map[string]string{
		"content": content,
	}
	jsonByte, _ := json.Marshal(body)
	stringToSign.Write(jsonByte)
	mac := hmac.New(sha1.New, []byte(clientSecret))
	mac.Write(stringToSign.Bytes())
	p := mac.Sum(nil)
	signature := base64.StdEncoding.EncodeToString(p)
	header["Signature"] = "id=" + id + ",timestamp=" + timeFormat + ",value=" + signature
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: tr}
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonByte))
	if err != nil {
		return "", err
	}
	// 添加header
	for k, v := range header {
		req.Header.Add(k, v)
	}
	response, err := client.Do(req)
	if err != nil {
		return "", err
	}
	respBody, err := io.ReadAll(response.Body)
	if err != nil {
		return "", err
	}
	if response.StatusCode != 200 {
		headers := []string{}
		for k, v := range response.Header {
			headers = append(headers, k+":["+strings.Join(v, ",")+"]")
		}
		err = errors.New("response :" + strconv.Itoa(response.StatusCode) + " response headers :{" + strings.Join(headers, ",") + "}")

	}
	return string(respBody), err
}
