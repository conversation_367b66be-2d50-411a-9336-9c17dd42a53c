package asset

import (
	"cmdb/app"
	"cmdb/app/service/statistic/asset"
	"cmdb/app/service/statistic/asset/report"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/now"
)

func GetDailyReport(c *gin.Context) {
	var statDate time.Time
	statDateStr := c.Query("stat_date")
	// 判断是否是YYYY-mm-dd格式
	statDate, err := time.Parse("2006-01-02", statDateStr)
	if err != nil {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	// 格式化statDate
	data, err := report.GetDailyReport(statDate)
	if err != nil {
		app.FailedResponseMsg(c, "获取数据失败")
		return
	}
	app.SuccessResponseData(c, data)
}

func GetMonthlyReport(c *gin.Context) {
	var reportMonth time.Time
	monthStr := c.Query("monthly")
	// 判断是否是YYYY-mm格式
	reportMonth, err := time.Parse("2006-01", monthStr)
	if err != nil {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	// 格式化reportMonth
	data, err := report.GetMonthlyReport(reportMonth)
	if err != nil {
		app.FailedResponseMsg(c, "获取数据失败")
		return
	}
	app.SuccessResponseData(c, data)
}

func GenStat(c *gin.Context) {
	statDateStr := c.Query("stat_date")
	if statDateStr == "" {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	// 判断是否是YYYY-mm-dd格式
	statDate, err := time.Parse("2006-01-02", statDateStr)
	if err != nil {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	// 异步统计
	go asset.Stat(statDate)
	app.SuccessResponseMsg(c, "提交统计成功")
}

func GenDailyReport(c *gin.Context) {
	err := report.GenDailyReport(now.BeginningOfDay().AddDate(0, 0, -1))
	if err != nil {
		app.Log().Error("生成日报失败", "err", err)
		app.FailedResponseMsg(c, "生成日报失败")
		return
	}
	app.SuccessResponseMsg(c, "生成日报成功")
}

func GenMonthlyReport(c *gin.Context) {
	err := report.GenLastMonthReport()
	if err != nil {
		app.Log().Error("生成月报失败", "err", err)
		app.FailedResponseMsg(c, "生成月报失败")
		return
	}
	app.SuccessResponseMsg(c, "生成月报成功")
}
