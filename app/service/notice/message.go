package notice

import (
	"cmdb/app"
	"strings"
	"time"
)

type MessageType string

const (
	// 美柚工作通知
	MessageMeiyou MessageType = "meiyou_notice"
	// 钉钉机器人通知
	MessageDingtalkRobot MessageType = "dingtalk_robot"
	// 邮件通知
	MessageEmail MessageType = "email"
)

type MessageStatus int

const (
	// 未发送
	MessageStatusUnsent MessageStatus = iota
	// 发送中
	MessageStatusSending
	// 发送成功
	MessageStatusSuccess
	// 发送失败
	MessageStatusFailed
)

// 通知消息
type Message struct {
	ID          uint          `gorm:"primaryKey;column:id;comment:ID" json:"id"`
	Title       string        `gorm:"type:varchar(255);column:title;comment:标题" json:"title"`
	Content     string        `gorm:"type:longtext;column:content;comment:内容" json:"content"`
	MessageType MessageType   `gorm:"type:varchar(100);column:message_type;comment:消息类型" json:"message_type"`
	Contact     string        `gorm:"type:varchar(255);index;column:contact;comment:联系人名称" json:"contact"`
	Link        string        `gorm:"type:varchar(1000);column:link;comment:链接或者邮箱" json:"link" `
	Status      MessageStatus `gorm:"type:int;index;column:status;default:0;comment:是否发送：0表示未发送 1表示发送成功 2 表示发送失败" json:"status"`
	AtMobiles   string        `gorm:"type:varchar(255);column:at_mobiles;comment:at手机号码，逗号分隔" json:"at_mobiles"`
	SendResult  string        `gorm:"type:text;column:send_result;comment:发送结果" json:"send_result"`
	CreatedAt   time.Time     `gorm:"column:created_at;comment:创建时间" json:"created_at"`
	SendTime    *time.Time    `gorm:"column:send_time;comment:发送时间" json:"send_time"`
}

// 设置表名
func (*Message) TableName() string {
	return "notice_messages"
}

func GetMessageDetailByID(id int) (message Message, err error) {
	err = app.DB().Where("id = ?", id).Take(&message).Error
	return
}

// 获取通知消息
func GetMessages(offset, limit int, contact, messageType *string, status *int, startTime, endTime *time.Time) (count int64, messages []Message, err error) {
	dbop := app.DB()
	if contact != nil {
		dbop = dbop.Where("contact LIKE ? OR link LIKE ?", "%"+*contact+"%", "%"+*contact+"%")
	}
	if messageType != nil {
		dbop = dbop.Where("message_type = ?", messageType)
	}
	if startTime != nil {
		dbop = dbop.Where("created_at >= ?", startTime)
	}
	if endTime != nil {
		dbop = dbop.Where("created_at <= ?", endTime)
	}
	if status != nil {
		dbop = dbop.Where("status = ?", status)
	}
	err = dbop.Model(&Message{}).Count(&count).Select(
		"id", "title", "link", "message_type", "contact",
		"status", "send_result", "created_at", "send_time", "at_mobiles",
	).Order("created_at DESC,send_time DESC").Offset(offset).Limit(limit).Find(&messages).Error
	return
}

// CreateMessage 添加消息
func CreateMessage(title, content string, messageType MessageType, contact, link string, atMobiles ...string) (err error) {
	err = app.DB().Create(&Message{
		Title:       title,
		Contact:     contact,
		Content:     content,
		Link:        link,
		Status:      MessageStatusUnsent,
		MessageType: messageType,
		AtMobiles:   strings.Join(atMobiles, ","),
	}).Error
	return
}
