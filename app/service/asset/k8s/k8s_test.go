package k8s

import (
	"cmdb/app"
	"testing"
)

func TestK8s(t *testing.T) {
	err := app.NewApp("../../../../app.ini")
	if err != nil {
		t.<PERSON>al(err)
	}
	err = app.ConnectDB()
	if err != nil {
		t.<PERSON>al(err)
	}
	c, err := GetClusterByID(1)
	if err != nil {
		t.<PERSON><PERSON>(err)
	}
	err = c.SyncNodes()
	if err != nil {
		t.<PERSON>al(err)
	}
	err = c.SyncWorkloads()
	if err != nil {
		t.<PERSON>al(err)
	}
}
