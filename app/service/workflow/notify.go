package workflow

import (
	"bytes"
	"cmdb/app/service/notice"
	"encoding/json"
	"errors"
	"fmt"
	"strings"

	"gorm.io/gorm"
)

// notifyNextProcess 函数用于通知下一个流程的审批人
func (o Order) notifyNextProcess() (err error) {
	reTitle := ""
	// 定义审批人和抄送人的ID列表
	var approversIDs, ccIDs []uint
	// 获取下一个流程
	np, err := o.CurrentProcess()
	if err == nil {
		// 解析审批人ID列表
		err1 := json.Unmarshal(np.ApproversIDs, &approversIDs)
		if err1 == nil {
			approversIDs = append(approversIDs, approversIDs...)
		}
		// 解析抄送人ID列表
		err1 = json.Unmarshal(np.CCIDs, &ccIDs)
		if err1 == nil {
			approversIDs = append(approversIDs, ccIDs...)
		}
	} else if errors.Is(err, gorm.ErrRecordNotFound) {
		// 没有下一个流程就是结束了，直接通知申请人
		approversIDs = append(approversIDs, o.ApplicantID)
		reTitle = fmt.Sprintf("「工单已完成，请完成评价」 - %s", o.Title)
	}
	// 通知审批人
	err = o.notifyByApproverIDs(reTitle, approversIDs...)
	return
}

// genMessage 函数用于生成通知消息
func (o Order) genMessage(reTitle string) (title, content string, err error) {
	// 获取申请人信息
	applicant, err := o.GetApplicant()
	if err != nil {
		return
	}
	// 生成通知标题
	if o.Env > 0 {
		title = fmt.Sprintf("「%s - %s - %s」 - %s", o.Env.String(), o.ExtType.Name(), o.Status.String(), o.Title)
	} else {
		title = fmt.Sprintf("「%s - %s」 - %s", o.ExtType.Name(), o.Status.String(), o.Title)
	}
	if reTitle != "" {
		title = reTitle
	}
	// 生成通知内容
	var contents bytes.Buffer
	contents.WriteString(fmt.Sprintf(`
标题：%s
状态：%s
类型：%s
申请人：%s
申请时间：%s
访问地址 : https://cmdb-ops.meiyou.com/workflow/order/detail/%d
-------------------审批流程---------------------------
`, o.Title, o.Status.String(), o.ExtType.Name(),
		applicant.Name+"("+applicant.Email+")", o.CreatedAt.Format("2006-01-02 15:04:05"), o.ID))
	// 判断是否已经获取到下一个审批节点的联系人
	processes, err := o.GetProcessDetails()
	if err != nil {
		return
	}
	for i := range processes {
		if processes[i].ApproverID != 0 {
			// 获取审批人信息
			approver := processes[i].Approver
			contents.WriteString("********\n审批人：" +
				approver.Name + " 在 ")
			if processes[i].ApproveTime != nil {
				contents.WriteString(processes[i].ApproveTime.Format("2006-01-02 15:04:05"))
			}
			if processes[i].Status == PassProcessStatus {
				if processes[i].ProcessIndex == 0 {
					contents.WriteString(" 发起申请\n")
				} else {
					contents.WriteString(" 通过审批\n")
				}
			} else {
				contents.WriteString(" 驳回审批\n")
				break
			}
		} else {
			// 获取审批人信息
			approvers := processes[i].Approvers
			names := []string{}
			for j := range approvers {
				names = append(names, approvers[j].Name)
			}
			contents.WriteString("********\n审批人：" + strings.Join(names, "、") + " 待审批\n")
		}
	}
	content = contents.String()
	return
}

func (o Order) notifyByApproverIDs(reTitle string, ids ...uint) (err error) {
	// 去重
	tmpMap := make(map[uint]struct{})
	for _, v := range ids {
		tmpMap[v] = struct{}{}
	}
	ids = make([]uint, 0, len(tmpMap))
	for k := range tmpMap {
		ids = append(ids, k)
	}
	approvers, err := GetApproversByIDs(ids...)
	if err != nil {
		return
	}
	title, content, err := o.genMessage(reTitle)
	if err != nil {
		return
	}
	for _, approver := range approvers {
		err = notice.CreateMessage(title, content, notice.MessageMeiyou, approver.Name, approver.SN)
	}
	return
}
