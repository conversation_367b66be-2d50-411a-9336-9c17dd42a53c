package setting

import "strconv"

type SMTP struct {
	Name     string
	Host     string
	Port     int
	User     string
	Password string
}

func GetSMTPSetting() (smtp SMTP, err error) {
	smtp = SMTP{
		Name:     GetSettingValue("stmp", "name"),
		Host:     GetSettingValue("stmp", "host"),
		User:     GetSettingValue("stmp", "user"),
		Password: GetSettingValue("stmp", "password"),
	}
	port, err := strconv.Atoi(GetSettingValue("stmp", "port"))
	if err != nil {
		port = 25
	}
	smtp.Port = port
	return
}
