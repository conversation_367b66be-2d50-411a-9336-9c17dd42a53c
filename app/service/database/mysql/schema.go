package mysql

import (
	"cmdb/app"
	"cmdb/pkg/db"
	"time"

	"gorm.io/gorm"
)

var SYSTEMSchemas = []string{
	"sys", "information_schema", "mysql", "performance_schema", "percona",
	"SYS", "INFORMATION_SCHEMA", "MYSQL", "PERFORMANCE_SCHEMA", "PERCONA", "METRICS_SCHEMA",
}

type Schema struct {
	ID                      uint           `gorm:"column:id;primaryKey;comment:主键" json:"id"`
	InstanceID              uint           `gorm:"column:instance_id;index:idx_instance_id;comment:实例ID" json:"instance_id"`
	Name                    string         `gorm:"column:name;type:varchar(255);index;comment:名称" json:"name"`
	DefaultCharacterSetName string         `gorm:"column:default_character_set_name;type:varchar(255);comment:默认字符集" json:"default_character_set_name"`
	DefaultCollationName    string         `gorm:"column:default_collation_name;type:varchar(255);comment:默认校对集" json:"default_collation_name"`
	SyncTime                time.Time      `gorm:"column:sync_time;index" json:"sync_time"`
	DeletedAt               gorm.DeletedAt `gorm:"column:deleted_at;index" json:"-"`
}

func (Schema) TableName() string {
	return "asset_db_mysql_schemas"
}

func (instance Instance) SyncSchemas() (err error) {
	dbop, err := instance.NewConnect()
	if err != nil {
		return err
	}
	defer db.CloseDB(dbop)
	schemas := []Schema{}
	err = dbop.Unscoped().Table("information_schema.SCHEMATA").
		Select("SCHEMA_NAME AS schema_name", "DEFAULT_CHARACTER_SET_NAME AS default_character_set_name", "DEFAULT_COLLATION_NAME AS default_collation_name").Where("SCHEMA_NAME NOT IN (?)", SYSTEMSchemas).Find(&schemas).Error
	if err != nil {
		return
	}
	var version string
	err = dbop.Raw("SELECT VERSION() as version;").Scan(&version).Error
	if err != nil {
		return
	}
	go func() {
		app.DB().Model(&instance).Update("version", version)
	}()
	startTime := time.Now()
	for i := range schemas {
		schemas[i].InstanceID = instance.ID
		schemas[i].SyncTime = startTime
		exist := Schema{}
		err1 := app.DB().Where("name = ? AND instance_id = ?", schemas[i].Name, schemas[i].InstanceID).Take(&exist).Error
		if err1 == gorm.ErrRecordNotFound {
			err1 = app.DB().Create(&schemas[i]).Error
		} else if err1 == nil {
			updateMap := map[string]any{
				"sync_time": startTime,
			}
			if exist.DefaultCharacterSetName != schemas[i].DefaultCharacterSetName {
				updateMap["default_character_set_name"] = schemas[i].DefaultCharacterSetName
			}
			if exist.DefaultCollationName != schemas[i].DefaultCollationName {
				updateMap["default_collation_name"] = schemas[i].DefaultCollationName
			}
			if len(updateMap) > 0 {
				err1 = app.DB().Model(&exist).Updates(updateMap).Error
			}
		} else {
			// 查询错误
			err = err1
			return
		}
		if err1 != nil {
			app.Log().Error("同步数据库失败", "host", instance.Host, "port", instance.Port, "schema", schemas[i].Name, "err", err.Error())
		}
	}
	err = app.DB().Where("sync_time < ? AND instance_id = ?", startTime.Add(-1*time.Hour), instance.ID).Delete(&Schema{}).Error
	return
}
