package k8s

import (
	"cmdb/app"
	"cmdb/app/service/asset/k8s"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

type PodRow struct {
	k8s.Pod
	ClusterName string `json:"cluster_name"`
}

func GetPods(c *gin.Context) {
	// 获取分页参数
	page, _ := strconv.Atoi(c<PERSON>("page", "1"))
	limit, _ := strconv.Atoi(c<PERSON>("limit", "10"))
	// 计算偏移量
	offset := (page - 1) * limit
	var ip *string
	if val := c.Query("ip"); val != "" {
		ip = &val
	}
	var keyword *string
	if val := strings.TrimSpace(c.Query("keyword")); val != "" {
		keyword = &val
	}
	clusterIDStr := c.Query("cluster_id")
	var clusterID *int
	if val, err1 := strconv.Atoi(clusterIDStr); err1 == nil && val > 0 {
		clusterID = &val
	}
	workloadIDStr := strings.TrimSpace(c.Query("workload_id"))
	var workloadIDs []uint
	idStrs := strings.Split(workloadIDStr, ",")
	for _, idStr := range idStrs {
		if val, err1 := strconv.Atoi(idStr); err1 == nil && val > 0 {
			workloadIDs = append(workloadIDs, uint(val))
		}
	}
	count, data, err := k8s.GetPods(offset, limit, clusterID, ip, keyword, workloadIDs...)
	if err != nil {
		app.Log().Error("获取数据失败", "err", err)
		app.FailedResponseMsg(c, "获取数据失败")
		return
	}
	newData := []PodRow{}
	tmpStore := map[uint]string{}
	for _, v := range data {
		v.Labels, _ = v.GetLabels()
		if val, ok := tmpStore[v.ClusterID]; ok {
			newData = append(newData, PodRow{Pod: v, ClusterName: val})
		} else {
			if name, err := k8s.GetClusterName(v.ClusterID); err == nil {
				tmpStore[v.ClusterID] = name
				newData = append(newData, PodRow{Pod: v, ClusterName: name})
			} else {
				newData = append(newData, PodRow{Pod: v, ClusterName: ""})
			}
		}
	}
	app.SuccessResponseDataCount(c, newData, count)

}
