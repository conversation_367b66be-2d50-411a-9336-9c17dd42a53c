package nginx

import (
	"cmdb/app"
	"cmdb/app/service/appops/nginx"
	"cmdb/app/service/audit"
	"strconv"

	"github.com/gin-gonic/gin"
)

func GetNginxBackends(c *gin.Context) {
	var ip, serverName, keyword *string
	if val := c.Query("ip"); val != "" {
		ip = &val
	}
	if val := c.<PERSON>("keyword"); val != "" {
		keyword = &val
	}
	if val := c.Query("server_name"); val != "" {
		serverName = &val
	}
	limit, _ := strconv.Atoi(c.<PERSON>("limit", "10"))
	page, _ := strconv.Atoi(c.<PERSON>("page", "1"))
	offset := (page - 1) * limit
	count, data, err := nginx.GetNginxBackends(offset, limit, ip, serverName, keyword)
	if err != nil {
		app.Log().Error("获取nginx后端配置失败", "err", err)
		app.FailedResponseMsg(c, "获取nginx后端配置失败")
		return
	}
	app.SuccessResponseDataCount(c, data, count)
}

func SyncNginxs(c *gin.Context) {
	go func() {
		err := nginx.Sync()
		if err != nil {
			app.Log().Error("同步nginx后端配置失败", "err", err)
		}
	}()
	audit.LogAPIOP(c, "应用运维", "同步nginx后端配置")
	app.SuccessResponseMsg(c, "同步nginx后端配置，提交成功，正在处理中...")
}
