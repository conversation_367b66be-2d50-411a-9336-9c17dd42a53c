package workflow

import (
	"cmdb/app"
	"cmdb/app/service/audit"
	"cmdb/app/service/auth"
	"cmdb/app/service/workflow"
	"strconv"

	"github.com/gin-gonic/gin"
)

// 工单管理员可以操作的接口，帮忙申请工单的那种

// 申请工单
func AdminApplyOrder(c *gin.Context) {
	// 将参数转换为int类型
	code := c.Param("code")
	if code == "" {
		// 记录日志
		app.Log().Debug("申请工单时，存在非法参数", "code", c.<PERSON>m("code"))
		// 返回错误信息
		app.FailedResponseMsg(c, "存在非法参数")
		return
	}
	// 获取申请人
	applicantID, err := strconv.Atoi(c.Param("applicant_id"))
	if err != nil {
		// 记录日志
		app.Log().Debug("申请工单时，存在非法参数", "applicant_id", c.<PERSON>("applicant_id"))
		// 返回错误信息
		app.FailedResponseMsg(c, "存在非法参数")
		return
	}
	// 根据id获取工单类型
	flow, err := workflow.GetFlowByCode(code)
	if err != nil {
		// 记录日志
		app.Log().Error("申请工单时，工单类型不存在", "err", err, "code", code)
		// 返回错误信息
		app.FailedResponseMsg(c, "工单类型不存在")
		return
	}
	// 生成表单
	form := flow.ExtType.GenForm()
	// 绑定参数
	if err := c.ShouldBind(form); err != nil {
		// 记录日志
		app.Log().Debug("申请工单时，参数绑定失败", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "参数绑定失败")
		return
	}
	// 获取登录用户
	loginUser, err := auth.GetLoginUser(c)
	if err != nil {
		// 记录日志
		app.Log().Error("申请工单时，获取用户信息失败", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "获取用户信息失败")
		return
	}
	// 根据id获取申请人
	applicant, err := workflow.GetApplicantByID(uint(applicantID))
	if err != nil {
		// 记录日志
		app.Log().Error("申请工单时，查询申请人失败", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "查询申请人失败")
		return
	}
	// 创建工单
	err = form.Create(applicant, flow, &loginUser)
	if err != nil {
		// 记录日志
		app.Log().Error("申请工单时，创建工单失败", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "创建工单失败："+err.Error())
		return
	}
	// 记录操作日志
	audit.LogAPIOP(c, "工单管理", loginUser.Name+"("+loginUser.Username+")替"+applicant.Name+"("+applicant.Username+")申请工单:"+flow.Name)
	// 返回成功信息
	app.SuccessResponseMsg(c, "提交成功")
}

// 获取工单流程
func GetAdminOrderProcesses(c *gin.Context) {
	// 将参数转换为int类型
	code := c.Param("code")
	if code == "" {
		// 记录日志
		app.Log().Debug("获取工单流程时，存在非法参数", "code", c.Param("code"))
		// 返回错误信息
		app.FailedResponseMsg(c, "存在非法参数")
		return
	}
	// 获取申请人
	applicantID, err := strconv.Atoi(c.Param("applicant_id"))
	if err != nil {
		// 记录日志
		app.Log().Debug("获取工单流程时，存在非法参数", "applicant_id", c.Param("applicant_id"))
		// 返回错误信息
		app.FailedResponseMsg(c, "存在非法参数")
		return
	}
	// 根据id获取工单类型
	flow, err := workflow.GetFlowByCode(code)
	if err != nil {
		// 记录日志
		app.Log().Debug("获取工单流程时，工单类型不存在", "err", err, "code", c.Param("code"))
		// 返回错误信息
		app.FailedResponseMsg(c, "工单类型不存在")
		return
	}
	// 获取登录用户
	loginUser, err := auth.GetLoginUser(c)
	if err != nil {
		// 记录日志
		app.Log().Error("获取工单流程时，获取用户信息失败", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "获取用户信息失败")
		return
	}
	// 根据id获取用户信息
	loginUser, err = auth.GetUserByID(int(loginUser.ID))
	if err != nil {
		// 记录日志
		app.Log().Error("获取工单流程时，获取用户信息失败", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "获取用户信息失败")
		return
	}
	// 根据id获取申请人
	applicant, err := workflow.GetApplicantByID(uint(applicantID))
	if err != nil {
		// 记录日志
		app.Log().Error("获取工单流程时，查询申请人失败", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "查询申请人失败")
		return
	}
	// 生成流程
	processes, err := flow.GenProcesses(code, applicant, &loginUser)
	if err != nil {
		// 记录日志
		app.Log().Error("获取工单流程时，生成流程失败", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "生成流程失败")
		return
	}
	// 获取流程详情
	psDetails := []workflow.ProcessDetail{}
	for i := range processes {
		p := workflow.ProcessDetail{
			Process: processes[i],
		}
		err = p.Detail()
		if err != nil {
			// 记录日志
			app.Log().Error("获取工单流程时，获取流程详情失败", "err", err)
			// 返回错误信息
			app.FailedResponseMsg(c, "获取流程详情失败")
			return
		}
		psDetails = append(psDetails, p)
	}
	// 返回成功信息
	app.SuccessResponseData(c, psDetails)
}

// 获取用户所有工单
func GetAdminMyAllOrders(c *gin.Context) {
	// 获取ext_type参数
	var extType *workflow.OrderExtType
	if val := c.Query("ext_type"); val != "" {
		extTypeID, err := strconv.Atoi(val)
		if err == nil {
			if et, err := workflow.GetExtTypeByID(extTypeID); err == nil {
				extType = &et
			}
		}
	}
	// 获取申请人ID
	applicantID, err := strconv.Atoi(c.Query("applicant_id"))
	if err != nil {
		// 记录日志
		app.Log().Debug("获取用户信息失败", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "获取用户信息失败")
		return
	}
	// 创建Applicant对象
	applicant, err := workflow.GetApplicantByID(uint(applicantID))
	if err != nil {
		// 记录错误日志
		app.Log().Error("获取用户信息失败", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "获取用户信息失败")
		return
	}
	// 获取用户所有工单
	data, err := applicant.GetMyAllOrders(extType)
	if err != nil {
		// 记录错误日志
		app.Log().Error("获取用户工单失败", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "获取用户工单失败")
		return
	}
	// 返回成功信息
	app.SuccessResponseData(c, data)
}
