package mysql

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/setting"
	"cmdb/pkg/utils"
	"errors"
	"fmt"
	"strings"

	"database/sql"

	_ "github.com/go-sql-driver/mysql"
)

// SQLCheckForm 检查SQL语句表单
type SQLCheckForm struct {
	ProjectID  uint   `json:"project_id" binding:"required"`
	DBName     string `json:"db_name" binding:"required"`
	SQLContent string `json:"sql_content" binding:"required"`
}

// SQLExcuteForm 执行SQL语句表单
type SQLExcuteForm struct {
	ProjectID  uint   `json:"project_id" binding:"required"`
	DBName     string `json:"db_name" binding:"required"`
	SQLContent string `json:"sql_content" binding:"required"`
	Backup     bool   `json:"backup"`
}

// CheckSQL 检查sql语句
func CheckSQL(sqlCheckForm *SQLCheckForm) (result []map[string]string, err error) {
	goInceptionHost := setting.GetSettingValue("goInception", "host")
	goInceptionPort := setting.GetSettingValue("goInception", "port")
	goInceptionUser := setting.GetSettingValue("goInception", "user")
	project := Project{}
	err = app.DB().Select(
		"id", "admin_user", "admin_password", "port", "link",
	).Where("id = ?", sqlCheckForm.ProjectID).First(&project).Error
	if err != nil {
		return
	}
	dbop, err := sql.Open("mysql", fmt.Sprintf("%s:@tcp(%s:%s)/", goInceptionUser, goInceptionHost, goInceptionPort))
	if err != nil {
		return
	}
	defer dbop.Close()
	password, _ := utils.Base64Decode(project.AdminPassword)
	checkSQL := fmt.Sprintf(`/*--user=%s;--password=%s;--host=%s;--port=%d;--check=1;*/
    inception_magic_start;
	use  %s;
	%s
	inception_magic_commit;`, project.AdminUser, password, project.Link, project.Port, sqlCheckForm.DBName, sqlCheckForm.SQLContent)
	fmt.Println(checkSQL)
	rows, err := dbop.Query(checkSQL)
	if err != nil {
		return
	}
	defer rows.Close()
	fmt.Println(rows.Columns())
	result = []map[string]string{}
	errMessages := []string{}
	for rows.Next() {
		var order_id, stage, error_level, stage_status, error_message, sql, affected_rows, sequence, backup_dbname, execute_time, sqlsha1, backup_time, needMerge []uint8
		err = rows.Scan(&order_id, &stage, &error_level, &stage_status, &error_message, &sql, &affected_rows, &sequence, &backup_dbname, &execute_time, &sqlsha1, &backup_time, &needMerge)
		if string(error_level) != "0" {
			errMessages = append(errMessages, string(sql)+" : "+string(error_message))
		}
		result = append(result, map[string]string{
			"order_id":      string(order_id),
			"stage":         string(stage),
			"error_level":   string(error_level),
			"stage_status":  string(stage_status),
			"error_message": string(error_message),
			"sql":           string(sql),
			"affected_rows": string(affected_rows),
			"sequence":      string(sequence),
			"backup_dbname": string(backup_dbname),
			"execute_time":  string(execute_time),
			"sqlsha1":       string(sqlsha1),
			"backup_time":   string(backup_time),
			"need_merge":    string(needMerge),
		})
	}
	if len(errMessages) > 0 {
		err = errors.New(strings.Join(errMessages, "\n"))
	}
	return
}

var (
	ErrOnlyTestingExec = errors.New("只有测试环境可以运行")
)

// 执行sql语句
func ExecSQL(sqlExcuteForm SQLExcuteForm) (result []map[string]string, err error) {
	project := Project{}
	err = app.DB().Select(
		"id", "admin_user", "admin_password", "port", "link", "env",
	).Where("id = ?", sqlExcuteForm.ProjectID).Take(&project).Error
	if err != nil {
		return
	}
	// 只有测试环境才执行
	if project.Env != asset.TESTENV {
		err = ErrOnlyTestingExec
		return
	}
	se, err := setting.GetGoInceptionSetting()
	if err != nil {
		return
	}
	dbop, err := sql.Open("mysql", fmt.Sprintf("%s:@tcp(%s:%d)/", se.User, se.Host, se.Port))
	if err != nil {
		return
	}
	defer dbop.Close()
	backupArgs := ""
	if sqlExcuteForm.Backup {
		backupArgs = "--backup=1;"
	}
	password, _ := utils.Base64Decode(project.AdminPassword)
	checkSQL := fmt.Sprintf(`/*--user=%s;--password=%s;--host=%s;--port=%d;--execute=1;%s;*/
    inception_magic_start;
	use  %s;
	%s
	inception_magic_commit;`, project.AdminUser, password, project.Link, project.Port, backupArgs, sqlExcuteForm.DBName, sqlExcuteForm.SQLContent)
	fmt.Println(checkSQL)
	rows, err := dbop.Query(checkSQL)
	if err != nil {
		return
	}
	defer rows.Close()
	result = []map[string]string{}
	errMessages := []string{}
	for rows.Next() {
		var order_id, stage, error_level, stage_status, error_message, sql, affected_rows, sequence, backup_dbname, execute_time, sqlsha1, backup_time, needMerge []uint8
		err = rows.Scan(&order_id, &stage, &error_level, &stage_status, &error_message, &sql, &affected_rows, &sequence, &backup_dbname, &execute_time, &sqlsha1, &backup_time, &needMerge)
		if string(error_level) != "0" {
			errMessages = append(errMessages, string(sql)+" : "+string(error_message))
		}
		result = append(result, map[string]string{
			"order_id":      string(order_id),
			"stage":         string(stage),
			"error_level":   string(error_level),
			"stage_status":  string(stage_status),
			"error_message": string(error_message),
			"sql":           string(sql),
			"affected_rows": string(affected_rows),
			"sequence":      string(sequence),
			"backup_dbname": string(backup_dbname),
			"execute_time":  string(execute_time),
			"sqlsha1":       string(sqlsha1),
			"backup_time":   string(backup_time),
			"need_merge":    string(needMerge),
		})
	}
	if len(errMessages) > 0 {
		err = errors.New(strings.Join(errMessages, "\n"))
	}
	return
}
