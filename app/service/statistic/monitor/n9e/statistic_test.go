package n9e

import (
	"database/sql"
	"testing"
	"time"
)

func TestCalculateRecoveryRate(t *testing.T) {
	tests := []struct {
		name           string
		recoveredCount int64
		totalCount     int64
		expected       float64
	}{
		{
			name:           "正常恢复率计算",
			recoveredCount: 80,
			totalCount:     100,
			expected:       80.0,
		},
		{
			name:           "完全恢复",
			recoveredCount: 100,
			totalCount:     100,
			expected:       100.0,
		},
		{
			name:           "无恢复",
			recoveredCount: 0,
			totalCount:     100,
			expected:       0.0,
		},
		{
			name:           "总数为0",
			recoveredCount: 0,
			totalCount:     0,
			expected:       0.0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := CalculateRecoveryRate(tt.recoveredCount, tt.totalCount)
			if result != tt.expected {
				t.<PERSON><PERSON>rf("CalculateRecoveryRate() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestCalculateAvgDuration(t *testing.T) {
	tests := []struct {
		name        string
		avgDuration sql.NullFloat64
		count       int64
		expected    float64
	}{
		{
			name:        "正常平均时长（数据库已计算平均值）",
			avgDuration: sql.NullFloat64{Float64: 360.0, Valid: true},
			count:       10,
			expected:    360.0,
		},
		{
			name:        "NULL值处理",
			avgDuration: sql.NullFloat64{Float64: 0, Valid: false},
			count:       10,
			expected:    0.0,
		},
		{
			name:        "负数处理（异常情况）",
			avgDuration: sql.NullFloat64{Float64: -100.0, Valid: true},
			count:       5,
			expected:    0.0,
		},
		{
			name:        "零值处理",
			avgDuration: sql.NullFloat64{Float64: 0.0, Valid: true},
			count:       5,
			expected:    0.0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := CalculateAvgDuration(tt.avgDuration, tt.count)
			if result != tt.expected {
				t.Errorf("CalculateAvgDuration() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestCalculateTotalAvgDuration(t *testing.T) {
	tests := []struct {
		name          string
		totalDuration sql.NullFloat64
		count         int64
		expected      float64
	}{
		{
			name:          "正常总时长计算",
			totalDuration: sql.NullFloat64{Float64: 3600.0, Valid: true},
			count:         10,
			expected:      360.0,
		},
		{
			name:          "NULL值处理",
			totalDuration: sql.NullFloat64{Float64: 0, Valid: false},
			count:         10,
			expected:      0.0,
		},
		{
			name:          "计数为0",
			totalDuration: sql.NullFloat64{Float64: 3600.0, Valid: true},
			count:         0,
			expected:      0.0,
		},
		{
			name:          "负数总时长处理",
			totalDuration: sql.NullFloat64{Float64: -3600.0, Valid: true},
			count:         10,
			expected:      0.0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := CalculateTotalAvgDuration(tt.totalDuration, tt.count)
			if result != tt.expected {
				t.Errorf("CalculateTotalAvgDuration() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestGetGroupNameWithDefault(t *testing.T) {
	tests := []struct {
		name      string
		groupName string
		expected  string
	}{
		{
			name:      "正常组名",
			groupName: "生产环境",
			expected:  "生产环境",
		},
		{
			name:      "空组名",
			groupName: "",
			expected:  "(未设置资源组)",
		},
		{
			name:      "空格组名",
			groupName: "   ",
			expected:  "   ",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GetGroupNameWithDefault(tt.groupName)
			if result != tt.expected {
				t.Errorf("GetGroupNameWithDefault() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestSeverityType_String(t *testing.T) {
	tests := []struct {
		name     string
		severity SeverityType
		expected string
	}{
		{
			name:     "紧急告警",
			severity: SeverityEmergency,
			expected: "Emergency",
		},
		{
			name:     "警告告警",
			severity: SeverityWarning,
			expected: "Warning",
		},
		{
			name:     "通知告警",
			severity: SeverityNotice,
			expected: "Notice",
		},
		{
			name:     "未知类型",
			severity: SeverityType(99),
			expected: "Unknown",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.severity.String()
			if result != tt.expected {
				t.Errorf("SeverityType.String() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestAlertHisEvent_TableName(t *testing.T) {
	event := AlertHisEvent{}
	expected := "n9e.alert_his_event"
	result := event.TableName()
	if result != expected {
		t.Errorf("AlertHisEvent.TableName() = %v, want %v", result, expected)
	}
}

func TestAlertCurEvent_TableName(t *testing.T) {
	event := AlertCurEvent{}
	expected := "n9e.alert_cur_event"
	result := event.TableName()
	if result != expected {
		t.Errorf("AlertCurEvent.TableName() = %v, want %v", result, expected)
	}
}

func TestFormatUnixTime(t *testing.T) {
	tests := []struct {
		name      string
		timestamp int64
		expected  string
	}{
		{
			name:      "零时间戳",
			timestamp: 0,
			expected:  "",
		},
		{
			name:      "正常时间戳",
			timestamp: 1640995200, // 2022-01-01 00:00:00 UTC
			expected:  time.Unix(1640995200, 0).Format("2006-01-02 15:04:05"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := FormatUnixTime(tt.timestamp)
			if result != tt.expected {
				t.Errorf("FormatUnixTime() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestFormatDuration(t *testing.T) {
	tests := []struct {
		name     string
		seconds  int64
		expected string
	}{
		{
			name:     "零秒",
			seconds:  0,
			expected: "0秒",
		},
		{
			name:     "60秒",
			seconds:  60,
			expected: "1分钟",
		},
		{
			name:     "3661秒",
			seconds:  3661,
			expected: "1小时1分钟1秒",
		},
		{
			name:     "86400秒",
			seconds:  86400,
			expected: "1天",
		},
		{
			name:     "90061秒",
			seconds:  90061,
			expected: "1天1小时1分钟1秒",
		},
		{
			name:     "负数",
			seconds:  -100,
			expected: "0秒",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := FormatDuration(tt.seconds)
			if result != tt.expected {
				t.Errorf("FormatDuration() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestCalculateCurrentDuration(t *testing.T) {
	now := time.Now().Unix()

	tests := []struct {
		name        string
		triggerTime int64
		expectZero  bool
	}{
		{
			name:        "正常触发时间",
			triggerTime: now - 3600, // 1小时前
			expectZero:  false,
		},
		{
			name:        "零触发时间",
			triggerTime: 0,
			expectZero:  true,
		},
		{
			name:        "负触发时间",
			triggerTime: -1,
			expectZero:  true,
		},
		{
			name:        "未来触发时间",
			triggerTime: now + 3600, // 1小时后
			expectZero:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := CalculateCurrentDuration(tt.triggerTime)
			if tt.expectZero && result != 0 {
				t.Errorf("CalculateCurrentDuration() = %v, want 0", result)
			}
			if !tt.expectZero && result <= 0 {
				t.Errorf("CalculateCurrentDuration() = %v, want > 0", result)
			}
		})
	}
}

// 基准测试
func BenchmarkCalculateRecoveryRate(b *testing.B) {
	for i := 0; i < b.N; i++ {
		CalculateRecoveryRate(80, 100)
	}
}

func BenchmarkCalculateAvgDuration(b *testing.B) {
	totalDuration := sql.NullFloat64{Float64: 3600.0, Valid: true}
	for i := 0; i < b.N; i++ {
		CalculateAvgDuration(totalDuration, 10)
	}
}

func BenchmarkFormatDuration(b *testing.B) {
	for i := 0; i < b.N; i++ {
		FormatDuration(90061)
	}
}

// 示例函数
func ExampleCalculateRecoveryRate() {
	rate := CalculateRecoveryRate(80, 100)
	println(rate) // 输出: 80.0
}

func ExampleSeverityType_String() {
	severity := SeverityEmergency
	println(severity.String()) // 输出: Emergency
}

func ExampleFormatDuration() {
	duration := FormatDuration(3661)
	println(duration) // 输出: 1小时1分钟1秒
}
