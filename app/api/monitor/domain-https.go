package monitor

import (
	"cmdb/app"
	"cmdb/app/service/audit"
	"cmdb/app/service/monitor/https"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

func GetDomainHttpsInfo(c *gin.Context) {
	id, err := strconv.Atoi(c.<PERSON>m("id"))
	if err != nil {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	domainHttps, err := https.GetDomainHttpsByID(id)
	if err != nil {
		app.Log().Error("获取域名https失败", "err", err)
		app.FailedResponseMsg(c, "获取域名https失败")
		return
	}
	info, err := domainHttps.GetInfo()
	if err != nil {
		app.Log().Error("获取域名https信息失败", "err", err)
		app.FailedResponseMsg(c, "获取域名https信息失败")
		return
	}
	app.SuccessResponseData(c, info)
}

func GetDomainHttps(c *gin.Context) {
	var keyword *string
	var enableMonitor *bool
	var isSecure *int
	if val := strings.TrimSpace(c.Query("keyword")); val != "" {
		keyword = &val
	}
	if val := c.Query("enable_monitor"); val != "" {
		var boolVal bool
		if val == "true" {
			boolVal = true
			enableMonitor = &boolVal
		} else if val == "false" {
			boolVal = false
			enableMonitor = &boolVal
		}
	}
	if val := strings.TrimSpace(c.Query("is_secure")); val != "" {
		id, err1 := strconv.Atoi(val)
		if err1 == nil {
			isSecure = &id
		}
	}
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	offset := (page - 1) * limit
	count, data, err := https.GetDomainHttps(offset, limit, keyword, enableMonitor, isSecure)
	if err != nil {
		app.Log().Error("获取域名https列表失败", "err", err)
		app.FailedResponseMsg(c, "获取域名https列表失败")
		return
	}
	app.SuccessResponseDataCount(c, data, count)
}

type MonitorDomainHttpsForm struct {
	IDs           []uint `json:"ids" binding:"required"`
	EnableMonitor *bool  `json:"enable_monitor"`
}

func BatSwitchNoticeDomainHttps(c *gin.Context) {
	var form MonitorDomainHttpsForm
	if err := c.ShouldBindJSON(&form); err != nil {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	okCount, err := https.BatSwitchNoticeDomainHttps(*form.EnableMonitor, form.IDs...)
	if err != nil {
		app.Log().Error("监控域名https失败", "err", err)
		app.FailedResponseMsg(c, "监控域名https失败")
		return
	}
	app.SuccessResponseMsg(c, fmt.Sprintf("成功 %d 个，失败 %d 个", okCount, len(form.IDs)-int(okCount)))
}

func DeleteDomainHttps(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	domainHttps, err := https.GetDomainHttpsByID(id)
	if err != nil {
		app.Log().Error("获取域名https失败", "err", err)
		app.FailedResponseMsg(c, "获取域名https失败")
		return
	}
	err = domainHttps.Delete()
	if err != nil {
		app.Log().Error("删除域名https失败", "err", err)
		app.FailedResponseMsg(c, "删除域名https失败")
		return
	}
	audit.LogAPIOP(c, "监控管理", fmt.Sprintf("删除域名https %s", domainHttps.Domain))
	app.SuccessResponseMsg(c, "删除成功")
}

func UpdateDomainHttps(c *gin.Context) {
	var form https.DomainHttpsForm
	if err := c.ShouldBindJSON(&form); err != nil {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	domainHttps, err := https.GetDomainHttpsByID(id)
	if err != nil {
		app.Log().Error("获取域名https失败", "err", err)
		app.FailedResponseMsg(c, "获取域名https失败")
		return
	}
	err = domainHttps.Update(form)
	if err != nil {
		app.Log().Error("更新域名https失败", "err", err)
		app.FailedResponseMsg(c, "更新域名https失败")
		return
	}
	audit.LogAPIOP(c, "监控管理", fmt.Sprintf("更新域名https %s", domainHttps.Domain))
	app.SuccessResponseMsg(c, "更新成功")
}

func CreateDomainHttps(c *gin.Context) {
	var form https.DomainHttpsForm
	if err := c.ShouldBindJSON(&form); err != nil {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	err := form.Create()
	if err != nil {
		msg := "创建域名https失败"
		if err == https.ErrDomainHttpsExist {
			msg = "域名已存在"
		} else {
			app.Log().Error(msg, "err", err)
		}
		app.FailedResponseMsg(c, msg)
		return
	}
	audit.LogAPIOP(c, "监控管理", fmt.Sprintf("创建域名https %s", form.Domain))
	app.SuccessResponseMsg(c, "创建成功")
}

func AutoCreateDomainHttps(c *gin.Context) {
	var err error
	// 创建一个done通道
	done := make(chan struct{}, 1)
	go func() {
		err = https.AutoImportDomain()
		if err != nil {
			app.Log().Error("自动创建域名https失败", "err", err)
			return
		}
		done <- struct{}{}
	}()
	ticker := time.NewTicker(500 * time.Millisecond)
	select {
	case <-done:
		if err != nil {
			app.FailedResponseMsg(c, err.Error())
		} else {
			app.SuccessResponseMsg(c, "同步成功")
			return
		}
		break
	case <-ticker.C:
		app.SuccessResponseMsg(c, "任务提交成功")
		break
	}
}

func AutoCheckDomainHttps(c *gin.Context) {
	var err error
	// 创建一个done通道
	done := make(chan struct{}, 1)
	go func() {
		err = https.AutoUpdateAndCheckDomainHttps()
		if err != nil {
			app.Log().Error("自动检查域名https失败", "err", err)
			app.FailedResponseMsg(c, "自动检查域名https失败")
			return
		}
		done <- struct{}{}
	}()
	ticker := time.NewTicker(500 * time.Millisecond)
	select {
	case <-done:
		if err != nil {
			app.FailedResponseMsg(c, err.Error())
		} else {
			app.SuccessResponseMsg(c, "同步成功")
		}
		break
	case <-ticker.C:
		app.SuccessResponseMsg(c, "任务提交成功")
		break
	}
}

type BatCheckDomainHttpsIsSecureForm struct {
	IDs []int `json:"ids" binding:"required"`
}

func BatCheckDomainHttpsIsSecure(c *gin.Context) {
	var form BatCheckDomainHttpsIsSecureForm
	if err := c.ShouldBindJSON(&form); err != nil {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	done := make(chan struct{}, 1)
	go func() {
		err := https.BatCheckDomainHttpsIsSecure(form.IDs...)
		if err != nil {
			app.Log().Error("批量检查域名证书失败", "err", err)
			app.FailedResponseMsg(c, "批量检查域名证书失败")
			return
		}
		done <- struct{}{}
	}()
	ticker := time.NewTicker(500 * time.Millisecond)
	select {
	case <-done:
		app.SuccessResponseMsg(c, "批量检查成功")
		break
	case <-ticker.C:
		app.SuccessResponseMsg(c, "任务提交成功")
		break
	}
}

type BatDeleteDomainHttpsForm struct {
	IDs []uint `json:"ids" binding:"required"`
}

func BatDeleteDomainHttps(c *gin.Context) {
	var form BatDeleteDomainHttpsForm
	if err := c.ShouldBindJSON(&form); err != nil {
		app.Log().Error("参数错误", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	okCount, err := https.BatDeleteDomainHttps(form.IDs...)
	if err != nil {
		app.Log().Error("删除监控域名https失败", "err", err)
		app.FailedResponseMsg(c, "删除失败")
		return
	}
	app.SuccessResponseMsg(c, fmt.Sprintf("成功 %d 个，失败 %d 个", okCount, len(form.IDs)-int(okCount)))
}
