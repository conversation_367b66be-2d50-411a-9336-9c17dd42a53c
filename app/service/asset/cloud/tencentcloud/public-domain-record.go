package tencentcloud

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/asset/cloud"
	"cmdb/pkg/utils"
	"errors"
	"strconv"
	"time"

	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	dnspod "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/dnspod/v20210323"
	"gorm.io/gorm"
)

func (a Account) syncPublicDomainsRecords() (result cloud.SyncResults, err error) {
	domains := []asset.PublicDomain{}
	err = app.DB().Where("account_id = ?", a.ID).Find(&domains).Error
	if err != nil {
		return
	}
	for _, domain := range domains {
		// 同步域名记录
		r, err := a.syncPagePublicDomainsRecords(domain, 0)
		// 创建同步结果对象
		sr := cloud.SyncResult{
			Key: domain.Name,
		}
		if err == nil {
			// 同步成功
			sr.Result = "同步成功"
			// 回收过期主机
			err = asset.ExpiredPublicDomainRecords(domain.DomainID, time.Now().Add(-10*time.Minute))
			if err != nil {
				// 回收失败
				sr.Result = "同步成功，但是回收Domain record失败" + err.Error()
			}
		} else {
			// 同步失败
			sr.Result = r + " " + err.Error()
		}
		// 将同步结果添加到结果集
		result = append(result, sr)
	}
	return
}

func (a Account) syncPagePublicDomainsRecords(domain asset.PublicDomain, offset uint64) (result string, err error) {
	// 解码AccessSecret
	accessSecret, err := utils.Base64Decode(a.AccessSecret)
	if err != nil {
		err = errors.New("AccessSecret解码失败")
		return
	}
	// 创建凭证
	credential := common.NewCredential(
		a.AccessKey,
		accessSecret,
	)
	// 创建客户端配置
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = "dnspod.tencentcloudapi.com"
	// 创建客户端
	client, err := dnspod.NewClient(credential, "", cpf)
	if err != nil {
		err = errors.New("创建客户端失败")
		return
	}
	request := dnspod.NewDescribeRecordListRequest()
	request.Domain = common.StringPtr(domain.Name)
	request.Offset = common.Uint64Ptr(offset)
	request.Limit = common.Uint64Ptr(3000)
	response, err := client.DescribeRecordList(request)
	if err != nil {
		err = errors.New("获取域名记录失败")
		return
	}
	now := time.Now()
	// 解析结果
	for _, record := range response.Response.RecordList {
		recordId := ""
		if record.RecordId != nil {
			recordId = strconv.FormatUint(*record.RecordId, 10)
		}
		name := ""
		if record.Name != nil {
			name = *record.Name
		}
		value := ""
		if record.Value != nil {
			value = *record.Value
		}
		ttl := 0
		if record.TTL != nil {
			ttl = int(*record.TTL)
		}
		recordType := "A"
		if record.Type != nil {
			recordType = *record.Type
		}
		line := ""
		if record.Line != nil {
			line = *record.Line
		}
		status := ""
		if record.Status != nil {
			status = *record.Status
		}
		remark := ""
		if record.Remark != nil {
			remark = *record.Remark
		}
		// 获取创建时间
		createdOn, _ := time.ParseInLocation("2006-01-02 15:04:05", *record.UpdatedOn, time.Local)
		// 获取更新时间
		updatedOn, _ := time.ParseInLocation("2006-01-02 15:04:05", *record.UpdatedOn, time.Local)
		exist := asset.PublicDomainRecord{}
		genErr := app.DB().Where("account_id = ? AND domain_id = ? AND record_id = ?", a.ID, domain.DomainID, recordId).Take(&exist).Error
		if errors.Is(genErr, gorm.ErrRecordNotFound) {
			err = app.DB().Create(&asset.PublicDomainRecord{
				AccountID: a.ID,
				DomainID:  domain.DomainID,
				RecordID:  recordId,
				Rr:        name,
				Value:     value,
				TTL:       ttl,
				Type:      recordType,
				Line:      line,
				Status:    status,
				Remark:    remark,
				CreatedAt: createdOn,
				UpdatedAt: updatedOn,
				SyncTime:  now,
			}).Error
		} else if genErr == nil {
			updateMap := map[string]any{
				"sync_time": now,
			}
			if exist.Rr != name {
				updateMap["rr"] = name
			}
			if exist.Value != value {
				updateMap["value"] = value
			}
			if exist.TTL != ttl {
				updateMap["ttl"] = ttl
			}
			if exist.Type != recordType {
				updateMap["type"] = recordType
			}
			if exist.Line != line {
				updateMap["line"] = line
			}
			if exist.Status != status {
				updateMap["status"] = status
			}
			if exist.Remark != remark {
				updateMap["remark"] = remark
			}
			if !exist.UpdatedAt.Equal(updatedOn) {
				updateMap["updated_at"] = updatedOn
			}
			err = app.DB().Model(&exist).Updates(updateMap).Error
		} else {
			err = genErr
		}
		if err != nil {
			return
		}
	}
	if response.Response.RecordCountInfo.TotalCount != nil && *response.Response.RecordCountInfo.TotalCount > offset+3000 {
		offset += 3000
		result, err = a.syncPagePublicDomainsRecords(domain, offset)
	}
	return
}
