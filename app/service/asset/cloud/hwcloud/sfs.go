package hwcloud

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/pkg/utils"
	"strconv"

	"github.com/huaweicloud/huaweicloud-sdk-go-v3/core/auth/basic"
	sfs "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/sfsturbo/v1"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/sfsturbo/v1/model"
	region "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/sfsturbo/v1/region"
)

func StatAllAccountSFSStorages() (storageTotal int64, err error) {
	accounts, err := GetAllAccounts()
	if err != nil {
		return
	}
	for _, account := range accounts {
		storage, err := account.getAllSFSStorages()
		if err != nil {
			app.Log().Error("统计华为云SFS存储容量异常", "account", account.Name, "err", err.Error())
			continue
		}
		storageTotal += storage
	}
	return
}

func (a Account) getAllSFSStorages() (storageTotal int64, err error) {
	dcs, err := asset.GetDatacentersByCloudType(asset.HuaweiCloudType)
	if err != nil {
		return
	}
	for _, dc := range dcs {
		app.Log().Info("开始统计华为云SFS存储容量", "账户", a.Name, "数据中心", dc.Name)
		count, err := a.getSFSStoragesByRegion(dc.Code, 0)
		app.Log().Info("完成统计华为云SFS存储容量", "账户", a.Name, "数据中心", dc.Name, "存储数量", count)
		if err != nil {
			app.Log().Error("统计华为云SFS存储容量异常", "account", a.Name, "datacenter", dc.Name, "err", err.Error())
			continue
		}
		storageTotal += int64(count)
	}
	return
}

func (a Account) getSFSStoragesByRegion(regionCode string, offset int64) (storageTotal float64, err error) {
	secret, err := utils.Base64Decode(a.AccessSecret)
	if err != nil {
		app.Log().Error("解密华为云账号密钥异常", "account", a.Name, "err", err.Error())
		return
	}
	auth, err := basic.NewCredentialsBuilder().WithAk(a.AccessKey).WithSk(secret).SafeBuild()
	if err != nil {
		app.Log().Error("构建认证失败", "account", a.Name, "err", err.Error())
		return
	}
	regionID, err := region.SafeValueOf(regionCode)
	if err != nil {
		app.Log().Error("非法regionID", "account", a.Name, "err", err.Error())
		return
	}
	clientBuild, err := sfs.SFSTurboClientBuilder().WithRegion(regionID).WithCredential(auth).SafeBuild()
	if err != nil {
		app.Log().Error("构建客户端失败", "account", a.Name, "err", err.Error())
		return
	}
	client := sfs.NewSFSTurboClient(clientBuild)
	limit := int64(100)
	request := &model.ListSharesRequest{
		Limit:  &limit,
		Offset: &offset,
	}
	response, err := client.ListShares(request)
	if err != nil {
		app.Log().Error("获取SFS存储列表失败", "account", a.Name, "err", err.Error())
		return
	}
	for _, volume := range *response.Shares {
		size, err := strconv.ParseFloat(*volume.Size, 64)
		if err != nil {
			app.Log().Error("转换SFS存储容量失败", "account", a.Name, "err", err.Error())
			continue
		}
		storageTotal += size * 1024 * 1024 * 1024
	}
	if response.Count != nil && int64(*response.Count) > offset+limit {
		subStorageTotal, err := a.getSFSStoragesByRegion(regionCode, offset+limit)
		if err != nil {
			app.Log().Error("列举华为云账号SFS存储异常", "account", a.Name, "err", err.Error())
		}
		storageTotal += subStorageTotal
	}
	return
}
