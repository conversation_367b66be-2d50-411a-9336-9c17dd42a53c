package hwcloud

import (
	"cmdb/app"
	"cmdb/pkg/utils"
	"strconv"

	"github.com/huaweicloud/huaweicloud-sdk-go-v3/core/auth/global"
	bss "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/bss/v2"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/bss/v2/model"
	region "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/bss/v2/region"
	"github.com/shopspring/decimal"
)

type MonthlyBill struct {
	// ID        uint `gorm:"column:id;primary_key;auto_increment;comment:主键ID"`
	AccountID uint `gorm:"column:account_id;index;comment:账户ID"`
	// 资源详单数据所在账期，东八区时间，格式为YYYY-MM。 例如2020-01。
	Cycle *string `gorm:"column:cycle;type:varchar(255);index;comment:账期" json:"cycle,omitempty"`
	// 消费日期，东八区时间，格式为YYYY-MM-DD。  说明： 当statistic_type=2时该字段才有值，否则返回null。
	BillDate *string `gorm:"column:bill_date;type:varchar(255);comment:消费日期" json:"bill_date,omitempty"`
	// 账单类型。 1：消费-新购2：消费-续订3：消费-变更4：退款-退订5：消费-使用8：消费-自动续订9：调账-补偿14：消费-服务支持计划月末扣费16：调账-扣费18：消费-按月付费20：退款-变更23：消费-节省计划抵扣24：退款-包年/包月转按需
	// BillType *int32 `gorm:"column:bill_type;type:int;comment:账单类型" json:"bill_type,omitempty"`
	BillType *string `gorm:"column:bill_type;type:varchar(255);comment:账单类型" json:"bill_type,omitempty"`
	// 消费的客户账号ID。 如果是普通客户或者企业子客户查询消费记录，只能查询到客户自己的消费记录，且此处显示的是客户自己的客户ID。如果是企业主查询消费记录，可以查询到企业主以及企业子客户的消费记录，此处为消费的实际客户ID。如果是企业主自己的消费记录，则为企业主ID；如果是某个企业子客户的消费记录，则此处为企业子账号ID。
	CustomerId *string `gorm:"column:customer_id;type:varchar(255);comment:客户ID" json:"customer_id,omitempty"`
	// 云服务区编码，例如：“cn-north-1”。具体请参见地区和终端节点对应云服务的“区域”列的值。
	Region *string `gorm:"column:region;type:varchar(255);index;comment:云服务区编码" json:"region,omitempty"`
	// 云服务区名称，例如：“华北-北京一”。具体请参见地区和终端节点对应云服务的“区域名称”列的值。
	RegionName *string `gorm:"column:region_name;type:varchar(255);index;comment:云服务区名称" json:"region_name,omitempty"`
	// 云服务类型编码，例如OBS的云服务类型编码为“hws.service.type.obs”。您可以调用查询云服务类型列表接口获取。
	CloudServiceType *string `gorm:"column:cloud_service_type;type:varchar(256);index;comment:云服务类型编码" json:"cloud_service_type,omitempty"`
	// 资源类型编码，例如ECS的VM为“hws.resource.type.vm”。您可以调用查询资源类型列表接口获取。
	ResourceTypeCode *string `gorm:"column:resource_type_code;type:varchar(255);index;comment:资源类型编码" json:"resource_type_code,omitempty"`
	// 云服务类型名称。例如ECS的云服务类型名称为“弹性云服务器”。
	CloudServiceTypeName *string `gorm:"column:cloud_service_type_name;type:varchar(255);comment:云服务类型名称" json:"cloud_service_type_name,omitempty"`
	// 资源类型名称。例如ECS的资源类型名称为“云主机”。
	ResourceTypeName *string `gorm:"column:resource_type_name;type:varchar(255);comment:资源类型名称" json:"resource_type_name,omitempty"`
	// 资源实例ID。
	ResInstanceId *string `gorm:"column:res_instance_id;type:varchar(256);index;comment:资源实例ID" json:"res_instance_id,omitempty"`
	// 资源名称。客户在创建资源的时候，可以输入资源名称，有些资源也可以在管理资源时，修改资源名称。
	ResourceName *string `gorm:"column:resource_name;type:varchar(255);comment:资源名称" json:"resource_name,omitempty"`
	// 资源标签。客户在管理资源的时候，可以设置资源标签。
	ResourceTag *string `gorm:"column:resource_tag;type:varchar(8192);comment:资源标签" json:"resource_tag,omitempty"`
	// SKU编码，在账单中唯一标识一个资源的规格。
	SkuCode *string `gorm:"column:sku_code;type:varchar(255);comment:SKU编码" json:"sku_code,omitempty"`
	// 企业项目标识（企业项目ID）。 default项目对应ID：0未归集（表示该云服务不支持企业项目管理能力）项目对应ID：null其余项目对应ID获取方法请参见[如何获取企业项目ID](https://support.huaweicloud.com/usermanual-em/zh-cn_topic_0126101490.html)。
	EnterpriseProjectId *string `gorm:"column:enterprise_project_id;type:varchar(255);comment:企业项目标识（企业项目ID）" json:"enterprise_project_id,omitempty"`
	// 企业项目名称。
	EnterpriseProjectName *string `gorm:"column:enterprise_project_name;type:varchar(255);index;comment:企业项目名称" json:"enterprise_project_name,omitempty"`

	// 计费模式。 1 : 包年/包月3：按需10：预留实例11：节省计划
	// ChargeMode *int32 `gorm:"column:charge_mode;type:int;comment:计费模式" json:"charge_mode,omitempty"`
	ChargeMode *string `gorm:"column:charge_mode;type:varchar(255);comment:计费模式" json:"charge_mode,omitempty"`

	// 客户购买云服务类型的消费金额，包含代金券、现金券，精确到小数点后2位。  说明： consume_amount的值等于cash_amount，credit_amount，coupon_amount，flexipurchase_coupon_amount，stored_card_amount，bonus_amount，debt_amount，adjustment_amount的总和。
	ConsumeAmount *decimal.Decimal `gorm:"column:consume_amount;type:decimal(10,3);comment:客户购买云服务类型的消费金额" json:"consume_amount,omitempty"`

	// 现金支付金额。
	CashAmount *decimal.Decimal `gorm:"column:cash_amount;type:decimal(10,3);comment:现金支付金额" json:"cash_amount,omitempty"`

	// 信用额度支付金额。
	CreditAmount *decimal.Decimal `gorm:"column:credit_amount;type:decimal(10,3);comment:信用额度支付金额" json:"credit_amount,omitempty"`

	// 代金券支付金额。
	CouponAmount *decimal.Decimal `gorm:"column:coupon_amount;type:decimal(10,3);comment:代金券支付金额" json:"coupon_amount,omitempty"`

	// 现金券支付金额。
	FlexipurchaseCouponAmount *decimal.Decimal `gorm:"column:flexipurchase_coupon_amount;type:decimal(10,3);comment:现金券支付金额" json:"flexipurchase_coupon_amount,omitempty"`

	// 储值卡支付金额。
	StoredCardAmount *decimal.Decimal `gorm:"column:stored_card_amount;type:decimal(10,3);comment:储值卡支付金额" json:"stored_card_amount,omitempty"`

	// 奖励金支付金额（用于现网客户未使用完的奖励金）。
	BonusAmount *decimal.Decimal `gorm:"column:bonus_amount;type:decimal(10,3);comment:奖励金支付金额" json:"bonus_amount,omitempty"`

	// 欠费金额。
	DebtAmount *decimal.Decimal `gorm:"column:debt_amount;type:decimal(10,3);comment:欠费金额" json:"debt_amount,omitempty"`

	// 欠费核销金额。
	AdjustmentAmount *decimal.Decimal `gorm:"column:adjustment_amount;type:decimal(10,3);comment:欠费核销金额" json:"adjustment_amount,omitempty"`

	// 官网价。
	OfficialAmount *decimal.Decimal `gorm:"column:official_amount;type:decimal(10,3);comment:官网价" json:"official_amount,omitempty"`

	// 对应官网价折扣金额。
	DiscountAmount *decimal.Decimal `gorm:"column:discount_amount;type:decimal(10,3);comment:对应官网价折扣金额" json:"discount_amount,omitempty"`

	// 金额单位。 1：元
	MeasureId *int32 `gorm:"column:measure_id;type:int;comment:金额单位" json:"measure_id,omitempty"`

	// 周期类型： 19：年20：月24：天25：小时5：一次性
	PeriodType *int32 `gorm:"column:period_type;type:int;comment:周期类型" json:"period_type,omitempty"`
	// 根资源标识。
	RootResourceId *string `gorm:"column:root_resource_id;type:varchar(255);comment:根资源标识" json:"root_resource_id,omitempty"`
	// 父资源标识。
	ParentResourceId *string `gorm:"column:parent_resource_id;type:varchar(255);comment:父资源标识" json:"parent_resource_id,omitempty"`
	// 订单ID 或 交易ID。 账单类型为1，2，3，4，8时为订单ID；其它场景下为： 交易ID(非月末扣费：应收ID；月末扣费：账单ID)。
	TradeId *string `gorm:"column:trade_id;type:varchar(255);comment:订单ID 或 交易ID" json:"trade_id,omitempty"`
	// 唯一标识。 该字段为预留字段。
	BillId *string `gorm:"column:bill_id;type:varchar(255);comment:唯一标识" json:"bill_id,omitempty"`
	// 产品的规格描述。
	ProductSpecDesc *string `gorm:"column:product_spec_desc;type:varchar(255);comment:产品的规格描述" json:"product_spec_desc,omitempty"`

	// 整机的子云服务的自身的云服务类型编码。
	SubServiceTypeCode *string `gorm:"column:sub_service_type_code;type:varchar(255);comment:整机的子云服务的自身的云服务类型编码" json:"sub_service_type_code,omitempty"`

	// 整机的子云服务的自身的云服务类型名称。
	SubServiceTypeName *string `gorm:"column:sub_service_type_name;type:varchar(255);comment:整机的子云服务的自身的云服务类型名称" json:"sub_service_type_name,omitempty"`

	// 整机的子云服务的自身的资源类型编码。
	SubResourceTypeCode *string `gorm:"column:sub_resource_type_code;type:varchar(255);comment:整机的子云服务的自身的资源类型编码" json:"sub_resource_type_code,omitempty"`

	// 整机的子云服务的自身的资源类型名称。
	SubResourceTypeName *string `gorm:"column:sub_resource_type_name;type:varchar(255);comment:整机的子云服务的自身的资源类型名称" json:"sub_resource_type_name,omitempty"`

	// 整机的子云服务的自身的资源ID，资源标识。（如果为预留实例，则为预留实例标识）
	SubResourceId *string `gorm:"column:sub_resource_id;type:varchar(255);comment:整机的子云服务的自身的资源ID，资源标识。（如果为预留实例，则为预留实例标识）" json:"sub_resource_id,omitempty"`

	// 整机的子云服务的自身的资源名称，资源标识。（如果为预留实例，则为预留实例标识）
	SubResourceName *string `gorm:"column:sub_resource_name;type:varchar(256);comment:整机的子云服务的自身的资源名称，资源标识。（如果为预留实例，则为预留实例标识）" json:"sub_resource_name,omitempty"`

	// 原订单ID 。
	PreOrderId *string `gorm:"column:pre_order_id;type:varchar(255);comment:原订单ID" json:"pre_order_id,omitempty"`

	// |参数名称：支付账号ID。| |参数的约束及描述：如果是普通客户或者财务独立企业子客户或者企业主客户查询消费记录，此处为客户自己的客户ID。如果是财务托管企业子查询消费记录，此处为企业主客户ID或自己的客户ID。|
	PayerAccountId *string `gorm:"column:payer_account_id;type:varchar(255);comment:支付账号ID" json:"payer_account_id,omitempty"`

	// |参数名称：费用对应的资源使用的开始时间| |参数的约束及描述：费用对应的资源使用的开始时间，statistic_type=3有效，statistic_type=1或者2该字段保留。|
	EffectiveTime *string `gorm:"column:effective_time;type:varchar(255);comment:费用对应的资源使用的开始时间" json:"effective_time,omitempty"`

	// |参数名称：费用对应的资源使用的结束时间| |参数的约束及描述：费用对应的资源使用的结束时间，statistic_type=3有效，statistic_type=1或者2该字段保留。|
	ExpireTime *string `gorm:"column:expire_time;type:varchar(255);comment:费用对应的资源使用的结束时间" json:"expire_time,omitempty"`
}

func (MonthlyBill) TableName() string {
	return "asset_monthly_bills_hwcloud"
}

func (a Account) syncMonthlyBill(billCycle string) (result string, err error) {
	result, bills, err := a.syncPageMonthlyBills(billCycle, 1)
	if err != nil {
		return
	}
	result += "保存 账单" + strconv.Itoa(len(bills)) + "条 "
	dbop := app.DB().Begin()
	err = dbop.Where("account_id = ? AND cycle = ?", a.ID, billCycle).Delete(&MonthlyBill{}).Error
	if err != nil {
		result += "删除失败:" + err.Error()
		dbop.Rollback()
		return
	}
	err = dbop.CreateInBatches(&bills, 1000).Error
	if err != nil {
		result += "插入失败:" + err.Error()
		dbop.Rollback()
		return
	}
	err = dbop.Commit().Error
	if err != nil {
		result += "提交失败:" + err.Error()
		return
	}
	result += "同步账单成功"
	return
}

func (a Account) syncPageMonthlyBills(billCycle string, page int32) (result string, bills []MonthlyBill, err error) {
	// 解码访问密钥
	secret, _ := utils.Base64Decode(a.AccessSecret)
	auth, err := global.NewCredentialsBuilder().WithAk(a.AccessKey).WithSk(secret).SafeBuild()
	if err != nil {
		result += "认证失败"
		return
	}
	regionID, err := region.SafeValueOf("cn-north-1")
	if err != nil {
		result += "regionID 失败"
		return
	}
	clientBuild, err := bss.BssClientBuilder().WithRegion(regionID).WithCredential(auth).SafeBuild()
	if err != nil {
		result += "客户端创建失败"
		return
	}
	client := bss.NewBssClient(clientBuild)
	limit := int32(1000)
	offset := (page - 1) * limit
	includeZeroRecord := false
	request := &model.ListCustomerselfResourceRecordDetailsRequest{}
	request.Body = &model.QueryResRecordsDetailReq{
		Limit:             &limit,
		Offset:            &offset,
		Cycle:             billCycle,
		IncludeZeroRecord: &includeZeroRecord,
	}
	response, err := client.ListCustomerselfResourceRecordDetails(request)
	if err != nil {
		result += "请求 ListCustomerselfResourceRecordDetails 失败"
		return
	}
	if response.MonthlyRecords != nil {
		bills = a.transformMonthlyBills(billCycle, *response.MonthlyRecords)
	}
	if len(bills) > 0 || response.TotalCount != nil && *response.TotalCount > (page-1)*limit {
		result += "第" + strconv.Itoa(int(page)) + "页" + "获取总条数:" + strconv.Itoa(int(*response.TotalCount)) + "；"
		r1, pageBills, err1 := a.syncPageMonthlyBills(billCycle, page+1)
		if err1 != nil {
			result += r1 + ":" + err1.Error()
			err = err1
			return
		}
		result += r1
		bills = append(bills, pageBills...)
	}
	return
}

var billTypeMap = map[int32]string{
	1:  "消费-新购",
	2:  "消费-续订",
	3:  "消费-变更",
	4:  "退款-退订",
	5:  "消费-使用",
	8:  "消费-自动续订",
	9:  "调账-补偿",
	14: "消费-服务支持计划月末扣费",
	16: "调账-扣费",
	18: "消费-按月付费",
	20: "退款-变更",
	23: "消费-节省计划抵扣",
	24: "退款-包年/包月转按需",
}
var chargeModeMap = map[int32]string{
	1:  "包年/包月",
	3:  "按需",
	10: "预留实例",
	11: "节省计划",
}

func (a Account) transformMonthlyBills(billCycle string, bills []model.MonthlyBillRes) (monthlyBills []MonthlyBill) {
	for _, bill := range bills {
		billType := ""
		if bill.BillType != nil {
			billType = billTypeMap[*bill.BillType]
		}
		chargeMode := ""
		if bill.ChargeMode != nil {
			chargeMode = chargeModeMap[*bill.ChargeMode]
		}
		monthlyBill := MonthlyBill{
			AccountID:                 a.ID,
			Cycle:                     &billCycle,
			BillDate:                  bill.BillDate,
			BillType:                  &billType,
			CustomerId:                bill.CustomerId,
			Region:                    bill.Region,
			RegionName:                bill.RegionName,
			CloudServiceType:          bill.CloudServiceType,
			ResourceTypeCode:          bill.ResourceTypeCode,
			CloudServiceTypeName:      bill.CloudServiceTypeName,
			ResourceTypeName:          bill.ResourceTypeName,
			ResInstanceId:             bill.ResInstanceId,
			ResourceName:              bill.ResourceName,
			ResourceTag:               bill.ResourceTag,
			SkuCode:                   bill.SkuCode,
			EnterpriseProjectId:       bill.EnterpriseProjectId,
			EnterpriseProjectName:     bill.EnterpriseProjectName,
			ChargeMode:                &chargeMode,
			ConsumeAmount:             bill.ConsumeAmount,
			CashAmount:                bill.CashAmount,
			CreditAmount:              bill.CreditAmount,
			CouponAmount:              bill.CouponAmount,
			FlexipurchaseCouponAmount: bill.FlexipurchaseCouponAmount,
			StoredCardAmount:          bill.StoredCardAmount,
			BonusAmount:               bill.BonusAmount,
			DebtAmount:                bill.DebtAmount,
			AdjustmentAmount:          bill.AdjustmentAmount,
			OfficialAmount:            bill.OfficialAmount,
			DiscountAmount:            bill.DiscountAmount,
			MeasureId:                 bill.MeasureId,
			PeriodType:                bill.PeriodType,
			RootResourceId:            bill.RootResourceId,
			ParentResourceId:          bill.ParentResourceId,
			TradeId:                   bill.TradeId,
			BillId:                    bill.Id,
			ProductSpecDesc:           bill.ProductSpecDesc,
			SubServiceTypeCode:        bill.SubServiceTypeCode,
			SubServiceTypeName:        bill.SubServiceTypeName,
			SubResourceTypeCode:       bill.SubResourceTypeCode,
			SubResourceTypeName:       bill.SubResourceTypeName,
			SubResourceId:             bill.SubResourceId,
			SubResourceName:           bill.SubResourceName,
			PreOrderId:                bill.PreOrderId,
			PayerAccountId:            bill.PayerAccountId,
			EffectiveTime:             bill.EffectiveTime,
			ExpireTime:                bill.ExpireTime,
		}
		monthlyBills = append(monthlyBills, monthlyBill)
	}
	return
}
