package asset

import (
	"cmdb/app"
	"cmdb/pkg/db"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"
)

var (
	ErrDatacenterExist            = errors.New("数据中心已经存在")
	ErrDatacenterExistAssociation = errors.New("存在关联数据")
)

type Datacenter struct {
	ID        uint           `gorm:"column:id;primaryKey;comment:主键id" json:"id"`
	Name      string         `gorm:"type:varchar(255);column:name;index;comment:名称" json:"name"`
	Code      string         `gorm:"type:varchar(255);column:code;index;comment:ID" json:"code"`
	CloudType CloudType      `gorm:"column:cloud_type;index;comment:账户类型" json:"cloud_type"`
	Remark    string         `gorm:"column:remark;type:varchar(255);comment:备注" json:"remark"`
	CreatedAt time.Time      `gorm:"column:created_at;comment:创建时间" json:"created_at"`
	UpdatedAt time.Time      `gorm:"column:updated_at;comment:更新时间" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at;comment:软删除" json:"-"`
}

func (Datacenter) TableName() string {
	return "asset_datacenters"
}

type DatacenterForm struct {
	Name      string    `json:"name" binding:"required,max=255"`
	Code      string    `json:"code" binding:"max=255"`
	Remark    string    `json:"remark" binding:"max=255"`
	CloudType CloudType `json:"cloudType"`
}

func (form DatacenterForm) Create() (err error) {
	err = app.DB().Select("id").Where("name = ?", form.Name).Take(&Datacenter{}).Error
	if err == nil {
		err = ErrDatacenterExist
		return
	} else if errors.Is(err, gorm.ErrRecordNotFound) {
		err = app.DB().Create(&Datacenter{
			Name:      form.Name,
			Code:      form.Code,
			Remark:    form.Remark,
			CloudType: form.CloudType,
		}).Error
	}
	return
}

func GetDatacenterByID(id int) (d Datacenter, err error) {
	err = app.DB().Where("id = ?", id).Take(&d).Error
	return
}

func GetDatacenterNameByID(id int) (name string, err error) {
	err = app.DB().Model(&Datacenter{}).Select("name").Where("id = ?", id).Scan(&name).Error
	return
}

func GetDatacenterCodeByID(id int) (code string, err error) {
	err = app.DB().Model(&Datacenter{}).Select("code").Where("id = ?", id).Scan(&code).Error
	return
}

func (d Datacenter) Update(form DatacenterForm) (err error) {
	updateMap := map[string]any{}
	if d.Name != form.Name {
		updateMap["name"] = form.Name
	}
	if d.Remark != form.Remark {
		updateMap["remark"] = form.Remark
	}
	if d.Code != form.Code {
		updateMap["code"] = form.Code
	}
	if d.CloudType != form.CloudType {
		updateMap["cloud_type"] = form.CloudType
	}
	if len(updateMap) == 0 {
		return
	} else {
		fmt.Println(updateMap)
	}
	exist := Datacenter{}
	err = app.DB().Select("id").Where("name = ?", form.Name).Take(&exist).Error
	if err == nil && d.ID != exist.ID {
		// 已经存在
		err = ErrDatacenterExist
		return
	}
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		// 查询异常
		return
	}
	err = app.DB().Model(&d).Updates(updateMap).Error
	return
}

func (d Datacenter) Delete() (err error) {
	var count int64
	err = app.DB().Model(&Host{}).Where("datacenter_id = ?", d.ID).Count(&count).Error
	if err != nil {
		return
	}
	if count > 0 {
		err = ErrDatacenterExistAssociation
		return
	}
	err = app.DB().Delete(&d).Error
	return
}

func GetDatacenters(offset, limit int, cloudType *CloudType, keyword string) (ds []Datacenter, count int64, err error) {
	dbop := app.DB()
	if keyword != "" {
		dbop = db.MLike(dbop, keyword, "name", "remark").Or("code = ?", keyword)
	}
	if cloudType != nil {
		dbop = dbop.Where("cloud_type = ?", cloudType)
	}
	err = dbop.Model(&Datacenter{}).Count(&count).Order("cloud_type , name").Offset(offset).Limit(limit).Find(&ds).Error
	return
}

func GetDatacentersByCloudType(cloudType CloudType) (ds []Datacenter, err error) {
	err = app.DB().Where("cloud_type = ?", cloudType).Find(&ds).Error
	return
}

func GetAllDatacenters() (ds []Datacenter, err error) {
	err = app.DB().Find(&ds).Error
	return
}

func (d Datacenter) CountHost() (count int64, err error) {
	err = app.DB().Model(&Host{}).Where("datacenter_id = ?", d.ID).Count(&count).Error
	return
}
