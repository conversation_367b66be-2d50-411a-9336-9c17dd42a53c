package k8s

import (
	"cmdb/app"
	"cmdb/app/service/asset/k8s"
	"cmdb/app/service/audit"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

func SyncK8s(c *gin.Context) {
	// 将参数id转换为int类型
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		// 记录日志
		app.Log().Debug("同步k8s信息时，存在非法参数id：" + c.<PERSON><PERSON>("id"))
		// 返回错误信息
		app.FailedResponseMsg(c, "非法参数")
		return
	}
	cluster, err := k8s.GetClusterByID(id)
	if err != nil {
		// 记录日志
		app.Log().Debug("同步k8s信息时，获取集群信息失败：" + err.Error())
		// 返回错误信息
		app.FailedResponseMsg(c, "获取集群信息失败")
		return
	}
	// 创建一个done通道
	done := make(chan struct{}, 1)
	// 启动一个goroutine进行同步
	go func() {
		// 记录日志
		audit.LogAPIOP(c, "资产管理", "提交同步k8s集群："+cluster.Name+" 信息")
		err = cluster.SyncNodes()
		if err != nil {
			// 记录日志
			app.Log().Error("同步k8s信息时，获取节点信息失败：" + err.Error())
		}
		err = cluster.SyncNamespaces()
		if err != nil {
			// 记录日志
			app.Log().Error("同步k8s信息时，获取命名空间信息失败：" + err.Error())
		}
		err = cluster.SyncServices()
		if err != nil {
			// 记录日志
			app.Log().Error("同步k8s信息时，获取服务信息失败：" + err.Error())
		}
		err = cluster.SyncWorkloads()
		if err != nil {
			// 记录日志
			app.Log().Error("同步k8s信息时，获取工作负载信息失败：" + err.Error())
		}
		err = cluster.SyncPods()
		if err != nil {
			// 记录日志
			app.Log().Error("同步k8s信息时，获取pod信息失败：" + err.Error())
		}
		// 发送完成信号
		done <- struct{}{}
	}()
	ticker := time.NewTicker(500 * time.Millisecond)
	select {
	case <-done:
		if err != nil {
			app.FailedResponseMsg(c, err.Error())
		} else {
			app.SuccessResponseMsg(c, "同步成功")
		}
		break
	case <-ticker.C:
		app.SuccessResponseMsg(c, "任务提交成功,正在同步中，请稍后...")
		break
	}
}
