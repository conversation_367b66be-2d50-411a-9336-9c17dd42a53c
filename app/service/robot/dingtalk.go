package robot

import (
	"bytes"
	"cmdb/app/service/setting"
	"cmdb/pkg/dingtalk"
	"fmt"
	"strings"
	"time"
)

// 钉钉消息
type DingChatHeader struct {
	ContentType string `json:"Content-Type"`
	Timestamp   string `json:"timestamp"`
	Sign        string `json:"sign"`
}

// 钉钉消息体
type DingChatBody struct {
	Msgtype           string    `json:"Msgtype"`
	Text              Text      `json:"text"`
	MsgID             string    `json:"msgId"`
	CreateAt          int       `json:"createAt"`
	ConversationType  string    `json:"conversationType"`
	ConversationID    string    `json:"conversationId"`
	ConversationTitle string    `json:"conversationTitle"`
	SenderID          string    `json:"senderId"`
	SenderNick        string    `json:"senderNick"`
	SenderCorpID      string    `json:"senderCorpId"`
	SenderStaffID     string    `json:"senderStaffId"`
	ChatbotUserID     string    `json:"chatbotUserId"`
	AtUsers           []AtUsers `json:"atUsers"`
}

// Text DingChatBody
type Text struct {
	Content string `json:"content"`
}

// DingChatBody
type AtUsers struct {
	DingTalkID string `json:"dingtalkId"`
	StaffID    string `json:"staffId"`
}

// 响应消息
type Response struct {
	Code int
	Msg  string
}

//  回复消息体
// type HTTPBody struct {
// 	MsgType string `json:"msgtype"`
// 	Text    Text   `json:"text"`
// 	Code    int    `json:"code"`
// }

// 回复消息体 markdown 类型
type HTTPBodyMarkdown struct {
	MsgType  string   `json:"msgtype"`
	Markdown Markdown `json:"markdown"`
}

// Markdown 消息体
type Markdown struct {
	Title string `json:"title"`
	Text  string `json:"text"`
}

// 钉钉消息服务
func (service *DingChatHeader) DingChatServices(dingChatBody *DingChatBody, usage, mode string) (msg HTTPBodyMarkdown, err error) {
	if mode == "" {
		err = dingtalk.CheckSinCode(setting.GetDingtalkAppSecret().AppSecret, service.Timestamp, service.Sign, usage)
		if err != nil {
			msg.Markdown.Text = err.Error()
			return
		}
	}
	context := strings.Fields(strings.TrimSpace(dingChatBody.Text.Content))
	if len(context) > 0 {
		if context[0] == "监控开启" || context[0] == "监控关闭" || context[0] == "监控抑制" {
			newContext := []string{"监控", context[0][6:]}
			if len(context) > 1 {
				newContext = append(newContext, context[1:]...)
			}
			context = newContext
		}
		if command, ok := dingChatCommands[context[0]]; ok {
			msg = command.Func(command.Usage, context[1:]...)
			msg.MsgType = "markdown"
			msg.Markdown.Text += "\n\n----\n使用方式：@我 " + context[0] + " " + command.Usage + "\n\n回复时间：" + time.Now().Format("2006-01-02 15:04:05")
			return
		}
	}
	msg.MsgType = "markdown"
	msg.Markdown.Title = "使用方法"
	contents := bytes.Buffer{}
	contents.WriteString("### 可以@我发操作选项：\n")
	for k, v := range dingChatCommands {
		contents.WriteString(fmt.Sprintf("- %v %v\n", k, v.Usage))
	}
	contents.WriteString("\n\n回复时间：" + time.Now().Format("2006-01-02 15:04:05"))
	msg.Markdown.Text = contents.String()
	return
}
