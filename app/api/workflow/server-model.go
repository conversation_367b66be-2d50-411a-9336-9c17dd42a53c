package workflow

import (
	"cmdb/app"
	"cmdb/app/service/workflow"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

func GetServerModels(c *gin.Context) {
	var spec *string
	var cpu, memory *int
	if val := strings.TrimSpace(c.Query("spec")); val != "" {
		spec = &val
	}
	if val := strings.TrimSpace(c.Query("cpu")); val != "" {
		cpu = new(int)
		val1, err1 := strconv.Atoi(val)
		if err1 == nil {
			cpu = &val1
		}
	}
	if val := strings.TrimSpace(c.Query("memory")); val != "" {
		memory = new(int)
		val1, err1 := strconv.Atoi(val)
		if err1 == nil {
			memory = &val1
		}
	}
	limit, _ := strconv.Atoi(c.<PERSON>("limit", "10"))
	page, _ := strconv.Atoi(c.<PERSON><PERSON>ult<PERSON><PERSON>("page", "1"))
	offset := (page - 1) * limit
	count, data, err := workflow.GetServerModels(offset, limit, spec, cpu, memory)
	if err != nil {
		app.Log().Error("获取数据失败", "err", err)
		app.FailedResponseMsg(c, "获取数据失败")
		return
	}
	app.SuccessResponseDataCount(c, data, count)
}
