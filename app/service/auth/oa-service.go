package auth

import (
	"bytes"
	"cmdb/app"
	"crypto/hmac"
	"crypto/sha1"
	"crypto/tls"
	"encoding/base64"
	"encoding/json"
	"errors"
	"io"
	"log/slog"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/tidwall/gjson"
	"gorm.io/gorm"
)

// OA登录相关
func GetOAInfo(code string) (user, leader User, departments []Department, err error) {
	timeStamp := time.Now().UTC().Format("2006-01-02T15:04:05Z")
	data := map[string]string{
		"Accept":        "application/json",
		"Authorization": "Code " + code,
	}
	canonicalizedQueryString := ""
	for _, v := range data {
		canonicalizedQueryString += v
	}
	canonicalizedQueryString2 := timeStamp + "POST/access-tokens"
	SignatureSource := canonicalizedQueryString2 + canonicalizedQueryString
	clientSecret := []byte("AuWn2Cu4tLJEHAqZqoiQ5ZM6FE5ccgxNG6mm5nF3")
	stringToSign := []byte(SignatureSource)
	mac := hmac.New(sha1.New, clientSecret)
	mac.Write(stringToSign)
	p := mac.Sum(nil)
	signature := base64.StdEncoding.EncodeToString(p)
	header := "id=d90c989884e2459bab4ef1ea38225b94,timestamp=" + timeStamp + ",value=" + signature
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: tr}
	req, err := http.NewRequest("POST", "https://oa-service.meiyou.com/access-tokens", bytes.NewBuffer([]byte("")))
	if err != nil {
		return
	}
	req.Header.Add("Signature", header)
	req.Header.Add("Authorization", "Code "+code)
	req.Header.Add("Accept", "application/json")
	response, err := client.Do(req)
	if err != nil {
		return
	}
	body, err := io.ReadAll(response.Body)
	if err != nil {
		return
	}
	if response.StatusCode != 200 {
		err = ErrOACode
		return
	}
	jsonStr := string(body)
	if !gjson.Valid(jsonStr) {
		err = ErrOAData
		return
	}
	user.Name = gjson.Get(jsonStr, "user.name").String()
	user.Email = gjson.Get(jsonStr, "user.email").String()
	user.Username = strings.Split(user.Email, "@")[0]
	// 用户名是邮箱前缀
	user.SN = gjson.Get(jsonStr, "user.sn").String()
	leaderEmail := gjson.Get(jsonStr, "user.leaders.0.email").String()
	leaderUsername := strings.Split(leaderEmail, "@")[0]
	leaderName := gjson.Get(jsonStr, "user.leaders.0.name").String()
	departments = []Department{}
	if gjson.Get(jsonStr, "user.department.family.company.name").String() != "" {
		departments = append(departments, Department{
			Name:   gjson.Get(jsonStr, "user.department.family.company.name").String(),
			Remark: "公司",
			Family: COMPANYFAMILY,
		})
	}
	if gjson.Get(jsonStr, "user.department.family.business_unit.name").String() != "" {
		departments = append(departments, Department{
			Name:   gjson.Get(jsonStr, "user.department.family.business_unit.name").String(),
			Remark: "事业部",
			Family: BUSINESSUNITFAMILY,
		})
	}
	if gjson.Get(jsonStr, "user.department.family.unit.name").String() != "" {
		departments = append(departments, Department{
			Name:   gjson.Get(jsonStr, "user.department.family.unit.name").String(),
			Remark: "事业部虚线",
			Family: UNITFAMILY,
		})
	}
	if gjson.Get(jsonStr, "user.department.family.center.name").String() != "" {
		departments = append(departments, Department{
			Name:   gjson.Get(jsonStr, "user.department.family.center.name").String(),
			Remark: "中心",
			Family: CENTERFAMILY,
		})
	}
	if gjson.Get(jsonStr, "user.department.family.team.name").String() != "" {
		departments = append(departments, Department{
			Name:   gjson.Get(jsonStr, "user.department.family.team.name").String(),
			Remark: "团队",
			Family: TEAMFAMILY,
		})
	}
	leader.Name = gjson.Get(jsonStr, "user.leaders.0.name").String()
	leader.Email = leaderEmail
	leader.Name = leaderName
	leader.SN = gjson.Get(jsonStr, "user.leaders.0.sn").String()
	leader.Username = leaderUsername
	return
}

// oa登录验证
func OAAuth(toke string) (user User, err error) {
	u, leader, departments, err := GetOAInfo(toke)
	if err != nil {
		u, leader, departments, err = GetOAInfo(toke)
		if err != nil {
			return
		}
	}
	// 判断用户是否存在
	user, err = GetUserByUsername(u.Username)
	if err == nil {
		if err := user.OASetLeader(leader); err != nil {
			app.Log().Error("自动设置领导异常", slog.Any("err", err))
		}
		if err := user.OASetDeparment(departments...); err != nil {
			app.Log().Error("自动部门分组异常", slog.Any("err", err))
		}
		// 如果存在要判断是否禁用
		if user.IsDisabled {
			err = ErrUserDisabled
			return
		}
		return
	}
	// 如果不是找不到行的错误，认为是查询错误，直接返回
	if err != gorm.ErrRecordNotFound {
		app.Log().Error("数据库操作失败 " + err.Error())
		err = ErrDBOP
		return
	}
	err = app.DB().Create(&u).Error
	if err != nil {
		err = ErrCreateUser
		return
	}
	// 判断用户是否存在
	user, _ = GetUserByUsername(u.Username)
	go func() {
		if err1 := user.OASetLeader(leader); err1 != nil {
			app.Log().Error("自动设置领导异常", "err", err1)
		}
	}()
	return
}

func GetOAUsers(sn string) (resp string, err error) {
	timeFormat := time.Now().Format("2006-01-02T15:04:05.000-07:00")
	id := "0cadba854b744b4f8c20ea7781b00fe7"
	clientSecret := "0cJDsN+0MN76snBTCDtjXxBpntsSGbosvvrAYJ2E"
	host := "https://oa-service.meiyou.com"
	method := "GET"
	path := "/users"
	if sn != "" {
		path += "?sn=" + sn
	}
	url := host + path
	header := map[string]string{
		"Scope":        "3",
		"Accept":       "application/json",
		"Content-Type": "application/json",
		"Application":  "38655780",
	}
	headerKeys := []string{}
	for key := range header {
		headerKeys = append(headerKeys, key)
	}
	sort.Strings(headerKeys)
	var stringToSign bytes.Buffer
	stringToSign.WriteString(timeFormat + method + path)
	for _, key := range headerKeys {
		stringToSign.WriteString(header[key])
	}
	body := map[string]string{}
	jsonByte, _ := json.Marshal(body)
	stringToSign.Write(jsonByte)
	mac := hmac.New(sha1.New, []byte(clientSecret))
	mac.Write(stringToSign.Bytes())
	p := mac.Sum(nil)
	signature := base64.StdEncoding.EncodeToString(p)
	header["Signature"] = "id=" + id + ",timestamp=" + timeFormat + ",value=" + signature
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: tr}
	req, err := http.NewRequest("GET", url, bytes.NewBuffer(jsonByte))
	if err != nil {
		return
	}
	for k, v := range header {
		req.Header.Add(k, v)
	}
	response, err := client.Do(req)
	if err != nil {
		return
	}
	respBody, err := io.ReadAll(response.Body)
	if err != nil {
		return
	}
	if response.StatusCode != 200 {
		err = errors.New("response is " + strconv.Itoa(response.StatusCode))
	}
	resp = string(respBody)
	return
}

// 同步OA用户
func UpdateOAUsers() (err error) {
	startTime := time.Now()
	resp, err := GetOAUsers("")
	if err != nil {
		return
	}
	userMaps := []map[string]any{}
	err = json.Unmarshal([]byte(resp), &userMaps)
	if err != nil {
		return
	}
	dbop := app.DB().Begin()
	for i := range userMaps {
		email := userMaps[i]["email"].(string)
		username := strings.Split(email, "@")[0]
		user := User{}
		name := userMaps[i]["name"].(string)
		SN := strconv.Itoa(int(userMaps[i]["sn"].(float64)))
		err1 := app.DB().Where("username = ?", username).Take(&user).Error
		if err1 == nil {
			updateMap := map[string]any{
				"synced_at": startTime,
			}
			if user.Email != email {
				updateMap["email"] = email
			}
			if user.Name != name {
				updateMap["name"] = name
			}
			if user.SN != SN {
				updateMap["sn"] = SN
			}
			app.DB().Model(&user).UpdateColumns(updateMap)
			if leaders, ok := userMaps[i]["leaders"].([]any); ok {
				lead := User{}
				if len(leaders) < 1 {
					continue
				}
				leader := leaders[0].(map[string]any)
				leaderEmail := leader["email"].(string)
				leaderEmailSplits := strings.Split(leaderEmail, "@")
				if len(leaderEmailSplits) < 1 {
					continue
				}
				leaderUsername := leaderEmailSplits[0]
				lName := leader["name"].(string)
				lEmail := leaderEmail
				lSN := strconv.Itoa(int(leader["sn"].(float64)))
				err1 = dbop.Select("id").Where("username = ?", leaderUsername).Take(&lead).Error
				if err1 != nil {
					if err1 == gorm.ErrRecordNotFound {
						err1 = dbop.Create(&User{
							Name:       lName,
							Username:   leaderUsername,
							SN:         lSN,
							Email:      lEmail,
							IsDisabled: false,
							IsAdmin:    false,
							SyncedAt:   &startTime,
						}).Error
						if err1 != nil {
							continue
						}
						dbop.Select("id").Where("username = ?", leaderUsername).Take(&lead)
					} else {
						continue
					}
				}
				if lead.ID != user.LeaderID {
					dbop.Model(&user).UpdateColumn("leader_id", lead.ID)
				}
			}
		} else if errors.Is(err1, gorm.ErrRecordNotFound) {
			err1 = dbop.Create(&User{
				Name:       name,
				Username:   username,
				SN:         SN,
				Email:      email,
				IsDisabled: false,
				IsAdmin:    false,
				SyncedAt:   &startTime,
			}).Error
			if err1 != nil {
				continue
			}
			err1 = dbop.Select("id").Where("username = ?", username).Take(&user).Error
			if err1 != nil {
				continue
			}
			if leaders, ok := userMaps[i]["leaders"].([]any); ok {
				lead := User{}
				if len(leaders) < 1 {
					continue
				}
				leader := leaders[0].(map[string]any)
				leaderEmail := leader["email"].(string)
				leaderEmailSplits := strings.Split(leaderEmail, "@")
				if len(leaderEmailSplits) < 1 {
					continue
				}
				leaderUsername := leaderEmailSplits[0]
				lName := leader["name"].(string)
				lEmail := leaderEmail
				lSN := strconv.Itoa(int(leader["sn"].(float64)))
				err1 = dbop.Select("id").Where("username = ?", leaderUsername).Take(&lead).Error
				if err1 != nil {
					if err1 == gorm.ErrRecordNotFound {
						err1 = dbop.Create(&User{
							Name:       lName,
							Username:   leaderUsername,
							SN:         lSN,
							Email:      lEmail,
							IsDisabled: false,
							IsAdmin:    false,
							SyncedAt:   &startTime,
						}).Error
						if err1 != nil {
							continue
						}
						dbop.Select("id").Where("username = ?", leaderUsername).Take(&lead)
					} else {
						continue
					}
				}
				if lead.ID != user.LeaderID {
					dbop.Model(&user).UpdateColumn("leader_id", lead.ID)
				}
			}
		}
	}
	err = dbop.Model(&User{}).Where("is_disabled = ?", false).Where("synced_at < ? OR synced_at IS NULL", startTime.Add(-1*time.Minute)).Update("is_disabled", true).Error
	if err != nil {
		dbop.Rollback()
		return
	}
	err = dbop.Commit().Error
	if err != nil {
		dbop.Rollback()
	}
	return
}
