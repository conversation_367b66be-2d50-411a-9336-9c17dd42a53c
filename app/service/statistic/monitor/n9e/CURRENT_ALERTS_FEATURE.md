# 夜莺(N9E)当前活跃告警功能实现总结

## 📋 功能概述

新增了获取当前活跃告警数据的功能，允许用户查询并返回当前正在发生的告警事件列表，支持多种过滤条件和分页查询。

## 🎯 实现的功能

### 1. **数据源**
- 使用 `alert_cur_event` 表（当前告警事件表）
- 查询正在发生的实时告警数据

### 2. **查询条件支持**
- ✅ **时间范围过滤**（可选）：支持 `start_time` 和 `end_time` 参数
- ✅ **严重程度过滤**（可选）：支持 `severity` 参数（0/1/2）
- ✅ **集群过滤**（可选）：支持 `cluster` 参数
- ✅ **实例标识过滤**（可选）：支持 `target_ident` 参数
- ✅ **分页查询**（必需）：支持 `page` 和 `limit` 参数

### 3. **返回数据字段**
- ✅ **基础信息**：告警ID、规则名称、严重程度、目标实例、触发时间
- ✅ **详细信息**：告警描述、集群信息、业务组信息
- ✅ **计算字段**：持续时间、格式化的时间显示
- ✅ **排序**：按触发时间倒序排列

### 4. **接口设计**
- ✅ **HTTP接口**：`GET /api/v1/statistic/monitor/n9e/alerts/current`
- ✅ **参数验证**：完整的参数验证和错误处理
- ✅ **响应格式**：使用标准的分页响应格式 `SuccessResponseDataCount`

## 📁 新增文件和修改

### 1. **模型层** (`model.go`)

#### 新增结构体
```go
// CurrentAlert 当前告警信息
type CurrentAlert struct {
    ID               uint64 `json:"id"`                 // 告警ID
    RuleID           uint64 `json:"rule_id"`            // 规则ID
    RuleName         string `json:"rule_name"`          // 规则名称
    RuleNote         string `json:"rule_note"`          // 规则备注
    Severity         int8   `json:"severity"`           // 严重程度
    SeverityText     string `json:"severity_text"`      // 严重程度文本
    TargetIdent      string `json:"target_ident"`       // 目标实例标识
    TargetNote       string `json:"target_note"`        // 目标实例备注
    TriggerTime      int64  `json:"trigger_time"`       // 触发时间
    TriggerTimeText  string `json:"trigger_time_text"`  // 触发时间文本
    TriggerValue     string `json:"trigger_value"`      // 触发值
    FirstTriggerTime *int64 `json:"first_trigger_time"` // 首次触发时间
    Cluster          string `json:"cluster"`            // 集群
    GroupID          uint64 `json:"group_id"`           // 业务组ID
    GroupName        string `json:"group_name"`         // 业务组名称
    Annotations      string `json:"annotations"`        // 告警描述
    Tags             string `json:"tags"`               // 标签
    NotifyCurNumber  int    `json:"notify_cur_number"`  // 当前通知次数
    Duration         int64  `json:"duration"`           // 持续时间(秒)
    DurationText     string `json:"duration_text"`      // 持续时间文本
}
```

#### 新增工具函数
```go
// FormatUnixTime 格式化Unix时间戳为可读字符串
func FormatUnixTime(timestamp int64) string

// FormatDuration 格式化持续时间为可读字符串
func FormatDuration(seconds int64) string

// CalculateCurrentDuration 计算当前告警的持续时间
func CalculateCurrentDuration(triggerTime int64) int64
```

### 2. **服务层** (`statistic.go`)

#### 新增函数
```go
// GetCurrentAlerts 获取当前活跃的告警数据
func GetCurrentAlerts(startTime, endTime *time.Time, severity *int8, cluster, targetIdent string, offset, limit int) (int64, []CurrentAlert, error)
```

**功能特点**：
- 使用GORM的链式查询构建动态条件
- 支持可选参数的灵活组合
- 自动计算告警持续时间
- 格式化时间显示
- 处理资源组默认值

### 3. **API层** (`n9e.go`)

#### 新增接口
```go
// GetCurrentAlerts 获取当前活跃的告警数据
func GetCurrentAlerts(c *gin.Context)
```

**功能特点**：
- 完整的参数验证和错误处理
- 支持可选时间参数解析
- 严重程度参数验证（0-2范围）
- 标准的分页响应格式
- 详细的Swagger文档注释

### 4. **路由配置** (`route.go`)

#### 新增路由
```go
statisticGRAmin.GET("/monitor/n9e/alerts/current", statisticMonitor.GetCurrentAlerts)
```

### 5. **测试文件** (`statistic_test.go`)

#### 新增测试用例
- `TestFormatUnixTime` - 时间格式化测试
- `TestFormatDuration` - 持续时间格式化测试
- `TestCalculateCurrentDuration` - 当前持续时间计算测试
- 基准测试和示例函数

## 🔧 技术特性

### 1. **动态查询构建**
```go
// 构建查询条件
db := app.DB().Model(&AlertCurEvent{})

// 时间范围过滤（可选）
if startTime != nil {
    startTimestamp := startTime.Unix()
    db = db.Where("trigger_time >= ?", startTimestamp)
}
```

### 2. **智能时间处理**
```go
// 格式化持续时间为可读字符串
func FormatDuration(seconds int64) string {
    duration := time.Duration(seconds) * time.Second
    days := int(duration.Hours()) / 24
    hours := int(duration.Hours()) % 24
    minutes := int(duration.Minutes()) % 60
    secs := int(duration.Seconds()) % 60
    // 返回如："1天1小时1分钟1秒"
}
```

### 3. **参数验证**
```go
// 严重程度参数验证
if s, err := strconv.Atoi(severityStr); err != nil {
    app.FailedResponseMsg(c, "严重程度参数格式错误，应为数字：0(Emergency), 1(Warning), 2(Notice)")
    return
} else if s < 0 || s > 2 {
    app.FailedResponseMsg(c, "严重程度参数值错误，应为：0(Emergency), 1(Warning), 2(Notice)")
    return
}
```

### 4. **数据转换**
```go
// 转换为响应格式
currentAlert := CurrentAlert{
    ID:               alert.ID,
    RuleID:           alert.RuleID,
    RuleName:         alert.RuleName,
    Severity:         alert.Severity,
    SeverityText:     SeverityType(alert.Severity).String(),
    TriggerTimeText:  FormatUnixTime(alert.TriggerTime),
    GroupName:        GetGroupNameWithDefault(alert.GroupName),
    Duration:         CalculateCurrentDuration(alert.TriggerTime),
    DurationText:     FormatDuration(currentAlert.Duration),
}
```

## 📊 API使用示例

### 1. **获取所有当前告警**
```bash
GET /api/v1/statistic/monitor/n9e/alerts/current?page=1&limit=10
```

### 2. **按严重程度过滤**
```bash
GET /api/v1/statistic/monitor/n9e/alerts/current?severity=0&page=1&limit=10
```

### 3. **按集群过滤**
```bash
GET /api/v1/statistic/monitor/n9e/alerts/current?cluster=production&page=1&limit=10
```

### 4. **按实例过滤**
```bash
GET /api/v1/statistic/monitor/n9e/alerts/current?target_ident=192.168.1.100&page=1&limit=10
```

### 5. **时间范围过滤**
```bash
GET /api/v1/statistic/monitor/n9e/alerts/current?start_time=2024-01-01%2007:00:00&end_time=2024-01-01%2008:00:00&page=1&limit=10
```

### 6. **组合过滤**
```bash
GET /api/v1/statistic/monitor/n9e/alerts/current?severity=1&cluster=production&start_time=2024-01-01%2007:00:00&page=1&limit=10
```

## ✅ 质量保证

### 1. **测试覆盖**
- ✅ 单元测试：所有工具函数都有完整的测试用例
- ✅ 边界测试：时间格式化、持续时间计算的边界情况
- ✅ 基准测试：性能关键函数的基准测试

### 2. **错误处理**
- ✅ 参数验证：时间格式、严重程度范围、分页参数
- ✅ 数据库错误：查询失败的错误处理
- ✅ 边界情况：NULL值、零值、负值的处理

### 3. **代码质量**
- ✅ 编译通过：项目编译无错误
- ✅ 代码规范：遵循项目现有的代码风格
- ✅ 文档完善：详细的注释和API文档

## 🚀 部署和使用

### 1. **数据库要求**
- 确保 `n9e.alert_cur_event` 表存在且可访问
- 建议在 `trigger_time`, `severity`, `cluster`, `target_ident` 字段上创建索引

### 2. **权限要求**
- 需要管理员权限才能访问接口
- 确保数据库用户有读取权限

### 3. **性能建议**
- 大量数据时建议使用分页查询
- 可以通过过滤条件减少查询数据量
- 考虑添加缓存机制提升查询性能

## 📝 后续优化建议

1. **实时更新**：考虑使用WebSocket提供实时告警推送
2. **缓存机制**：对频繁查询的数据添加缓存
3. **聚合统计**：添加当前告警的聚合统计功能
4. **告警趋势**：提供告警数量的时间趋势分析
5. **导出功能**：支持告警数据的导出功能

## 🔗 相关文档

- [README.md](./README.md) - 完整的API使用指南
- [example.sh](./example.sh) - API调用示例脚本
- [OPTIMIZATION_SUMMARY.md](./OPTIMIZATION_SUMMARY.md) - 平均持续时间优化详情
- [TABLE_NAME_UPDATE.md](./TABLE_NAME_UPDATE.md) - 数据库表名修改记录
