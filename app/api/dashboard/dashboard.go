package dashboard

import (
	"cmdb/app"
	"cmdb/app/service"
	"cmdb/app/service/auth"

	"github.com/gin-gonic/gin"
)

func GetDashboardData(c *gin.Context) {
	user, err := auth.GetLoginUser(c)
	if err != nil {
		app.Log().Error("dashboard获取数据失败", "err", err)
		app.FailedResponseMsg(c, "获取数据失败")
		return
	}
	data, err := service.GetDashboardData(user)
	if err != nil {
		app.Log().Error("dashboard获取数据失败", "err", err)
		app.FailedResponseMsg(c, "获取数据失败")
		return
	}
	app.SuccessResponseData(c, data)
}
