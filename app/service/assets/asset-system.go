package assets

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/pkg/db"
	"errors"
	"time"

	"gorm.io/gorm"
)

// 错误
var (
	ErrAssetSystemExist = errors.New("资产系统已经存在")
)

// 系统
type AssetSystem struct {
	ID         uint           `gorm:"column:id;primaryKey;comment:主键" json:"id"`
	Name       string         `gorm:"column:name;type:varchar(255);index" json:"name"`
	Remark     string         `gorm:"column:remark;type:varchar(255);index" json:"remark"`
	BusinessID uint           `gorm:"column:business_id;index;comment:关联业务的ID" json:"business_id"`
	CreatedAt  time.Time      `gorm:"column:created_at;index" json:"created_at"`
	UpdatedAt  time.Time      `gorm:"column:updated_at;index" json:"updated_at"`
	DeletedAt  gorm.DeletedAt `gorm:"column:deleted_at;index" json:"deleted_at"`
}

// 获取表名
func (AssetSystem) TableName() string {
	return "assets_systems"
}

// 系统表单
type AssetSystemForm struct {
	Name       string `json:"name" binding:"required,max=255"`
	Remark     string `json:"remark" binding:"max=255"`
	BusinessID uint   `json:"business_id" `
}

// 创建系统
func (form AssetSystemForm) Create() (err error) {
	err = app.DB().Where("name = ? ", form.Name).Take(&AssetSystem{}).Error
	if err == nil {
		err = ErrAssetSystemExist
	} else if errors.Is(err, gorm.ErrRecordNotFound) {
		err = app.DB().Create(&AssetSystem{
			Name:       form.Name,
			Remark:     form.Remark,
			BusinessID: form.BusinessID,
		}).Error
	}
	return
}

// 通过ID获取asset system
func GetAssetSystemByID(id int) (system AssetSystem, err error) {
	err = app.DB().Where("id = ?", id).Take(&system).Error
	return
}

func (s AssetSystem) GetTags() (tags []asset.Tag, err error) {
	err = app.DB().Model(&AssetSystem{}).Association("Tags").Find(&tags)
	return
}

// 更新系统
func (system AssetSystem) Update(form AssetSystemForm) (err error) {
	updateMap := map[string]any{}
	if system.Name != form.Name {
		updateMap["name"] = form.Name
		err = app.DB().Where("name = ?", form.Name).Take(&AssetSystem{}).Error
		if err == nil && system.ID != 0 {
			err = ErrAssetSystemExist
			return
		} else if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		} else {
			err = nil
		}
	}
	if system.Remark != form.Remark {
		updateMap["remark"] = form.Remark
	}
	if system.BusinessID != form.BusinessID {
		updateMap["business_id"] = form.BusinessID
	}
	if len(updateMap) > 0 {
		err = app.DB().Model(&system).Updates(updateMap).Error
	}
	return
}

// 删除系统
func (system AssetSystem) Delete() (err error) {
	err = app.DB().Delete(&system).Error
	return
}

// 获取系统列表
func GetAssetSystems(offset int, limit int, businessID *int, keyword *string) (count int64, data []AssetSystem, err error) {
	dbop := app.DB().Model(&AssetSystem{})
	if businessID != nil {
		dbop = dbop.Where("business_id = ?", *businessID)
	}
	if keyword != nil && *keyword != "" {
		dbop = db.MLike(dbop, *keyword, "name", "remark")
	}
	err = dbop.Count(&count).Order("name").Offset(offset).Limit(limit).Find(&data).Error
	return
}
