package app

import (
	"log/slog"
	"os"

	"gopkg.in/ini.v1"
)

type httpConfig struct {
	Host string `ini:"host" comment:"监听地址，默认是0.0.0.0"`
	Port uint   `ini:"port" comment:"监听端口"`
}

// 数据库配置
type dbConfig struct {
	Host     string `ini:"host" comment:"数据库地址"`
	Port     uint   `ini:"port" comment:"数据库端口"`
	DBName   string `ini:"dbname" comment:"库名"`
	Username string `ini:"username" comment:"用户名"`
	Password string `ini:"password" comment:"密码"`
	ShowSQL  bool   `ini:"show_sql" comment:"打印sql"`
}

// 会话配置
//
//	type sessionConfig struct {
//		Name     string `ini:"name" comment:"名称"`
//		Address  string `ini:"address" comment:"redis地址"`
//		Password string `ini:"password" comment:"redis密码"`
//		DBIndex  uint   `ini:"dbindex" comment:"db：0 - 15"`
//		Maxage   int    `ini:"maxage" comment:"生命时长，单位是小时，最小24小时"`
//	}
type ansibleConfig struct {
	AnsiblePlaybookBinary string `ini:"playbook_bin" comment:"ansible-playbook二进制文件路径"`
	AnsibleBinary         string `ini:"bin" comment:"ansible-playbook二进制文件路径"`
	AnsibleInventoryFile  string `ini:"inventory_file" comment:"ansible inventory文件路径"`
}

// 配置
type config struct {
	LogLevel string        `ini:"log_level" comment:"日志级别"`
	DB       dbConfig      `ini:"db" comment:"数据库配置"`
	HTTP     httpConfig    `ini:"http" commet:"http配置"`
	Ansible  ansibleConfig `ini:"ansible" comment:"ansible配置"`
}

// 默认配置文件
var defautlConfig = config{
	LogLevel: "debug",
	HTTP: httpConfig{
		Host: "0.0.0.0",
		Port: 8080,
	},
	DB: dbConfig{
		Host: "127.0.0.1", Port: 3306, Username: "cmdb3",
		Password: "xxxxxxxx", DBName: "cmdb3", ShowSQL: false,
	},
	Ansible: ansibleConfig{
		AnsiblePlaybookBinary: "/usr/bin/ansible-playbook",
		AnsibleBinary:         "/usr/bin/ansible",
		AnsibleInventoryFile:  "/etc/ansible/hosts",
	},
}

// PrintDefaultConfig 默认配置
func PrintDefaultConfig() {
	cfg := ini.Empty()
	err := ini.ReflectFrom(cfg, &defautlConfig)
	if err == nil {
		cfg.WriteToIndent(os.Stdout, "")
	}
	if err != nil {
		slog.Error("打印异常", slog.Any("err", err))
	}
}
