package asset

import (
	"cmdb/app"
	"sort"
	"sync"
	"time"
)

type MonthlyBill struct {
	AccountID     int      `gorm:"column:account_id;comment:账户ID" json:"account_id"`
	BillCycle     *string  `gorm:"column:bill_cycle;comment:账单周期" json:"bill_cycle"`
	BillType      *string  `gorm:"column:bill_type;comment:账单类型" json:"bill_type"`
	ChargeMode    *string  `gorm:"column:charge_mode;comment:计费模式" json:"charge_mode"`
	Amount        *float64 `gorm:"column:amount;comment:金额" json:"amount"`
	Region        *string  `gorm:"column:region;comment:地域" json:"region"`
	Zone          *string  `gorm:"column:zone;comment:可用区" json:"zone"`
	PayTime       *string  `gorm:"column:pay_time;comment:扣费时间" json:"pay_time"`
	ResourceID    *string  `gorm:"column:resource_id;comment:资源ID" json:"resource_id"`
	ResourceName  *string  `gorm:"column:resource_name;comment:资源名称" json:"resource_name"`
	ResourceGroup *string  `gorm:"column:resource_group;comment:资源组" json:"resource_group"`
	ResourceType  *string  `gorm:"column:resource_type;comment:资源类型" json:"resource_type"`
	ProductName   *string  `gorm:"column:product_name;comment:产品名称" json:"product_name"`
}

func (a CloudAccount) GetMonthlyBills(offset, limit int, billCycle, resourceID, resourceName *string) (count int64, bills []MonthlyBill, err error) {
	dbop := app.DB().Where("account_id = ?", a.ID)
	switch a.CloudType {
	case TencentCloudType:
		if resourceID != nil {
			dbop = dbop.Where("resource_id = ?", *resourceID)
		}
		if billCycle != nil {
			dbop = dbop.Where("bill_cycle = ?", *billCycle)
		}
		if resourceName != nil {
			dbop = dbop.Where("resource_name = ?", *resourceName)
		}
		err = dbop.Table("asset_monthly_bills_tencentcloud").Select(
			"account_id AS account_id",
			"bill_cycle AS bill_cycle",
			"action_type_name AS bill_type",
			"pay_mode_name AS charge_mode",
			"real_total_cost AS amount",
			"region_name AS region",
			"zone_name AS zone",
			"pay_time AS pay_time",
			"resource_id AS resource_id",
			"resource_name AS resource_name",
			"project_name AS resource_group",
			"business_code_name AS resource_type",
			"product_code_name AS product_name",
		).Count(&count).Order("bill_cycle DESC,pay_time DESC, resource_id DESC").Offset(offset).Limit(limit).Find(&bills).Error
	case AliyunCloudType:
		if resourceID != nil {
			dbop = dbop.Where("instance_id = ?", *resourceID)
		}
		if billCycle != nil {
			dbop = dbop.Where("bill_cycle = ?", *billCycle)
		}
		if resourceName != nil {
			dbop = dbop.Where("nick_name = ?", *resourceName)
		}
		err = dbop.Table("asset_monthly_bills_aliyun").Select(
			"account_id AS account_id",
			"bill_cycle AS bill_cycle",
			"item_name AS bill_type",
			"subscription_type AS charge_mode",
			"pretax_amount AS amount",
			"region AS region",
			"zone AS zone",
			"billing_date AS pay_time",
			"instance_id AS resource_id",
			"nick_name AS resource_name",
			"resource_group AS resource_group",
			"product_detail AS resource_type",
			"product_name AS product_name",
		).Count(&count).Order("bill_cycle DESC,instance_id DESC").Offset(offset).Limit(limit).Find(&bills).Error
	case HuaweiCloudType:
		if resourceID != nil {
			dbop = dbop.Where("res_instance_id = ?", *resourceID)
		}
		if billCycle != nil {
			dbop = dbop.Where("cycle = ?", *billCycle)
		}
		if resourceName != nil {
			dbop = dbop.Where("resource_name = ?", *resourceName)
		}
		err = dbop.Table("asset_monthly_bills_hwcloud").Select(
			"account_id AS account_id",
			"cycle AS bill_cycle",
			"bill_type AS bill_type",
			"charge_mode AS charge_mode",
			"consume_amount AS amount",
			"region_name AS region",
			"region AS zone",
			// "pay_time AS pay_time",
			"res_instance_id AS resource_id",
			"resource_name AS resource_name",
			"enterprise_project_name AS resource_group",
			"cloud_service_type_name AS resource_type",
			"resource_type_name AS product_name",
		).Count(&count).Order("cycle DESC,res_instance_id DESC").Offset(offset).Limit(limit).Find(&bills).Error
	}
	return
}

func (a CloudAccount) GetMonthlyBillAmount(cycle string) (amount *float64, err error) {
	switch a.CloudType {
	case TencentCloudType:
		err = app.DB().Table("asset_monthly_bills_tencentcloud").Select("SUM(real_total_cost) AS amount").Where("account_id = ? AND bill_cycle = ?", a.ID, cycle).Order("bill_cycle").Scan(&amount).Error
	case AliyunCloudType:
		err = app.DB().Table("asset_monthly_bills_aliyun").Select("SUM(pretax_amount) AS amount").Where("account_id = ? AND bill_cycle = ?", a.ID, cycle).Order("bill_cycle").Scan(&amount).Error
	case HuaweiCloudType:
		err = app.DB().Table("asset_monthly_bills_hwcloud").Select("SUM(consume_amount) AS amount").Where("account_id = ? AND cycle = ?", a.ID, cycle).Order("cycle").Scan(&amount).Error
	}
	return
}

// 获取资源组名称，如果为空则返回默认值
func getResourceGroupName(name string) string {
	if name == "" {
		return "（未设置资源组）"
	}
	return name
}

func (rs ResourceGroup) GetMonthlyBillAmount(cycle string) (amount *float64, err error) {
	a, err := rs.GetCloudAccounts()
	if err != nil {
		return
	}

	// 确保资源组名称不为空
	groupName := getResourceGroupName(rs.GroupDisplayName)

	switch a.CloudType {
	case TencentCloudType:
		err = app.DB().Table("asset_monthly_bills_tencentcloud").Select("SUM(real_total_cost) AS amount").Where("account_id = ? AND bill_cycle = ? AND project_name = ?", a.ID, cycle, groupName).Order("bill_cycle").Scan(&amount).Error
	case AliyunCloudType:
		err = app.DB().Table("asset_monthly_bills_aliyun").Select("SUM(pretax_amount) AS amount").Where("account_id = ? AND bill_cycle = ? AND resource_group = ?", a.ID, cycle, groupName).Order("bill_cycle").Scan(&amount).Error
	case HuaweiCloudType:
		err = app.DB().Table("asset_monthly_bills_hwcloud").Select("SUM(consume_amount) AS amount").Where("account_id = ? AND cycle = ? AND enterprise_project_name = ?", a.ID, cycle, groupName).Order("cycle").Scan(&amount).Error
	}
	return
}
func (rs ResourceGroup) GetMonthlyBillAmounts(startTime, endTime time.Time) (data []MonthlyBillAmount, err error) {
	billCycles := []string{
		endTime.Format("2006-01"),
	}
	for d := startTime; d.Before(endTime); d = d.AddDate(0, 1, 0) {
		billCycles = append(billCycles, d.Format("2006-01"))
	}
	a, err := rs.GetCloudAccounts()
	if err != nil {
		return
	}

	// 确保资源组名称不为空
	groupName := getResourceGroupName(rs.GroupDisplayName)

	switch a.CloudType {
	case TencentCloudType:
		dbop := app.DB().Where("project_name = ?", groupName)
		err = dbop.Table("asset_monthly_bills_tencentcloud").Select("bill_cycle AS bill_cycle,SUM(real_total_cost) AS amount").Where("bill_cycle IN (?)", billCycles).Group("bill_cycle").Order("bill_cycle").Find(&data).Error
	case AliyunCloudType:
		dbop := app.DB().Where("resource_group = ?", groupName)
		err = dbop.Table("asset_monthly_bills_aliyun").Select("bill_cycle AS bill_cycle,SUM(pretax_amount) AS amount").Where("bill_cycle IN (?)", billCycles).Group("bill_cycle").Order("bill_cycle").Find(&data).Error
	case HuaweiCloudType:
		dbop := app.DB().Where("enterprise_project_name = ?", groupName)
		err = dbop.Table("asset_monthly_bills_hwcloud").Select("cycle AS bill_cycle,SUM(consume_amount) AS amount").Where("cycle IN (?)", billCycles).Group("cycle").Order("cycle").Find(&data).Error
	}
	return
}

type MonthlyBillAmount struct {
	BillCycle string   `json:"bill_cycle"`
	Amount    *float64 `json:"amount"`
}

func (a CloudAccount) GetMonthlyBillAmounts(startTime, endTime time.Time, resourceID *string) (data []MonthlyBillAmount, err error) {
	billCycles := []string{
		endTime.Format("2006-01"),
	}
	for d := startTime; d.Before(endTime); d = d.AddDate(0, 1, 0) {
		billCycles = append(billCycles, d.Format("2006-01"))
	}
	dbop := app.DB().Where("account_id = ?", a.ID)
	switch a.CloudType {
	case TencentCloudType:
		if resourceID != nil {
			dbop = dbop.Where("resource_id = ?", *resourceID)
		}
		err = dbop.Table("asset_monthly_bills_tencentcloud").Select("bill_cycle AS bill_cycle,SUM(real_total_cost) AS amount").Where("bill_cycle IN (?)", billCycles).Group("bill_cycle").Order("bill_cycle").Find(&data).Error
	case AliyunCloudType:
		if resourceID != nil {
			dbop = dbop.Where("instance_id = ?", *resourceID)
		}
		err = dbop.Table("asset_monthly_bills_aliyun").Select("bill_cycle AS bill_cycle,SUM(pretax_amount) AS amount").Where("bill_cycle IN (?)", billCycles).Group("bill_cycle").Order("bill_cycle").Find(&data).Error
	case HuaweiCloudType:
		if resourceID != nil {
			dbop = dbop.Where("res_instance_id = ?", *resourceID)
		}
		err = dbop.Table("asset_monthly_bills_hwcloud").Select("cycle AS bill_cycle,SUM(consume_amount) AS amount").Where("cycle IN (?)", billCycles).Group("cycle").Order("cycle").Find(&data).Error
	}
	return
}

type MonthlyBillResourceTypeAmount struct {
	ResourceType string  `gorm:"column:resource_type;comment:资源类型" json:"resource_type"`
	Amount       float64 `gorm:"column:amount;comment:金额" json:"amount"`
}

func (a CloudAccount) GetMonthlyBillResourceTypeAmount(billCycle string) (data []MonthlyBillResourceTypeAmount, err error) {
	dbop := app.DB().Where("account_id = ?", a.ID)
	switch a.CloudType {
	case TencentCloudType:
		err = dbop.Table("asset_monthly_bills_tencentcloud").Select("business_code_name AS resource_type,SUM(real_total_cost) AS amount").Where("bill_cycle = ?", billCycle).Group("resource_type").Order("resource_type").Find(&data).Error
	case AliyunCloudType:
		err = dbop.Table("asset_monthly_bills_aliyun").Select("product_name AS resource_type,SUM(pretax_amount) AS amount").Where("bill_cycle = ?", billCycle).Group("resource_type").Order("resource_type").Find(&data).Error
	case HuaweiCloudType:
		err = dbop.Table("asset_monthly_bills_hwcloud").Select("resource_type_name AS resource_type,SUM(consume_amount) AS amount").Where("cycle = ?", billCycle).Group("resource_type").Order("resource_type").Find(&data).Error
	}
	return
}

type MonthlyBillResourceTypeAmounts struct {
	BillCycle    string  `gorm:"column:bill_cycle;comment:账单周期" json:"bill_cycle"`
	ResourceType string  `gorm:"column:resource_type;comment:资源类型" json:"resource_type"`
	Amount       float64 `gorm:"column:amount;comment:金额" json:"amount"`
}

func (a CloudAccount) GetMonthlyBillResourceTypeAmountsByCycles(startTime, endTime time.Time) (finalData map[string]any, err error) {
	data := make(map[string][]MonthlyBillAmount)
	billCycles := []string{
		endTime.Format("2006-01"),
	}
	for d := startTime; d.Before(endTime); d = d.AddDate(0, 1, 0) {
		billCycles = append(billCycles, d.Format("2006-01"))
	}
	sort.Strings(billCycles)
	sData := []MonthlyBillResourceTypeAmounts{}
	dbop := app.DB().Where("account_id = ?", a.ID)
	switch a.CloudType {
	case TencentCloudType:
		err = dbop.Table("asset_monthly_bills_tencentcloud").Select("bill_cycle AS bill_cycle,business_code_name AS resource_type,ROUND(SUM(real_total_cost),2) AS amount").Where("bill_cycle IN (?)", billCycles).Group("bill_cycle,resource_type").Order("bill_cycle,resource_type").Find(&sData).Error
	case AliyunCloudType:
		err = dbop.Table("asset_monthly_bills_aliyun").Select("bill_cycle AS bill_cycle,product_name AS resource_type,ROUND(SUM(pretax_amount),2) AS amount").Where("bill_cycle IN (?)", billCycles).Group("bill_cycle,resource_type").Order("bill_cycle,resource_type").Find(&sData).Error
	case HuaweiCloudType:
		err = dbop.Table("asset_monthly_bills_hwcloud").Select("cycle AS bill_cycle,resource_type_name AS resource_type,ROUND(SUM(consume_amount),2) AS amount").Where("cycle IN (?)", billCycles).Group("cycle,resource_type").Order("cycle,resource_type").Find(&sData).Error
	}
	for _, v := range sData {
		if val, ok := data[v.ResourceType]; ok {
			data[v.ResourceType] = append(val, MonthlyBillAmount{
				BillCycle: v.BillCycle,
				Amount:    &v.Amount,
			})
		} else {
			data[v.ResourceType] = []MonthlyBillAmount{
				{
					BillCycle: v.BillCycle,
					Amount:    &v.Amount,
				},
			}
		}
	}
	newData := []any{}
	newBillCycles := []string{}
	for i, cycle := range billCycles {
		if i == 0 {
			newBillCycles = append(newBillCycles, cycle)
		} else {
			newBillCycles = append(newBillCycles, cycle, cycle+"_change")
		}
	}
	for k, v := range data {
		amounts := map[string]any{
			"resource_type": k,
		}
		var currentAmount, preAmount float64
		for i, cycle := range billCycles {
			var found bool
			for _, amount := range v {
				if amount.BillCycle == cycle {
					amounts[cycle] = amount.Amount
					if amount.Amount != nil {
						currentAmount = *amount.Amount
					} else {
						currentAmount = 0
					}
					found = true
					break
				}
			}
			if !found {
				amounts[cycle] = 0
			}
			if i > 0 {
				amounts[cycle+"_change"] = currentAmount - preAmount
			}
			preAmount = currentAmount
		}
		newData = append(newData, amounts)
	}
	finalData = map[string]any{
		"data":    newData,
		"columns": append([]string{"resource_type"}, newBillCycles...),
	}
	return
}

// 获取云服务商的月度账单
func (cloudType CloudType) GetCloudTypeMonthlyBills(billCycle string) (amount *float64, err error) {
	switch cloudType {
	case TencentCloudType:
		// 获取腾讯云的月度账单
		err = app.DB().Table("asset_monthly_bills_tencentcloud").Select("SUM(real_total_cost) AS amount").Where("bill_cycle = ?", billCycle).Scan(&amount).Error
		if err != nil {
			return
		}
	case AliyunCloudType:
		// 获取阿里云的月度账单
		err = app.DB().Table("asset_monthly_bills_aliyun").Select("SUM(pretax_amount) AS amount").Where("bill_cycle = ?", billCycle).Scan(&amount).Error
		if err != nil {
			return
		}
	case HuaweiCloudType:
		// 获取华为云的月度账单
		err = app.DB().Table("asset_monthly_bills_hwcloud").Select("SUM(consume_amount) AS amount").Where("cycle = ?", billCycle).Scan(&amount).Error
		if err != nil {
			return
		}
	}
	return
}

// 获取云服务商的月度账单
type CloudTypeMonthlyBills struct {
	Name         string              `json:"name"`
	CloudType    CloudType           `json:"cloud_type"`
	MonthlyBills []MonthlyBillAmount `json:"monthly_bills"`
}

// 获取云服务商的月度账单
func GetCloudTypeMonthlyBills(startTime, endTime time.Time) (data []CloudTypeMonthlyBills, err error) {
	billCycles := []string{
		endTime.Format("2006-01"),
	}
	for d := startTime; d.Before(endTime); d = d.AddDate(0, 1, 0) {
		billCycles = append(billCycles, d.Format("2006-01"))
	}
	data = []CloudTypeMonthlyBills{
		{
			Name:         TencentCloudType.String(),
			CloudType:    TencentCloudType,
			MonthlyBills: []MonthlyBillAmount{},
		},
		{
			Name:         AliyunCloudType.String(),
			CloudType:    AliyunCloudType,
			MonthlyBills: []MonthlyBillAmount{},
		},
		{
			Name:         HuaweiCloudType.String(),
			CloudType:    HuaweiCloudType,
			MonthlyBills: []MonthlyBillAmount{},
		},
	}
	var wg sync.WaitGroup
	maxConcurrent := make(chan struct{}, 10)
	for _, billCycle := range billCycles {
		wg.Add(1)
		go func(billCycle string) {
			defer wg.Done()
			maxConcurrent <- struct{}{}
			for i, cloudType := range data {
				amount, _ := cloudType.CloudType.GetCloudTypeMonthlyBills(billCycle)
				data[i].MonthlyBills = append(data[i].MonthlyBills, MonthlyBillAmount{
					BillCycle: billCycle,
					Amount:    amount,
				})
			}
			<-maxConcurrent
		}(billCycle)
	}
	wg.Wait()
	return
}
