package mysql

import (
	"cmdb/app"
	"cmdb/app/service/appops/domain"
	"cmdb/app/service/audit"
	"cmdb/app/service/database/mysql"
	"encoding/json"
	"errors"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

func AddInstance(c *gin.Context) {
	var form mysql.InstanceForm
	if err := c.ShouldBindJSON(&form); err != nil {
		app.Log().Debug("添加MySQL实例，验证表单失败", "err", err)
		app.FailedResponseMsg(c, "表单验证失败")
		return
	}
	if err := form.Create(); err != nil {
		msg := "添加失败"
		if errors.Is(err, mysql.ErrInstanceExist) {
			msg = err.Error()
		} else {
			app.Log().Error("添加MySQL失败", "err", err)
		}
		app.FailedResponseMsg(c, msg)
		return
	}
	audit.LogAPIOP(c, "资产管理", "添加MySQL实例："+form.Name)
	app.SuccessResponseMsg(c, "添加成功")
}

func UpdateInstance(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.Log().Debug("更新MySQL实例，参数错误", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	var form mysql.InstanceForm
	if err := c.ShouldBindJSON(&form); err != nil {
		app.Log().Debug("更新MySQL实例，验证表单失败", "err", err)
		app.FailedResponseMsg(c, "表单验证失败")
		return
	}
	instance, err := mysql.GetInstanceByID(id)
	if err != nil {
		app.Log().Error("更新MySQL实例失败", "err", err)
		app.FailedResponseMsg(c, "更新失败")
		return
	}
	if err := instance.Update(form); err != nil {
		msg := "更新失败"
		if errors.Is(err, mysql.ErrInstanceExist) {
			msg = err.Error()
		} else {
			app.Log().Error("更新MySQL实例失败", "err", err)
		}
		app.FailedResponseMsg(c, msg)
		return
	}
	audit.LogAPIOP(c, "资产管理", "更新MySQL实例："+form.Name)
	app.SuccessResponseMsg(c, "更新成功")
}

func DeleteInstance(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.Log().Debug("删除MySQL实例，参数错误", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	instance, err := mysql.GetInstanceByID(id)
	if err != nil {
		app.Log().Error("删除MySQL实例失败", "err", err)
		app.FailedResponseMsg(c, "删除失败")
		return
	}
	if err := instance.Delete(); err != nil {
		app.Log().Error("删除MySQL实例失败", "err", err)
		app.FailedResponseMsg(c, "删除失败")
		return
	}
	audit.LogAPIOP(c, "资产管理", "删除MySQL实例："+instance.Name)
	app.SuccessResponseMsg(c, "删除成功")
}

type InstanceItem struct {
	mysql.Instance
	Domains []domain.Domain `json:"domains"`
}

func GetInstances(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	offset := (page - 1) * limit
	var keyword *string
	if val := c.Query("keyword"); val != "" {
		keyword = &val
	}
	var instanceType *mysql.InstanceType
	if val := c.Query("instance_type"); val != "" {
		t := mysql.InstanceType(val)
		instanceType = &t
	}
	count, data, err := mysql.GetInstances(offset, limit, instanceType, keyword)
	if err != nil {
		app.Log().Error("获取MySQL实例列表失败", "err", err)
		app.FailedResponseMsg(c, "获取失败")
		return
	}
	newData := make([]InstanceItem, len(data))
	for i, instance := range data {
		// 获取实例的域名
		newData[i] = InstanceItem{
			Instance: instance,
		}
		ids := []int{}
		if err1 := json.Unmarshal(instance.BusinessDomainIDs, &ids); err1 == nil {
			if len(ids) > 0 {
				newData[i].Domains, err = domain.GetDomainsByIDs(ids)
				if err != nil {
					app.Log().Error("获取MySQL实例域名失败", "err", err)
				}

			}
		}
	}
	app.SuccessResponseDataCount(c, newData, count)
}

func GetInstanceTables(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.Log().Debug("获取MySQL实例表，参数错误", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	offset := (page - 1) * limit
	var keyword *string
	if val := c.Query("keyword"); val != "" {
		keyword = &val
	}
	var schema *string
	if val := c.Query("schema"); val != "" {
		schema = &val
	}

	instance, err := mysql.GetInstanceByID(id)
	if err != nil {
		app.Log().Error("获取MySQL实例表，获取MySQL实例失败", "err", err)
		app.FailedResponseMsg(c, "获取MySQL实例失败")
		return
	}
	count, data, err := instance.GetTables(offset, limit, schema, keyword)
	if err != nil {
		app.Log().Error("获取MySQL实例表失败", "err", err)
		app.FailedResponseMsg(c, "获取失败")
		return
	}
	app.SuccessResponseDataCount(c, data, count)

}

func SyncInstanceMetadata(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.Log().Debug("同步MySQL实例元数据，参数错误", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	instance, err := mysql.GetInstanceByID(id)
	if err != nil {
		app.Log().Error("同步MySQL实例元数据，获取MySQL实例失败", "err", err)
		app.FailedResponseMsg(c, "获取MySQL实例失败")
		return
	}
	// 创建一个done通道
	done := make(chan struct{}, 1)
	// 启动一个goroutine进行同步
	go func() {
		// 记录日志
		audit.LogAPIOP(c, "资产管理", "提交同步 MySQL 实例元数据，实例："+instance.Name)
		// 同步数据库
		if err = instance.SyncSchemas(); err != nil {
			app.Log().Error("同步MySQL实例数据库失败", "err", err)
		}
		// 同步表
		if err = instance.SyncTables(); err != nil {
			app.Log().Error("同步MySQL实例表失败", "err", err)
		}
		// 发送完成信号
		done <- struct{}{}
	}()
	// 等待同步完成
	ticker := time.NewTicker(500 * time.Millisecond)
	select {
	case <-done:
		if err != nil {
			app.FailedResponseMsg(c, err.Error())
		} else {
			app.SuccessResponseMsg(c, "同步成功")
		}
		break
	case <-ticker.C:
		app.SuccessResponseMsg(c, "任务提交成功")
		break
	}
}

func TestConnection(c *gin.Context) {
	var form mysql.InstanceForm
	if err := c.ShouldBindJSON(&form); err != nil {
		app.Log().Debug("测试MySQL实例连接，验证表单失败", "err", err)
		app.FailedResponseMsg(c, "表单验证失败")
		return
	}
	var username string
	var err error
	if username, err = form.TestConnect(); err != nil {
		app.Log().Error("测试MySQL实例连接失败", "err", err)
		app.FailedResponseMsg(c, "连接失败:"+err.Error())
		return
	}
	app.SuccessResponseMsg(c, "连接成功，用户名:"+username)
}
