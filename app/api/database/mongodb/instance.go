package mongodb

import (
	"cmdb/app"
	"cmdb/app/service/audit"
	"cmdb/app/service/database/mongodb"
	"strconv"

	"github.com/gin-gonic/gin"
)

// 获取MongoDB实例列表
func GetInstances(c *gin.Context) {
	// 获取页码，默认为1
	page, _ := strconv.Atoi(c.<PERSON><PERSON>("page", "1"))
	// 获取每页显示数量，默认为10
	limit, _ := strconv.Atoi(c.<PERSON><PERSON>ult<PERSON>("limit", "10"))
	// 计算偏移量
	offset := (page - 1) * limit
	var keyword *string
	// 获取关键字
	if val := c.Query("keyword"); val != "" {
		keyword = &val
	}
	// 调用数据库获取实例列表
	count, data, err := mongodb.GetInstances(offset, limit, keyword)
	if err != nil {
		// 记录错误日志
		app.Log().Error("获取MongoDB实例列表失败", "err", err)
		// 返回失败响应
		app.FailedResponseMsg(c, "获取失败")
		return
	}
	// 返回成功响应
	app.SuccessResponseDataCount(c, data, count)
}

// 获取所有MongoDB实例
func GetAllInstances(c *gin.Context) {
	// 调用数据库获取所有实例
	data, err := mongodb.GetAllInstances()
	if err != nil {
		// 记录错误日志
		app.Log().Error("获取MongoDB实例列表失败", "err", err)
		// 返回失败响应
		app.FailedResponseMsg(c, "获取失败")
		return
	}
	// 返回成功响应
	app.SuccessResponseData(c, data)
}

func CreateInstance(c *gin.Context) {
	var form mongodb.InstanceForm
	// 解析表单数据
	if err := c.ShouldBind(&form); err != nil {
		// 记录错误日志
		app.Log().Error("解析表单数据失败", "err", err)
		// 返回失败响应
		app.FailedResponseMsg(c, "解析失败")
		return
	}
	if err := form.Create(); err != nil {
		// 记录错误日志
		app.Log().Error("创建MongoDB实例失败", "err", err)
		// 返回失败响应
		app.FailedResponseMsg(c, "创建失败")
		return
	}
	audit.LogAPIOP(c, "数据库管理", "创建MongoDB实例:"+form.Name)
	// 返回成功响应
	app.SuccessResponseMsg(c, "创建成功")
}

func UpdateInstance(c *gin.Context) {
	var form mongodb.InstanceForm
	// 解析表单数据
	if err := c.ShouldBind(&form); err != nil {
		// 记录错误日志
		app.Log().Error("解析表单数据失败", "err", err)
		// 返回失败响应
		app.FailedResponseMsg(c, "解析失败")
		return
	}
	var id int
	var err error
	if id, err = strconv.Atoi(c.Param("id")); err != nil {
		// 记录错误日志
		app.Log().Error("解析参数失败", "err", err)
		// 返回失败响应
		app.FailedResponseMsg(c, "解析失败")
		return
	}
	instance, err := mongodb.GetInstanceByID(id)
	if err != nil {
		// 记录错误日志
		app.Log().Error("获取MongoDB实例失败", "err", err)
		// 返回失败响应
		app.FailedResponseMsg(c, "获取失败")
		return
	}
	if err = instance.Update(form); err != nil {
		// 记录错误日志
		app.Log().Error("更新MongoDB实例失败", "err", err)
		// 返回失败响应
		app.FailedResponseMsg(c, "更新失败")
		return
	}
	audit.LogAPIOP(c, "数据库管理", "更新MongoDB实例:"+form.Name)
	// 返回成功响应
	app.SuccessResponseMsg(c, "更新成功")
}

func DeleteInstance(c *gin.Context) {
	var id int
	var err error
	if id, err = strconv.Atoi(c.Param("id")); err != nil {
		// 记录错误日志
		app.Log().Error("解析参数失败", "err", err)
		// 返回失败响应
		app.FailedResponseMsg(c, "解析失败")
		return
	}
	instance, err := mongodb.GetInstanceByID(id)
	if err != nil {
		// 记录错误日志
		app.Log().Error("获取MongoDB实例失败", "err", err)
		// 返回失败响应
		app.FailedResponseMsg(c, "获取失败")
		return
	}
	err = instance.Delete()
	if err != nil {
		// 记录错误日志
		app.Log().Error("删除MongoDB实例失败", "err", err)
		// 返回失败响应
		app.FailedResponseMsg(c, "删除失败")
		return
	}
	audit.LogAPIOP(c, "数据库管理", "删除MongoDB实例:"+instance.Name)
	// 返回成功响应
	app.SuccessResponseMsg(c, "删除成功")
}
