package task

import (
	"cmdb/app"
	"cmdb/app/service/audit"
	"cmdb/app/service/auth"
	"cmdb/app/service/task"
	"errors"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

func GetBatchTasks(c *gin.Context) {
	page, _ := strconv.Atoi(c<PERSON>("page", "1"))
	limit, _ := strconv.Atoi(c<PERSON>("limit", "10"))
	offset := (page - 1) * limit
	var keyword *string
	if val := strings.TrimSpace(c.Query("keyword")); val != "" {
		keyword = &val
	}
	var status *int
	if val := strings.TrimSpace(c.Query("status")); val != "" {
		if vat, err1 := strconv.Atoi(val); err1 == nil {
			status = &vat
		}
	}
	var taskType *string
	if val := strings.TrimSpace(c.<PERSON><PERSON>("task_type")); val != "" {
		taskType = &val
	}
	var ip *string
	if val := strings.TrimSpace(c.Query("ip")); val != "" {
		ip = &val
	}
	count, data, err := task.GetBatchTasks(offset, limit, taskType, ip, keyword, status)
	if err != nil {
		app.Log().Error("获取批量任务列表失败", "err", err)
		app.FailedResponseMsg(c, "获取任务失败")
		return
	}
	app.SuccessResponseDataCount(c, data, count)
}

func ReRunBatchTask(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	t, err := task.GetBatchTaskByID(id)
	if err != nil {
		app.FailedResponseMsg(c, "任务不存在")
		return
	}
	u, err := auth.GetLoginUser(c)
	if err != nil {
		app.FailedResponseMsg(c, "用户未登录")
		return
	}
	// 执行任务
	err = t.ReRun(u.Name + "(" + u.Username + ")")
	if err != nil {
		msg := "任务执行失败"
		if errors.Is(err, task.ErrBatchTaskRan) {
			msg = err.Error()
		} else {
			app.Log().Error("执行任务失败", "err", err)
		}
		app.FailedResponseMsg(c, msg)
		return
	}
	audit.LogAPIOP(c, "任务管理", "重试执行异步任务:"+t.IP+"的"+t.Name)
	app.SuccessResponseMsg(c, "任务提交成功")
}

func RunBatchTask(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	t, err := task.GetBatchTaskByID(id)
	if err != nil {
		app.FailedResponseMsg(c, "任务不存在")
		return
	}
	u, err := auth.GetLoginUser(c)
	if err != nil {
		app.FailedResponseMsg(c, "用户未登录")
		return
	}
	// 执行任务
	err = t.Run(u.Name + "(" + u.Username + ")")
	if err != nil {
		msg := "任务执行失败"
		if errors.Is(err, task.ErrBatchTaskRan) {
			msg = err.Error()
		} else {
			app.Log().Error("执行任务失败", "err", err)
		}
		app.FailedResponseMsg(c, msg)
		return
	}
	audit.LogAPIOP(c, "任务管理", "执行异步任务:"+t.IP+"的"+t.Name)
	app.SuccessResponseMsg(c, "任务提交成功")
}

func DeleteBatchTask(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	t, err := task.GetBatchTaskByID(id)
	if err != nil {
		app.Log().Error("获取批量任务失败", "err", err)
		app.FailedResponseMsg(c, "任务不存在")
		return
	}
	err = t.Delete()
	if err != nil {
		app.FailedResponseMsg(c, "任务删除失败")
		return
	}
	audit.LogAPIOP(c, "任务管理", "删除任务:"+t.IP+" "+t.Name)
	app.SuccessResponseMsg(c, "		删除成功")
}

func CreateBatchTask(c *gin.Context) {
	var form task.BatchTaskForm
	err := c.ShouldBind(&form)
	if err != nil {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	u, err := auth.GetLoginUser(c)
	if err != nil {
		app.FailedResponseMsg(c, "用户未登录")
		return
	}
	err = form.CreateTasks(u.Name + "(" + u.Username + ")")
	if err != nil {
		app.FailedResponseMsg(c, "任务创建失败")
		return
	}
	audit.LogAPIOP(c, "任务管理", "创建批量任务")
	app.SuccessResponseMsg(c, "任务创建成功")
}

func BatRunBatchTask(c *gin.Context) {
	var form task.BatchRunForm
	err := c.ShouldBind(&form)
	if err != nil {
		app.Log().Error("批量提交任务失败，验证表单失败", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	u, err := auth.GetLoginUser(c)
	if err != nil {
		app.FailedResponseMsg(c, "用户未登录")
		return
	}
	err = form.BatRun(u.Name + "(" + u.Username + ")")
	if err != nil {
		app.Log().Error("批量提交任务失败", "err", err)
		app.FailedResponseMsg(c, "任务提交失败")
		return
	}
	app.SuccessResponseMsg(c, "提交成功")
}

func GetBatchTask(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	task, err := task.GetBatchTaskByID(id)
	if err != nil {
		app.FailedResponseMsg(c, "任务获取失败")
		return
	}
	app.SuccessResponseData(c, task)
}
