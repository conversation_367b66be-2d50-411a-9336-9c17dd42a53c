package workflow

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/auth"
	"cmdb/app/service/database/mongodb"
	"errors"
	"time"
)

type OrderMongoDB struct {
	ID         uint   `gorm:"column:id;primarykey;comment:id" json:"id"`
	SN         string `gorm:"type:varchar(128);unique;column:sn;comment:工单单号" json:"sn"`
	Content    string `gorm:"type:text;column:content;comment:工单内容" json:"content"`
	InstanceID uint   `gorm:"column:instance_id;comment:关联实例id" json:"instance_id"`
	Username   string `gorm:"column:username;comment:用户名" json:"username"`
	DBName     string `gorm:"column:db_name;comment:数据库名" json:"db_name"`
	Role       string `gorm:"column:role;type:varchar(255);comment:权限角色" json:"role"`
}

// TableName 表名
func (OrderMongoDB) TableName() string {
	return "workflow_order_mongodbs"
}

type OrderMongoDBDetail struct {
	Order      `gorm:"-"`
	Content    string            `gorm:"type:text;column:content;comment:工单内容" json:"content"`
	InstanceID uint              `gorm:"column:instance_id;comment:关联实例id" json:"instance_id"`
	Instance   *mongodb.Instance `gorm:"-" json:"instance,omitempty"`
	Username   string            `gorm:"column:username;comment:用户名" json:"username"`
	DBName     string            `gorm:"column:db_name;comment:数据库名" json:"db_name"`
	Role       string            `gorm:"column:role;type:varchar(255);comment:权限角色" json:"role"`
}

// 设置表名
func (OrderMongoDBDetail) TableName() string {
	return "workflow_order_mongodbs"
}

func GetOrderMongoDBDetail(o Order) (detail OrderMongoDBDetail, err error) {
	detail = OrderMongoDBDetail{}
	err = app.DB().Where("sn = ?", o.SN).Take(&detail).Error
	if err != nil {
		return
	}
	detail.Order = o
	instance, err := mongodb.GetInstanceByID(int(detail.InstanceID))
	if err != nil {
		return
	}
	detail.Instance = &instance
	return
}

type OrderMongoDBForm struct {
	Content    string    `json:"content" binding:"required"`
	DBName     string    `json:"db_name" binding:"required,max=64"`
	Role       string    `json:"role" binding:"required,max=64"`
	InstanceID uint      `json:"instance_id" binding:"required"`
	PlanTime   time.Time `json:"plan_time" binding:"required"`
	Orders     []string  `json:"orders"`
	Env        asset.ENV `json:"env"`
	Critical   bool      `json:"critical"`
}

func (form OrderMongoDBForm) GenProcesses(sn string, a Applicant, admin *auth.User) (processes []Process, err error) {
	var flow Flow
	err = app.DB().Where("order_type = ?", MongoDBOrderType).Take(&flow).Error
	if err != nil {
		return
	}
	flow.Nodes, err = flow.GetFlowNodes()
	if err != nil {
		return
	}
	processes, err = flow.genProcesses(sn, a, admin)
	return
}

func (form OrderMongoDBForm) check() (err error) {
	return
}

func (form OrderMongoDBForm) Create(a Applicant, flow Flow, admin *auth.User) (err error) {
	err = form.check()
	if err != nil {
		return
	}
	sn := genSN("mongodb")
	processes, err := flow.genProcesses(sn, a, admin)
	if err != nil || len(processes) == 0 {
		return
	}
	status := ApprovingStatus
	var index uint
	if len(processes) == 0 {
		err = errors.New("生成流程失败")
		return
	} else if len(processes) <= 1 {
		// 如果流程没有配置就直接结束流程
		status = CompletedStatus
	} else {
		// 如果有流程就获取下一个节点的索引
		index = processes[1].ProcessIndex
		processes[1].Status = ApprovingProcessStatus
	}
	planTime := time.Now().AddDate(0, 0, 1)
	order := Order{
		Title:        "申请MongoDB账号 - " + a.Name,
		OrderType:    MongoDBOrderType.Name(),
		Env:          form.Env,
		ExtType:      MongoDBExtType,
		SN:           sn,
		Critical:     form.Critical,
		ApplicantID:  a.ID,
		Status:       status,
		PlanTime:     &planTime,
		ProcessIndex: index,
	}
	extOrder := OrderMongoDB{
		SN:         sn,
		Content:    form.Content,
		InstanceID: form.InstanceID,
		DBName:     form.DBName,
		Username:   a.Username,
		Role:       form.Role,
	}
	dbop := app.DB().Begin()
	err = dbop.Create(&order).Error
	if err != nil {
		dbop.Rollback()
		return
	}
	err = dbop.Create(&extOrder).Error
	if err != nil {
		dbop.Rollback()
		return
	}
	err = dbop.Create(&processes).Error
	if err != nil {
		dbop.Rollback()
		return
	}
	err = dbop.Commit().Error
	if err != nil {
		dbop.Rollback()
	}
	go order.notifyNextProcess()
	return
}

func (form OrderMongoDBForm) Update(o Order) (err error) {
	extOrder := OrderMongoDB{}
	err = app.DB().Where("sn = ?", o.SN).Take(&extOrder).Error
	if err != nil {
		return
	}
	updateMap := map[string]any{}
	if form.Content != extOrder.Content {
		updateMap["content"] = form.Content
	}
	if form.InstanceID != extOrder.InstanceID {
		updateMap["instance_id"] = form.InstanceID
	}
	if form.DBName != extOrder.DBName {
		updateMap["db_name"] = form.DBName
	}
	if form.Role != extOrder.Role {
		updateMap["role"] = form.Role
	}
	if len(updateMap) > 0 {
		err = app.DB().Model(&extOrder).Updates(updateMap).Error
	}
	return
}
