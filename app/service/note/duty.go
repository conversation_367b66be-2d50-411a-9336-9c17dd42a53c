package note

import (
	"bytes"
	"cmdb/app"
	"cmdb/app/service/auth"
	"cmdb/app/service/notice"
	"cmdb/app/service/setting"
	"errors"
	"time"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

var (
	ErrPassedDuty = errors.New("值班日期已过")
)

// 排班人员
type DutyMember struct {
	ID         uint `gorm:"column:id;primarykey;comment:主键ID" json:"id"`
	UID        uint `gorm:"column:uid;index;comment:用户ID" json:"uid"`
	OrderIndex uint `gorm:"column:order_index;index;comment:用户排序" json:"order_index"`
}

// 设置表名
func (DutyMember) TableName() string {
	return "note_duty_members"
}

// 排班
type DutyMemberDetail struct {
	DutyMember
	Name  string `gorm:"column:name" json:"name"`
	Phone string `gorm:"column:phone" json:"phone"`
	SN    string `gorm:"column:sn" json:"sn"`
}

// 设置表名
func (d DutyMemberDetail) TableName() string {
	return d.DutyMember.TableName()
}

type Duty struct {
	ID      uint           `gorm:"column:id;primarykey;comment:主键ID" json:"id"`
	UID     uint           `gorm:"column:uid;index;comment:用户ID" json:"uid"`
	DueDate datatypes.Date `gorm:"column:due_date;type:date;unique;comment:值班日期" json:"due_date"`
}

func (Duty) TableName() string {
	return "note_dutys"
}

// 排班
type DutyDetail struct {
	Duty
	Name          string `gorm:"column:name" json:"name"`
	Phone         string `gorm:"column:phone" json:"phone"`
	SN            string `gorm:"column:sn" json:"sn"`
	DueDateString string `gorm:"-" json:"due_date_string"`
}

// 设置表名
func (detail *DutyDetail) TableName() string {
	return detail.Duty.TableName()
}

// 值班人员
type DutyMemberForm struct {
	UID        uint `json:"uid" binding:"required,min=1"`
	OrderIndex uint `json:"order_index" binding:"required,min=1"`
}

type DutyMembersForm struct {
	Members []DutyMemberForm `json:"members" binding:"required"`
}

// 更新排班人员
func UpdateDutyMembers(forms DutyMembersForm) (err error) {
	indexs := []uint{}
	dbop := app.DB().Begin()
	for i := range forms.Members {
		if forms.Members[i].OrderIndex < 1 {
			continue
		}
		indexs = append(indexs, forms.Members[i].OrderIndex)
		member := DutyMember{}
		err = app.DB().Select("id").Where("order_index = ?", forms.Members[i].OrderIndex).Take(&member).Error
		if err == nil {
			err = dbop.Model(&member).Update("uid", forms.Members[i].UID).Error
		} else if err == gorm.ErrRecordNotFound {
			err = dbop.Create(&DutyMember{OrderIndex: forms.Members[i].OrderIndex, UID: forms.Members[i].UID}).Error
		}
		if err != nil {
			dbop.Rollback()
			return
		}
	}
	if len(indexs) > 0 {
		err = dbop.Where("order_index NOT IN (?)", indexs).Delete(&DutyMember{}).Error
		if err != nil {
			dbop.Rollback()
			return
		}
	} else {
		// 清空记录
		err = dbop.Where("1=1").Delete(&DutyMember{}).Error
		if err != nil {
			dbop.Rollback()
			return
		}
	}
	err = dbop.Commit().Error
	if err != nil {
		dbop.Rollback()
	}
	return
}

// 自动排班
func AutoScheduleDuty(startTime time.Time, startUID uint) (err error) {
	scheduleMembers := []DutyMember{}
	err = app.DB().Select("id", "uid").Order("order_index").Find(&scheduleMembers).Error
	if err != nil {
		return
	}
	number := len(scheduleMembers)
	if number < 1 {
		// 清空值班
		err = app.DB().Where("due_date >= ?", startTime).Delete(&Duty{}).Error
		return
	}
	// 需要自动排版的时间
	days := number * number * 2
	startIndex := 0
	if startUID > 0 {
		// 获取开始日期时候的索引
		for i := range scheduleMembers {
			if scheduleMembers[i].UID == startUID {
				startIndex = i % number
				break
			}
		}
	}
	if days < 60 {
		days = 60
	}
	dbop := app.DB().Begin()
	dueDate := startTime
	for i := 1; i <= int(days); i++ {
		dueDate = startTime.AddDate(0, 0, i)
		index := (startIndex + i) % number
		s := Duty{}
		err = app.DB().Select("id").Where("due_date = ?", dueDate.Format(time.DateOnly)).Take(&s).Error
		if err == gorm.ErrRecordNotFound {
			err = dbop.Create(&Duty{
				UID:     scheduleMembers[index].UID,
				DueDate: datatypes.Date(dueDate),
			}).Error
			if err != nil {
				dbop.Rollback()
				return
			}
		} else if err == nil {
			err = dbop.Model(&s).Updates(map[string]any{
				"uid": scheduleMembers[index].UID,
			}).Error
			if err != nil {
				dbop.Rollback()
				return
			}
		} else {
			dbop.Rollback()
			return
		}
		if index == number-1 && number%7 == 0 {
			// 如果已经是最后一个人，就判断是否要位移一位
			newScheduleMembers := make([]DutyMember, number)
			for j := range scheduleMembers {
				ii := (j - 1 + number) % number
				newScheduleMembers[j] = scheduleMembers[ii]
			}
			scheduleMembers = newScheduleMembers
		}
	}
	err = dbop.Where("due_date > ?", dueDate).Delete(&Duty{}).Error
	if err != nil {
		dbop.Rollback()
		return
	}
	err = dbop.Commit().Error
	if err != nil {
		dbop.Rollback()
	}
	return
}

// 获取所有的排班人员
func GetAllDutyMembers() (members []DutyMemberDetail, err error) {
	err = app.DB().Select(
		"note_duty_members.id", "note_duty_members.uid", "note_duty_members.order_index",
		"auth_users.name", "auth_users.phone",
	).Joins("JOIN auth_users ON auth_users.id = note_duty_members.uid").
		Order("note_duty_members.order_index").Find(&members).Error
	return
}

// 按照时间获取排班
func GetDutys(startTime, endTime time.Time) (dutyLists []DutyDetail, err error) {
	err = app.DB().Select(
		"note_dutys.id", "note_dutys.uid", "auth_users.name",
		"auth_users.phone", "note_dutys.uid",
		"auth_users.sn", "note_dutys.due_date",
	).Joins("JOIN auth_users ON auth_users.id = note_dutys.uid").
		Where("due_date >= ? AND due_date <? ",
			time.Date(startTime.Year(), startTime.Month(), startTime.Day(), 0, 0, 0, 0, time.Local),
			time.Date(endTime.Year(), endTime.Month(), endTime.Day(), 0, 0, 0, 0, time.Local)).
		Order("note_dutys.due_date").Find(&dutyLists).Error
	return
}

// NotifyDuty 通知值班
func NotifyDuty() (err error) {
	dueDate := time.Now()
	dueDate = time.Date(dueDate.Year(), dueDate.Month(), dueDate.Day(), 0, 0, 0, 0, time.Local)
	schedule := DutyDetail{}
	err = app.DB().Select(
		"note_dutys.id", "note_dutys.due_date", "note_dutys.uid",
		"auth_users.name", "auth_users.sn", "auth_users.phone",
	).Joins("LEFT JOIN auth_users ON auth_users.id = note_dutys.uid").
		Where("due_date = ?", dueDate).Take(&schedule).Error
	if err != nil {
		return
	}
	var contents bytes.Buffer
	contents.WriteString(schedule.Name + " 你好！\n今天：" + dueDate.Format("2006-01-02") + "是你值班！")
	contents.WriteString("\n\n联系方式\n")
	members, err := GetAllDutyMembers()
	if err == nil {
		for i := range members {
			contents.WriteString(members[i].Name + "：" + members[i].Phone + "\n")
		}
	}
	contents.WriteString("\n排班详情： https://cmdb-ops.meiyou.com/note/duty\n")
	err = notice.CreateMessage("值班通知", contents.String(), notice.MessageMeiyou, schedule.Name, schedule.SN)
	if err != nil {
		return
	}

	err = notice.CreateMessage("值班通知", contents.String(),
		notice.MessageDingtalkRobot, "值班通知机器人", setting.GetDutyConfig().DingtalkRobotURL, schedule.Phone)
	return
}

// GetDutyByID 获取值班对象
func GetDutyByDate(day time.Time) (s Duty, err error) {
	err = app.DB().Select("id", "uid", "due_date").
		Where("due_date = ?", day.Format("2006-01-02")).Take(&s).Error
	return
}

// AdjustDuty 调班
func AdjustDuty(from, to *Duty) (err error) {
	now := time.Now().Truncate(24 * time.Hour)
	if time.Time(from.DueDate).Before(now) || time.Time(to.DueDate).Before(now) {
		return errors.New("值班日期已经过了")
	}
	dbop := app.DB().Begin()
	err = dbop.Model(&Duty{ID: from.ID}).Update("uid", to.UID).Error
	if err != nil {
		dbop.Rollback()
		return
	}
	err = dbop.Model(&Duty{ID: to.ID}).Update("uid", from.UID).Error
	if err != nil {
		dbop.Rollback()
		return
	}
	err = dbop.Commit().Error
	if err != nil {
		dbop.Rollback()
	}
	return
}

// 自动排班
func CronAutoSchedule() (err error) {
	startTime := time.Now()
	schedule := Duty{}
	var startUID uint
	err = app.DB().Select("id", "uid", "due_date").
		Where("due_date = (?)", app.DB().Model(&Duty{}).Select("MAX(due_date)")).Take(&schedule).Error
	if err == nil {
		startUID = schedule.UID
		startTime = time.Time(schedule.DueDate)
	}
	err = AutoScheduleDuty(startTime, startUID)
	return
}

// 获取值班人员名字
func GetDutyMemberName(date datatypes.Date) (name string, err error) {
	err = app.DB().Model(auth.User{}).Select("name").Where("id IN (?)",
		app.DB().Model(&Duty{}).Select("uid").Where("due_date = ?", date)).
		Limit(1).Scan(&name).Error
	return
}
