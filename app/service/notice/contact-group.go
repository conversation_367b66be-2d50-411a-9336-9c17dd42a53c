package notice

import (
	"cmdb/app"
	"cmdb/pkg/db"
	"errors"
	"log/slog"
	"sync"
	"time"

	"gorm.io/gorm"
)

var (
	ErrContactGroupExist = errors.New("联系人分组已经存在")
)

// 联系人分组
type ContactGroup struct {
	ID        uint      `gorm:"primaryKey;column:id;comment:主键" json:"id"`
	Name      string    `gorm:"type:varchar(255);unique;column:name;comment:名称" json:"name" `
	Remark    string    `gorm:"type:varchar(255);column:remark;comment:备注" json:"remark"`
	UpdatedAt time.Time `gorm:"column:updated_at;comment:更新时间" json:"updated_at"`
	Contacts  []Contact `gorm:"many2many:notice_contact_contact_groups" json:"contacts,omitempty"`
}

func (ContactGroup) TableName() string {
	return "notice_contact_groups"
}

// 联系人组表单
type ContactGroupForm struct {
	Name       string `json:"name" binding:"required,max=255"`
	Remark     string `json:"remark" binding:"max=255"`
	ContactIDs []uint `json:"contact_ids"`
}

// 创建分组
func (form *ContactGroupForm) Create() (err error) {
	// 查询是否存在同名分组
	err = app.DB().Select("id").Where("name = ?", form.Name).Take(&ContactGroup{}).Error
	if err == nil {
		err = ErrContactGroupExist
		return
	}
	// 如果不存在，则创建分组
	if err == gorm.ErrRecordNotFound {
		err = app.DB().Create(&ContactGroup{
			Name:   form.Name,
			Remark: form.Remark,
		}).Error
		if err != nil {
			return
		}
		// 如果有联系人ID，则关联联系人
		if len(form.ContactIDs) > 0 {
			group := ContactGroup{}
			err1 := app.DB().Select("id").Where("name = ?", form.Name).Take(&group).Error
			if err1 != nil {
				app.Log().Error("添加联系人分组时，查询分组失败", slog.Any("err", err1))
				return
			}
			contacts := []Contact{}
			err1 = app.DB().Select("id").Where("id in (?)", form.ContactIDs).Find(&contacts).Error
			if err1 != nil {
				app.Log().Error("添加联系人分组时，查询联系人失败", slog.Any("err", err1))
				return
			}
			err1 = app.DB().Model(&group).Association("Contacts").Append(&contacts)
			if err1 != nil {
				app.Log().Error("添加联系人分组时，关联联系人失败", slog.Any("err", err1))
				return
			}
		}
	}
	return
}

// 分页获取联系人分组列表
func GetContactGroups(offset, limit int, keyword *string) (count int64, groups []ContactGroup, err error) {
	dbop := app.DB()
	// 如果有搜索关键字，则模糊搜索
	if keyword != nil {
		dbop = db.MLike(dbop, *keyword, "name")
	}
	// 查询联系人分组列表
	err = dbop.Model(&ContactGroup{}).Count(&count).Order("name").Offset(offset).Limit(limit).Find(&groups).Error
	if err != nil {
		return
	}
	// 并发获取每个分组下的联系人
	var wg sync.WaitGroup
	max := make(chan struct{}, 10)
	for i := range groups {
		wg.Add(1)
		max <- struct{}{}
		go func(i int) {
			groups[i].Contacts, _ = groups[i].GetContacts()
			wg.Done()
			<-max
		}(i)
	}
	wg.Wait()
	close(max)
	return
}

// 获取所有联系人分组列表
func GetAllContactGroups() (groups []ContactGroup, err error) {
	// 查询所有联系人分组
	err = app.DB().Order("name").Find(&groups).Error
	return
}

// GetGroupByID 通过ID获取联系人分组
func GetContactGroupByID(id int) (group ContactGroup, err error) {
	// 通过ID查询联系人分组
	err = app.DB().Where("id = ?", id).Take(&group).Error
	return
}

// GetGroupByName 通过Name获取联系人分组
func GetContactGroupByName(name string) (group ContactGroup, err error) {
	// 通过Name查询联系人分组
	err = app.DB().Where("name = ?", name).Take(&group).Error
	return
}

// CreateAndGetGroup 创建分组并返回
func CreateAndGetGroup(name, remark string) (group ContactGroup, err error) {
	// 查询是否存在同名分组
	group, err = GetContactGroupByName(name)
	if err == gorm.ErrRecordNotFound {
		form := ContactGroupForm{Name: name, Remark: remark}
		// 如果不存在，则创建分组
		err = form.Create()
		if err != nil {
			return
		}
		// 创建成功后，再次查询分组
		group, err = GetContactGroupByName(name)
	}
	return
}

// Update 更新分组
func (group ContactGroup) Update(form ContactGroupForm) (err error) {
	// 查询是否存在同名分组
	exit := ContactGroup{}
	err = app.DB().Select("id").Where("name = ?", form.Name).Take(&exit).Error
	if err == nil && exit.ID != group.ID {
		return ErrContactGroupExist
	}
	// 如果不存在，则更新分组
	if err == nil || err == gorm.ErrRecordNotFound {
		updateMap := map[string]interface{}{}
		if group.Name != form.Name {
			updateMap["name"] = form.Name
		}
		if group.Remark != form.Remark {
			updateMap["remark"] = form.Remark
		}
		if len(updateMap) > 0 {
			err = app.DB().Model(group).Updates(updateMap).Error
		}
		if err == nil {
			// 更新联系人
			var contacts = []Contact{}
			err = app.DB().Select("id").Where("id IN (?)", form.ContactIDs).Find(&contacts).Error
			if err != nil {
				return
			}
			err = app.DB().Model(&group).Association("Contacts").Replace(contacts)
			if err != nil {
				return
			}
		}
	}
	return
}

// Delete 删除分组
func (group *ContactGroup) Delete() (err error) {
	dbop := app.DB().Begin()
	// 清理关联联系人
	err = dbop.Model(group).Association("Contacts").Clear()
	if err != nil {
		dbop.Rollback()
		return
	}
	err = dbop.Delete(group).Error
	if err != nil {
		dbop.Rollback()
		return
	}
	// 提交会话
	err = dbop.Commit().Error
	if err != nil {
		dbop.Rollback()
	}
	return
}

// GetContacts 获取分组里的联系人
func (group *ContactGroup) GetContacts() (contacts []Contact, err error) {
	err = app.DB().Model(group).Association("Contacts").Find(&contacts)
	return
}

// GetContactGroupsByIDs 通过ID获取联系人分组列表
func GetContactGroupsByIDs(ids ...uint) (groups []ContactGroup, err error) {
	if len(ids) > 0 {
		err = app.DB().Where("id IN (?)", ids).Find(&groups).Error
	}
	return
}
