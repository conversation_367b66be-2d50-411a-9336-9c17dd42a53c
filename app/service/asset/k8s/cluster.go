package k8s

import (
	"cmdb/app"
	"cmdb/pkg/db"
	"encoding/base64"
	"errors"
	"log/slog"
	"time"

	"gorm.io/gorm"
)

// 定义集群已存在的错误
var (
	ErrClusterExist = errors.New("集群已存在")
)

// 定义集群结构体
type Cluster struct {
	ID             uint           `gorm:"column:id;primaryKey;comment:主键" json:"id"`
	Name           string         `gorm:"column:name;type:varchar(255);index" json:"name"`
	ClusterType    string         `gorm:"column:cluster_type;type:varchar(255);index" json:"cluster_type"`
	ClusterVersion string         `gorm:"column:cluster_version;type:varchar(255);index" json:"cluster_version"`
	Token          string         `gorm:"column:token;type:text;comment:Token" json:"-"`
	Remark         string         `gorm:"column:remark;type:varchar(255)" json:"remark"`
	UpdatedAt      time.Time      `gorm:"column:updated_at;index" json:"updated_at"`
	DeletedAt      gorm.DeletedAt `gorm:"column:deleted_at;index" json:"-"`
}

// 定义集群表名
func (Cluster) TableName() string {
	return "asset_k8s_clusters"
}

// 定义集群表单结构体
type ClusterForm struct {
	Name           string `json:"name" binding:"required,max=255"`
	ClusterType    string `json:"cluster_type" binding:"required,max=255"`
	ClusterVersion string `json:"cluster_version" binding:"required,max=255"`
	Token          string `json:"token"`
	Remark         string `json:"remark" binding:"max=255"`
}

// 创建集群
func (form ClusterForm) Create() (err error) {
	err = app.DB().Where("name = ?", form.Name).Take(&Cluster{}).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = app.DB().Create(&Cluster{
			Name:           form.Name,
			ClusterType:    form.ClusterType,
			ClusterVersion: form.ClusterVersion,
			Token:          base64.StdEncoding.EncodeToString([]byte(form.Token)),
			Remark:         form.Remark,
		}).Error
	} else if err == nil {
		err = ErrClusterExist
	}
	return
}

// 更新集群
func (cluster Cluster) Update(form ClusterForm) (err error) {
	exist := Cluster{}
	err = app.DB().Where("name = ?", form.Name).Take(&exist).Error
	if err == nil && exist.ID != cluster.ID {
		return ErrClusterExist
	} else if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	} else {
		err = nil
	}
	updateMap := map[string]any{}
	if form.Name != cluster.Name {
		updateMap["name"] = form.Name
	}
	if form.ClusterType != cluster.ClusterType {
		updateMap["cluster_type"] = form.ClusterType
	}
	if form.ClusterVersion != cluster.ClusterVersion {
		updateMap["cluster_version"] = form.ClusterVersion
	}
	if form.Remark != cluster.Remark {
		updateMap["remark"] = form.Remark
	}
	if form.Token != "" {
		token := base64.StdEncoding.EncodeToString([]byte(form.Token))
		if token != cluster.Token {
			updateMap["token"] = token
		}
	}
	if len(updateMap) > 0 {
		err = app.DB().Model(&cluster).Updates(updateMap).Error
	}
	return
}

// 获取集群列表
func GetClusters(offset, limit int, keyword string) (count int64, data []Cluster, err error) {
	dbop := app.DB()
	if keyword != "" {
		dbop = db.MLike(dbop, keyword, "name", "cluster_type", "remark")
	}
	err = dbop.Model(&Cluster{}).Count(&count).Order("name").Offset(offset).Limit(limit).Find(&data).Error
	return
}

// 根据ID获取集群
func GetClusterByID(id int) (c Cluster, err error) {
	err = app.DB().Where("id = ?", id).Take(&c).Error
	return
}

// 同步所有集群
func SyncAllClusters() (err error) {
	clusters := []Cluster{}
	err = app.DB().Find(&clusters).Error
	if err != nil {
		return
	}
	for _, cluster := range clusters {
		err1 := cluster.SyncNamespaces()
		if err1 != nil {
			app.Log().Error("同步命名空间失败", slog.String("cluster", cluster.Name), slog.String("error", err1.Error()))
		}
		err1 = cluster.SyncNodes()
		if err1 != nil {
			app.Log().Error("同步节点失败", slog.String("cluster", cluster.Name), slog.String("error", err1.Error()))
		}
		err1 = cluster.SyncWorkloads()
		if err1 != nil {
			app.Log().Error("同步负载均衡失败", slog.String("cluster", cluster.Name), slog.String("error", err1.Error()))
		}
		err1 = cluster.SyncServices()
		if err1 != nil {
			app.Log().Error("同步服务失败", slog.String("cluster", cluster.Name), slog.String("error", err1.Error()))
		}
		err1 = cluster.SyncPods()
		if err1 != nil {
			app.Log().Error("同步pod失败", slog.String("cluster", cluster.Name), slog.String("error", err1.Error()))
		}
	}
	return
}

// 删除集群
func (cluster Cluster) Delete() (err error) {
	return app.DB().Delete(&cluster).Error
}

func GetAllClusters() (cs []Cluster, err error) {
	err = app.DB().Order("name").Find(&cs).Error
	return
}

func GetClusterName(cloudID uint) (name string, err error) {
	err = app.DB().Model(&Cluster{}).Select("name").Where("id = ?", cloudID).Scan(&name).Error
	return
}

func (c Cluster) CountNode() (count int64, err error) {
	err = app.DB().Model(&Node{}).Where("cluster_id = ?", c.ID).Count(&count).Error
	return
}

func (c Cluster) CountWorkload() (count int64, err error) {
	err = app.DB().Model(&Workload{}).Where("cluster_id = ?", c.ID).Count(&count).Error
	return
}

func (c Cluster) CountPod() (count int64, err error) {
	err = app.DB().Model(&Pod{}).Where("cluster_id = ?", c.ID).Count(&count).Error
	return
}

func (c Cluster) CountNamespace() (count int64, err error) {
	err = app.DB().Model(&Namespace{}).Where("cluster_id = ?", c.ID).Count(&count).Error
	return
}
func (c Cluster) CountService() (count int64, err error) {
	err = app.DB().Model(&Service{}).Where("cluster_id = ?", c.ID).Count(&count).Error
	return
}

func (c Cluster) CountContainer() (count int64, err error) {
	err = app.DB().Model(&Container{}).Where("cluster_id = ?", c.ID).Count(&count).Error
	return
}

func GetClusterNamebyID(id int) (name string, err error) {
	err = app.DB().Model(&Cluster{}).Select("name").Where("id =?", id).Scan(&name).Error
	return
}
