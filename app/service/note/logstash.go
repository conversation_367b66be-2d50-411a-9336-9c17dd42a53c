package note

import (
	"context"
	"strings"
	"time"

	"gopkg.in/olivere/elastic.v6"
)

type Log5xx struct {
	HttpHost     string
	StatusCounts []StatusUpstreamAddrCount
}

type StatusUpstreamAddrCount struct {
	Status             int
	UpstreamAddrCounts []UpstreamAddrCount
}
type UpstreamAddrCount struct {
	UpstreamAddr string
	Count        int64
}

// 获取elk日志上的web日志
func Get5xxLogs(host string, startTime, endTime time.Time) (logs []Log5xx, err error) {
	logs = []Log5xx{}
	c, err := initClient()
	if err != nil {
		return
	}
	defer c.Stop()
	hosts := strings.Split(host, ",")
	hostsQ := []elastic.Query{}
	for i := range hosts {
		if hosts[i] == "" {
			continue
		}
		hostsQ = append(hostsQ, elastic.NewMatchPhraseQuery("http_Host", hosts[i]))
	}
	query := elastic.NewBoolQuery()
	if len(hostsQ) > 0 {
		query.Must(elastic.NewBoolQuery().Should(hostsQ...))
	}
	query.Must(elastic.NewRangeQuery("@timestamp").Gte(startTime).Lt(endTime))
	query.Must(elastic.NewRangeQuery("status").Gte(500))
	// aggs := elastic.NewDateHistogramAggregation().Interval("10s").Field("@timestamp").TimeZone("Asia/Shanghai")
	hostAggs := elastic.NewTermsAggregation().Field("http_Host")
	statusAgg := elastic.NewTermsAggregation().Field("status")
	upstreamAddr := elastic.NewTermsAggregation().Field("upstream_addr")
	statusAgg.SubAggregation("upstream_addr", upstreamAddr)
	hostAggs.SubAggregation("status", statusAgg)
	result, err := c.Search("logstash-*").Query(query).Aggregation("host", hostAggs).Sort("@timestamp", true).Size(0).Do(context.Background())
	if err != nil {
		return
	}
	agg, found := result.Aggregations.Terms("host")
	if !found {
		return
	}
	// 遍历桶数据
	for _, hostBucket := range agg.Buckets {
		statusBuckets, found := hostBucket.Terms("status")
		if !found {
			continue
		}
		hostStatusCount := Log5xx{
			HttpHost:     hostBucket.Key.(string),
			StatusCounts: []StatusUpstreamAddrCount{},
		}
		for _, statusBucket := range statusBuckets.Buckets {

			upstreamAddrBuckets, found := statusBucket.Terms("upstream_addr")
			if !found {
				continue
			}
			upstreamAddrCounts := []UpstreamAddrCount{}
			for _, upstreaAddrBucket := range upstreamAddrBuckets.Buckets {
				upstreamAddrCounts = append(upstreamAddrCounts, UpstreamAddrCount{
					UpstreamAddr: upstreaAddrBucket.Key.(string),
					Count:        upstreaAddrBucket.DocCount,
				})
			}
			hostStatusCount.StatusCounts = append(hostStatusCount.StatusCounts, StatusUpstreamAddrCount{
				Status:             int(statusBucket.Key.(float64)),
				UpstreamAddrCounts: upstreamAddrCounts,
			})
		}
		logs = append(logs, hostStatusCount)
	}
	return
}
