package mysql

import (
	"bytes"
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/pkg/db"
	"cmdb/pkg/utils"
	"encoding/json"
	"errors"
	"sync"
	"time"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

var (
	ErrInstanceExist = errors.New("实例已存在")
)

type InstanceType string

const (
	MySQLInstanceType = "mysql"
	TidbInstanceType  = "tidb"
)

type Instance struct {
	ID                uint           `gorm:"column:id;primaryKey;comment:主键" json:"id"`
	Name              string         `gorm:"column:name;type:varchar(255);index;comment:实例名称" json:"name"`
	InstanceType      InstanceType   `gorm:"column:instance_type;type:varchar(255);default:'mysql';index;comment:实例类型" json:"instance_type"`
	Host              string         `gorm:"column:host;type:varchar(255);index;comment:主机" json:"host"`
	Port              uint           `gorm:"column:port;type:int;index;comment:端口" json:"port"`
	Remark            string         `gorm:"column:remark;type:varchar(255);comment:备注" json:"remark"`
	Username          string         `gorm:"column:username;type:varchar(255);comment:用户名" json:"username"`
	Password          string         `gorm:"column:password;type:varchar(255);comment:密码" json:"-"`
	Version           string         `gorm:"column:version;type:varchar(255);comment:版本" json:"version"`
	ReplicaCount      int            `gorm:"column:replica_count;type:int;comment:副本数" json:"replica_count"`
	CiAddress         string         `gorm:"column:ci_address;type:varchar(255);index;comment:ci地址" json:"ci_address"`
	CreatedAt         time.Time      `gorm:"column:created_at;index" json:"created_at"`
	UpdatedAt         time.Time      `gorm:"column:updated_at;index" json:"updated_at"`
	DeletedAt         gorm.DeletedAt `gorm:"column:deleted_at;index" json:"-"`
	Tags              []asset.Tag    `gorm:"many2many:asset_db_mysql_instance_tags;joinForeignKey:instance_id;joinReferences:tag_id" json:"tags"`
	BusinessDomainIDs datatypes.JSON `gorm:"column:business_domain_ids;type:json;comment:业务域ID" json:"business_domain_ids"`
}

func (Instance) TableName() string {
	return "asset_db_mysql_instances"
}

type InstanceForm struct {
	Name              string       `json:"name" binding:"required,max=255"`
	Host              string       `json:"host" binding:"required,max=255"`
	Port              uint         `json:"port" binding:"required,min=1,max=65535"`
	Remark            string       `json:"remark" binding:"max=255"`
	Username          string       `json:"username" binding:"required,max=255"`
	Password          string       `json:"password" binding:"max=255"`
	ReplicaCount      int          `json:"replica_count" binding:"min=0"`
	CiAddress         string       `json:"ci_address" binding:"max=255"`
	InstanceType      InstanceType `json:"instance_type" binding:"required"`
	BusinessDomainIDs []uint       `json:"business_domain_ids"`
}

func (form InstanceForm) Create() (err error) {
	err = app.DB().Where("name = ?", form.Name).Take(&Instance{}).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		var businessDomainIDByte []byte
		businessDomainIDByte, _ = json.Marshal(&form.BusinessDomainIDs)
		err = app.DB().Create(&Instance{
			Name:              form.Name,
			Host:              form.Host,
			Port:              form.Port,
			Remark:            form.Remark,
			CiAddress:         form.CiAddress,
			ReplicaCount:      form.ReplicaCount,
			Username:          form.Username,
			Password:          utils.Base64Encode(form.Password),
			InstanceType:      MySQLInstanceType,
			BusinessDomainIDs: businessDomainIDByte,
		}).Error
	} else if err == nil {
		err = ErrInstanceExist
	}
	return
}

func GetInstanceByID(id int) (instance Instance, err error) {
	err = app.DB().Where("id = ?", id).Take(&instance).Error
	return
}

func GetInstances(offset, limit int, instanceType *InstanceType, keyword *string) (count int64, instances []Instance, err error) {
	dbop := app.DB()
	if instanceType != nil {
		dbop = dbop.Where("instance_type = ?", *instanceType)
	}
	if keyword != nil {
		dbop = db.MLike(dbop, *keyword, "name", "remark", "ci_address")
	}
	err = dbop.Model(&Instance{}).Count(&count).
		Order("created_at DESC").Offset(offset).Limit(limit).Find(&instances).Error
	if err != nil {
		return
	}
	if len(instances) > 0 {
		var wg sync.WaitGroup
		sem := make(chan struct{}, 10) // 限制最大并发为10

		for i, instance := range instances {
			wg.Add(1)
			sem <- struct{}{} // 获取一个信号量
			go func(instance Instance) {
				defer wg.Done()
				app.DB().Model(&instance).Association("Tags").Find(&instances[i].Tags)
				<-sem // 释放信号量
			}(instance)
		}
		wg.Wait() // 等待所有 goroutine 完成

	}
	return
}

func (instance Instance) Update(form InstanceForm) (err error) {
	updateMap := map[string]any{}
	if form.Name != instance.Name {
		exist := Instance{}
		err = app.DB().Where("name = ?", form.Name).Take(&exist).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			// 查询错误
			return
		} else if err == nil && exist.ID != instance.ID {
			err = ErrInstanceExist
			return
		}
		updateMap["name"] = form.Name
	}
	if instance.Host != form.Host {
		updateMap["host"] = form.Host
	}
	if instance.Port != form.Port {
		updateMap["port"] = form.Port
	}
	if form.Remark != instance.Remark {
		updateMap["remark"] = form.Remark
	}
	if form.InstanceType != instance.InstanceType {
		updateMap["instance_type"] = form.InstanceType
	}
	if form.Username != instance.Username {
		updateMap["username"] = form.Username
	}
	if form.Password != "" && utils.Base64Encode(form.Password) != instance.Password {
		updateMap["password"] = utils.Base64Encode(form.Password)
	}
	if form.CiAddress != instance.CiAddress {
		updateMap["ci_address"] = form.CiAddress
	}
	if form.ReplicaCount != instance.ReplicaCount {
		updateMap["replica_count"] = form.ReplicaCount
	}
	businessDomainIDsByte, _ := json.Marshal(&form.BusinessDomainIDs)
	if !bytes.Equal(businessDomainIDsByte, instance.BusinessDomainIDs) {
		updateMap["business_domain_ids"] = businessDomainIDsByte
	}
	if len(updateMap) > 0 {
		err = app.DB().Model(&instance).Updates(updateMap).Error
	}
	return
}

func (instance Instance) Delete() (err error) {
	err = app.DB().Delete(&instance).Error
	return
}

func (instance Instance) NewConnect() (dbop *gorm.DB, err error) {
	password, _ := utils.Base64Decode(instance.Password)
	dbop, err = db.NewMySQLDBConnect(db.MysqlConnParmas{
		Host:     instance.Host,
		Port:     instance.Port,
		User:     instance.Username,
		Password: password,
	}, nil)
	return
}

func (form InstanceForm) TestConnect() (username string, err error) {
	dbop, err := db.NewMySQLDBConnect(db.MysqlConnParmas{
		Host:     form.Host,
		Port:     form.Port,
		User:     form.Username,
		Password: form.Password,
	}, nil)
	if err != nil {
		return
	}
	defer func() {
		d, err1 := dbop.DB()
		if err1 == nil {
			d.Close()
		}
	}()
	err = dbop.Raw("select user()").Scan(&username).Error
	if err != nil {
		return
	}
	return
}

func StatAllInstances() (total int64, err error) {
	err = app.DB().Raw(`select 
	Sum(admt.data_length + admt.index_length)* (admi.replica_count + 1) AS total
from
	cmdb3.asset_db_mysql_tables admt ,
	cmdb3.asset_db_mysql_instances admi
where
	admt.instance_id = admi.id
	and admt.deleted_at is null
	and admi.deleted_at is null;`).Scan(&total).Error
	return
}

func StatInstanceTypeStorage(instanceType InstanceType) (total int64, err error) {
	err = app.DB().Raw(`SELECT 
    SUM(instance_total * (replica_count + 1)) AS grand_total
FROM (
    SELECT 
        admi.id,
        admi.replica_count,
        SUM(admt.data_length + admt.index_length) AS instance_total
    FROM 
        cmdb3.asset_db_mysql_tables admt
    INNER JOIN 
        cmdb3.asset_db_mysql_instances admi 
        ON admt.instance_id = admi.id
    WHERE 
        admt.deleted_at IS NULL
        AND admi.deleted_at IS NULL
        AND admi.instance_type = ?
    GROUP BY 
        admi.id, admi.replica_count
) AS subquery;`, instanceType).Scan(&total).Error
	return
}
