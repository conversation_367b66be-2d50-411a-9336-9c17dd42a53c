package notice

import "cmdb/pkg/dingtalk"

type DintalkTestContent struct {
	RobotURL    string `json:"robot_url" binding:"required"`
	Sign        string `json:"sign"`
	MessageType string `json:"message_type" binding:"required"`
	Content     string `json:"content" binding:"required"`
}

func (form DintalkTestContent) Send() (err error) {
	var msg dingtalk.Message
	if form.MessageType == "markdown" {
		msg = dingtalk.NewMarkdownMessage("测试", form.Content)
	} else {
		msg = dingtalk.NewTextMessage(form.Content)
	}
	err = dingtalk.Send(msg, form.RobotURL, form.Sign)
	return
}
