package auth

import (
	"bufio"
	"cmdb/app"
	"cmdb/pkg/utils"
	"errors"
	"fmt"
	"os"
	"strings"

	"gorm.io/gorm"
)

// 创建管理用户
func CreateAdminUser() (err error) {
	inputReader := bufio.NewReader(os.Stdin)
	fmt.Printf("邮箱: ")
	email, err := inputReader.ReadString('\n')
	if err != nil {
		return
	}
	email = strings.TrimSpace(email)
	fmt.Printf("用户名: ")
	username, err := inputReader.ReadString('\n')
	if err != nil {
		return
	}
	username = strings.TrimSpace(username)
	fmt.Printf("密码: ")
	password, err := inputReader.ReadString('\n')
	if err != nil {
		return
	}
	password = strings.TrimSpace(password)
	if username == "" || password == "" {
		// 判断用户名密码是否为空
		return errors.New("用户名或密码不能为空")
	}
	err = app.DB().Select("id").Where("username= ?", username).Take(&User{}).Error
	if err == gorm.ErrRecordNotFound {
		// 用户表单
		err = app.DB().Create(&User{
			Username:   username,
			Name:       username,
			Email:      email,
			Password:   utils.EncodePassword(password),
			Remark:     "通过终端创建",
			IsDisabled: false,
			IsAdmin:    true,
		}).Error
	} else if err == nil {
		return errors.New("用户已经存在")
	}
	return

}
