package auth

import (
	"cmdb/app"
	"cmdb/pkg/db"
	"errors"

	"gorm.io/gorm"
)

// 设置用户分组可以用于审批等

// Group 用户分组
type Group struct {
	ID     uint   `gorm:"column:id;primaryKey;comment:主键id" json:"id"`
	Name   string `gorm:"column:name;type:varchar(255);unique;comment:分组名称" json:"name"`
	Remark string `gorm:"type:varchar(255);column:remark;comment:备注" json:"remark"`
	Users  []User `gorm:"many2many:auth_user_groups" json:"users,omitempty"`
}

// TableName 设置表名
func (Group) TableName() string {
	return "auth_groups"
}

type GroupForm struct {
	Name   string `json:"name" binding:"required,max=255"`
	Remark string `json:"remark" binding:"max=255"`
}

// Create 创建用户分组
func (form GroupForm) Create() (err error) {
	err = app.DB().Select("id").Where("name = ?", form.Name).Take(&Group{}).Error
	if err == nil {
		return ErrGroupExist
	}
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = app.DB().Create(&Group{
			Name:   form.Name,
			Remark: form.Remark,
		}).Error
	}
	return
}

// GetGroups 获取用户分组列表
func GetGroups(offset, limit int, keyword *string) (count int64, groups []Group, err error) {
	dbop := app.DB()
	if keyword != nil {
		dbop = db.MLike(dbop, *keyword, "name", "remark")
	}
	err = dbop.Model(&Group{}).Count(&count).Order("name,id DESC").Offset(offset).Limit(limit).Find(&groups).Error
	return
}

// GetAllGroups 获取所有用户分组
func GetAllGroups() (groups []Group, err error) {
	err = app.DB().Model(&Group{}).Order("name").Find(&groups).Error
	return
}

// GetGroupsByIDs 根据ID获取用户分组
func GetGroupsByIDs(ids ...uint) (groups []Group, err error) {
	if len(ids) == 0 {
		return
	}
	err = app.DB().Model(&Group{}).Where("id IN (?)", ids).Find(&groups).Error
	return
}

// GetGroupNamesByIDs 根据ID获取用户分组名称
func GetGroupNamesByIDs(ids ...uint) (name []string, err error) {
	name = make([]string, 0)
	if len(ids) > 0 {
		err = app.DB().Model(&Group{}).Select("name").Where("id IN ?", ids).Scan(&name).Error
	}
	return
}

// GetUsers 获取用户分组中的用户
func (group Group) GetUsers() (users []User, err error) {
	err = app.DB().Model(&group).Association("Users").Find(&users)
	return
}

// AddUser 添加用户到用户分组
func (group Group) AddUser(users ...User) (err error) {
	return app.DB().Model(&group).Association("Users").Append(&users)
}

// Delete 删除用户分组
func (group Group) Delete() (err error) {
	return app.DB().Delete(&group).Error
}

// GetGroupByID 根据ID获取用户分组
func GetGroupByID(id int) (group Group, err error) {
	err = app.DB().Where("id = ?", id).Take(&group).Error
	return
}

// Update 更新用户分组
func (group Group) Update(form GroupForm) (err error) {
	updateMap := map[string]any{}
	if group.Name != form.Name {
		updateMap["name"] = form.Name
		exist := Group{}
		err = app.DB().Where("name = ?", form.Name).Take(&exist).Error
		if err == nil && exist.ID != group.ID {
			return ErrGroupExist
		} else if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		} else {
			err = nil
		}
	}
	if group.Remark != form.Remark {
		updateMap["remark"] = form.Remark
	}
	if len(updateMap) > 0 {
		err = app.DB().Model(&group).Updates(updateMap).Error
	}
	return
}
