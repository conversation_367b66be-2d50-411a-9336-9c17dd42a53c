package aliyun

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/asset/cloud"
	"cmdb/pkg/utils"
	"errors"
	"time"

	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/requests"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/alidns"
	"gorm.io/gorm"
)

// 同步账号下的所有公共域名
func (a Account) syncPublicDomains() (result cloud.SyncResults, err error) {
	// 调用同步分页域名的函数
	r, err := a.syncPageDomains(1)
	// 创建同步结果对象
	sr := cloud.SyncResult{
		Key: "账号：" + a.Name,
	}
	if err == nil {
		// 同步成功
		sr.Result = "同步成功"
		// 回收过期主机
		err = a.ExpiredPublicDomains(time.Now().Add(-10 * time.Minute))
		if err != nil {
			// 回收失败
			sr.Result = "同步成功，但是回收Domain失败" + err.<PERSON>rror()
		}
	} else {
		// 同步失败
		sr.Result = r + " " + err.Error()
	}
	// 将同步结果添加到结果集
	result = append(result, sr)
	return
}

// 同步分页域名
func (a Account) syncPageDomains(page int) (result string, err error) {
	// 解码AccessSecret
	accessSecret, _ := utils.Base64Decode(a.AccessSecret)
	// 创建客户端
	client, err := alidns.NewClientWithAccessKey("cn-beijing", a.AccessKey, accessSecret)
	if err != nil {
		// 创建客户端失败
		result = "创建客户端失败"
		return
	}
	// 创建请求对象
	request := alidns.CreateDescribeDomainsRequest()
	request.Scheme = "https"
	request.PageSize = requests.NewInteger(100)
	if page < 1 {
		page = 1
	}
	request.PageNumber = requests.NewInteger(page)
	// 发送请求
	response, err := client.DescribeDomains(request)
	if err != nil {
		return
	}
	// 获取当前时间
	now := time.Now()
	// 遍历域名
	for _, domain := range response.Domains.Domain {
		// 查询域名是否存在
		exist := asset.PublicDomain{}
		err = app.DB().Where("domain_id = ? AND account_id = ? ", domain.DomainId, a.ID).Take(&exist).Error
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 域名不存在，创建新域名
			err = app.DB().Create(&asset.PublicDomain{
				AccountID:       a.ID,
				Name:            domain.DomainName,
				DomainID:        domain.DomainId,
				CloudType:       a.CloudType,
				RecordCount:     uint64(domain.RecordCount),
				ResourceGroupID: domain.ResourceGroupId,
				CreatedAt:       time.UnixMilli(domain.CreateTimestamp),
				UpdatedAt:       time.UnixMilli(domain.CreateTimestamp),
				SyncTime:        now,
				Remark:          domain.Remark,
			}).Error
		} else if err == nil {
			// 域名存在，更新域名信息
			updateMap := map[string]any{
				"sync_time": now,
			}
			if exist.Name != domain.DomainName {
				updateMap["name"] = domain.DomainName
			}
			if exist.RecordCount != uint64(domain.RecordCount) {
				updateMap["record_count"] = uint64(domain.RecordCount)
			}
			if exist.Remark != domain.Remark {
				updateMap["remark"] = domain.Remark
			}
			if exist.ResourceGroupID != domain.ResourceGroupId {
				updateMap["resource_group_id"] = domain.ResourceGroupId
			}
			if exist.CreatedAt.UnixMilli() != domain.CreateTimestamp {
				updateMap["created_time"] = time.UnixMilli(domain.CreateTimestamp)
			}
			if exist.UpdatedAt.UnixMilli() != domain.CreateTimestamp {
				updateMap["updated_at"] = time.UnixMilli(domain.CreateTimestamp)
			}
			err = app.DB().Model(&exist).Updates(updateMap).Error
		}
		if err != nil {
			return
		}
	}
	// 如果还有更多域名，继续同步
	if response.TotalCount > int64(page*100) {
		result, err = a.syncPageDomains(page + 1)
	}
	return
}
