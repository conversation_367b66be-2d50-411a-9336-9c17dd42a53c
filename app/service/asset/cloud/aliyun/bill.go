package aliyun

import (
	"cmdb/app"
	"cmdb/pkg/utils"
	"errors"

	bssopenapi******** "github.com/alibabacloud-go/bssopenapi-********/v3/client"
	openapi "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	"github.com/alibabacloud-go/tea/tea"
)

type MonthlyBill struct {
	// ID                        uint     `gorm:"primaryKey;autoIncrement;comment:主键ID" json:"id"`
	AccountID                 uint     `gorm:"column:account_id;index;comment:账户ID" json:"account_id"`
	BillCycle                 string   `gorm:"column:bill_cycle;type:varchar(255);index;comment:账单周期" json:"bill_cycle"`
	AdjustAmount              *float32 `gorm:"column:adjust_amount;type:decimal(10,3);comment:调账金额" json:"adjust_amount"`
	BillAccountID             *string  `gorm:"column:bill_account_id;type:varchar(255);comment:账单账户ID" json:"bill_account_id"`
	BillAccountName           *string  `gorm:"column:bill_account_name;type:varchar(255);comment:账单账户名称" json:"bill_account_name"`
	BillingDate               *string  `gorm:"column:billing_date;type:varchar(255);comment:账单日期" json:"billing_date"`
	BillingItem               *string  `gorm:"column:billing_item;type:varchar(255);comment:账单项目" json:"billing_item"`
	BillingItemCode           *string  `gorm:"column:billing_item_code;type:varchar(255);comment:账单项目编码" json:"billing_item_code"`
	BillingType               *string  `gorm:"column:billing_type;type:varchar(255);comment:账单类型" json:"billing_type"`
	BizType                   *string  `gorm:"column:biz_type;type:varchar(255);comment:业务类型" json:"biz_type"`
	CashAmount                *float32 `gorm:"column:cash_amount;type:decimal(10,3);comment:现金金额" json:"cash_amount"`
	CommodityCode             *string  `gorm:"column:commodity_code;type:varchar(255);comment:商品编码" json:"commodity_code"`
	CostUnit                  *string  `gorm:"column:cost_unit;type:varchar(255);comment:成本单元" json:"cost_unit"`
	Currency                  *string  `gorm:"column:currency;type:varchar(255);comment:货币" json:"currency"`
	DeductedByCashCoupons     *float32 `gorm:"column:deducted_by_cash_coupons;type:decimal(10,3);comment:现金券抵扣" json:"deducted_by_cash_coupons"`
	DeductedByCoupons         *float32 `gorm:"column:deducted_by_coupons;type:decimal(10,3);comment:代金券抵扣" json:"deducted_by_coupons"`
	DeductedByPrepaidCard     *float32 `gorm:"column:deducted_by_prepaid_card;type:decimal(10,3);comment:预付卡抵扣" json:"deducted_by_prepaid_card"`
	DeductedByResourcePackage *string  `gorm:"column:deducted_by_resource_package;type:varchar(255);comment:资源包抵扣" json:"deducted_by_resource_package"`
	InstanceConfig            *string  `gorm:"column:instance_config;type:longtext;comment:实例配置" json:"instance_config"`
	InstanceID                *string  `gorm:"column:instance_id;type:varchar(255);comment:实例ID" json:"instance_id"`
	InstanceSpec              *string  `gorm:"column:instance_spec;type:varchar(255);comment:实例规格" json:"instance_spec"`
	InternetIP                *string  `gorm:"column:internet_ip;type:varchar(255);comment:公网IP" json:"internet_ip"`
	IntranetIP                *string  `gorm:"column:intranet_ip;type:varchar(255);comment:内网IP" json:"intranet_ip"`
	InvoiceDiscount           *float32 `gorm:"column:invoice_discount;type:decimal(10,3);comment:发票折扣" json:"invoice_discount"`
	Item                      *string  `gorm:"column:item;type:varchar(255);comment:账单项目" json:"item"`
	ItemName                  *string  `gorm:"column:item_name;type:varchar(255);comment:账单项目名称" json:"item_name"`
	ListPrice                 *string  `gorm:"column:list_price;type:varchar(255);comment:列表价格" json:"list_price"`
	ListPriceUnit             *string  `gorm:"column:list_price_unit;type:varchar(255);comment:列表价格单位" json:"list_price_unit"`
	NickName                  *string  `gorm:"column:nick_name;type:varchar(255);comment:实例名称" json:"nick_name"`
	OutstandingAmount         *float32 `gorm:"column:outstanding_amount;type:decimal(10,3);comment:未结清金额" json:"outstanding_amount"`
	OwnerID                   *string  `gorm:"column:owner_id;type:varchar(255);comment:所有者ID" json:"owner_id"`
	PaymentAmount             *float32 `gorm:"column:payment_amount;type:decimal(10,3);comment:支付金额" json:"payment_amount"`
	PipCode                   *string  `gorm:"column:pip_code;type:varchar(255);comment:PIP编码" json:"pip_code"`
	PretaxAmount              *float32 `gorm:"column:pretax_amount;type:decimal(10,3);comment:未税金额" json:"pretax_amount"`
	PretaxGrossAmount         *float32 `gorm:"column:pretax_gross_amount;type:decimal(10,3);comment:未税总金额" json:"pretax_gross_amount"`
	ProductCode               *string  `gorm:"column:product_code;type:varchar(255);comment:商品编码" json:"product_code"`
	ProductDetail             *string  `gorm:"column:product_detail;type:varchar(255);comment:商品详情" json:"product_detail"`
	ProductName               *string  `gorm:"column:product_name;type:varchar(255);comment:商品名称" json:"product_name"`
	ProductType               *string  `gorm:"column:product_type;type:varchar(255);comment:商品类型" json:"product_type"`
	Region                    *string  `gorm:"column:region;type:varchar(255);comment:区域" json:"region"`
	ResourceGroup             *string  `gorm:"column:resource_group;type:varchar(255);index;comment:资源组" json:"resource_group"`
	ServicePeriod             *string  `gorm:"column:service_period;type:varchar(255);comment:服务周期" json:"service_period"`
	ServicePeriodUnit         *string  `gorm:"column:service_period_unit;type:varchar(255);comment:服务周期单位" json:"service_period_unit"`
	SubscriptionType          *string  `gorm:"column:subscription_type;type:varchar(255);index;comment:订阅类型" json:"subscription_type"`
	UsageUnit                 *string  `gorm:"column:usage_unit;type:varchar(255);comment:使用单位" json:"usage_unit"`
	Zone                      *string  `gorm:"column:zone;type:varchar(255);comment:可用区" json:"zone"`
}

func (MonthlyBill) TableName() string {
	return "asset_monthly_bills_aliyun"
}

func (a Account) syncMonthlyBills(billCycle string) (result string, err error) {
	result, bills, err := a.syncPageMonthlyBills(billCycle, nil)
	if err != nil {
		result += err.Error()
		return
	}
	dbop := app.DB().Begin()
	err = dbop.Where("bill_cycle = ? AND account_id = ?", billCycle, a.ID).Delete(&MonthlyBill{}).Error
	if err != nil {
		dbop.Rollback()
		result += "删除账单失败:" + err.Error()
		return
	}
	err = dbop.CreateInBatches(&bills, 1000).Error
	if err != nil {
		dbop.Rollback()
		result += "保存账单失败:" + err.Error()
		return
	}
	err = dbop.Commit().Error
	if err != nil {
		result += "提交事务失败:" + err.Error()
		return
	}
	result += "同步账单成功"
	return
}

func (a Account) syncPageMonthlyBills(billCycle string, nextToken *string) (result string, bills []MonthlyBill, err error) {
	accessKeyId := a.AccessKey
	accessKeySecret, _ := utils.Base64Decode(a.AccessSecret)
	config := &openapi.Config{
		// 必填，您的 AccessKey ID
		AccessKeyId: &accessKeyId,
		// 必填，您的 AccessKey Secret
		AccessKeySecret: &accessKeySecret,
	}
	// 访问的域名
	endPoint := "business.aliyuncs.com"
	config.Endpoint = &endPoint
	client, err := bssopenapi********.NewClient(config)
	if err != nil {
		result += "创建客户端失败"
		return
	}
	queryInstanceBillRequest := &bssopenapi********.DescribeInstanceBillRequest{
		BillingCycle:     &billCycle,
		IsHideZeroCharge: tea.Bool(true),
		MaxResults:       tea.Int32(300),
	}
	if nextToken != nil {
		queryInstanceBillRequest.NextToken = nextToken
	}

	// 复制代码运行请自行打印 API 的返回值
	response, err := client.DescribeInstanceBill(queryInstanceBillRequest)
	if err != nil {
		result += "获取账单失败:" + err.Error()
		return
	}
	if response.Body.Code != nil && *response.Body.Code != "Success" {
		result = *response.Body.Message
		err = errors.New(result)
		return
	}
	bills = a.transformMonthlyBills(billCycle, response.Body.Data.Items...)
	if response.Body.Data.NextToken != nil {
		r1, pageBills, err1 := a.syncPageMonthlyBills(billCycle, response.Body.Data.NextToken)
		if err1 != nil {
			result = r1 + ":" + err1.Error()
			err = err1
			return
		}
		result += r1
		bills = append(bills, pageBills...)
	}
	return
}

var subscriptionTypeMap = map[string]string{
	"Subscription": "包年/包月",
	"PayAsYouGo":   "按需",
}

func (a Account) transformMonthlyBills(billCycle string, bills ...*bssopenapi********.DescribeInstanceBillResponseBodyDataItems) (monthlyBills []MonthlyBill) {
	for _, bill := range bills {
		subscriptionType := "包年/包月"
		if bill.SubscriptionType != nil {
			subscriptionType = subscriptionTypeMap[*bill.SubscriptionType]
		}
		monthlyBills = append(monthlyBills, MonthlyBill{
			AccountID:                 a.ID,
			BillCycle:                 billCycle,
			InstanceID:                bill.InstanceID,
			SubscriptionType:          &subscriptionType,
			AdjustAmount:              bill.AdjustAmount,
			PretaxAmount:              bill.PretaxAmount,
			BillAccountID:             bill.BillAccountID,
			BillAccountName:           bill.BillAccountName,
			BillingDate:               bill.BillingDate,
			BillingItem:               bill.BillingItem,
			BillingItemCode:           bill.BillingItemCode,
			BillingType:               bill.BillingType,
			BizType:                   bill.BizType,
			CashAmount:                bill.CashAmount,
			CommodityCode:             bill.CommodityCode,
			CostUnit:                  bill.CostUnit,
			Currency:                  bill.Currency,
			DeductedByCashCoupons:     bill.DeductedByCashCoupons,
			DeductedByCoupons:         bill.DeductedByCoupons,
			DeductedByPrepaidCard:     bill.DeductedByPrepaidCard,
			DeductedByResourcePackage: bill.DeductedByResourcePackage,
			InstanceConfig:            bill.InstanceConfig,
			InstanceSpec:              bill.InstanceSpec,
			InternetIP:                bill.InternetIP,
			IntranetIP:                bill.IntranetIP,
			InvoiceDiscount:           bill.InvoiceDiscount,
			Item:                      bill.Item,
			ItemName:                  bill.ItemName,
			ListPrice:                 bill.ListPrice,
			ListPriceUnit:             bill.ListPriceUnit,
			NickName:                  bill.NickName,
			OutstandingAmount:         bill.OutstandingAmount,
			OwnerID:                   bill.OwnerID,
			PaymentAmount:             bill.PaymentAmount,
			PipCode:                   bill.PipCode,
			PretaxGrossAmount:         bill.PretaxGrossAmount,
			ProductCode:               bill.ProductCode,
			ProductDetail:             bill.ProductDetail,
			ProductName:               bill.ProductName,
			ProductType:               bill.ProductType,
			Region:                    bill.Region,
			ResourceGroup:             bill.ResourceGroup,
			ServicePeriod:             bill.ServicePeriod,
			ServicePeriodUnit:         bill.ServicePeriodUnit,
			UsageUnit:                 bill.UsageUnit,
			Zone:                      bill.Zone,
		})
	}
	return
}
