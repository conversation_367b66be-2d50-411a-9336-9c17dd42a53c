package gitcode

import (
	"cmdb/app"
	"cmdb/pkg/db"
	"errors"
	"time"

	goGitlab "github.com/xanzy/go-gitlab"
	"gorm.io/gorm"
)

type Group struct {
	ID          uint           `gorm:"column:id;primaryKey;comment:ID" json:"id"`
	GitlabID    uint           `gorm:"type:varchar(255);column:gitlab_id;index;comment:GitLab ID" json:"gitlab_id"`
	GroupID     int            `gorm:"type:varchar(255);column:group_id;index;comment:组 ID" json:"group_id"`
	Name        string         `gorm:"type:varchar(255);column:name;index;comment:名称" json:"name"`
	Description string         `gorm:"type:varchar(255);column:description;index;comment:描述" json:"description"`
	HttpUrl     string         `gorm:"type:varchar(255);column:http_url;index;comment:HTTP URL" json:"http_url"`
	DeletedAt   gorm.DeletedAt `gorm:"column:deleted_at;index;comment:删除时间" json:"deleted_at"`
	SyncTime    time.Time      `gorm:"column:sync_time;index;comment:同步时间" json:"sync_time"`
}

func (Group) TableName() string {
	return "asset_gitcode_groups"
}

func (gitlab Gitlab) SyncGroups() (err error) {
	syncTime := time.Now()
	// 初始化 GitLab 客户端
	client, err := gitlab.GetClient()
	if err != nil {
		return
	}

	// 获取所有组
	opt := &goGitlab.ListGroupsOptions{
		ListOptions: goGitlab.ListOptions{
			PerPage: 100,
			Page:    1,
		},
	}

	for {
		groups, resp, err := client.Groups.ListGroups(opt)
		if err != nil {
			return err
		}

		// 遍历组并同步到数据库
		for _, g := range groups {
			exist := Group{}
			// 检查数据库中是否已存在该组
			err = app.DB().Where("gitlab_id = ? AND group_id = ?", gitlab.ID, g.ID).Take(&exist).Error
			if errors.Is(err, gorm.ErrRecordNotFound) {
				// 如果不存在，则创建新的组记录
				err = app.DB().Create(&Group{
					GitlabID:    gitlab.ID,
					GroupID:     g.ID,
					Name:        g.Name,
					Description: g.Description,
					HttpUrl:     g.WebURL,
					SyncTime:    time.Now(),
				}).Error
			} else if err == nil {
				updateMap := map[string]any{
					"sync_time": time.Now(),
				}
				if exist.Name != g.Name {
					updateMap["name"] = g.Name
				}
				if exist.Description != g.Description {
					updateMap["description"] = g.Description
				}
				if exist.HttpUrl != g.WebURL {
					updateMap["http_url"] = g.WebURL
				}
				err = app.DB().Model(&exist).Updates(updateMap).Error
			}
			if err != nil {
				return err
			}
		}

		// 检查是否还有下一页
		if resp.CurrentPage >= resp.TotalPages {
			break
		}
		opt.Page = resp.NextPage
	}

	// 删除已删除的组，通过sync_time来判断
	err = app.DB().Where("gitlab_id = ? AND sync_time < ?", gitlab.ID, syncTime.Add(-1*time.Hour)).
		Delete(&Group{}).Error
	return
}

func GetGroups(offset int, limit int, keyword *string, gitlabID *int) (count int64, data []Group, err error) {
	dbop := app.DB()
	if keyword != nil {
		dbop = db.MLike(dbop, *keyword, "name", "description", "http_url")
	}
	if gitlabID != nil {
		dbop = dbop.Where("gitlab_id = ?", *gitlabID)
	}
	err = dbop.Model(&Group{}).Count(&count).Order("name").Offset(offset).Limit(limit).Find(&data).Error
	return
}

func (git Gitlab) GetGroups() (groups []Group, err error) {
	err = app.DB().Where("gitlab_id = ?", git.ID).Order("name").Find(&groups).Error
	return
}

func (group Group) GetGitlabName() (name string, err error) {
	err = app.DB().Model(&Gitlab{}).Select("name").Where("id = ?", group.GitlabID).Scan(&name).Error
	return
}
