package mysql

import (
	"cmdb/app"
	"cmdb/app/service/database/mysql"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// GetInstanceLengthHistories 获取实例历史大小
func GetInstanceLengthHistories(c *gin.Context) {
	id, err := strconv.Atoi(c.<PERSON>m("id"))
	if err != nil {
		app.Log().Debug("非法参数", "err", err)
		app.FailedResponseMsg(c, "非法参数")
		return
	}
	instance, err := mysql.GetInstanceByID(id)
	if err != nil {
		msg := "获取实例失败"
		if err == gorm.ErrRecordNotFound {
			msg = "实例不存在"
		} else {
			app.Log().Error("获取实例失败", "err", err)
		}
		app.FailedResponseMsg(c, msg)
		return
	}
	now := time.Now()
	endTime, err := time.ParseInLocation("2006-01-02", c.<PERSON><PERSON>("end_time"), time.Local)
	if err != nil {
		endTime = now
	}
	startTime, err := time.ParseInLocation("2006-01-02", c.Query("start_time"), time.Local)
	if err != nil {
		startTime = endTime.AddDate(0, -3, 0)
	}
	stats, err := instance.GetInstanceLengthHistories(startTime, endTime)
	if err != nil {
		app.Log().Error("获取实例大小历史记录失败", "err", err)
		app.FailedResponseMsg(c, "获取实例大小历史记录失败")
		return
	}
	app.SuccessResponseData(c, stats)
}

// GetInstanceSchemaLengthHistories 获取实例Schema历史大小
func GetInstanceSchemaLengthHistories(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.FailedResponseMsg(c, "非法参数")
		return
	}
	instance, err := mysql.GetInstanceByID(id)
	if err != nil {
		msg := "获取实例失败"
		if err == gorm.ErrRecordNotFound {
			msg = "实例不存在"
		} else {
			app.Log().Error("获取实例失败", "err", err)
		}
		app.FailedResponseMsg(c, msg)
		return
	}
	schema := c.Param("name")
	now := time.Now()
	endTime, err := time.ParseInLocation("2006-01-02", c.Query("end_time"), time.Local)
	if err != nil {
		endTime = now
	}
	startTime, err := time.ParseInLocation("2006-01-02", c.Query("start_time"), time.Local)
	if err != nil {
		startTime = endTime.AddDate(0, -3, 0)
	}
	stats, err := instance.GetInstanceSchemaLengthHistories(schema, startTime, endTime)
	if err != nil {
		app.Log().Error("获取实例大小历史记录失败", "err", err)
		app.FailedResponseMsg(c, "获取实例大小历史记录失败")
		return
	}
	app.SuccessResponseData(c, stats)
}

// GetInstanceTableLengthHistories 获取实例历史大小
func GetInstanceTableLengthHistories(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.FailedResponseMsg(c, "非法参数")
		return
	}
	instance, err := mysql.GetInstanceByID(id)
	if err != nil {
		msg := "获取实例失败"
		if err == gorm.ErrRecordNotFound {
			msg = "实例不存在"
		} else {
			app.Log().Error("获取实例失败", "err", err)
		}
		app.FailedResponseMsg(c, msg)
		return
	}
	schema := c.Param("schema")
	name := c.Param("name")
	now := time.Now()
	endTime, err := time.ParseInLocation("2006-01-02", c.Query("end_time"), time.Local)
	if err != nil {
		endTime = now
	}
	startTime, err := time.ParseInLocation("2006-01-02", c.Query("start_time"), time.Local)
	if err != nil {
		startTime = endTime.AddDate(0, -3, 0)
	}
	stats, err := instance.GetInstanceTableLengthHistories(schema, name, startTime, endTime)
	if err != nil {
		app.Log().Error("获取实例大小历史记录失败", "err", err)
		app.FailedResponseMsg(c, "获取实例大小历史记录失败")
		return
	}
	app.SuccessResponseData(c, stats)
}
