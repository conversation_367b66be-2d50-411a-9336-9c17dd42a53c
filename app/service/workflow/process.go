package workflow

import (
	"cmdb/app"
	"cmdb/app/service/auth"
	"encoding/json"
	"strings"
	"time"

	"gorm.io/datatypes"
)

const (
	// 未审批流程状态
	UnapproveProcessStatus = uint(iota)
	// 审批中流程状态
	ApprovingProcessStatus
	// 通过流程状态
	PassProcessStatus
	// 驳回流程状态
	RejectProcessStatus
)

var (
	// 过滤不走上级领导流程的工号
	SUPERLEADER_SNs = []string{"1", "2", "3", "780"}
)

// 审批流程
type Process struct {
	ID           uint           `gorm:"column:id;primarykey;comment:id" json:"id"`
	ProcessIndex uint           `gorm:"column:process_index;index;comment:流程索引，从小到大顺序递增" json:"process_index"`
	SN           string         `gorm:"type:varchar(128);column:sn;index;comment:关联工单单号" json:"sn"`
	ApproverID   uint           `gorm:"column:approver_id;index;comment:审批者ID" json:"-"`
	ApproversIDs datatypes.JSON `gorm:"column:approvers_id;comment:可审批者ID" json:"-"`
	CCIDs        datatypes.JSON `gorm:"column:ccs_id;comment:抄送者ID" json:"-"`
	NodeName     string         `gorm:"column:node_name;type:varchar(255);comment:节点名称" json:"node_name"`
	Status       uint           `gorm:"column:status;index;comment:审批状态" json:"status"`
	Comment      string         `gorm:"type:longtext;column:comment;comment:审批备注" json:"comment"`
	ApproveTime  *time.Time     `gorm:"column:approve_time;default:null;" json:"approve_time"`
	CreatedAt    time.Time      `gorm:"column:created_at" json:"created_at"`
}

// 表名
func (*Process) TableName() string {
	return "workflow_processes"
}

type ProcessDetail struct {
	Process
	Approver  Approver   `gorm:"-" json:"approver"`
	Approvers []Approver `gorm:"-" json:"approvers"`
	CCs       []Approver `gorm:"-" json:"ccs"`
}

// 表名
func (p ProcessDetail) TableName() string {
	return p.Process.TableName()
}

// 获取审批流程详情
func (p *ProcessDetail) Detail() (err error) {
	if p.ApproverID > 0 {
		var approver auth.User
		approver, err = auth.GetUserByID(int(p.ApproverID))
		if err != nil {
			return
		}
		p.Approver = Approver{User: approver}
		if p.ProcessIndex == 0 {
			gs, err1 := p.Approver.GetDepartments()
			if err1 == nil && len(gs) > 0 {
				fileds := strings.Split(gs[0].Name, "-")
				if len(fileds) > 0 {
					p.Approver.Name = fileds[len(fileds)-1] + " - " + p.Approver.Name
				}
			}
		}
	}
	ids := []uint{}
	json.Unmarshal(p.ApproversIDs, &ids)
	if len(ids) > 0 {
		p.Approvers, err = GetApproversByIDs(ids...)
	}
	ids = []uint{}
	json.Unmarshal(p.CCIDs, &ids)
	if len(ids) > 0 {
		p.CCs, err = GetApproversByIDs(ids...)
	}
	return
}

// 获取工单的审批流程详情
func (o Order) GetProcessDetails() (ps []ProcessDetail, err error) {
	err = app.DB().Where("sn = ?", o.SN).Order("process_index").Find(&ps).Error
	if err != nil {
		return
	}
	for i := range ps {
		err = ps[i].Detail()
		if err != nil {
			return
		}
	}
	return
}
