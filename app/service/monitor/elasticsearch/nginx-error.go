package elasticsearch

import (
	"context"
	"time"

	"gopkg.in/olivere/elastic.v6"
)

type NginxErrorStat struct {
	ErrorType string
	App       string
	Domain    string
	Count     int64
}

type NginxErrorType struct {
	Index         string
	ErrorType     string
	SearchFilters []SearchFilter
}

var NginxErrorTypes = []NginxErrorType{
	{Index: "nginx_error-*", ErrorType: "MySQL server has gone away", SearchFilters: []SearchFilter{
		{Col: "message", Keywords: "MySQL server has gone away", Operator: "AND"},
		{Col: "loglevel", Keywords: "error", Operator: "AND"},
		{Col: "env", Keywords: "prod", Operator: "AND"},
	}},
	{Index: "nginx_error-*", ErrorType: "redis error", SearchFilters: []SearchFilter{
		{Col: "message", Keywords: "redis error", Operator: "AND"},
		{Col: "loglevel", Keywords: "error", Operator: "AND"},
		{Col: "env", Keywords: "prod", Operator: "AND"},
	}},
	{Index: "nginx_error-*", ErrorType: "mysql error", SearchFilters: []SearchFilter{
		{Col: "message", Keywords: "mysql timeout", Operator: "AND"},
		{Col: "loglevel", Keywords: "error", Operator: "AND"},
		{Col: "env", Keywords: "prod", Operator: "AND"},
	}},
}

func (sp NginxErrorType) CheckNginxError(startTime, endTime time.Time) (stats []NginxErrorStat, err error) {
	client, err := initClient()
	if err != nil {
		return
	}
	defer client.Stop()
	query := elastic.NewBoolQuery()
	qs := []elastic.Query{elastic.NewRangeQuery("@timestamp").Gte(startTime).Lt(endTime)}
	for i := range sp.SearchFilters {
		qs = append(qs, elastic.NewMatchQuery(sp.SearchFilters[i].Col, sp.SearchFilters[i].Keywords).Operator(sp.SearchFilters[i].Operator))
	}
	query.Must(qs...)
	result, err := client.Search(sp.Index).Query(query).Aggregation("app", elastic.NewTermsAggregation().Field("app").SubAggregation("domain", elastic.NewTermsAggregation().Field("domain"))).Size(0).Do(context.Background())
	if err != nil {
		return
	}
	if result.Hits.TotalHits > 0 {
		appAgg, found := result.Aggregations.Terms("app")
		if found {
			for _, appBucket := range appAgg.Buckets {
				app, ok := appBucket.Key.(string)
				if !ok {
					return
				}
				domainAgg, found := appBucket.Aggregations.Terms("domain")
				if found {
					for _, domainBucket := range domainAgg.Buckets {
						domain, ok := domainBucket.Key.(string)
						if !ok {
							return
						}
						count := domainBucket.DocCount
						if count > 10 {
							// 大于10
							stats = append(stats, NginxErrorStat{
								App: app, Domain: domain, Count: count, ErrorType: sp.ErrorType,
							})
						}
					}
				}
			}
		}
	}
	return
}
