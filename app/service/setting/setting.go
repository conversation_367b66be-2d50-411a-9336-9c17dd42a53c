package setting

import (
	"cmdb/app"
	"cmdb/pkg/utils"
)

const (
	// NormalSettingType 表示普通设置类型
	NormalSettingType = uint(iota)
	// BoolSettingType 表示布尔值设置类型
	BoolSettingType
	// PasswordSettingType 表示密码设置类型
	PasswordSettingType
	// ContactsSettingType 表示联系人设置类型
	ContactsSettingType
)

type SettingItem struct {
	ID          uint   `gorm:"primaryKey;column:id;comment:ID" json:"id"`
	Section     string `gorm:"type:varchar(250);index;column:section;comment:配置节" json:"section"`
	Name        string `gorm:"type:varchar(250);index;column:name;comment:配置值" json:"name"`
	Alias       string `gorm:"type:varchar(255);column:alias;comment:配置值别名" json:"alias"`
	SettingType uint   `gorm:"column:setting_type;default:0;comment:配置类型" json:"setting_type"`
	Content     string `gorm:"type:varchar(400);column:content;comment:配置内容" json:"content"`
	OrderIndex  uint   `gorm:"column:order_index;default:0;comment:配置项排序" json:"order_index"`
}

func (*SettingItem) TableName() string {
	return "settings"
}

// 获取所有的配置节
func GetSettingSections() (sections []SettingSection, err error) {
	err = app.DB().Order("alias").Find(&sections).Error
	return
}

// 获取配置节下的所有配置
func (section *SettingSection) GetItems() (err error) {
	err = app.DB().Where("section = ?", section.Name).Order("order_index").Find(&section.Items).Error
	if err != nil {
		return err
	}
	for i := range section.Items {
		// 不返回密码
		if section.Items[i].SettingType == PasswordSettingType {
			section.Items[i].Content = ""
		}
	}
	return
}

// 通过名称获取配置节对象
func GetSettingSectionByName(name string) (section SettingSection, err error) {
	err = app.DB().Where("name = ?", name).Take(&section).Error
	return
}

// 通过名称获取配置节对象
func GetSettingValue(section, name string) (content string) {
	item := SettingItem{}
	err := app.DB().Where("name = ? AND section = ?", name, section).Limit(1).Take(&item).Error
	if err != nil {
		app.Log().Error("获取配置失败", "section", section, "name", name, "err", err)
		return
	}
	if item.SettingType == PasswordSettingType {
		content, _ = utils.Base64Decode(item.Content)
	} else {
		content = item.Content
	}
	return
}

type SettingForm struct {
	Section string `json:"section"`
	Name    string `json:"name"`
	Content string `json:"content" binding:"max=400"`
}

// 更新配置
func UpdateSetting(form []SettingForm) (err error) {
	if len(form) == 0 {
		return
	}
	dbop := app.DB().Begin()
	for i := range form {
		item := SettingItem{}
		err = app.DB().Where("section = ? AND name =?",
			form[i].Section, form[i].Name).Take(&item).Error
		if err != nil {
			dbop.Rollback()
			return
		}
		if item.SettingType == PasswordSettingType {
			if form[i].Content != "" {
				err = dbop.Model(&item).Update("content", utils.Base64Encode(form[i].Content)).Error
				if err != nil {
					dbop.Rollback()
					return
				}
			}
		} else {
			err = dbop.Model(&item).Update("content", form[i].Content).Error
			if err != nil {
				dbop.Rollback()
				return
			}
		}
	}
	err = dbop.Commit().Error
	if err != nil {
		dbop.Rollback()
	}
	return
}
