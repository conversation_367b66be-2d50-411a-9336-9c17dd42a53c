package asset

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/database/mysql"
	"cmdb/app/service/monitor"
	"encoding/json"
	"time"

	"gorm.io/datatypes"
)

func statMongoDBStorage() (storageTotal int64, err error) {
	mongodbTag, err := asset.GetTagByNameValue("db", "mongodb")
	if err != nil {
		return
	}
	hosts, err := mongodbTag.GetHosts()
	if err != nil {
		return
	}
	ips := []string{}
	for _, host := range hosts {
		ips = append(ips, host.IP)
	}
	storageTotal, err = monitor.StatsHostDiskDataXUsage(ips...)
	return
}

func statStarrocksStorage() (storageTotal int64, err error) {
	starrocksTag, err := asset.GetTagByNameValue("db", "starrocks")
	if err != nil {
		return
	}
	hosts, err := starrocksTag.GetHosts()
	if err != nil {
		return
	}
	ips := []string{}
	for _, host := range hosts {
		ips = append(ips, host.IP)
	}
	storageTotal, err = monitor.StatsHostDiskDataXUsage(ips...)
	return
}

func statClickhouseStorage() (storageTotal int64, err error) {
	clickhouseTag, err := asset.GetTagByNameValue("db", "clickhouse")
	if err != nil {
		return
	}
	hosts, err := clickhouseTag.GetHosts()
	if err != nil {
		return
	}
	ips := []string{}
	for _, host := range hosts {
		ips = append(ips, host.IP)
	}
	storageTotal, err = monitor.StatsHostDiskDataXUsage(ips...)
	return
}

type DatasourceStatistic struct {
	DataSize       float64 `json:"data_size"`
	DataSourceName string  `json:"data_source_name"`
}
type DatasourceStatistics struct {
	DatasourceStatistics []DatasourceStatistic `json:"datasource_statistics"`
	Count                int                   `json:"count"`
}

type DILStat struct {
	YMD               string         `gorm:"column:ymd;comment:年月日" json:"ymd"`
	StatisticJsonData datatypes.JSON `gorm:"column:statistic_json_data;comment:统计数据" json:"statistic_json_data"`
	CreatedAt         time.Time      `gorm:"column:created_at;comment:创建时间" json:"created_at"`
	UpdatedAt         time.Time      `gorm:"column:updated_at;comment:更新时间" json:"updated_at"`
}

func (DILStat) TableName() string {
	return "coach_metadata.hw_dli_technical_assets_statistic"
}

func statDliStorage() (storageTotal float64, err error) {
	var instance mysql.Instance
	err = app.DB().Where("name = ?", "柚先森").Take(&instance).Error
	if err != nil {
		return
	}
	var stat DILStat
	dbop, err := instance.NewConnect()
	if err != nil {
		return
	}
	app.Log().Info("数据库连接成功")
	defer func() {
		d, _ := dbop.DB()
		if d != nil {
			d.Close()
		}
	}()
	err = dbop.Model(&DILStat{}).Order("ymd DESC").Take(&stat).Error
	if err != nil {
		return
	}
	app.Log().Info("获取柚先森DLI存储容量成功")
	var stats DatasourceStatistics
	err = json.Unmarshal(stat.StatisticJsonData, &stats)
	if err != nil {
		app.Log().Error("获取柚先森DLI存储容量时，解析统计数据异常", "err", err.Error())
		return
	}
	app.Log().Info("获取柚先森DLI存储容量成功", "stats", stats)
	for i := range stats.DatasourceStatistics {
		storageTotal += stats.DatasourceStatistics[i].DataSize
	}
	return
}
