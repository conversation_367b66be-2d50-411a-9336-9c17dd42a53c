package note

import (
	"cmdb/app"
	"cmdb/app/service/audit"
	"cmdb/app/service/auth"
	"cmdb/app/service/note"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

func GetDutyMembers(c *gin.Context) {
	members, err := note.GetAllDutyMembers()
	if err != nil {
		app.Log().Error("获取所有值班人员失败", "err", err)
		app.FailedResponseMsg(c, "获取所有值班人员失败")
		return
	}
	app.SuccessResponseData(c, members)
}

// 重新排班
func ReAutoSchedule(c *gin.Context) {
	startTime, err := time.ParseInLocation("2006-01-02", c.Query("start_time"), time.Local)
	if err != nil {
		startTime = time.Now()
	}
	var uid uint
	duty, err := note.GetDutyByDate(startTime)
	if err == nil {
		uid = duty.UID
		startTime = time.Time(duty.DueDate)
	} else if err == gorm.ErrRecordNotFound {
		uid = 0
		startTime = time.Now()
	}
	err = note.AutoScheduleDuty(startTime, uint(uid))
	if err != nil {
		app.Log().Error("自动排班失败", "err", err)
		app.FailedResponseMsg(c, "自动排班失败")
		return
	}
	audit.LogAPIOP(c, "排班管理", "重新排班")
	app.SuccessResponseMsg(c, "操作成功")
}

// 获取排班
func GetDutys(c *gin.Context) {
	monthString := strings.TrimSpace(c.Query("month"))
	begin := time.Now()
	var err error
	if monthString != "" {
		begin, err = time.ParseInLocation("2006-01", monthString, time.Local)
		if err != nil {
			app.Log().Error("非法月份", "err", err)
			app.FailedResponseMsg(c, "非法月份")
			return
		}
	}
	end := begin.AddDate(0, 1, 0)
	startTime := time.Date(begin.Year(), begin.Month(), begin.Day(), 0, 0, 0, 0, time.Local)
	endTime := time.Date(end.Year(), end.Month(), end.Day(), 0, 0, 0, 0, time.Local)
	schedules, err := note.GetDutys(startTime, endTime)
	if err != nil {
		app.Log().Error("获取排班失败", "err", err)
		app.FailedResponseMsg(c, "获取排班失败")
		return
	}
	for i := range schedules {
		schedules[i].DueDateString = time.Time(schedules[i].DueDate).Format("2006-01-02")
	}
	app.SuccessResponseData(c, schedules)
}

type AdjustScheduleForm struct {
	FromDateString string `json:"from_date" binding:"required"`
	ToDateString   string `json:"to_date" binding:"required"`
}

// AdjustSchedule 调班
func AdjustSchedule(c *gin.Context) {
	var form AdjustScheduleForm
	err := c.BindJSON(&form)
	if err != nil {
		app.Log().Debug("调班接口，非法字段", "err", err)
		app.FailedResponseMsg(c, "非法字段")
		return
	}
	fromDate, err := time.ParseInLocation("2006-01-02", form.FromDateString, time.Local)
	if err != nil {
		app.FailedResponseMsg(c, "非法日期")
		return
	}
	toDate, err := time.ParseInLocation("2006-01-02", form.ToDateString, time.Local)
	if err != nil {
		app.FailedResponseMsg(c, "非法日期")
		return
	}
	now := time.Now().Truncate(24 * time.Hour)
	if fromDate.Before(now) || toDate.Before(now) {
		app.FailedResponseMsg(c, note.ErrPassedDuty.Error())
		return
	}
	from, err := note.GetDutyByDate(fromDate)
	if err != nil {
		app.FailedResponseMsg(c, "操作排班对象不存在")
		return
	}
	to, err := note.GetDutyByDate(toDate)
	if err != nil {
		app.FailedResponseMsg(c, "操作排班对象不存在")
		return
	}
	if from.UID == to.UID {
		app.FailedResponseMsg(c, "无效调班")
		return
	}
	err = note.AdjustDuty(&from, &to)
	if err != nil {
		msg := "调班失败"
		if err == note.ErrPassedDuty {
			msg = err.Error()
		} else {
			app.Log().Error("调班失败", "err", err)
		}
		app.FailedResponseMsg(c, msg)
		return
	}
	fromUser, _ := auth.GetUserByID(int(from.UID))
	toUser, _ := auth.GetUserByID(int(to.UID))
	audit.LogAPIOP(c, "值班管理", form.FromDateString+" "+fromUser.Name+" => "+form.ToDateString+" "+toUser.Name)
	app.SuccessResponseMsg(c, "操作成功")
}

// 更新值班人员
func UpdateDutyMembers(c *gin.Context) {
	var forms note.DutyMembersForm
	err := c.ShouldBind(&forms)
	if err != nil {
		app.Log().Debug("更新值班人员，验证表单失败", "err", err)
		app.FailedResponseMsg(c, "非法字段")
		return
	}
	err = note.UpdateDutyMembers(forms)
	if err != nil {
		app.Log().Error("更新值班人员失败", "err", err)
		app.FailedResponseMsg(c, "更新值班人员失败")
		return
	}
	audit.LogAPIOP(c, "排班管理", "更新值班人员")
	app.SuccessResponseMsg(c, "更新成功")
}

func NotifyDuty(c *gin.Context) {
	err := note.NotifyDuty()
	if err != nil {
		app.Log().Error("通知失败", "err", err)
		app.FailedResponseMsg(c, "通知失败")
		return
	}
	audit.LogAPIOP(c, "排班管理", "通知值班人员")
	app.SuccessResponseMsg(c, "通知成功")
}
