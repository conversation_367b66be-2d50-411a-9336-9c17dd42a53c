package aliyun

import (
	"strconv"
	"time"

	"errors"

	"github.com/aliyun/alibaba-cloud-sdk-go/services/ddoscoo"
	"gorm.io/gorm"

	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/asset/cloud"
	"cmdb/pkg/utils"
)

func (a Account) syncDDoSSet() (result cloud.SyncResults, err error) {
	startTime := time.Now()
	instances, err := a.syncInstances(1)
	if err != nil {
		result = append(result, cloud.SyncResult{
			Key:    "DDoS",
			Result: "同步失败" + err.<PERSON>rror(),
		})
		return
	}
	err = a.saveDDoSSet(instances)
	if err != nil {
		result = append(result, cloud.SyncResult{
			Key:    "DDoS",
			Result: "同步成功，保存失败" + err.Error(),
		})
		return
	}
	err = a.cleanDDoSSet(startTime)
	if err != nil {
		result = append(result, cloud.SyncResult{
			Key:    "DDoS",
			Result: "同步成功，但是清理历史数据失败" + err.<PERSON>rror(),
		})
		return
	}
	result = append(result, cloud.SyncResult{
		Key:    "DDoS",
		Result: "同步成功",
	})
	rs, err := a.syncDDoSDomains()
	if err != nil {
		result = append(result, cloud.SyncResult{
			Key:    "DDoS Domain",
			Result: "同步失败" + err.Error(),
		})
		return
	} else {
		result = append(result, rs...)
	}
	return
}

// SyncInstances synchronizes DDoS instances from Alibaba Cloud
func (a Account) syncInstances(page int) ([]asset.CloudDDosProtection, error) {
	secret, _ := utils.Base64Decode(a.AccessSecret)
	client, err := ddoscoo.NewClientWithAccessKey("cn-hangzhou", a.AccessKey, secret)
	if err != nil {
		return nil, err
	}
	request := ddoscoo.CreateDescribeInstancesRequest()
	request.Scheme = "https"
	request.PageNumber = strconv.Itoa(page)
	request.PageSize = strconv.Itoa(50)
	request.RegionId = "cn-hangzhou"

	response, err := client.DescribeInstances(request)
	if err != nil {
		return nil, err
	}
	total := response.TotalCount
	var instances []asset.CloudDDosProtection
	for _, instance := range response.Instances {
		var enable bool
		if instance.Enabled == 1 {
			enable = true
		}
		var expireTime *time.Time
		t := time.UnixMilli(instance.ExpireTime)
		expireTime = &t
		createTime := time.UnixMilli(instance.CreateTime)
		instances = append(instances, asset.CloudDDosProtection{
			AccountID:  a.ID,
			InstanceID: instance.InstanceId,
			Status:     instance.Status,
			Enabled:    enable,
			IP:         instance.Ip,
			IPVersion:  instance.IpVersion,
			IPMode:     instance.IpMode,
			ExpireTime: expireTime,
			Remark:     instance.Remark,
			CreateTime: createTime,
			SyncTime:   time.Now(),
		})
	}
	if total > int64(page*50) {
		subInstances, err := a.syncInstances(page + 1)
		if err != nil {
			return nil, err
		}
		instances = append(instances, subInstances...)
	}
	return instances, nil
}

func (a Account) saveDDoSSet(instances []asset.CloudDDosProtection) (err error) {
	for _, instance := range instances {
		exist := asset.CloudDDosProtection{}
		err = app.DB().Where("account_id = ? AND instance_id = ?", a.ID, instance.InstanceID).Take(&exist).Error
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = app.DB().Create(&instance).Error
		} else {
			updateMap := map[string]any{
				"sync_time": time.Now(),
			}
			if instance.Enabled != exist.Enabled {
				updateMap["enabled"] = instance.Enabled
			}
			if instance.Remark != exist.Remark {
				updateMap["remark"] = instance.Remark
			}
			if instance.ExpireTime != exist.ExpireTime {
				updateMap["expire_time"] = instance.ExpireTime
			}
			if instance.IP != exist.IP {
				updateMap["ip"] = instance.IP
			}
			if instance.IPVersion != exist.IPVersion {
				updateMap["ip_version"] = instance.IPVersion
			}
			if instance.IPMode != exist.IPMode {
				updateMap["ip_mode"] = instance.IPMode
			}
			if instance.CreateTime != exist.CreateTime {
				updateMap["create_time"] = instance.CreateTime
			}
			err = app.DB().Model(&exist).Updates(updateMap).Error
		}
		if err != nil {
			return
		}
	}
	return
}

func (a Account) cleanDDoSSet(syncTime time.Time) (err error) {
	err = app.DB().Where("account_id = ? AND sync_time < ?", a.ID, syncTime.Add(time.Minute*-5)).Delete(&asset.CloudDDosProtection{}).Error
	return
}
