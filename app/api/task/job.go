package task

import (
	"cmdb/app"
	"cmdb/app/service/task"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

func GetJobs(c *gin.Context) {
	keyword := strings.TrimSpace(c.Query("keyword"))
	page, _ := strconv.Atoi(c.<PERSON><PERSON>("page", "1"))
	limit, _ := strconv.Atoi(c<PERSON>("limit", "10"))
	offset := (page - 1) * limit
	count, data, err := task.GetJobs(offset, limit, keyword)
	if err != nil {
		app.Log().Error("获取任务日志失败", "err", err)
		app.FailedResponseMsg(c, "获取数据失败")
		return
	}
	app.SuccessResponseDataCount(c, data, count)
}

func GetJobLogs(c *gin.Context) {
	name := strings.TrimSpace(c.Query("name"))
	page, _ := strconv.Atoi(c.<PERSON><PERSON><PERSON><PERSON>("page", "1"))
	limit, _ := strconv.Atoi(c.<PERSON>("limit", "10"))
	offset := (page - 1) * limit
	count, data, err := task.GetJobLogs(offset, limit, name)
	if err != nil {
		app.Log().Error("获取任务日志失败", "err", err)
		app.FailedResponseMsg(c, "获取数据失败")
		return
	}
	app.SuccessResponseDataCount(c, data, count)
}
