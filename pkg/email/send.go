package email

import (
	"fmt"
	"net/smtp"
	"strings"
)

// Send 发送邮件
func Send(name, user, password, host string, port int, toEmails, subject, body, mailtype string) error {
	auth := smtp.PlainAuth("", user, password, host)
	var contentType string
	if mailtype == "html" {
		contentType = "Content-Type: text/" + mailtype + "; charset=UTF-8"
	} else {
		contentType = "Content-Type: text/plain" + "; charset=UTF-8"
	}

	msg := []byte("to: " + toEmails + "\r\nfrom: " + name + " <" + user + ">\r\nsubject: " + subject + "\r\n" + contentType + "\r\n\r\n" + body)
	sendTo := strings.Split(toEmails, ";")
	err := smtp.SendMail(fmt.Sprintf("%s:%d", host, port), auth, user, sendTo, msg)
	return err
}
