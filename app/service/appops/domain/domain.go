package domain

import (
	"cmdb/app"
	"cmdb/pkg/db"
	"errors"
	"strings"

	"gorm.io/gorm"
)

type DomainType string

const (
	PublicDomain  DomainType = "public"
	PrivateDomain DomainType = "private"
)

var (
	ErrDomainExist = errors.New("域名已经存在")
)

type Domain struct {
	ID         uint           `gorm:"column:id;primaryKey;comment:ID" json:"id"`
	Name       string         `gorm:"type:varchar(255);column:name;index;comment:名称" json:"name"`
	DomainType DomainType     `gorm:"type:varchar(255);column:domain_type;comment:域名类型" json:"domain_type"`
	BusinessID uint           `gorm:"column:business_id;index;comment:关联业务的ID" json:"business_id"`
	Remark     string         `gorm:"type:varchar(255);column:remark;comment:描述" json:"remark"`
	DeletedAt  gorm.DeletedAt `gorm:"column:deleted_at;comment:删除时间" json:"-"`
}

func (Domain) TableName() string {
	return "appops_domains"
}

type DomainForm struct {
	Name       string     `json:"name" binding:"required,max=255"`
	DomainType DomainType `json:"domain_type" binding:"required,oneof=public private"`
	BusinessID uint       `json:"business_id"`
	Remark     string     `json:"remark" binding:"max=255"`
}

func (form DomainForm) Create() (err error) {
	// 先判断是否存在，存在就返回已经存在
	if err = app.DB().Model(&Domain{}).Where("name = ?", form.Name).Take(&Domain{}).Error; err == nil {
		err = ErrDomainExist
	}
	if errors.Is(err, gorm.ErrRecordNotFound) {
		// 如果不存在，就创建
		err = app.DB().Create(&Domain{
			Name:       form.Name,
			DomainType: form.DomainType,
			BusinessID: form.BusinessID,
			Remark:     form.Remark,
		}).Error
	}
	return
}

func GetDomains(offset, limit int, businessID *int, keyword *string) (count int64, domains []Domain, err error) {
	dbop := app.DB().Model(&Domain{})
	// 业务ID搜索
	if businessID != nil && *businessID > 0 {
		dbop = dbop.Where("business_id = ?", *businessID)
	}
	// 关键词搜索
	if keyword != nil && *keyword != "" {
		dbop = db.MLike(dbop, *keyword, "name", "remark")
	}
	err = dbop.Count(&count).Order("name").Offset(offset).Limit(limit).Find(&domains).Error
	return
}

func GetDomainByID(id int) (domain Domain, err error) {
	err = app.DB().Where("id = ?", id).Take(&domain).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = nil
	}
	return
}
func (d *Domain) Update(form DomainForm) (err error) {
	updateMap := map[string]any{}
	// 判断名称是否重复
	if d.Name != form.Name {
		exist := &Domain{}
		err = app.DB().Model(&Domain{}).Where("name = ?", form.Name).Take(exist).Error
		if err == nil && exist.ID != d.ID {
			err = ErrDomainExist
			return
		}
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = nil
		}
		updateMap["name"] = form.Name
	}
	if d.DomainType != form.DomainType {
		updateMap["domain_type"] = form.DomainType
	}
	if d.BusinessID != form.BusinessID {
		updateMap["business_id"] = form.BusinessID
	}
	if d.Remark != form.Remark {
		updateMap["remark"] = form.Remark
	}
	if len(updateMap) > 0 {
		err = app.DB().Model(&d).Updates(updateMap).Error
	}
	return
}

func (d *Domain) Delete() (err error) {
	err = app.DB().Delete(d).Error
	return
}

func GetAllDomains() (domains []Domain, err error) {
	err = app.DB().Model(&Domain{}).Find(&domains).Error
	return
}

func GetDomainsByIDs(ids []int) (domains []Domain, err error) {
	if len(ids) == 0 {
		return
	}
	err = app.DB().Model(&Domain{}).Where("id IN (?)", ids).Find(&domains).Error
	return
}

func GenDomain(name string) (err error) {
	// 先判断是否存在，存在就返回已经存在
	if err = app.DB().Unscoped().Model(&Domain{}).Where("name = ?", name).Take(&Domain{}).Error; errors.Is(err, gorm.ErrRecordNotFound) {
		dominaType := PublicDomain
		if strings.Contains(name, "meiyoucloud.com") {
			dominaType = PrivateDomain
		}
		err = app.DB().Create(&Domain{
			Name:       name,
			DomainType: dominaType,
			Remark:     "同步自动生成",
		}).Error
	}
	return
}
