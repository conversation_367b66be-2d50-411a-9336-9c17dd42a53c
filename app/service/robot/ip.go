package robot

import (
	"cmdb/app/service"
	"cmdb/app/service/assets"
)

type AssetInfo struct {
	IP            string           `json:"ip"`
	PublicIP      string           `json:"public_ip"`
	Name          string           `json:"name"`
	AssetType     assets.AssetType `json:"asset_type"`
	AssetTypeName string           `json:"asset_type_name"`
}

func queryIP(usage string, params ...string) (msg HTTPBodyMarkdown) {
	if len(params) == 0 {
		msg.Markdown.Title = "IP查询"
		msg.Markdown.Text = usage
		return
	}
	ip := params[0]
	msg.Markdown.Title = "IP查询"
	msg.Markdown.Text = ""
	data, err := service.Search(ip)
	if err != nil {
		msg.Markdown.Text = err.Error()
		return
	}
	for i := range data {
		msg.Markdown.Text += "## " + data[i].AssetTypeName + "\n"
		msg.Markdown.Text += "- " + data[i].Name + "\n"
		msg.Markdown.Text += "- " + data[i].IP + "\n"
		msg.Markdown.Text += "- " + data[i].ExtInfo + "\n"
		msg.Markdown.Text += "---\n"
	}
	return
}
