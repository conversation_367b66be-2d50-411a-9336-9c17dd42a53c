package asset

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/asset/cloud/aliyun"
	"strconv"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
)

type DDoSProtection struct {
	asset.CloudDDosProtection
	Account string `json:"account"`
}

func GetDDosProtections(c *gin.Context) {
	var keyword *string
	// 获取查询参数
	if val := c.Query("keyword"); val != "" {
		keyword = &val
	}
	var accountID *int
	// 将查询参数转换为int类型
	id, err := strconv.Atoi(c.<PERSON>fault<PERSON>("account_id", "0"))
	if err == nil && id > 0 {
		accountID = &id
	}
	// 获取分页参数
	page, _ := strconv.Atoi(c.<PERSON>fault<PERSON><PERSON>y("page", "1"))
	limit, _ := strconv.Atoi(c.<PERSON>fault<PERSON><PERSON>("limit", "10"))
	// 计算偏移量
	offset := (page - 1) * limit
	count, data, err := asset.GetCloudDDosProtections(offset, limit, accountID, keyword)
	if err != nil {
		app.Log().Error("获取DDoS防护列表失败", "err", err)
		app.FailedResponseMsg(c, "获取数据失败")
		return
	}
	newData := make([]DDoSProtection, len(data))
	if len(data) > 0 {
		tmpCloudAccountStore := sync.Map{}
		wg := sync.WaitGroup{}
		wg.Add(len(data))
		for i := range data {
			go func() {
				// 获取云账户名称
				if val, ok := tmpCloudAccountStore.Load(data[i].AccountID); ok {
					newData[i].Account, _ = val.(string)
				} else {
					account, err1 := asset.GetAccountNameByID(int(data[i].AccountID))
					if err1 == nil {
						newData[i].Account = account
						tmpCloudAccountStore.Store(data[i].AccountID, account)
					}
				}
				newData[i].CloudDDosProtection = data[i]
				wg.Done()
			}()
		}
		wg.Wait()
	}
	app.SuccessResponseDataCount(c, newData, count)
}

type DDoSDomain struct {
	asset.CloudDDosDomain
	Account string `json:"account"`
}

func GetDDosDomains(c *gin.Context) {
	var keyword *string
	// 获取查询参数
	if val := c.Query("keyword"); val != "" {
		keyword = &val
	}
	var accountID *int
	// 将查询参数转换为int类型
	id, err := strconv.Atoi(c.DefaultQuery("account_id", "0"))
	if err == nil && id > 0 {
		accountID = &id
	}
	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	// 计算偏移量
	offset := (page - 1) * limit
	count, data, err := asset.GetCloudDDosDomains(offset, limit, accountID, keyword)
	if err != nil {
		app.Log().Error("获取DDoS域名列表失败", "err", err)
		app.FailedResponseMsg(c, "获取数据失败")
		return
	}
	newData := make([]DDoSDomain, len(data))
	if len(data) > 0 {
		tmpCloudAccountStore := sync.Map{}
		wg := sync.WaitGroup{}
		wg.Add(len(data))
		for i := range data {
			go func() {
				// 获取云账户名称
				if val, ok := tmpCloudAccountStore.Load(data[i].AccountID); ok {
					newData[i].Account, _ = val.(string)
				} else {
					account, err1 := asset.GetAccountNameByID(int(data[i].AccountID))
					if err1 == nil {
						newData[i].Account = account
						tmpCloudAccountStore.Store(data[i].AccountID, account)
					}
				}
				newData[i].CloudDDosDomain = data[i]
				wg.Done()
			}()
		}
		wg.Wait()
	}
	app.SuccessResponseDataCount(c, newData, count)
}

func GetAllDDosDomainBps(c *gin.Context) {
	var startTime, endTime time.Time
	var err error
	if val := c.Query("start_time"); val != "" {
		startTime, err = time.Parse("2006-01-02", val)
		if err != nil {
			app.FailedResponseMsg(c, "参数错误 , start_time 格式为:YYYY-mm-dd")
			return
		}
	} else {
		startTime = time.Now().AddDate(0, 0, -7)
	}
	if val := c.Query("end_time"); val != "" {
		endTime, err = time.Parse("2006-01-02", val)
		if err != nil {
			app.FailedResponseMsg(c, "参数错误 , end_time 格式为:YYYY-mm-dd")
			return
		}
	} else {
		endTime = time.Now()
	}
	data, err := asset.GetCloudDDosDomainDailyBpsList(startTime, endTime)
	if err != nil {
		app.FailedResponseMsg(c, "获取数据失败")
		return
	}
	app.SuccessResponseData(c, data)
}
func GetDDosDomainBps(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	var startTime, endTime time.Time
	if val := c.Query("start_time"); val != "" {
		startTime, err = time.Parse("2006-01-02", val)
		if err != nil {
			app.FailedResponseMsg(c, "参数错误 , start_time 格式为:YYYY-mm-dd")
			return
		}
	} else {
		startTime = time.Now().AddDate(0, 0, -7)
	}
	if val := c.Query("end_time"); val != "" {
		endTime, err = time.Parse("2006-01-02", val)
		if err != nil {
			app.FailedResponseMsg(c, "参数错误 , end_time 格式为:YYYY-mm-dd")
			return
		}
	} else {
		endTime = time.Now()
	}
	domain, err := asset.GetCloudDDosDomainByID(id)
	if err != nil {
		app.FailedResponseMsg(c, "参数错误")
		return
	}

	data, err := domain.GetDailyBpsList(startTime, endTime)
	if err != nil {
		app.FailedResponseMsg(c, "获取数据失败")
		return
	}
	app.SuccessResponseData(c, data)
}

func SyncDDosDomainBps(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.Log().Error("参数错误", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	var statDate time.Time
	if val := c.Query("stat_date"); val != "" {
		statDate, err = time.Parse("2006-01-02", val)
		if err != nil {
			app.Log().Error("stat_date参数错误", "err", err)
			app.FailedResponseMsg(c, "参数错误 , stat_date 格式为:YYYY-mm-dd")
			return
		}
	} else {
		app.Log().Error("stat_date参数错误, stat_date 不能为空", "val", val)
		app.FailedResponseMsg(c, "参数错误, stat_date 不能为空")
		return
	}
	domain, err := asset.GetCloudDDosDomainByID(id)
	if err != nil {
		app.Log().Error("同步DDoS域名流量时，获取DDoS域名失败", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	timeout := time.Millisecond * 500
	done := make(chan struct{})
	go func() {
		err = aliyun.SyncDDosDomainBps(domain, statDate)
		if err != nil {
			app.Log().Error("同步数据失败", "err", err)
			app.FailedResponseMsg(c, "同步数据失败")
			return
		}
		done <- struct{}{}
	}()
	select {
	case <-time.Tick(timeout):
		app.SuccessResponseMsg(c, "同步数据中，请稍后查看")
	case <-done:
		app.SuccessResponseMsg(c, "同步数据成功")
	}
}
