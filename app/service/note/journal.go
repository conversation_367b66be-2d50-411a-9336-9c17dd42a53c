package note

import (
	"bytes"
	"cmdb/app"
	"cmdb/pkg/db"
	"encoding/json"
	"time"

	"gorm.io/datatypes"
	"gorm.io/gorm/clause"
)

// 运维事件记录
type Journal struct {
	ID         uint           `gorm:"primaryKey;column:id;comment:ID" json:"id"`
	Title      string         `gorm:"type:varchar(255);column:title;comment:标题" json:"title" `
	EventType  string         `gorm:"column:event_type;type:varchar(255);comment:事件类型" json:"event_type"`
	RegionType string         `gorm:"column:region_type;type:varchar(255);comment:区域类型" json:"region_type"`
	Tags       datatypes.JSON `gorm:"column:tags;comment:标签" json:"tags"`
	StartTime  *time.Time     `gorm:"column:start_time" json:"start_time"`
	EndTime    *time.Time     `gorm:"column:end_time" json:"end_time"`
	Content    string         `gorm:"type:longtext;column:content;comment:内容" json:"content"`
	Conclusion string         `gorm:"type:longtext;column:conclusion;comment:结论;" json:"conclusion"`
	Projects   datatypes.JSON `gorm:"column:projects;comment:关联项目" json:"projects"`
	CreatedBy  string         `gorm:"type:varchar(255);column:created_by;comment:创建者" json:"created_by"`
	CreatedAt  time.Time      `gorm:"column:created_at;comment:创建时间" json:"created_at"`
	UpdatedAt  time.Time      `gorm:"column:updated_at;comment:更新时间" json:"updated_at"`
}

// TableName 设置表名
func (*Journal) TableName() string {
	return "note_journals"
}

// JournalForm 事件表单
type JournalForm struct {
	Title      string     `json:"title" binding:"required,max=255"`
	EventType  string     `json:"event_type" binding:"max=255"`
	RegionType string     `json:"region_type" binding:"max=255"`
	Tags       []string   `json:"tags"`
	StartTime  *time.Time `json:"start_time"`
	EndTime    *time.Time `json:"end_time"`
	Content    string     `json:"content" `
	Conclusion string     `json:"conclusion" `
	Projects   []string   `json:"projects"`
	CreatedBy  string     `json:"created_by" binding:"max=255"`
}

// 分页获取事件列表
func GetJournals(offset, limit int, keyword, eventType, project, tag, regionType *string) (count int64, js []Journal, err error) {
	dbop := app.DB()
	if keyword != nil {
		dbop = db.MLike(dbop, *keyword, "title")
	}
	if eventType != nil {
		dbop = dbop.Where("event_type = ?", eventType)
	}
	if regionType != nil {
		dbop = dbop.Where("region_type = ?", regionType)
	}
	if project != nil {
		dbop = dbop.Clauses(clause.Expr{
			SQL:  "JSON_CONTAINS(projects,JSON_ARRAY(?))",
			Vars: []interface{}{project},
		})
	}
	if tag != nil {
		dbop = dbop.Clauses(clause.Expr{
			SQL:  "JSON_CONTAINS(tags,JSON_ARRAY(?))",
			Vars: []interface{}{tag},
		})
	}
	err = dbop.Model(&Journal{}).Count(&count).Select(
		"id", "title", "event_type", "start_time", "end_time", "projects", "tags",
		"created_by", "updated_at", "created_at", "region_type",
	).Order("created_at DESC , id DESC").Offset(offset).Limit(limit).Find(&js).Error
	return
}

// 添加事件
func (form JournalForm) Create() (err error) {
	projects, err := json.Marshal(&form.Projects)
	if err != nil {
		return err
	}
	tags, err := json.Marshal(&form.Tags)
	if err != nil {
		return err
	}
	err = app.DB().Create(&Journal{
		Title:      form.Title,
		CreatedBy:  form.CreatedBy,
		Content:    form.Content,
		Conclusion: form.Conclusion,
		EventType:  form.EventType,
		StartTime:  form.StartTime,
		EndTime:    form.EndTime,
		RegionType: form.RegionType,
		Projects:   projects,
		Tags:       tags,
	}).Error
	return
}

// 更新事件
func (j Journal) Update(form *JournalForm) (err error) {
	projects, err := json.Marshal(&form.Projects)
	if err != nil {
		return
	}
	tags, err := json.Marshal(&form.Tags)
	if err != nil {
		return
	}
	// 通过判断字段是否变化来进行更新
	updateMap := map[string]any{}
	if j.Title != form.Title {
		updateMap["title"] = form.Title
	}
	if j.Content != form.Content {
		updateMap["content"] = form.Content
	}
	if j.Conclusion != form.Conclusion {
		updateMap["conclusion"] = form.Conclusion
	}
	if j.EventType != form.EventType {
		updateMap["event_type"] = form.EventType
	}
	if j.RegionType != form.RegionType {
		updateMap["region_type"] = form.RegionType
	}
	if j.StartTime != form.StartTime {
		updateMap["start_time"] = form.StartTime
	}
	if j.EndTime != form.EndTime {
		updateMap["end_time"] = form.EndTime
	}
	if !bytes.Equal(j.Projects, projects) {
		updateMap["projects"] = projects
	}
	if !bytes.Equal(j.Tags, tags) {
		updateMap["tags"] = tags
	}
	if len(updateMap) > 0 {
		err = app.DB().Model(&j).Updates(updateMap).Error
	}
	return
}

// Delete 删除事件
func (j *Journal) Delete() error {
	return app.DB().Delete(j).Error
}

// 通过ID获取事件
func GetJournalByID(id int) (Journal Journal, err error) {
	err = app.DB().Where("id = ?", id).Take(&Journal).Error
	return
}
