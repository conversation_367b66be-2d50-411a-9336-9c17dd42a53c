package notice

import (
	"bytes"
	"cmdb/app"
	"cmdb/app/service/setting"
	"cmdb/pkg/dingtalk"
	"cmdb/pkg/email"
	"errors"
	"log/slog"
	"strings"
	"sync"
	"time"
)

// 发送消息
func SendMessage() (err error) {
	contacts := []Message{}
	err = app.DB().Select([]string{
		"DISTINCT link , message_type",
	}).Where("status = ? ", MessageStatusUnsent).Group("link , message_type").Find(&contacts).Error
	if err != nil {
		return
	}
	var wg sync.WaitGroup
	for i := range contacts {
		wg.Add(1)
		go func(i int) {
			defer wg.Done()
			messages := []Message{}
			err = app.DB().Where("status = ? and link = ? and message_type = ?", MessageStatusUnsent, contacts[i].Link, contacts[i].MessageType).Find(&messages).Error
			if err != nil {
				return
			}
			var content bytes.Buffer
			atMobilesMap := map[string]string{}
			atMobiles := []string{}
			subject := ""
			for k := range messages {
				mobiles := strings.Split(messages[k].AtMobiles, ",")
				for kk := range mobiles {
					atMobilesMap[mobiles[kk]] = ""
				}
				if k == 0 {
					subject = messages[k].Title
				}
				content.WriteString(messages[k].Title + "\n" + messages[k].Content + "\n")
			}
			for mobile := range atMobilesMap {
				atMobiles = append(atMobiles, mobile)
			}
			resp := ""
			switch contacts[i].MessageType {
			case MessageDingtalkRobot:
				resp, err = dingtalk.SendMessage(contacts[i].Link, content.String(), atMobiles...)
				if err != nil {
					resp, err = dingtalk.SendMessage(contacts[i].Link, content.String(), atMobiles...)
				}
			case MessageMeiyou:
				resp, err = dingtalk.SentMeiyouNotice(contacts[i].Link, content.String()+" \n--发送时间："+time.Now().Format("2006-01-02 15:04:05"))
			case MessageEmail:
				smtpSetting, err := setting.GetSMTPSetting()
				if err != nil {
					return
				}
				err = email.Send(smtpSetting.Name, smtpSetting.User, smtpSetting.Password, smtpSetting.Host, smtpSetting.Port, contacts[i].Link, subject, "<p>"+strings.ReplaceAll(content.String(), "\n", "</p><p>")+"</p>", "html")
				if err != nil {
					resp = err.Error()
				}
			default:
				err = errors.New("不支持的消息类型")
			}
			updateMap := map[string]interface{}{
				"send_time": time.Now(),
			}
			if err != nil {
				updateMap["status"] = MessageStatusFailed
				updateMap["send_result"] = resp + " err:" + err.Error()
			} else {
				updateMap["status"] = MessageStatusSuccess
				updateMap["send_result"] = resp
			}
			for k := range messages {
				err1 := app.DB().Model(&messages[k]).Updates(updateMap).Error
				if err1 != nil {
					app.Log().Error("更新消息记录失败", slog.Any("err", err1))
				}
			}
		}(i)
	}
	wg.Wait()
	return
}
