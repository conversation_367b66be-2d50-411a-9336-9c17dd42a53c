package mysql

import (
	"bytes"
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/pkg/db"
	"cmdb/pkg/utils"
	"encoding/json"
	"errors"
	"strings"
	"sync"
	"time"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

var (
	ErrProjectExist = errors.New("mysql项目已经存在")
)

// Project 结构体定义了MySQL项目的字段
type Project struct {
	ID            uint           `gorm:"column:id;primary_key;comment:ID" json:"id"`
	Name          string         `gorm:"type:varchar(255);column:name;index;comment:项目名称" json:"name"`
	Link          string         `gorm:"type:varchar(255);column:link;index;comment:链接" json:"link"`
	Port          uint           `gorm:"column:port;comment:链接端口" json:"port"`
	ExtLink       string         `gorm:"type:varchar(255);column:ext_link;comment:扩展链接" json:"ext_link"`
	AdminUser     string         `gorm:"type:varchar(255);column:admin_user;comment:管理用户" json:"admin_user"`
	AdminPassword string         `gorm:"type:varchar(255);column:admin_password;comment:管理密码" json:"-"`
	Env           asset.ENV      `gorm:"column:env;default:0;index;comment:环境设置" json:"env"`
	Remark        string         `gorm:"type:varchar(255);column:remark;comment:备注" json:"remark"`
	DBAGroupID    uint           `gorm:"column:dba_group_id;comment:DBA分组" json:"dba_group_id"`
	IsSensitive   bool           `gorm:"column:is_sensitive;default:0;comment:是否是敏感数据，1表示是" json:"is_sensitive"`
	DBs           datatypes.JSON `gorm:"column:dbs;comment:数据库库名" json:"dbs"`
	CustomDBs     datatypes.JSON `gorm:"column:custom_dbs;comment:自定义数据库库名" json:"custom_dbs"`
	CreatedAt     time.Time      `gorm:"column:created_at;comment:创建时间" json:"created_at"`
	UpdatedAt     time.Time      `gorm:"column:updated_at;comment:更新时间" json:"updated_at"`
	DeletedAt     gorm.DeletedAt `gorm:"column:deleted_at;index;comment:软删除" json:"-"`
}

// TableName 返回Project表名
func (*Project) TableName() string {
	return "asset_db_mysql_projects"
}

// GetProjects 获取MySQL项目列表
func GetProjects(offset, limit int, env *int, keyword *string) (count int64, projects []Project, err error) {
	dbop := app.DB()
	if env != nil {
		dbop = dbop.Where("env = ?", *env)
	}
	if keyword != nil {
		dbop = db.MLike(dbop, *keyword, "name", "link", "ext_link")
	}
	err = dbop.Model(&Project{}).Count(&count).Order("created_at DESC , name").Offset(offset).Limit(limit).Find(&projects).Error
	return
}

// ProjectForm 结构体定义了MySQL项目表单的字段
type ProjectForm struct {
	Name          string    `json:"name" binding:"required,max=255"`
	Link          string    `json:"link" binding:"required,max=255"`
	Port          uint      `json:"port" binding:"min=1,max=65535"`
	ExtLink       string    `json:"ext_link" binding:"max=255"`
	AdminUser     string    `json:"admin_user" binding:"required,max=255"`
	AdminPassword string    `json:"admin_password" binding:"max=255"`
	Remark        string    `json:"remark" binding:"max=255"`
	Env           asset.ENV `json:"env"`
	CustomDBs     []string  `json:"custom_dbs" `
	IsSensitive   bool      `json:"is_sensitive"`
	DBAGroupID    uint      `json:"dba_group_id"`
}

// Create 创建MySQL项目
func (form ProjectForm) Create() (err error) {
	err = app.DB().Select("id").Where("name = ?", form.Name).Take(&Project{}).Error
	if err == nil {
		return ErrProjectExist
	}
	if err == gorm.ErrRecordNotFound {
		customDBsByte, _ := json.Marshal(&form.CustomDBs)
		err = app.DB().Create(&Project{
			Name:          form.Name,
			Link:          form.Link,
			Port:          form.Port,
			ExtLink:       form.ExtLink,
			Env:           form.Env,
			AdminPassword: utils.Base64Encode(form.AdminPassword),
			AdminUser:     form.AdminUser,
			Remark:        form.Remark,
			IsSensitive:   form.IsSensitive,
			CustomDBs:     customDBsByte,
			DBAGroupID:    form.DBAGroupID,
		}).Error
	}
	return
}

// Update 更新MySQL项目
func (project Project) Update(form ProjectForm) (err error) {
	updateMap := map[string]any{}
	if form.Name != project.Name {
		exist := Project{}
		err = app.DB().Select("id").Where("name = ?", form.Name).Take(&exist).Error
		if err == nil && exist.ID != project.ID {
			return ErrProjectExist
		} else if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		} else {
			err = nil
		}
		updateMap["name"] = form.Name
	}
	if form.AdminPassword != "" && utils.Base64Encode(form.AdminPassword) != project.AdminPassword {
		updateMap["admin_password"] = utils.Base64Encode(form.AdminPassword)
	}
	if form.AdminUser != project.AdminUser {
		updateMap["admin_user"] = form.AdminUser
	}
	if form.Link != project.Link {
		updateMap["link"] = form.Link
	}
	if form.ExtLink != project.ExtLink {
		updateMap["ext_link"] = form.ExtLink
	}
	if form.Port != project.Port {
		updateMap["port"] = form.Port
	}
	dbsByte, _ := json.Marshal(&form.CustomDBs)
	if form.CustomDBs != nil && !bytes.Equal(dbsByte, project.CustomDBs) {
		updateMap["custom_dbs"] = dbsByte
	}
	if form.Remark != project.Remark {
		updateMap["remark"] = form.Remark
	}
	if form.Env != project.Env {
		updateMap["env"] = form.Env
	}
	if form.DBAGroupID != project.DBAGroupID {
		updateMap["dba_group_id"] = form.DBAGroupID
	}
	if form.IsSensitive != project.IsSensitive {
		updateMap["is_sensitive"] = form.IsSensitive
	}
	oldProject := Project{}
	err = app.DB().Select("id").Where("name = ?", form.Name).Take(&oldProject).Error
	if err == nil && oldProject.ID != project.ID {
		return ErrProjectExist
	}
	if len(updateMap) > 0 {
		err = app.DB().Model(&project).Updates(updateMap).Error
	}
	return
}

// GetProjectByID 根据ID获取MySQL项目
func GetProjectByID(id int) (project Project, err error) {
	err = app.DB().Unscoped().Where("id = ?", id).Take(&project).Error
	return
}

// GetAllProjects 获取所有MySQL项目
func GetAllProjects() (projects []Project, err error) {
	err = app.DB().Order("created_at DESC , name ").Find(&projects).Error
	return
}

// Delete 删除MySQL项目
func (project *Project) Delete() (err error) {
	err = app.DB().Delete(project).Error
	return
}

// SchemaTable 结构体定义了MySQL项目的表结构
type SchemaTable struct {
	Name   string `gorm:"column:TABLE_NAME" json:"table_name"`
	Schema string `gorm:"column:TABLE_SCHEMA" json:"table_schema"`
}

// TableSchema 结构体定义了MySQL项目的表结构
type TableSchema struct {
	SchemaName              string `gorm:"column:SCHEMA_NAME"`
	DefaultCharacterSetName string `gorm:"column:DEFAULT_CHARACTER_SET_NAME"`
	DefaultCollationName    string `gorm:"column:DEFAULT_COLLATION_NAME"`
}

// TableName 返回TableSchema表名
func (TableSchema) TableName() string {
	return "SCHEMATA"
}

// SyncProjectsDBs 同步MySQL项目的数据库库名
func SyncProjectsDBs() (err error) {
	projects := []Project{}
	err = app.DB().Select(
		"id", "name", "link", "port", "admin_password", "admin_user",
	).Find(&projects).Error
	if err != nil {
		return
	}
	errMessages := []string{}
	var wg sync.WaitGroup
	for i := range projects {
		wg.Add(1)
		go func(i int) {
			schemas := []TableSchema{}
			defer wg.Done()
			password, _ := utils.Base64Decode(projects[i].AdminPassword)
			var op *gorm.DB
			var err1 error
			op, err1 = db.NewMySQLDBConnect(db.MysqlConnParmas{
				Host:     projects[i].Link,
				Port:     projects[i].Port,
				User:     projects[i].AdminUser,
				Password: password,
				DB:       "information_schema",
			}, nil)
			if err1 != nil {
				errMessages = append(errMessages, "获取项目 "+projects[i].Name+" 库名失败，"+err1.Error())
				return
			}
			defer func() {
				sqlDB, err1 := op.DB()
				if err1 == nil {
					sqlDB.Close()
				}
			}()
			err1 = op.Select("SCHEMA_NAME").Where(
				"SCHEMA_NAME NOT IN (?)",
				SYSTEMSchemas,
			).Find(&schemas).Error
			if err1 != nil {
				errMessages = append(errMessages, projects[i].Name+"获取项目库名失败，"+err1.Error())
				return
			}
			dbNames := []string{}
			for i := range schemas {
				for _, v := range SYSTEMSchemas {
					if v == schemas[i].SchemaName {
						continue
					}
				}
				dbNames = append(dbNames, schemas[i].SchemaName)
			}
			dbsByte, _ := json.Marshal(&dbNames)
			err = app.DB().Model(&projects[i]).Update("dbs", dbsByte).Error
			if err != nil {
				errMessages = append(errMessages, projects[i].Name+"更新项目库名失败，"+err.Error())
			}
		}(i)
	}
	wg.Wait()
	if len(errMessages) > 0 {
		err = errors.New(strings.Join(errMessages, ";"))
	}
	return
}

func (project Project) GetProjectTableMetaData(schemaName string, tableName []string) (tables []Table, err error) {
	password, _ := utils.Base64Decode(project.AdminPassword)
	dbop, err := db.NewMySQLDBConnect(db.MysqlConnParmas{
		Host:     project.Link,
		Port:     project.Port,
		User:     project.AdminUser,
		Password: password,
		DB:       "information_schema",
	}, nil)
	if err != nil {
		return
	}
	defer func() {
		d, err1 := dbop.DB()
		if err1 == nil {
			d.Close()
		}
	}()
	err = dbop.Unscoped().Table("information_schema.TABLES").
		Select("TABLE_SCHEMA AS table_schema", "TABLE_NAME AS name", "TABLE_TYPE AS table_type", "`ENGINE` AS table_engine", "ROW_FORMAT as row_format", "TABLE_COLLATION as collation", "CREATE_OPTIONS as create_options", "TABLE_COMMENT AS table_comment", "CREATE_TIME AS create_time", "UPDATE_TIME AS update_time", "DATA_LENGTH AS data_length", "INDEX_LENGTH AS index_length", "TABLE_ROWS as table_rows").Where("TABLE_SCHEMA = ? AND TABLE_NAME IN (?)", schemaName, tableName).Find(&tables).Error
	if err != nil {
		return
	}
	return
}
