package hwcloud

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/asset/cloud"
	"cmdb/pkg/utils"
	"errors"
	"time"

	"github.com/huaweicloud/huaweicloud-sdk-go-v3/core/auth/basic"
	region "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/bss/v2/region"
	"gorm.io/gorm"

	vpc "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/vpc/v2"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/vpc/v2/model"
)

func (a Account) syncSubnets() (result cloud.SyncResults, err error) {
	dcs, err := asset.GetDatacentersByCloudType(asset.HuaweiCloudType)
	if err != nil {
		return
	}
	for _, dc := range dcs {
		r, err := a.syncRegionSubnets(dc, "")
		sr := cloud.SyncResult{
			Key: dc.Name,
		}
		if err == nil {
			sr.Result = "同步成功"
			// 回收过期主机
			err = a.expiredSubnets(time.Now().Add(-10*time.Minute), dc)
			if err != nil {
				sr.Result = "同步成功，但是回收子网失败" + err.Error()
			}
		} else {
			sr.Result = r + " " + err.Error()
		}
		result = append(result, sr)
	}
	return
}

func (a Account) expiredSubnets(expiredTime time.Time, dc asset.Datacenter) (err error) {
	subnets := []asset.SubNet{}
	err = app.DB().Where("account_id = ? and sync_time < ? and datacenter_id = ?", a.ID, expiredTime, dc.ID).Find(&subnets).Error
	if err != nil {
		return
	}
	for i := range subnets {
		err = subnets[i].Delete()
		if err != nil {
			return
		}
	}
	return
}

func (a Account) syncRegionSubnets(dc asset.Datacenter, marker string) (result string, err error) {
	secret, err := utils.Base64Decode(a.AccessSecret)
	if err != nil {
		result = "解密secret失败"
		return
	}
	auth, err := basic.NewCredentialsBuilder().WithAk(a.AccessKey).WithSk(secret).SafeBuild()
	if err != nil {
		result = "构建认证失败"
		return
	}
	regionID, err := region.SafeValueOf(dc.Code)
	if err != nil {
		result = "非法regionID："
		return
	}
	clientBuild, err := vpc.VpcClientBuilder().WithRegion(regionID).WithCredential(auth).SafeBuild()
	if err != nil {
		result = "构建客户端失败"
		return
	}
	client := vpc.NewVpcClient(clientBuild)
	var limit int32 = 2000
	var markerPoint *string
	if marker != "" {
		markerPoint = &marker
	}
	response, err := client.ListSubnets(&model.ListSubnetsRequest{
		Limit:  &limit,
		Marker: markerPoint,
	})
	if err != nil {
		return
	}
	dataLength := len(*response.Subnets)
	if dataLength > 0 {
		err = a.saveSubnets(dc, *response.Subnets)
		if err != nil {
			return
		}
		result, err = a.syncRegionSubnets(dc, (*response.Subnets)[dataLength-1].Id)
	}
	return
}

func (a Account) saveSubnets(dc asset.Datacenter, subnets []model.Subnet) (err error) {
	now := time.Now()
	for _, subnet := range subnets {
		exist := asset.SubNet{}
		err = app.DB().Where("account_id = ? and v_switch_id = ? and datacenter_id = ?", a.ID, subnet.NeutronNetworkId, dc.ID).First(&exist).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		createTime, err1 := time.ParseInLocation("2006-01-02T15:04:05Z", subnet.CreatedAt.String(), time.Local)
		if err1 != nil {
			createTime = now
		}
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = app.DB().Create(&asset.SubNet{
				AccountID:       a.ID,
				DatacenterID:    dc.ID,
				Name:            subnet.Name,
				VSwitchID:       subnet.NeutronNetworkId,
				ResourceGroupID: subnet.TenantId,
				IPv4CIDR:        subnet.Cidr,
				IPv6CIDR:        subnet.CidrV6,
				Description:     subnet.Description,
				VPCID:           subnet.VpcId,
				ZoneID:          subnet.AvailabilityZone,
				CreatedAt:       createTime,
				SyncTime:        &now,
			}).Error
		} else if err == nil {
			updateMap := map[string]any{"sync_time": &now}
			if exist.Name != subnet.Name {
				updateMap["name"] = subnet.Name
			}
			if exist.Description != subnet.Description {
				updateMap["description"] = subnet.Description
			}
			if exist.IPv4CIDR != subnet.Cidr && !subnet.Ipv6Enable {
				updateMap["ipv4_cidr"] = subnet.Cidr
			}
			if exist.IPv6CIDR != subnet.CidrV6 && subnet.Ipv6Enable {
				updateMap["ipv6_cidr"] = subnet.CidrV6
			}
			if exist.ZoneID != subnet.AvailabilityZone {
				updateMap["zone_id"] = subnet.AvailabilityZone
			}
			if exist.ResourceGroupID != subnet.TenantId {
				updateMap["resource_group_id"] = subnet.TenantId
			}
			err = app.DB().Model(&exist).Updates(updateMap).Error
		}
		if err != nil {
			return
		}
	}
	return
}
