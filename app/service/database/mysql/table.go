package mysql

import (
	"cmdb/app"
	"cmdb/pkg/db"
	"time"

	"gorm.io/gorm"
)

type Table struct {
	ID            uint           `gorm:"column:id;primaryKey;comment:主键" json:"id"`
	InstanceID    uint           `gorm:"column:instance_id;index:idx_instance_id;comment:实例ID" json:"instance_id"`
	TableSchema   string         `gorm:"column:table_schema;type:varchar(255);index" json:"table_schema"`
	Name          string         `gorm:"column:name;type:varchar(255);index" json:"name"`
	TableType     string         `gorm:"column:table_type;type:varchar(255)" json:"table_type"`
	TableEngine   string         `gorm:"column:table_engine;type:varchar(255)" json:"table_engine"`
	Comment       string         `gorm:"column:comment;type:varchar(255)" json:"comment"`
	RowFormat     string         `gorm:"column:row_format;type:varchar(255)" json:"row_format"`
	Collation     string         `gorm:"column:collation;type:varchar(255)" json:"collation"`
	CreateOptions string         `gorm:"column:create_options;type:varchar(255)" json:"create_options"`
	DataLength    uint           `gorm:"column:data_length;type:bigint(20)" json:"data_length"`
	IndexLength   uint           `gorm:"column:index_length;type:bigint(20)" json:"index_length"`
	TableRows     uint           `gorm:"column:table_rows;type:bigint(20)" json:"table_rows"`
	CreateTime    time.Time      `gorm:"column:create_time;index" json:"create_time"`
	UpdateTime    *time.Time     `gorm:"column:update_time;index" json:"update_time"`
	SyncTime      time.Time      `gorm:"column:sync_time;index" json:"sync_time"`
	DeletedAt     gorm.DeletedAt `gorm:"column:deleted_at;index" json:"-"`
}

func (Table) TableName() string {
	return "asset_db_mysql_tables"
}

func (instance Instance) GetTables(offset, limit int, schema, keyword *string) (count int64, data []Table, err error) {
	dbop := app.DB().Model(&Table{}).Where("instance_id = ?", instance.ID)
	if schema != nil {
		dbop = dbop.Where("table_schema = ?", *schema)
	}
	if keyword != nil {
		dbop = db.MLike(dbop, *keyword, "name", "comment")
	}
	err = dbop.Count(&count).Offset(offset).Limit(limit).Find(&data).Error
	return
}

func (t Table) StatHistory(recordTime string) (err error) {
	return app.DB().Create(&TableLengthHistory{
		InstanceID:  t.InstanceID,
		Schema:      t.TableSchema,
		Name:        t.Name,
		DataLength:  t.DataLength,
		IndexLength: t.IndexLength,
		RecordTime:  recordTime,
		CreatedAt:   time.Now(),
	}).Error
}

func (instance Instance) SyncTables() (err error) {
	dbop, err := instance.NewConnect()
	if err != nil {
		return
	}
	tables := []Table{}
	err = dbop.Unscoped().Table("information_schema.TABLES").
		Select("TABLE_SCHEMA AS table_schema", "TABLE_NAME AS name", "TABLE_TYPE AS table_type", "`ENGINE` AS table_engine", "ROW_FORMAT as row_format", "TABLE_COLLATION as collation", "CREATE_OPTIONS as create_options", "TABLE_COMMENT AS table_comment", "CREATE_TIME AS create_time", "UPDATE_TIME AS update_time", "DATA_LENGTH AS data_length", "INDEX_LENGTH AS index_length", "TABLE_ROWS as table_rows").Where("TABLE_SCHEMA NOT IN (?)", SYSTEMSchemas).Find(&tables).Error
	db.CloseDB(dbop)
	if err != nil {
		return
	}
	startTime := time.Now()
	recordTime := startTime.Format("2006-01-02 15:04:05")
	for i := range tables {
		tables[i].InstanceID = instance.ID
		tables[i].SyncTime = startTime
		exist := Table{}
		err1 := app.DB().Where("name = ? AND table_schema = ? AND instance_id = ?", tables[i].Name, tables[i].TableSchema, tables[i].InstanceID).Take(&exist).Error
		if err1 == gorm.ErrRecordNotFound {
			err1 = app.DB().Create(&tables[i]).Error
			exist = tables[i]
		} else if err1 == nil {
			updateMap := map[string]any{
				"sync_time": startTime,
			}
			if tables[i].TableType != exist.TableType {
				updateMap["table_type"] = tables[i].TableType
			}
			if tables[i].TableEngine != exist.TableEngine {
				updateMap["table_engine"] = tables[i].TableEngine
			}
			if tables[i].RowFormat != exist.RowFormat {
				updateMap["row_format"] = tables[i].RowFormat
			}
			if tables[i].Collation != exist.Collation {
				updateMap["collation"] = tables[i].Collation
			}
			if tables[i].CreateOptions != exist.CreateOptions {
				updateMap["create_options"] = tables[i].CreateOptions
			}
			if tables[i].Comment != exist.Comment {
				updateMap["comment"] = tables[i].Comment
			}
			if !exist.CreateTime.Equal(tables[i].CreateTime) {
				updateMap["create_time"] = tables[i].CreateTime
			}
			if exist.TableRows != tables[i].TableRows {
				updateMap["table_rows"] = tables[i].TableRows
			}
			if exist.DataLength != tables[i].DataLength {
				updateMap["data_length"] = tables[i].DataLength
			}
			if exist.IndexLength != tables[i].IndexLength {
				updateMap["index_length"] = tables[i].IndexLength
			}
			updateMap["update_time"] = tables[i].UpdateTime
			if len(updateMap) > 0 {
				err1 = app.DB().Model(&exist).Updates(updateMap).Error
			}
		} else {
			// 查询错误
			err = err1
			return
		}
		if err1 != nil {
			app.Log().Error("同步表失败", "name", tables[i].Name, "table_schema", tables[i].TableSchema, "instance_id", tables[i].InstanceID, "err", err1.Error())
		}
		if err2 := exist.StatHistory(recordTime); err2 != nil {
			app.Log().Error("记录表历史失败", "name", tables[i].Name, "table_schema", tables[i].TableSchema, "instance_id", tables[i].InstanceID, "err", err2.Error())
		}
	}
	err = app.DB().Where("sync_time < ? AND instance_id = ?", startTime.Add(-1*time.Hour), instance.ID).Delete(&Table{}).Error
	return
}
