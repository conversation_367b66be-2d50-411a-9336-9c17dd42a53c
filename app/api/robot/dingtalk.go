package robot

import (
	"cmdb/app"
	"cmdb/app/service/audit"
	"cmdb/app/service/robot"
	"net/http"

	"github.com/gin-gonic/gin"
)

// 钉钉消息控制器
func DingTalkChatAPI(c *gin.Context) {
	var header robot.DingChatHeader
	var body robot.DingChatBody
	var httpBody robot.HTTPBodyMarkdown
	usage := c.Param("usage")
	mode := c.<PERSON>m("mode")
	err := c.ShouldBindHeader(&header)
	if err == nil {
		err = c.ShouldBindJSON(&body)
		if err == nil {
			audit.LogOP(body.SenderNick, body.ConversationTitle, c.ClientIP(),
				"钉钉机器人", body.Text.Content)
			httpBody, err = header.DingChatServices(&body, usage, mode)
			if err != nil {
				app.Log().Error("处理钉钉机器人请求失败", "err", err, "body", body)
			}
		} else {
			app.Log().Error("处理钉钉机器人请求体失败", "err", err)
		}
	} else {
		app.Log().Error("处理钉钉机器人请求头失败", "err", err)
	}
	c.JSON(http.StatusOK, httpBody)
}
