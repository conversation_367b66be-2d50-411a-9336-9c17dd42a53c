package k8s

import (
	"cmdb/app"
	"cmdb/pkg/db"
	"context"
	"encoding/base64"
	"errors"
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/clientcmd"
)

type Service struct {
	ID                 uint           `gorm:"column:id;primaryKey;comment:主键ID" json:"id"`
	ClusterID          uint           `gorm:"column:cluster_id;comment:集群ID" json:"cluster_id"`
	Namespace          string         `gorm:"column:namespace;comment:命名空间" json:"namespace"`
	Name               string         `gorm:"column:name;comment:服务名称" json:"name"`
	PortType           string         `gorm:"column:port_type;comment:端口类型" json:"port_type"`
	ClusterIP          string         `gorm:"column:cluster_ip;comment:集群IP" json:"cluster_ip"`
	ExternalIPs        string         `gorm:"column:external_ips;comment:外部IP" json:"external_ips"`
	LoadBalanceIngress string         `gorm:"column:load_balance_ingress;comment:负载均衡ingress" json:"load_balance_ingress"`
	LoadBalanceIP      string         `gorm:"column:load_balance_ip;comment:负载均衡IP" json:"load_balance_ip"`
	Port               string         `gorm:"column:port;comment:端口" json:"port"`
	SelectorLabels     []Label        `gorm:"many2many:asset_k8s_service_selector_labels;joinForeignKey:service_id;joinReferences:label_id" json:"selector_labels"`
	Labels             []Label        `gorm:"many2many:asset_k8s_service_labels;joinForeignKey:service_id;joinReferences:label_id" json:"labels"`
	SyncTime           time.Time      `gorm:"column:sync_time;comment:同步时间" json:"sync_time"`
	DeletedAt          gorm.DeletedAt `gorm:"column:deleted_at;index" json:"-"`
}

func (Service) TableName() string {
	return "asset_k8s_service"
}

func (s Service) SetSelectorLabels(labels []Label) (err error) {
	ls := []Label{}
	for i := range labels {
		var l Label
		l, err = GenLabelKey(labels[i].LabelKey, labels[i].LabelValue)
		if err != nil {
			return
		}
		ls = append(ls, l)
	}
	err = app.DB().Model(&s).Association("SelectorLabels").Replace(ls)
	return
}

func (s Service) SetLabels(labels []Label) (err error) {
	ls := []Label{}
	for i := range labels {
		var l Label
		l, err = GenLabelKey(labels[i].LabelKey, labels[i].LabelValue)
		if err != nil {
			return
		}
		ls = append(ls, l)
	}
	err = app.DB().Model(&s).Association("Labels").Replace(ls)
	return
}

func (c Cluster) getServices() (services []Service, err error) {
	kubeconfigBytes, err := base64.StdEncoding.DecodeString(c.Token)
	if err != nil {
		return
	}
	// 使用 kubeconfig 文件创建一个 Kubernetes 客户端
	config, err := clientcmd.Load(kubeconfigBytes)
	if err != nil {
		return
	}
	client, err := clientcmd.NewDefaultClientConfig(*config, &clientcmd.ConfigOverrides{}).ClientConfig()
	if err != nil {
		return
	}
	clientset, err := kubernetes.NewForConfig(client)
	if err != nil {
		return
	}
	servicesList, err := clientset.CoreV1().Services("").List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		return
	}
	for _, service := range servicesList.Items {
		selectorLabels := []Label{}
		for k, v := range service.Spec.Selector {
			selectorLabels = append(selectorLabels, Label{LabelKey: k, LabelValue: v})
		}
		labels := []Label{}
		for k, v := range service.Labels {
			labels = append(labels, Label{LabelKey: k, LabelValue: v})
		}
		loadBalanceIngresses := []string{}
		loadBalanceIPs := []string{}
		for _, ingress := range service.Status.LoadBalancer.Ingress {
			if ingress.Hostname != "" {
				loadBalanceIngresses = append(loadBalanceIngresses, ingress.Hostname)
			}
			if ingress.IP != "" {
				loadBalanceIPs = append(loadBalanceIPs, ingress.IP)
			}
		}
		ports := []string{}
		for _, port := range service.Spec.Ports {
			ports = append(ports, fmt.Sprintf("%s %d:%d/%s", port.Name, port.Port, port.TargetPort.IntValue(), port.Protocol))
		}
		services = append(services, Service{
			ClusterID:          c.ID,
			Name:               service.Name,
			Namespace:          service.Namespace,
			ClusterIP:          service.Spec.ClusterIP,
			PortType:           string(service.Spec.Type),
			LoadBalanceIngress: strings.Join(loadBalanceIngresses, ","),
			LoadBalanceIP:      strings.Join(loadBalanceIPs, ","),
			Port:               strings.Join(ports, " , "),
			ExternalIPs:        strings.Join(service.Spec.ExternalIPs, ","),
			SelectorLabels:     selectorLabels,
			Labels:             labels,
			SyncTime:           time.Now(),
		})
	}
	return
}

func (c Cluster) SyncServices() (err error) {
	services, err := c.getServices()
	if err != nil {
		return
	}
	for _, service := range services {
		var existingService Service
		if err = app.DB().Where("cluster_id = ? AND name = ? AND namespace = ?", service.ClusterID, service.Name, service.Namespace).Take(&existingService).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				if err := app.DB().Create(&service).Error; err != nil {
					return err
				}
			}
			continue
		} else {
			updateMap := map[string]any{
				"sync_time": time.Now(),
			}
			if service.PortType != existingService.PortType {
				updateMap["port_type"] = service.PortType
			}
			if service.ClusterIP != existingService.ClusterIP {
				updateMap["cluster_ip"] = service.ClusterIP
			}
			if service.Port != existingService.Port {
				updateMap["port"] = service.Port
			}
			if service.LoadBalanceIngress != existingService.LoadBalanceIngress {
				updateMap["load_balance_ingress"] = service.LoadBalanceIngress
			}
			if service.LoadBalanceIP != existingService.LoadBalanceIP {
				updateMap["load_balance_ip"] = service.LoadBalanceIP
			}
			if service.ExternalIPs != existingService.ExternalIPs {
				updateMap["external_ips"] = service.ExternalIPs
			}
			if err = app.DB().Model(&existingService).Updates(updateMap).Error; err != nil {
				return
			}
			if err = existingService.SetSelectorLabels(service.SelectorLabels); err != nil {
				return
			}
			if err = existingService.SetLabels(service.Labels); err != nil {
				return
			}
		}
	}
	if err := app.DB().Where("sync_time < ? AND cluster_id = ? ", time.Now().Add(-10*time.Minute), c.ID).Delete(&Service{}).Error; err != nil {
		return err
	}
	return
}

func GetServices(offset, limit int, clusterID *int, namespace *string, keyword *string) (services []Service, total int64, err error) {
	dbop := app.DB().Model(&Service{})
	if clusterID != nil && *clusterID != 0 {
		dbop = dbop.Where("cluster_id = ?", *clusterID)
	}
	if namespace != nil && *namespace != "" {
		dbop = dbop.Where("namespace = ?", *namespace)
	}
	if keyword != nil && *keyword != "" {
		dbop = db.MLike(dbop, *keyword, "name", "namespace", "cluster_ip", "external_ips", "load_balance_ingress", "load_balance_ip")
	}
	err = dbop.Count(&total).Error
	if err != nil {
		return
	}
	err = dbop.Order("namespace DESC, name DESC").Offset(offset).Limit(limit).Find(&services).Error
	return
}

func (s Service) GetLabels() (labels []Label, err error) {
	err = app.DB().Model(&s).Association("Labels").Find(&labels)
	return
}

func (s Service) GetSelectorLabels() (labels []Label, err error) {
	err = app.DB().Model(&s).Association("SelectorLabels").Find(&labels)
	return
}

func (s Service) GetWorkloads() (workloads []Workload, err error) {
	labels, err := s.GetLabels()
	if err != nil {
		return
	}
	ids := []uint{}
	for i := range labels {
		ids = append(ids, labels[i].ID)
	}
	err = app.DB().Where("id IN (SELECT label_id ,count(1) as total FROM asset_k8s_workload_labels group by 1  HAVING total = ? WHERE workload_id IN (SELECT workload_id FROM asset_k8s_service_labels WHERE service_id = ? AND namespace = ? AND cluster_id = ?))", len(ids), s.ID, s.Namespace, s.ClusterID).Find(&workloads).Error
	return
}
