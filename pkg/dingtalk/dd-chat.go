package dingtalk

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"errors"
	"strconv"
	"time"
)

// 校验sing合法性
func CheckSinCode(appSecret, timestamp, singCode, usage string) error {
	// var appSecret string
	requestTimeStamp, _ := strconv.ParseInt(timestamp, 10, 64)
	nowTimeStamp := time.Now().UnixNano() / 1e6
	if err := checkParameter(requestTimeStamp, nowTimeStamp); err != nil {
		return err
	}
	mac := hmac.New(sha256.New, []byte(appSecret))
	mac.Write([]byte(timestamp + "\n" + appSecret))
	expectedMAC := mac.Sum(nil)
	singStr := base64.StdEncoding.EncodeToString(expectedMAC)
	if singStr != singCode {
		var err error = errors.New("请求的时间为未来时间")
		return err
	}
	return nil
}

// checkParameter 检验timestamp参数合法性
func checkParameter(startTime, endTime int64) error {
	// string 转为时间类型
	if startTime > endTime {
		var err error = errors.New("请求的时间为未来时间")
		return err
	} else if endTime-startTime > 3600000 {
		var err error = errors.New("请求时间过期")
		return err
	}
	return nil
}
