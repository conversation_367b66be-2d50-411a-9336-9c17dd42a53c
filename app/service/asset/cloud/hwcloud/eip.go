package hwcloud

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/asset/cloud"
	"cmdb/pkg/utils"
	"errors"
	"time"

	"github.com/huaweicloud/huaweicloud-sdk-go-v3/core/auth/basic"
	eip "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/eip/v2"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/eip/v2/model"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/eip/v2/region"
	"gorm.io/gorm"
)

func (a Account) syncEIPs() (result cloud.SyncResults, err error) {
	dcs, err := asset.GetDatacentersByCloudType(asset.HuaweiCloudType)
	if err != nil {
		return
	}
	for _, dc := range dcs {
		r, err := a.syncRegionEIPs(dc, nil)
		sr := cloud.SyncResult{
			Key: dc.Name,
		}
		if err == nil {
			sr.Result = "同步成功"
			// 回收过期主机
			err = a.expiredEIPs(time.Now().Add(-10*time.Minute), dc)
			if err != nil {
				sr.Result = "同步成功，但是回收主机失败" + err.Error()
			}
		} else {
			sr.Result = r + " " + err.Error()
		}
		result = append(result, sr)
	}
	return
}

func (a Account) syncRegionEIPs(dc asset.Datacenter, marker *string) (result string, err error) {
	secret, err := utils.Base64Decode(a.AccessSecret)
	if err != nil {
		result = "解密secret失败"
		return
	}
	auth, err := basic.NewCredentialsBuilder().WithAk(a.AccessKey).WithSk(secret).SafeBuild()
	if err != nil {
		result = "构建认证失败"
		return
	}
	regionID, err := region.SafeValueOf(dc.Code)
	if err != nil {
		result = "非法regionID："
		return
	}
	clientBuild, err := eip.EipClientBuilder().WithRegion(regionID).WithCredential(auth).SafeBuild()
	if err != nil {
		result = "构建客户端失败"
		return
	}
	client := eip.NewEipClient(clientBuild)
	var limit int32 = 2000
	response, err := client.ListPublicips(&model.ListPublicipsRequest{
		Limit:  &limit,
		Marker: marker,
	})
	if err != nil {
		return
	}
	dataLength := len(*response.Publicips)
	if dataLength > 0 {
		err = a.saveEIPs(dc, *response.Publicips)
		if err != nil {
			return
		}
		result, err = a.syncRegionEIPs(dc, (*response.Publicips)[dataLength-1].Id)
	}
	return
}

func (a Account) saveEIPs(dc asset.Datacenter, eips []model.PublicipShowResp) (err error) {
	for _, eip := range eips {
		now := time.Now()
		exist := asset.PublicIP{}
		err = app.DB().Where("sn =  ? AND cloud_type = ? AND account_id = ? AND datacenter_id = ? ", eip.Id, asset.HuaweiCloudType, a.ID, dc.ID).Take(&exist).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		var err1 error
		var createTime time.Time
		createTime, err1 = time.ParseInLocation("2006-01-02 15:04:05 ", *eip.CreateTime, time.Local)
		if err1 != nil {
			createTime = now
		}
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 不存在创建
			var err1 error
			var createTime time.Time
			createTime, err1 = time.ParseInLocation("2006-01-02 15:04:05 ", *eip.CreateTime, time.Local)
			if err1 != nil {
				createTime = now
			}
			new := asset.PublicIP{
				CloudType:    asset.HuaweiCloudType,
				DatacenterID: dc.ID,
				CreatedAt:    createTime,
				SyncTime:     &now,
				AccountID:    a.ID,
				Status:       eip.Status.Value(),
			}
			if eip.Alias != nil {
				new.Name = *eip.Alias
			}
			if eip.Id != nil {
				new.SN = *eip.Id
			}
			if eip.PublicIpAddress != nil {
				new.IP = *eip.PublicIpAddress
			}
			if eip.PrivateIpAddress != nil {
				new.PrivateIP = *eip.PrivateIpAddress
			}
			err = app.DB().Create(&new).Error
			continue
		}
		// 更新
		updateMap := map[string]any{"sync_time": now}
		if exist.Status != eip.Status.Value() {
			updateMap["status"] = eip.Status.Value()
		}
		if eip.Alias != nil && exist.Name != *eip.Alias {
			updateMap["name"] = *eip.Alias
		}
		if eip.PublicIpAddress != nil && exist.IP != *eip.PublicIpAddress {
			updateMap["ip"] = *eip.PublicIpAddress
		}
		if eip.PrivateIpAddress != nil && exist.PrivateIP != *eip.PrivateIpAddress {
			updateMap["private_ip"] = *eip.PrivateIpAddress
		}
		if exist.CreatedAt != createTime {
			updateMap["created_at"] = createTime
		}
		err = app.DB().Model(&exist).Updates(updateMap).Error
		if err != nil {
			return
		}
	}
	return
}

func (a Account) expiredEIPs(expiredTime time.Time, dc asset.Datacenter) (err error) {
	eips := []asset.PublicIP{}
	err = app.DB().Where("account_id = ? AND sync_time < ? AND datacenter_id = ?", a.ID, expiredTime, dc.ID).Find(&eips).Error
	if err != nil {
		return
	}
	for i := range eips {
		err = eips[i].Delete()
		if err != nil {
			return
		}
	}
	return
}
