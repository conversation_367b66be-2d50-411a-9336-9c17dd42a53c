package workflow

import (
	"cmdb/app"
	"cmdb/pkg/db"
)

type ServerModel struct {
	ID          uint    `gorm:"column:id;primaryKey;comment:id" json:"id"`
	SpecFamily  string  `gorm:"column:spec_family;type:varchar(255);comment:规格族" json:"spec_family"`
	Spec        string  `gorm:"column:spec;type:varchar(255);unique;comment:实例规格" json:"spec"`
	CPU         string  `gorm:"column:cpu;type:varchar(255);comment:vCPU" json:"cpu"`
	CPUModel    string  `gorm:"column:cpu_model;type:varchar(255);comment:处理器型号" json:"cpu_model"`
	Memory      string  `gorm:"column:memory;type:varchar(255);comment:内存" json:"memory"`
	GPU         string  `gorm:"column:gpu;type:varchar(255);comment:显卡" json:"gpu"`
	ProcessFreq string  `gorm:"column:process_freq;type:varchar(255);comment:处理器主频/睿频" json:"process_freq"`
	LanBand     string  `gorm:"column:lan_band;comment:内网带宽" json:"lan_band"`
	LanPackage  string  `gorm:"column:lan_package;comment:内网收发包" json:"lan_package"`
	IOPS        string  `gorm:"colunn:iops;comment:存储IOPS" json:"iops"`
	Price       float64 `gorm:"column:price;comment:价格" json:"price"`
}

func (ServerModel) TableName() string {
	return "workflow_server_models"
}

func GetServerModels(offset, limit int, spec *string, cpu, memory *int) (count int64, types []ServerModel, err error) {
	dbop := app.DB().Model(&ServerModel{})
	if spec != nil {
		dbop = db.MLike(dbop, *spec, "spec")
	}
	if cpu != nil {
		dbop = dbop.Where("cpu = ?", cpu)
	}
	if memory != nil {
		dbop = dbop.Where("memory = ?", memory)
	}
	err = dbop.Count(&count).Order("spec").Offset(offset).Limit(limit).Find(&types).Error
	return
}

func GetServerModelCPUs() (cpus []string, err error) {
	err = app.DB().Model(&ServerModel{}).Distinct("cpu").Order("cpu").Scan(&cpus).Error
	return
}

func GetServerModelMemorys() (memorys []string, err error) {
	err = app.DB().Model(&ServerModel{}).Distinct("memory").Order("memory").Scan(&memorys).Error
	return
}
