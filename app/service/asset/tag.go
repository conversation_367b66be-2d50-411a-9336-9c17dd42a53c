package asset

import (
	"cmdb/app"
	"cmdb/pkg/db"
	"errors"
	"time"

	"gorm.io/gorm"
)

// 定义一个错误，表示标签已经存在
var (
	ErrTagExist = errors.New("标签已经存在")
)

// 定义一个Tag结构体，用于存储标签信息
type Tag struct {
	ID        uint           `gorm:"column:id;primaryKey;comment:主键" json:"id"`
	Key       string         `gorm:"column:key;type:varchar(255);uniqueIndex:kv_uniq" json:"key"`
	Value     string         `gorm:"column:value;type:varchar(255);uniqueIndex:kv_uniq" json:"value"`
	Remark    string         `gorm:"column:remark;type:varchar(255)" json:"remark"`
	CreatedAt time.Time      `gorm:"column:created_at;index" json:"created_at"`
	UpdatedAt time.Time      `gorm:"column:updated_at;index" json:"updated_at"`
	Hosts     []Host         `gorm:"many2many:asset_host_tags;joinForeignKey:tag_id;joinReferences:host_id" json:"-"`
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at;index" json:"-"`
}

// 定义Tag结构体的表名
func (Tag) TableName() string {
	return "asset_tags"
}

// 定义一个TagForm结构体，用于接收标签的表单数据
type TagForm struct {
	Key    string `json:"key" binding:"required,max=255"`
	Value  string `json:"value" binding:"required,max=255"`
	Remark string `json:"remark" binding:"max=255"`
}

// 创建标签
func (form TagForm) Create() (err error) {
	// 查询数据库中是否存在相同的标签
	err = app.DB().Where("`key` = ? AND `value` = ?", form.Key, form.Value).Take(&Tag{}).Error
	// 如果不存在，则创建新的标签
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = app.DB().Create(&Tag{
			Key: form.Key, Value: form.Value, Remark: form.Remark,
		}).Error
		// 如果存在，则返回标签已经存在的错误
	} else if err == nil {
		err = ErrTagExist
	}
	return
}

// 更新标签
func (tag Tag) Update(form TagForm) (err error) {
	// 查询数据库中是否存在相同的标签
	exist := Tag{}
	err = app.DB().Where("`key` = ? AND `value` = ?", form.Key, form.Value).Take(&exist).Error
	// 如果存在且不是当前标签，则返回标签已经存在的错误
	if err == nil && tag.ID != exist.ID {
		err = ErrTagExist
		return
	}
	// 如果不存在，则更新标签
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	// 定义一个map，用于存储需要更新的字段
	updateMap := map[string]any{}
	// 如果标签的key和表单的key不同，则更新key
	if tag.Key != form.Key {
		updateMap["`key`"] = form.Key
	}
	// 如果标签的value和表单的value不同，则更新value
	if tag.Value != form.Value {
		updateMap["`value`"] = form.Value
	}
	// 如果标签的remark和表单的remark不同，则更新remark
	if tag.Remark != form.Remark {
		updateMap["remark"] = form.Remark
	}
	// 如果有需要更新的字段，则更新标签
	if len(updateMap) > 0 {
		err = app.DB().Model(&tag).Updates(updateMap).Error
		// 如果没有需要更新的字段，则返回nil
	} else {
		err = nil
	}
	return
}

// 获取所有标签的key
func GetTagKeys() (keys []string, err error) {
	// 查询数据库中所有标签的key，并去重
	err = app.DB().Model(&Tag{}).Distinct("`key`").Scan(&keys).Error
	return
}

// 根据key获取标签
func GetTagValues(key string) (tags []Tag, err error) {
	// 查询数据库中key为指定值的标签
	err = app.DB().Where("`key` = ?", key).Find(&tags).Error
	return
}

func GetTags(offset, limit int, keyword string) (count int64, tags []Tag, err error) {
	dbop := app.DB()
	if keyword != "" {
		dbop = db.MLike(dbop, keyword, "`key`", "`value`")
	}
	err = dbop.Model(&Tag{}).Count(&count).Order("`key`,`value`").Offset(offset).Limit(limit).Find(&tags).Error
	return
}

func GetTagByID(id int) (tag Tag, err error) {
	err = app.DB().Where("id = ?", id).Take(&tag).Error
	return
}

func (t Tag) Delete() (err error) {
	return app.DB().Delete(&t).Error
}

func GetAllTags() (tags []Tag, err error) {
	err = app.DB().Find(&tags).Error
	return
}

func GetTagsIDs(ids []uint) (tags []Tag, err error) {
	err = app.DB().Where("id IN (?)", ids).Find(&tags).Error
	return
}

func GenTag(key, value string) (t Tag, err error) {
	form := TagForm{
		Key: key, Value: value,
	}
	err = app.DB().Where("`key` = ? AND `value` = ?", form.Key, form.Value).Take(&t).Error
	// 如果不存在，则创建新的标签
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = app.DB().Create(&Tag{
			Key: form.Key, Value: form.Value, Remark: form.Remark,
		}).Error
		if err != nil {
			return
		}
		err = app.DB().Where("`key` = ? AND `value` = ?", form.Key, form.Value).Take(&t).Error
	}
	return
}

func (t Tag) CountHost() (count int64) {
	count = app.DB().Model(&t).Association("Hosts").Count()
	return
}

func (t Tag) GetHosts() (hosts []Host, err error) {
	err = app.DB().Model(&t).Association("Hosts").Find(&hosts)
	return
}

func GetTagByNameValue(k, v string) (tag Tag, err error) {
	err = app.DB().Where("`key` = ? AND `value` = ?", k, v).Take(&tag).Error
	return
}

// TagServers 主机批量标签
func TagServers(servers []Host, tags []Tag) (err error) {
	for i := range servers {
		err = app.DB().Model(&servers[i]).Association("Tags").Append(tags)
		if err != nil {
			return
		}
	}
	return
}
