package setting

type ELKSetting struct {
	EsUrl      string `json:"es_url"`
	EsPassword string `json:"es_password"`
	EsUser     string `json:"es_user"`
	SetSniff   bool   `json:"set_sniff"`
}

func GetELKSetting() (data ELKSetting) {
	data.EsUrl = GetSettingValue("elk", "es_url")
	data.EsPassword = GetSettingValue("elk", "es_password")
	data.EsUser = GetSettingValue("elk", "es_user")
	if GetSettingValue("elk", "set_sniff") == "true" {
		data.SetSniff = true
	}
	return
}
