package workflow

import (
	"cmdb/app"
	"cmdb/app/service/auth"
	"cmdb/pkg/db"
	"encoding/json"
	"errors"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// 定义错误类型，表示非法的审批人类型
var (
	ErrInvalidApproverType = errors.New("非法的审批人类型")
)

// 定义审批人类型
type ApproverType uint

// 定义审批人类型常量
const (
	ApproverTypeUser ApproverType = iota
	ApproverTypeLeader
	ApproverTypeGroup
)

// 实现String方法，将审批人类型转换为字符串
func (at ApproverType) String() string {
	switch at {
	case ApproverTypeUser:
		return "用户"
	case ApproverTypeLeader:
		return "上级领导"
	case ApproverTypeGroup:
		return "用户组"
	default:
		return "未知"
	}
}

// 定义审批人结构体，包含auth.User结构体
type Approver struct {
	auth.User
}

// 实现TableName方法，返回审批人表名
func (a Approver) TableName() string {
	return a.User.TableName()
}

// 根据用户ID创建审批人
func NewApprover(u auth.User) (a Approver, err error) {
	err = app.DB().Where("id = ?", u.ID).Take(&u).Error
	a = Approver{
		User: u,
	}
	return
}

// 根据ID获取审批人
func GetApproverByID(id int) (approver Approver, err error) {
	u, err := auth.GetUserByID(id)
	if err != nil {
		return
	}
	approver.User = u
	return
}

// 根据用户名获取审批人
func GetApproverByUsername(username string) (approver Approver, err error) {
	u, err := auth.GetUserByUsername(username)
	if err != nil {
		return
	}
	approver.User = u
	return
}

// 根据ID列表获取审批人列表
func GetApproversByIDs(ids ...uint) (approvers []Approver, err error) {
	us, err := auth.GetUsersByIDs(ids...)
	if err != nil {
		return
	}
	for _, u := range us {
		approvers = append(approvers, Approver{User: u})
	}
	return
}

// 根据用户组ID列表获取审批人列表
func GetApproversByGroupIDs(ids ...uint) (approvers []Approver, err error) {
	if len(ids) == 0 {
		return
	}
	gs, err := auth.GetGroupsByIDs(ids...)
	if err != nil {
		return
	}
	var us []auth.User
	for _, g := range gs {
		us, err = g.GetUsers()
		if err != nil {
			return
		}
		for _, u := range us {
			approvers = append(approvers, Approver{User: u})
		}
	}
	return
}

// 根据审批人类型和ID列表获取审批人姓名列表
func (at ApproverType) GetNames(approverIds []uint) (names []string, err error) {
	switch at {
	case ApproverTypeUser:
		names, err = auth.GetUserNamesByIDs(approverIds...)
	case ApproverTypeLeader:
		names = []string{at.String()}
	case ApproverTypeGroup:
		names, err = auth.GetGroupNamesByIDs(approverIds...)
	default:
		err = ErrInvalidApproverType
	}
	return
}

// 获取审批人订单
func (a Approver) GetApproverOrders(offset, limit int, keyword *string, extType *OrderExtType, status *OrderStatus, applicantID *int) (count int64, orders []Order, err error) {
	// 获取数据库操作对象
	dbop := app.DB()
	dbop = dbop.Where(" sn IN (?)", app.DB().Model(&Process{}).Distinct("sn").
		// 查询审批人为当前审批人的订单
		Clauses(clause.Expr{
			SQL:  "JSON_CONTAINS(approvers_id,JSON_ARRAY(?))",
			Vars: []interface{}{a.ID},
		}),
	)
	// 如果传入的extType不为空，则添加查询条件
	if extType != nil {
		dbop = dbop.Where("ext_type = ?", *extType)
	}
	// 如果传入的applicantID不为空，则添加查询条件
	if applicantID != nil {
		dbop = dbop.Where("applicant_id = ?", *applicantID)
	}
	// 如果传入的keyword不为空，则添加模糊查询条件
	if keyword != nil {
		dbop = db.MLike(dbop, *keyword, "title")
	}
	// 如果传入的status不为空，则添加查询条件
	if status != nil {
		// 如果status为ICanApproveStatus，则查询审批人为当前审批人的订单
		if *status == ICanApproveStatus {
			dbop = dbop.Where("status = ? AND process_index > 0 AND sn IN (?)", ApprovingStatus, app.DB().Model(&Process{}).Distinct("sn").
				// 查询审批人为当前审批人的订单
				Clauses(clause.Expr{
					SQL:  "JSON_CONTAINS(approvers_id,JSON_ARRAY(?))",
					Vars: []interface{}{a.ID},
				}).Where("status = ?", ApprovingStatus),
			)
		} else {
			// 否则，查询状态为传入的status的订单
			dbop = dbop.Where("status = ?", *status)
		}
	}
	// 查询订单数量，并按照创建时间降序排序，分页查询订单信息
	err = dbop.Model(&Order{}).Count(&count).
		Order("created_at DESC").Offset(offset).Limit(limit).Find(&orders).Error
	return
}

type ApproveFrom struct {
	Comment                string   `json:"comment" `
	Status                 uint     `json:"status" binding:"required"`
	IsSensitive            bool     `json:"is_sensitive"`
	CloudPlatforms         []string `json:"cloud_platforms"`
	ServiceDurationSeconds uint64   `json:"service_duration_seconds"`
}

var (
	ErrForbiddenApprove = errors.New("无权审批")
)

func (a Approver) Approve(o Order, form ApproveFrom) (err error) {
	process := Process{}
	err = app.DB().Where("sn = ? AND status = ?", o.SN, ApprovingProcessStatus).Take(&process).Error
	if err != nil {
		return
	}
	approversID := []uint{}
	err = json.Unmarshal(process.ApproversIDs, &approversID)
	if err != nil {
		return
	}
	var ok bool
	for _, uid := range approversID {
		if a.ID == uid {
			ok = true
			break
		}
	}
	if !ok {
		return ErrForbiddenApprove
	}
	processUpdateMap := map[string]any{
		"status":       form.Status,
		"comment":      form.Comment,
		"approver_id":  a.ID,
		"approve_time": time.Now(),
	}
	if form.Comment == "" {
		switch form.Status {
		case PassProcessStatus:
			processUpdateMap["comment"] = "同意"
		case RejectProcessStatus:
			processUpdateMap["comment"] = "拒绝"
		}
	}
	var isLast bool
	// if errors.Is(err, gorm.ErrRecordNotFound) {
	// 	isLast = true
	// }
	if form.Status == RejectProcessStatus {
		isLast = true
	}
	// 如果是DGC工单，且审批人为lijiaye，则将is_sensitive字段设置为true
	if o.OrderType == "DGC(数据仓库)" && a.Username == "lijiaye" && form.IsSensitive && form.Status == PassProcessStatus {
		err = app.DB().Model(&OrderPermission{}).Where("sn = ?", o.SN).Update("is_sensitive", form.IsSensitive).Error
		if err != nil {
			return
		}
		// 增加流程
		err = o.AddProcess([]Approver{a}, "大数据负责人")
		if err != nil {
			app.Log().Error("DGC工单,增加流程失败", "err", err)
		}
		leader, _ := GetApproverByUsername("chris")
		err = o.AddProcess([]Approver{leader}, "事业部负责人")
		if err != nil {
			app.Log().Error("DGC工单,增加流程失败", "err", err)
		}
		isLast = false
	}
	var np Process
	err = app.DB().Where("sn = ? AND process_index > ?", o.SN, process.ProcessIndex).Order("process_index").Take(&np).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	} else if errors.Is(err, gorm.ErrRecordNotFound) {
		isLast = true
	}
	orderUpdateMap := map[string]any{}
	if isLast {
		orderUpdateMap["process_index"] = 0
		orderUpdateMap["status"] = form.Status
		orderUpdateMap["service_duration_seconds"] = form.ServiceDurationSeconds
		if len(form.CloudPlatforms) > 0 {
			platformsBytes, _ := json.Marshal(&form.CloudPlatforms)
			orderUpdateMap["cloud_platforms"] = platformsBytes
		}
	} else {
		orderUpdateMap["process_index"] = np.ProcessIndex
	}
	// 开启事务
	dbop := app.DB().Begin()
	err = dbop.Model(&process).Updates(processUpdateMap).Error
	if err != nil {
		dbop.Rollback()
		return
	}
	if !isLast && np.ID > 0 {
		err = dbop.Model(&np).Update("status", ApprovingProcessStatus).Error
		if err != nil {
			dbop.Rollback()
			return
		}
	}
	err = dbop.Model(&o).Updates(orderUpdateMap).Error
	if err != nil {
		dbop.Rollback()
		return
	}
	err = dbop.Commit().Error
	if err != nil {
		dbop.Rollback()
		return
	}
	go o.notifyNextProcess()
	return
}
