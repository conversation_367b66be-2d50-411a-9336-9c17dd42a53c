package asset

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/asset/cloud/aliyun"
	"cmdb/app/service/asset/cloud/hwcloud"
	"cmdb/app/service/database/mysql"
	"cmdb/app/service/monitor/prometheus"
	"errors"
	"time"

	"gorm.io/gorm"
)

func Stat(statDate time.Time) (err error) {
	app.Log().Info("开始统计资源")
	err = statComputerResource(statDate)
	if err != nil {
		app.Log().Error("统计计算机资源失败", "err", err)
		return
	}
	app.Log().Info("开始统计云主机资源")
	err = statCloudComputerResources(statDate)
	if err != nil {
		app.Log().Error("统计云主机资源失败", "err", err)
		return
	}
	app.Log().Info("开始统计区域主机资源")
	err = statRegionComputerResources(statDate)
	if err != nil {
		app.Log().Error("统计区域主机资源失败", "err", err)
		return
	}
	app.Log().Info("开始统计数据资源")
	err = statDataResource(statDate)
	if err != nil {
		app.Log().Error("统计数据资源失败", "err", err)
		return
	}
	app.Log().Info("开始统计可优化资源")
	err = statOptimizable(statDate)
	if err != nil {
		app.Log().Error("统计可优化资源失败", "err", err)
		return
	}
	app.Log().Info("统计完成")
	return
}

func statComputerResource(statDate time.Time) (err error) {
	data := ComputerResource{
		StatDate:      statDate,
		HostTotal:     0,
		MemoryMBTotal: 0,
		CPUTotal:      0,
		GPUTotal:      0,
		DLICUTotal:    0,
	}
	// 主机统计
	// 修改查询条件，移除可能导致数据为0的限制条件
	err = app.DB().Model(&asset.Host{}).
		Select("count(id) AS host_total, sum(cpu_thread) AS cpu_total, sum(memory) AS memory_mb_total, sum(gpu_amount) AS gpu_total").Where("host_type > ? AND charge_type = ? AND created_at < ?", asset.PhysicalHostType, asset.PrepaidChargeType, statDate).
		Where("deleted_at IS NULL").
		Take(&data).Error
	if err != nil {
		app.Log().Error("统计计算机资源失败", "err", err)
		// 即使查询失败，也继续执行，使用默认值
	}

	// 确保数据不为0，如果为0则尝试使用更宽松的查询条件
	if data.HostTotal == 0 {
		app.Log().Warn("主机总数为0，尝试使用更宽松的查询条件")
		err = app.DB().Model(&asset.Host{}).
			Select("count(id) AS host_total, sum(cpu_thread) AS cpu_total, sum(memory) AS memory_mb_total, sum(gpu_amount) AS gpu_total").Where("host_type > ? AND charge_type = ? AND created_at < ?", asset.PhysicalHostType, asset.PrepaidChargeType, statDate).
			Where("deleted_at IS NULL").
			Take(&data).Error
		if err != nil {
			app.Log().Error("使用更宽松的查询条件统计计算机资源失败", "err", err)
		}
	}

	// 获取DLI CPU数量
	cpuTotal, err := hwcloud.GetAllDILCPUCount()
	if err != nil {
		app.Log().Error("获取DLI CPU数量失败", "err", err)
		// 即使获取失败，也继续执行，使用默认值
	} else {
		data.DLICUTotal = uint64(cpuTotal)
	}

	// 记录统计结果
	app.Log().Info("计算机资源统计结果",
		"host_total", data.HostTotal,
		"cpu_total", data.CPUTotal,
		"memory_mb_total", data.MemoryMBTotal,
		"gpu_total", data.GPUTotal,
		"dli_cu_total", data.DLICUTotal)

	// 保存或更新数据
	exist := ComputerResource{}
	err = app.DB().Where("stat_date = ? ", statDate.Format("2006-01-02")).Take(&exist).Error
	if err == nil {
		updateMap := map[string]any{}
		if exist.CPUTotal != data.CPUTotal {
			updateMap["cpu_total"] = data.CPUTotal
		}
		if exist.MemoryMBTotal != data.MemoryMBTotal {
			updateMap["memory_mb_total"] = data.MemoryMBTotal
		}
		if exist.HostTotal != data.HostTotal {
			updateMap["host_total"] = data.HostTotal
		}
		if exist.GPUTotal != data.GPUTotal {
			updateMap["gpu_total"] = data.GPUTotal
		}
		if exist.DLICUTotal != data.DLICUTotal {
			updateMap["dli_cu_total"] = data.DLICUTotal
		}
		if len(updateMap) > 0 {
			err = app.DB().Model(&exist).Updates(updateMap).Error
			if err != nil {
				app.Log().Error("更新计算机资源统计数据失败", "err", err)
			} else {
				app.Log().Info("更新计算机资源统计数据成功")
			}
		} else {
			app.Log().Info("计算机资源统计数据无变化，无需更新")
		}
	} else if errors.Is(err, gorm.ErrRecordNotFound) {
		err = app.DB().Create(&data).Error
		if err != nil {
			app.Log().Error("创建计算机资源统计数据失败", "err", err)
		} else {
			app.Log().Info("创建计算机资源统计数据成功")
		}
	} else {
		app.Log().Error("查询计算机资源统计数据失败", "err", err)
	}
	return
}

func statCloudComputerResources(statDate time.Time) (err error) {
	var cloudStats []CloudComputerResource
	// 修改查询条件，移除可能导致数据为0的限制条件
	err = app.DB().Model(&asset.Host{}).
		Select("host_type AS cloud_type, count(id) AS host_total, sum(cpu_thread) AS cpu_total, sum(memory) AS memory_mb_total, sum(gpu_amount) AS gpu_total").Where("host_type > ? AND charge_type = ? AND created_at < ?", asset.PhysicalHostType, asset.PrepaidChargeType, statDate).
		Where("deleted_at IS NULL").
		Group("host_type").
		Find(&cloudStats).Error
	if err != nil {
		app.Log().Error("统计云计算资源失败", "err", err)
		return
	}

	// 记录统计结果
	app.Log().Info("云计算资源统计结果", "count", len(cloudStats))

	for _, stat := range cloudStats {
		exist := CloudComputerResource{}
		stat.CloudTypeName = stat.CloudType.String()
		stat.StatDate = statDate

		// 记录每个云类型的统计结果
		app.Log().Info("云计算资源统计详情",
			"cloud_type", stat.CloudTypeName,
			"host_total", stat.HostTotal,
			"cpu_total", stat.CPUTotal,
			"memory_mb_total", stat.MemoryMBTotal,
			"gpu_total", stat.GPUTotal)

		err1 := app.DB().Where("stat_date = ? AND cloud_type_name = ?", statDate, stat.CloudTypeName).Take(&exist).Error
		if err1 == nil {
			updateMap := map[string]any{}
			if exist.CPUTotal != stat.CPUTotal {
				updateMap["cpu_total"] = stat.CPUTotal
			}
			if exist.MemoryMBTotal != stat.MemoryMBTotal {
				updateMap["memory_mb_total"] = stat.MemoryMBTotal
			}
			if exist.HostTotal != stat.HostTotal {
				updateMap["host_total"] = stat.HostTotal
			}
			if exist.GPUTotal != stat.GPUTotal {
				updateMap["gpu_total"] = stat.GPUTotal
			}
			if len(updateMap) > 0 {
				err1 = app.DB().Model(&exist).Updates(updateMap).Error
				if err1 != nil {
					app.Log().Error("更新云计算资源统计数据失败", "err", err1, "cloud_type", stat.CloudTypeName)
				} else {
					app.Log().Info("更新云计算资源统计数据成功", "cloud_type", stat.CloudTypeName)
				}
			} else {
				app.Log().Info("云计算资源统计数据无变化，无需更新", "cloud_type", stat.CloudTypeName)
			}
		} else if errors.Is(err1, gorm.ErrRecordNotFound) {
			err1 = app.DB().Create(&stat).Error
			if err1 != nil {
				app.Log().Error("创建云计算资源统计数据失败", "err", err1, "cloud_type", stat.CloudTypeName)
			} else {
				app.Log().Info("创建云计算资源统计数据成功", "cloud_type", stat.CloudTypeName)
			}
		} else {
			app.Log().Error("查询云计算资源统计数据失败", "err", err1, "cloud_type", stat.CloudTypeName)
		}
	}
	return
}

func statRegionComputerResources(statDate time.Time) (err error) {
	// 获取国内数据中心ID
	cnDatacenterID := []uint{}
	err = app.DB().Model(&asset.Datacenter{}).Where("code like ?", "cn-%").Select("id").Scan(&cnDatacenterID).Error
	if err != nil {
		app.Log().Error("获取国内数据中心ID失败", "err", err)
		// 如果获取失败，使用空数组继续执行
		cnDatacenterID = []uint{}
	}

	// 记录数据中心ID
	app.Log().Info("获取到国内数据中心ID", "count", len(cnDatacenterID))

	// 统计国内区域计算资源
	var cnRegionComputerResource RegionComputerResource
	var query *gorm.DB

	// 如果有国内数据中心ID，则使用它们进行查询
	if len(cnDatacenterID) > 0 {
		query = app.DB().Model(&asset.Host{}).
			Select("count(id) AS host_total, sum(cpu_thread) AS cpu_total, sum(memory) AS memory_mb_total, sum(gpu_amount) AS gpu_total").Where("host_type > ? AND charge_type = ? AND created_at < ?", asset.PhysicalHostType, asset.PrepaidChargeType, statDate).
			Where("deleted_at IS NULL AND datacenter_id in (?)", cnDatacenterID)
	} else {
		// 如果没有国内数据中心ID，则使用默认查询
		query = app.DB().Model(&asset.Host{}).
			Select("count(id) AS host_total, sum(cpu_thread) AS cpu_total, sum(memory) AS memory_mb_total, sum(gpu_amount) AS gpu_total").Where("host_type > ? AND charge_type = ? AND created_at < ?", asset.PhysicalHostType, asset.PrepaidChargeType, statDate).
			Where("deleted_at IS NULL")
	}

	err = query.Take(&cnRegionComputerResource).Error
	if err != nil {
		app.Log().Error("统计国内区域计算资源失败", "err", err)
		// 使用默认值继续执行
		cnRegionComputerResource = RegionComputerResource{
			HostTotal:     0,
			CPUTotal:      0,
			MemoryMBTotal: 0,
			GPUTotal:      0,
		}
	}

	cnRegionComputerResource.RegionName = "国内"
	cnRegionComputerResource.StatDate = statDate

	// 记录国内区域计算资源统计结果
	app.Log().Info("国内区域计算资源统计结果",
		"host_total", cnRegionComputerResource.HostTotal,
		"cpu_total", cnRegionComputerResource.CPUTotal,
		"memory_mb_total", cnRegionComputerResource.MemoryMBTotal,
		"gpu_total", cnRegionComputerResource.GPUTotal)

	// 保存或更新国内区域计算资源统计数据
	exist := RegionComputerResource{}
	err = app.DB().Where("stat_date = ? AND region_name = ?", statDate.Format("2006-01-02"), cnRegionComputerResource.RegionName).Take(&exist).Error
	if err == nil {
		updateMap := map[string]any{}
		if exist.CPUTotal != cnRegionComputerResource.CPUTotal {
			updateMap["cpu_total"] = cnRegionComputerResource.CPUTotal
		}
		if exist.MemoryMBTotal != cnRegionComputerResource.MemoryMBTotal {
			updateMap["memory_mb_total"] = cnRegionComputerResource.MemoryMBTotal
		}
		if exist.HostTotal != cnRegionComputerResource.HostTotal {
			updateMap["host_total"] = cnRegionComputerResource.HostTotal
		}
		if exist.GPUTotal != cnRegionComputerResource.GPUTotal {
			updateMap["gpu_total"] = cnRegionComputerResource.GPUTotal
		}
		if len(updateMap) > 0 {
			err = app.DB().Model(&exist).Updates(updateMap).Error
			if err != nil {
				app.Log().Error("更新国内区域计算资源统计数据失败", "err", err)
			} else {
				app.Log().Info("更新国内区域计算资源统计数据成功")
			}
		} else {
			app.Log().Info("国内区域计算资源统计数据无变化，无需更新")
		}
	} else if errors.Is(err, gorm.ErrRecordNotFound) {
		err = app.DB().Create(&cnRegionComputerResource).Error
		if err != nil {
			app.Log().Error("创建国内区域计算资源统计数据失败", "err", err)
		} else {
			app.Log().Info("创建国内区域计算资源统计数据成功")
		}
	} else {
		app.Log().Error("查询国内区域计算资源统计数据失败", "err", err)
	}

	// 统计国际区域计算资源
	var intlRegionComputerResource RegionComputerResource

	// 如果有国内数据中心ID，则使用它们进行查询
	if len(cnDatacenterID) > 0 {
		query = app.DB().Model(&asset.Host{}).
			Select("count(id) AS host_total, sum(cpu_thread) AS cpu_total, sum(memory) AS memory_mb_total, sum(gpu_amount) AS gpu_total").Where("host_type > ? AND charge_type = ? AND created_at < ?", asset.PhysicalHostType, asset.PrepaidChargeType, statDate).
			Where("deleted_at IS NULL AND datacenter_id not in (?)", cnDatacenterID)
	} else {
		// 如果没有国内数据中心ID，则使用默认查询
		query = app.DB().Model(&asset.Host{}).
			Select("count(id) AS host_total, sum(cpu_thread) AS cpu_total, sum(memory) AS memory_mb_total, sum(gpu_amount) AS gpu_total").Where("host_type > ? AND charge_type = ? AND created_at < ?", asset.PhysicalHostType, asset.PrepaidChargeType, statDate).
			Where("deleted_at IS NULL")
	}

	err = query.Take(&intlRegionComputerResource).Error
	if err != nil {
		app.Log().Error("统计国际区域计算资源失败", "err", err)
		// 使用默认值继续执行
		intlRegionComputerResource = RegionComputerResource{
			HostTotal:     0,
			CPUTotal:      0,
			MemoryMBTotal: 0,
			GPUTotal:      0,
		}
	}

	intlRegionComputerResource.RegionName = "国际"
	intlRegionComputerResource.StatDate = statDate

	// 记录国际区域计算资源统计结果
	app.Log().Info("国际区域计算资源统计结果",
		"host_total", intlRegionComputerResource.HostTotal,
		"cpu_total", intlRegionComputerResource.CPUTotal,
		"memory_mb_total", intlRegionComputerResource.MemoryMBTotal,
		"gpu_total", intlRegionComputerResource.GPUTotal)

	// 保存或更新国际区域计算资源统计数据
	exist = RegionComputerResource{}
	err = app.DB().Where("stat_date = ? AND region_name = ?", statDate.Format("2006-01-02"), intlRegionComputerResource.RegionName).Take(&exist).Error
	if err == nil {
		updateMap := map[string]any{}
		if exist.CPUTotal != intlRegionComputerResource.CPUTotal {
			updateMap["cpu_total"] = intlRegionComputerResource.CPUTotal
		}
		if exist.MemoryMBTotal != intlRegionComputerResource.MemoryMBTotal {
			updateMap["memory_mb_total"] = intlRegionComputerResource.MemoryMBTotal
		}
		if exist.HostTotal != intlRegionComputerResource.HostTotal {
			updateMap["host_total"] = intlRegionComputerResource.HostTotal
		}
		if exist.GPUTotal != intlRegionComputerResource.GPUTotal {
			updateMap["gpu_total"] = intlRegionComputerResource.GPUTotal
		}
		if len(updateMap) > 0 {
			err = app.DB().Model(&exist).Updates(updateMap).Error
			if err != nil {
				app.Log().Error("更新国际区域计算资源统计数据失败", "err", err)
			} else {
				app.Log().Info("更新国际区域计算资源统计数据成功")
			}
		} else {
			app.Log().Info("国际区域计算资源统计数据无变化，无需更新")
		}
	} else if errors.Is(err, gorm.ErrRecordNotFound) {
		err = app.DB().Create(&intlRegionComputerResource).Error
		if err != nil {
			app.Log().Error("创建国际区域计算资源统计数据失败", "err", err)
		} else {
			app.Log().Info("创建国际区域计算资源统计数据成功")
		}
	} else {
		app.Log().Error("查询国际区域计算资源统计数据失败", "err", err)
	}

	return
}

func statDataResource(statDate time.Time) (err error) {
	var data DataResource
	data.StatDate = statDate
	nasStorageTotal, err := aliyun.StatsAllAccountsNasStorage()
	if err != nil {
		app.Log().Error("统计nas存储容量时，获取阿里云NAS存储容量异常", "err", err.Error())
	}
	ossStorageTotal, err := aliyun.StatsAllAccountsOssStorage()
	if err != nil {
		app.Log().Error("统计oss存储容量时，获取阿里云OSS存储容量异常", "err", err.Error())
	}
	sfsStorageTotal, err := hwcloud.StatAllAccountSFSStorages()
	if err != nil {
		app.Log().Error("统计sfs存储容量时，获取阿里云SFS存储容量异常", "err", err.Error())
	}
	obsStorageTotal, err := hwcloud.StatAllAccountsOBSStorage()
	if err != nil {
		app.Log().Error("统计obs存储容量时，获取阿里云OBS存储容量异常", "err", err.Error())
	}
	mysqlInstanceStorage, err := mysql.StatInstanceTypeStorage(mysql.MySQLInstanceType)
	if err != nil {
		app.Log().Error("统计mysql实例存储容量时，获取mysql实例存储容量异常", "err", err.Error())
	}
	tidbStorageTotal, err := mysql.StatInstanceTypeStorage(mysql.TidbInstanceType)
	if err != nil {
		app.Log().Error("统计tidb存储容量时，获取tidb存储容量异常", "err", err.Error())
	}
	mongodbStorageTotal, err := statMongoDBStorage()
	if err != nil {
		app.Log().Error("统计mongodb存储容量时，获取mongodb存储容量异常", "err", err.Error())
	}
	starrocksStorageTotal, err := statStarrocksStorage()
	if err != nil {
		app.Log().Error("统计starrocks存储容量时，获取starrocks存储容量异常", "err", err.Error())
	}
	clickhouseStorageTotal, err := statClickhouseStorage()
	if err != nil {
		app.Log().Error("统计clickhouse存储容量时，获取clickhouse存储容量异常", "err", err.Error())
	}
	dliStorageTotal, err := statDliStorage()
	if err != nil {
		app.Log().Error("统计dli存储容量时，获取dli存储容量异常", "err", err.Error())
	}
	data.NASStorageTotal = int64(nasStorageTotal)
	data.OSSStorageTotal = int64(ossStorageTotal)
	data.SFSStorageTotal = int64(sfsStorageTotal)
	data.OBSStorageTotal = int64(obsStorageTotal)
	data.MySQLStorageTotal = int64(mysqlInstanceStorage)
	data.TidbStorageTotal = int64(tidbStorageTotal)
	data.MongodbStorageTotal = int64(mongodbStorageTotal)
	data.StarrocksStorageTotal = int64(starrocksStorageTotal)
	data.ClickhouseStorageTotal = int64(clickhouseStorageTotal)
	data.DliStorageTotal = int64(dliStorageTotal)
	exist := DataResource{}
	err = app.DB().Where("stat_date = ?", statDate.Format("2006-01-02")).Take(&exist).Error
	if err == nil {
		updateMap := map[string]any{}
		if exist.NASStorageTotal != data.NASStorageTotal {
			updateMap["nas_storage_total"] = data.NASStorageTotal
		}
		if exist.OSSStorageTotal != data.OSSStorageTotal {
			updateMap["oss_storage_total"] = data.OSSStorageTotal
		}
		if exist.SFSStorageTotal != data.SFSStorageTotal {
			updateMap["sfs_storage_total"] = data.SFSStorageTotal
		}
		if exist.OBSStorageTotal != data.OBSStorageTotal {
			updateMap["obs_storage_total"] = data.OBSStorageTotal
		}
		if exist.MySQLStorageTotal != data.MySQLStorageTotal {
			updateMap["mysql_storage_total"] = data.MySQLStorageTotal
		}
		if exist.TidbStorageTotal != data.TidbStorageTotal {
			updateMap["tidb_storage_total"] = data.TidbStorageTotal
		}
		if exist.MongodbStorageTotal != data.MongodbStorageTotal {
			updateMap["mongodb_storage_total"] = data.MongodbStorageTotal
		}
		if exist.StarrocksStorageTotal != data.StarrocksStorageTotal {
			updateMap["starrocks_storage_total"] = data.StarrocksStorageTotal
		}
		if exist.ClickhouseStorageTotal != data.ClickhouseStorageTotal {
			updateMap["clickhouse_storage_total"] = data.ClickhouseStorageTotal
		}
		if exist.DliStorageTotal != data.DliStorageTotal {
			updateMap["dli_storage_total"] = data.DliStorageTotal
		}
		if len(updateMap) > 0 {
			err = app.DB().Model(&exist).Updates(updateMap).Error
		}
	} else if errors.Is(err, gorm.ErrRecordNotFound) {
		err = app.DB().Create(&data).Error
	}
	return
}

func statOptimizable(statDate time.Time) (err error) {
	var stat OptimizableStat
	stat.StatDate = statDate

	// 使用SQL直接计算AMD服务器占比
	var amdPercentage float64
	err = app.DB().Raw(`select COALESCE((select count(*) from asset_hosts ah where ah.is_amd = true and ah.deleted_at is null and ah.host_type > ? and ah.charge_type = ?) / NULLIF((select count(*) from asset_hosts ah where ah.deleted_at is null and ah.host_type > ? and ah.charge_type = ?), 0) * 100, 0) AS amd_percentage`, asset.PhysicalHostType, asset.PrepaidChargeType, asset.PhysicalHostType, asset.PrepaidChargeType).Scan(&amdPercentage).Error
	if err != nil {
		app.Log().Error("统计可优化资源时，获取AMD服务器占比异常", "err", err)
		// 使用默认值继续执行
		amdPercentage = 0
	}
	stat.AMDPercent = amdPercentage

	// 获取主机CPU和内存使用率
	var metricStat prometheus.HostMetric
	// err = app.DB().Model(&prometheus.HostMetric{}).
	// Select("COALESCE(AVG(max_load_usage), 0) AS avg_cpu_usage, COALESCE(AVG(max_memory_usage), 0) AS avg_memory_usage").
	// Where("stat_day = ?", statDate.Format("2006-01-02")).
	// Take(&metricStat).Error
	err = app.DB().Model(&prometheus.HostMetric{}).
		Select("COALESCE(AVG(LEAST(max_load_usage, 100)), 0) AS avg_cpu_usage, COALESCE(AVG(max_memory_usage), 0) AS avg_memory_usage").
		Where("stat_day = ?", statDate.Format("2006-01-02")).
		Take(&metricStat).Error
	if err != nil {
		app.Log().Error("统计可优化资源时，获取主机CPU和内存使用率异常", "err", err)
		// 使用默认值继续执行
		metricStat.AvgCpuUsage = 0
		metricStat.AvgMemoryUsage = 0
	}
	stat.CPUUsage = metricStat.AvgCpuUsage
	stat.MemoryUsage = metricStat.AvgMemoryUsage

	// 记录统计结果
	app.Log().Info("可优化资源统计结果",
		"amd_percent", stat.AMDPercent,
		"cpu_usage", stat.CPUUsage,
		"memory_usage", stat.MemoryUsage)

	// 保存或更新数据
	exist := OptimizableStat{}
	err = app.DB().Where("stat_date = ?", statDate.Format("2006-01-02")).Take(&exist).Error
	if err == nil {
		updateMap := map[string]any{}
		if exist.AMDPercent != stat.AMDPercent {
			updateMap["amd_percent"] = stat.AMDPercent
		}
		if exist.CPUUsage != stat.CPUUsage {
			updateMap["cpu_usage"] = stat.CPUUsage
		}
		if exist.MemoryUsage != stat.MemoryUsage {
			updateMap["memory_usage"] = stat.MemoryUsage
		}
		if len(updateMap) > 0 {
			err = app.DB().Model(&exist).Updates(updateMap).Error
			if err != nil {
				app.Log().Error("更新可优化资源统计数据失败", "err", err)
			} else {
				app.Log().Info("更新可优化资源统计数据成功")
			}
		} else {
			app.Log().Info("可优化资源统计数据无变化，无需更新")
		}
	} else if errors.Is(err, gorm.ErrRecordNotFound) {
		err = app.DB().Create(&stat).Error
		if err != nil {
			app.Log().Error("创建可优化资源统计数据失败", "err", err)
		} else {
			app.Log().Info("创建可优化资源统计数据成功")
		}
	} else {
		app.Log().Error("查询可优化资源统计数据失败", "err", err)
	}
	return
}
