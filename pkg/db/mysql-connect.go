package db

import (
	"errors"
	"fmt"
	"time"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

type MysqlConnParmas struct {
	Host     string
	Port     uint
	User     string
	Password string
	DB       string
}

// NewMySQLDBConnect 连接新的数据库
func NewMySQLDBConnect(parmas MysqlConnParmas, config *gorm.Config) (op *gorm.DB, err error) {
	dbURI := fmt.Sprintf("%s:%s@(%s:%d)/%s?parseTime=True&loc=Local",
		parmas.User, parmas.Password, parmas.Host, parmas.Port, parmas.DB)
	ok := make(chan struct{})
	go func() {
		if config == nil {
			config = &gorm.Config{
				QueryFields: true,
			}
		} else {
			// 尽量避免使用 * 查询
			config.QueryFields = true
		}
		op, err = gorm.Open(mysql.New(mysql.Config{
			DriverName: "mysql",
			DSN:        dbURI,
		}), config)
		ok <- struct{}{}
	}()
	for {
		select {
		case <-ok:
			return
		case <-time.After(time.Second * 10):
			return nil, errors.New("连接超时")
		}
	}
}

func CloseDB(dbop *gorm.DB) (err error) {
	if dbop == nil {
		return nil
	}
	sqlDB, err := dbop.DB()
	if err != nil {
		return err
	}
	return sqlDB.Close()
}
