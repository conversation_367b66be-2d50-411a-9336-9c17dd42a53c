package prometheus

import (
	"cmdb/app"
	"context"
	"time"

	"github.com/prometheus/client_golang/api"
	apiv1 "github.com/prometheus/client_golang/api/prometheus/v1"
	"github.com/prometheus/common/model"
)

type PingPrometheusHost struct {
	Datasource string
	Name       string
}

var PingPrometheusHosts = []PingPrometheusHost{
	{Datasource: "http://**********:9090", Name: "阿里云ping监控"},
	{Datasource: "http://**********:9090", Name: "华为云ping监控"},
}

type PingLost struct {
	Instance string
	Source   string
	Values   []model.SamplePair
}

func (host PingPrometheusHost) GetPingLost(startTime, endTime time.Time) (losts []PingLost, err error) {
	client, err := api.NewClient(api.Config{
		Address: host.Datasource,
	})
	if err != nil {
		return
	}

	v1api := apiv1.NewAPI(client)
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	// 替换为您要查询的指标和时间范围
	result, warnings, err := v1api.QueryRange(ctx, "max_over_time(fping_lost_count[1m])> 0", apiv1.Range{
		Start: startTime, End: endTime, Step: time.Minute})
	if err != nil {
		return
	}
	if len(warnings) > 0 {
		app.Log().Warn("查询出现warn", "warnings", warnings)
	}

	switch result.Type() {
	case model.ValVector:
		// vectorVal := result.(model.Vector)
		// for _, sample := range vectorVal {
		// 	record := PingLost{
		// 		Timestamp: sample.Timestamp.Time(),
		// 		Count:     int64(sample.Value),
		// 	}
		// fmt.Printf("Value: %f, Timestamp: %s\n", sample.Value, sample.Timestamp.Time())
		// 	for labelName, labelValue := range sample.Metric {
		// 		if labelName == model.LabelName("instance") {
		// 			record.Instance = string(labelValue)
		// 		}
		// 	}
		// 	losts = append(losts, record)
		// }
		// 处理其他可能的结果类型，如矩阵、标量等
	case model.ValMatrix:
		vectorVal := result.(model.Matrix)
		for _, sample := range vectorVal {
			instance := ""
			for labelName, labelValue := range sample.Metric {
				if labelName == model.LabelName("instance") {
					instance = string(labelValue)
				}
			}
			losts = append(losts, PingLost{
				Instance: instance,
				Values:   sample.Values,
				Source:   host.Name,
			})
		}
	}
	return
}
