package asynctask

import (
	"cmdb/app"
	"cmdb/app/service/appops/gitcode"
	"cmdb/app/service/appops/nginx"
	"cmdb/app/service/asset"
	"cmdb/app/service/asset/cloud/aliyun"
	"cmdb/app/service/asset/cloud/hwcloud"
	"cmdb/app/service/asset/cloud/tencentcloud"
	"cmdb/app/service/asset/k8s"
	"cmdb/app/service/auth"
	"cmdb/app/service/database/mysql"
	"cmdb/app/service/monitor/https"
	"cmdb/app/service/monitor/n9e"
	"cmdb/app/service/monitor/prometheus"
	"cmdb/app/service/note"
	"cmdb/app/service/notice"
	statisticAsset "cmdb/app/service/statistic/asset"
	"cmdb/app/service/statistic/asset/report"
	"cmdb/app/service/task"
	"cmdb/app/service/task/async"
	"log/slog"
	"os"
	"os/signal"
	"time"

	"github.com/jinzhu/now"
)

var asyncTask = async.AsyncTask{
	StartTasks: async.StartTasks{
		{
			Name: "启动任务 重置所有异步任务状态",
			Func: func() (err error) {
				app.Log().Info("开始任务 重置所有异步任务状态")
				startTime := time.Now()
				err = task.ResetJobs()
				if err != nil {
					app.Log().Error("重置所有异步任务状态", "err", err, "cost", time.Since(startTime).String())
				}
				app.Log().Info("重置所有异步任务状态", "cost", time.Since(startTime).String())
				return
			},
		},
	},
	CronTasks: async.CronTasks{
		Tasks: []async.CronTask{
			{
				// 每天凌晨0点，统计各个prometheus和n9e实例中主机的性能指标、统计前一天的资源使用情况
				Name:     "每天凌晨0点，统计各个prometheus和n9e实例中主机的性能指标、以及前一天的资源使用情况",
				CronSpec: "0 0 0 * * *",
				Func: func() {
					startTime := time.Now()
					app.Log().Info("开始任务 统计各个prometheus和n9e实例中主机的性能指标")
					// 统计前一天的数据
					statDay := now.BeginningOfDay().AddDate(0, 0, -1)
					err := prometheus.SyncHostMetrics(statDay)
					if err != nil {
						app.Log().Error("完成任务 统计各个prometheus和n9e实例中主机的性能指标", "cost", time.Since(startTime).String(), "err", err)
					} else {
						app.Log().Info("完成任务 统计各个prometheus和n9e实例中主机的性能指标", "cost", time.Since(startTime).String())
					}
					// 统计前一天的资源
					app.Log().Info("开始任务 统计前一天的资源使用情况")
					startTime = time.Now()
					statDay = now.BeginningOfDay().AddDate(0, 0, -1)
					err = statisticAsset.Stat(statDay)
					if err != nil {
						app.Log().Error("完成任务 统计前一天的资源使用情况失败", "cost", time.Since(startTime).String(), "err", err)
					} else {
						app.Log().Info("完成任务 统计前一天的资源使用情况", "cost", time.Since(startTime).String())
					}
				},
			},
			{
				// 每天凌晨2点，生成每日报告
				Name:     "每天凌晨2点，生成每日报告",
				CronSpec: "0 0 2 * * *",
				Func: func() {
					app.Log().Info("开始任务 生成每日报告")
					startTime := time.Now()
					err := report.GenDailyReport(now.BeginningOfDay().AddDate(0, 0, -1))
					if err != nil {
						app.Log().Error("完成任务 生成每日报告失败", "cost", time.Since(startTime).String(), "err", err)
						return
					}
					app.Log().Info("完成任务 生成每日报告", "cost", time.Since(startTime).String())
				},
			},
			// 每个月的1号 同步资产统计
			{
				Name:     "每月1号凌晨03点 同步资产统计",
				CronSpec: "0 0 3 1 * *",
				Func: func() {
					app.Log().Info("开始任务 同步资产统计")
					startTime := time.Now()
					err := report.GenLastMonthReport()
					if err != nil {
						app.Log().Error("完成任务 同步资产统计失败", "cost", time.Since(startTime).String(), "err", err)
						return
					}
					app.Log().Info("完成任务 同步资产统计", "cost", time.Since(startTime).String())
				},
			},
			{
				Name:     "每10分钟 同步kubernete集群信息",
				CronSpec: "00 */10 * * * *",
				Func: func() {
					app.Log().Info("开始任务 同步kubernete集群信息成功")
					startTime := time.Now()
					err := k8s.SyncAllClusters()
					if err != nil {
						app.Log().Error("完成任务 同步kubernete集群信息失败", "cost", time.Since(startTime).String(), "err", err)
						return
					}
					app.Log().Info("完成任务 同步kubernete集群信息成功", "cost", time.Since(startTime).String())
				},
			},
			{
				Name:     "每12小时 采集mysql表大小信息",
				CronSpec: "00 00 */12 * * *",
				Func: func() {
					app.Log().Info("开始任务 采集mysql表大小信息成功")
					startTime := time.Now()
					err := mysql.CollectInstancesTableLength()
					if err != nil {
						app.Log().Error("完成任务 采集mysql表大小信息失败", "cost", time.Since(startTime).String(), "err", err)
						return
					}
					app.Log().Info("完成任务 采集mysql表大小信息成功", "cost", time.Since(startTime).String())
				},
			},
			{
				Name:     "每1个小时 同步MySQL项目库信息",
				CronSpec: "00 00 * * * *",
				Func: func() {
					app.Log().Info("开始任务 同步MySQL项目库信息成功")
					startTime := time.Now()
					err := mysql.SyncProjectsDBs()
					if err != nil {
						app.Log().Error("完成任务 MySQL项目库信息失败", "cost", time.Since(startTime).String(), "err", err)
						return
					}
					app.Log().Info("完成任务 MySQL项目库信息成功", "cost", time.Since(startTime).String())
				},
			},
			{
				// 同步所有账户的上月账单
				Name:     "每个月2号、9号、13号的06点同步上个月账单",
				CronSpec: "0 00 06 2,9,13 * ? *",
				Func: func() {
					app.Log().Info("开始任务 同步上个月账单信息成功")
					startTime := time.Now()
					err := aliyun.SyncAllAccountsLastMonthMonthlyBills()
					if err != nil {
						app.Log().Error("完成任务 同步阿里云上个月账单信息失败", "cost", time.Since(startTime).String(), "err", err)
						return
					}
					app.Log().Info("完成任务 同步阿里云上个月账单信息成功", "cost", time.Since(startTime).String())
					err = hwcloud.SyncAllAccountsLastMonthMonthlyBills()
					if err != nil {
						app.Log().Error("完成任务 同步华为云上个月账单信息失败", "cost", time.Since(startTime).String(), "err", err)
						return
					}
					app.Log().Info("完成任务 同步华为云上个月账单信息成功", "cost", time.Since(startTime).String())
					err = tencentcloud.SyncAllAccountsLastMonthMonthlyBills()
					if err != nil {
						app.Log().Error("完成任务 同步腾讯云上个月账单信息失败", "cost", time.Since(startTime).String(), "err", err)
						return
					}
					app.Log().Info("完成任务 同步腾讯云上个月账单信息成功", "cost", time.Since(startTime).String())
				},
			},
			{
				Name:     "每天11点30分同步nginx后端配置",
				CronSpec: "00 30 11 * * *",
				Func: func() {
					app.Log().Info("开始任务 同步nginx后端配置成功")
					startTime := time.Now()
					err := nginx.Sync()
					if err != nil {
						app.Log().Error("完成任务 同步nginx后端配置失败", "cost", time.Since(startTime).String(), "err", err)
						return
					}
					app.Log().Info("完成任务 同步nginx后端配置成功", "cost", time.Since(startTime).String())
				},
			},
			{
				Name:     "每天06点30分同步OA用户",
				CronSpec: "00 30 06 * * *",
				Func: func() {
					app.Log().Info("开始任务 同步OA用户成功")
					startTime := time.Now()
					err := auth.UpdateOAUsers()
					if err != nil {
						app.Log().Error("完成任务 同步OA用户失败", "cost", time.Since(startTime).String(), "err", err)
						return
					}
					app.Log().Info("完成任务 同步OA用户成功", "cost", time.Since(startTime).String())
				},
			},
			{
				Name:     "每天06点30分同步账单信息",
				CronSpec: "00 30 06 * * *",
				Func: func() {
					app.Log().Info("开始任务 同步账单信息成功")
					startTime := time.Now()
					err := aliyun.SyncAllAccountsMonthlyBills()
					if err != nil {
						app.Log().Error("完成任务 同步阿里云账单信息失败", "cost", time.Since(startTime).String(), "err", err)
						return
					}
					app.Log().Info("完成任务 同步阿里云账单信息成功", "cost", time.Since(startTime).String())
					err = hwcloud.SyncAllAccountsMonthlyBills()
					if err != nil {
						app.Log().Error("完成任务 同步华为云账单信息失败", "cost", time.Since(startTime).String(), "err", err)
						return
					}
					app.Log().Info("完成任务 同步华为云账单信息成功", "cost", time.Since(startTime).String())
					err = tencentcloud.SyncAllAccountsMonthlyBills()
					if err != nil {
						app.Log().Error("完成任务 同步腾讯云账单信息失败", "cost", time.Since(startTime).String(), "err", err)
						return
					}
					app.Log().Info("完成任务 同步腾讯云账单信息成功", "cost", time.Since(startTime).String())
				},
			},
			{
				Name:     "每天8点10分 发送值班通知",
				CronSpec: "00 10 8 * * *",
				Func: func() {
					app.Log().Info("开始任务 发送值班通知")
					startTime := time.Now()
					err := note.NotifyDuty()
					if err != nil {
						app.Log().Error("完成任务 发送值班通知失败", "cost", time.Since(startTime).String(), "err", err)
						return
					}
					app.Log().Info("完成任务 发送值班通知成功", "cost", time.Since(startTime).String())
				},
			},
			{
				Name:     "每天00点10分 自动排班",
				CronSpec: "00 10 0 * * *",
				Func: func() {
					app.Log().Info("开始任务 自动排班")
					startTime := time.Now()
					err := note.CronAutoSchedule()
					if err != nil {
						app.Log().Error("完成任务 自动排班失败", "cost", time.Since(startTime).String(), "err", err)
						return
					}
					app.Log().Info("完成任务 自动排班成功", "cost", time.Since(startTime).String())
				},
			},
			{
				Name:     "每天1点10分 同步DDoS域名带宽",
				CronSpec: "00 10 1 * * *",
				Func: func() {
					app.Log().Info("开始任务 同步DDoS域名带宽")
					startTime := time.Now()
					err := aliyun.SyncAllAccountsDDoSDomainBps()
					if err != nil {
						app.Log().Error("完成任务 同步DDoS域名带宽失败", "cost", time.Since(startTime).String(), "err", err)
						return
					}
					app.Log().Info("完成任务 同步DDoS域名带宽成功", "cost", time.Since(startTime).String())
				},
			},
			{
				Name:     "每天 14点50分自动 导入域名监控",
				CronSpec: "00 50 14 * * *",
				Func: func() {
					app.Log().Info("开始任务 导入域名监控")
					startTime := time.Now()
					err := https.AutoUpdateAndCheckDomainHttps()
					if err != nil {
						app.Log().Error("完成任务 导入域名监控失败", "cost", time.Since(startTime).String(), "err", err)
						return
					}
					app.Log().Info("完成任务 导入域名监控成功", "cost", time.Since(startTime).String())
				},
			},
			{
				Name:     "MySQL 今日备份检查",
				CronSpec: "20 00 12,15 * * *",
				Func: func() {
					app.Log().Info("开始任务 检查MySQL备份")
					err := mysql.CheckTodyBackup()
					if err != nil {
						app.Log().Error("完成任务 检查MySQL备份失败", "err", err)
						return
					}
					app.Log().Info("完成任务 检查MySQL备份成功")
				},
			},
			// 每小时同步gitlab信息
			{
				Name:     "每小时 同步gitlab信息",
				CronSpec: "00 00 * * * *",
				Func: func() {
					app.Log().Info("开始任务 同步gitlab信息")
					startTime := time.Now()
					err := gitcode.SyncAllGitlab()
					if err != nil {
						app.Log().Error("完成任务 同步gitlab信息失败", "cost", time.Since(startTime).String(), "err", err)
						return
					}
					app.Log().Info("完成任务 同步gitlab信息成功", "cost", time.Since(startTime).String())
				},
			},
			// 每个小时过期n9e监控信息
			{
				Name:     "每小时 过期n9e监控信息",
				CronSpec: "00 00 * * * *",
				Func: func() {
					app.Log().Info("开始任务 过期n9e监控信息")
					startTime := time.Now()
					expiredTime := time.Now().Add(-2 * time.Hour)
					err := n9e.ExpiredN9eMonitorToHost(expiredTime)
					if err != nil {
						app.Log().Error("完成任务 过期n9e监控信息失败", "cost", time.Since(startTime).String(), "err", err)
						return
					}
					app.Log().Info("完成任务 过期n9e监控信息成功", "cost", time.Since(startTime).String())
				},
			},
		},
	},
	IntervalTasks: async.IntervalTasks{
		{
			Name:     "间隔10秒 执行消息发送",
			Interval: time.Second * 10,
			Func: func() (err error) {
				err = notice.SendMessage()
				return
			},
		},
		{
			Name:     "间隔10秒 执行主机信息到ansible inventory同步",
			Interval: time.Second * 10,
			Func: func() (err error) {
				err = asset.DumpHostToAnsibleInventory()
				if err != nil {
					app.Log().Error("完成任务 主机信息到ansible inventory同步失败", "err", err)
					return
				}
				app.Log().Info("完成任务 主机信息到ansible inventory同步成功")
				return
			},
		},
		{
			Name:     "间隔5秒 执行批量任务",
			Interval: time.Second * 5,
			Func: func() (err error) {
				err = task.AsyncExecTask()
				return
			},
		}, {
			Name:     "间隔5分钟 执行阿里云资产同步",
			Interval: time.Minute * 5,
			Func: func() (err error) {
				err = aliyun.SyncAllAccounts()
				return
			},
		}, {
			Name:     "间隔5分钟 执行华为云资产同步",
			Interval: time.Minute * 5,
			Func: func() (err error) {
				err = hwcloud.SyncAllAccounts()
				return
			},
		}, {
			Name:     "间隔5分钟 执行腾讯云资产同步",
			Interval: time.Minute * 5,
			Func: func() (err error) {
				err = tencentcloud.SyncAllAccounts()
				return
			},
		},
	},
}

func Run(config string) {
	err := app.NewApp(config)
	if err != nil {
		slog.Error("app 初始化失败", "err", err)
		return
	}
	err = app.ConnectDB()
	if err != nil {
		slog.Error("数据库连接失败", "err", err)
		return
	}
	defer func() {
		startTime := time.Now()
		app.Log().Info("开始 关闭 数据库连接")
		dbop, err1 := app.DB().DB()
		if err1 == nil {
			err = dbop.Close()
			if err != nil {
				app.Log().Error("关闭 数据库 连接失败", slog.Any("err", err))
			}
		}
		app.Log().Info("完成 关闭 数据库连接", "cost", time.Since(startTime).String())
	}()
	asyncTask.Run()
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, os.Interrupt)
	<-quit
	asyncTask.Stop()
}
