package auth

import (
	"cmdb/app"
	"cmdb/app/service/audit"
	"cmdb/app/service/auth"
	"encoding/json"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

type LoginForm struct {
	Username string `json:"username" binding:"required,max=255"`
	Password string `json:"password" binding:"required,max=255"`
}

type UserData struct {
	Avatar       string   `json:"avatar"`
	Username     string   `json:"username"`
	Nickname     string   `json:"nickname"`
	Roles        []string `json:"roles"`
	Permissions  []string `json:"permissions,omitempty"`
	AccessToken  string   `json:"accessToken"`
	RefreshToken string   `json:"refreshToken"`
	Expires      string   `json:"expires"`
}

type LoginResponseData struct {
	Success bool     `json:"success"`
	Data    UserData `json:"data,omitempty"`
	Msg     string   `json:"msg,omitempty"`
}

// 用户名密码登录
func Login(c *gin.Context) {
	var form LoginForm
	err := c.ShouldBind(&form)
	if err != nil {
		app.Log().Debug("登陆时验证表单失败", "err", err)
		c.JSON(http.StatusOK, LoginResponseData{
			Success: false,
			Msg:     "用户名或密码不能为空",
		})
		return
	}
	if form.Username == "" || form.Password == "" {
		c.JSON(http.StatusOK, LoginResponseData{
			Success: false,
			Msg:     "用户名或密码不能为空",
		})
		return
	}
	u, err := auth.Login(form.Username, form.Password)
	if err != nil {
		app.Log().Debug("登录失败", "err", err)
		clientIP := c.GetHeader("X-Forwarded-For")
		if clientIP == "" {
			clientIP = c.ClientIP()
		}
		audit.LogLogin(u.Name, u.Email, clientIP, c.Request.UserAgent(), "登陆失败", "密码登陆")
		c.JSON(http.StatusOK, LoginResponseData{
			Success: false,
			Msg:     "用户名或密码不正确",
		})
		return
	}
	token, err := u.GenerateToken(time.Hour * 7 * 24)
	if err != nil {
		app.Log().Error("登陆，生成token失败", "err", err)
		c.JSON(http.StatusOK, LoginResponseData{
			Success: false,
			Msg:     "登陆失败",
		})
		return
	}
	refreshToken, err := u.GenerateToken(time.Hour * 8 * 24)
	if err != nil {
		app.Log().Error("登陆，生成refresh token失败", "err", err)
		c.JSON(http.StatusOK, LoginResponseData{
			Success: false,
			Msg:     "登陆失败",
		})
		return
	}
	roles := []string{"common"}
	if u.IsAdmin {
		roles = []string{"admin"}
	}
	go u.UpdateLoginTime()
	clientIP := c.GetHeader("X-Forwarded-For")
	if clientIP == "" {
		clientIP = c.ClientIP()
	}
	audit.LogLogin(u.Name, u.Email, clientIP, c.Request.UserAgent(), "登陆成功", "密码登陆")
	c.JSON(http.StatusOK, LoginResponseData{
		Success: true,
		Data: UserData{
			AccessToken:  token,
			Username:     u.Username,
			Nickname:     u.Name,
			RefreshToken: refreshToken,
			Expires:      time.Now().Add(time.Hour * 24 * 7).Format("2006/01/02 15:04:05"),
			Roles:        roles,
		},
	})
}

type RefreshForm struct {
	RefreshToken string `json:"refreshToken"`
}

type RefreshTokenData struct {
	Roles []string `json:"roles"`
	/** `token` */
	AccessToken string `json:"accessToken"`
	/** 用于调用刷新`accessToken`的接口时所需的`token` */
	RefreshToken string `json:"refreshToken"`
	/** `accessToken`的过期时间（格式'xxxx/xx/xx xx:xx:xx'） */
	Expires string `json:"expires"`
}
type RefreshTokenResult struct {
	Success bool             `json:"success"`
	Data    RefreshTokenData `json:"data,omitempty"`
	Msg     string           `json:"msg,omitempty"`
}

func RefreshToken(c *gin.Context) {
	var u auth.User
	var err error
	refreshForm := RefreshForm{}
	err = c.ShouldBind(&refreshForm)
	if err != nil {
		if c.ClientIP() == "**********" || c.ClientIP() == "***********" {
			headerSub := c.GetHeader("X-Pomerium-Claim-Sub")
			headerToken := c.GetHeader("X-Pomerium-Jwt-Assertion")
			if headerToken != "" {
				sn := ""
				if len(strings.Split(headerSub, "/")) > 1 {
					sn = strings.Split(headerSub, "/")[1]
				}
				u, err = auth.OAGatewayAuth(sn)
				if err != nil {
					app.Log().Error("oa-gateway-user", "获取用户信息失败", err.Error())
					app.FailedResponseMsg(c, "获取用户信息失败")
					return
				}
			}
		}
	} else {
		u, err = auth.ParseToken(refreshForm.RefreshToken)
		if err != nil {
			app.Log().Error("登陆，解析token失败", "err", err)
			if c.ClientIP() == "**********" || c.ClientIP() == "***********" {
				headerSub := c.GetHeader("X-Pomerium-Claim-Sub")
				headerToken := c.GetHeader("X-Pomerium-Jwt-Assertion")
				if headerToken != "" {
					sn := ""
					if len(strings.Split(headerSub, "/")) > 1 {
						sn = strings.Split(headerSub, "/")[1]
					}
					u, err = auth.OAGatewayAuth(sn)
					if err != nil {
						app.Log().Error("oa-gateway-user", "获取用户信息失败", err.Error())
						app.FailedResponseMsg(c, "获取用户信息失败")
						return
					}
				}
			}
		}
	}

	newUser, err := auth.GetUserByID(int(u.ID))
	if err != nil {
		app.Log().Error("获取用户失败", "err", err)
		app.FailedResponseMsg(c, "获取用户失败")
		return
	}
	token, err := newUser.GenerateToken(time.Hour * 7 * 24)
	if err != nil {
		app.Log().Error("登陆，生成token失败", "err", err)
		app.FailedResponseMsg(c, "登陆失败")
		return
	}
	refreshToken, err := newUser.GenerateToken(time.Hour * 8 * 24)
	if err != nil {
		app.Log().Error("登陆，生成refresh token失败", "err", err)
		app.FailedResponseMsg(c, "登陆失败")
		return
	}
	roles := []string{}
	json.Unmarshal(newUser.Roles, &roles)
	if newUser.IsAdmin {
		roles = append(roles, "admin")
	}
	roles = append(roles, "common")
	go newUser.UpdateLoginTime()
	app.SuccessResponseData(c, RefreshTokenData{
		Roles:        roles,
		AccessToken:  token,
		RefreshToken: refreshToken,
		Expires:      time.Now().Add(time.Hour * 24 * 7).Format("2006/01/02 15:04:05"),
	})
}
