package workflow

import (
	"bytes"
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/auth"
	"encoding/json"
	"errors"
	"time"

	"gorm.io/datatypes"
)

type OrderServer struct {
	ID        uint           `gorm:"column:id;primarykey;comment:id" json:"id"`
	SN        string         `gorm:"type:varchar(128);unique;column:sn;comment:工单单号" json:"sn"`
	Content   string         `gorm:"type:text;column:content;comment:工单内容" json:"content"`
	Business  string         `gorm:"column:business;varchar(255);comment:业务"  json:"business" `
	ModelList datatypes.JSON `gorm:"column:model_list;comment:配置清单" json:"model_list"`
	ServerIDs datatypes.JSON `gorm:"column:server_ids;comment:关联服务器id" json:"server_ids"`
	Amount    float64        `gorm:"type:decimal(16,3);column:amount;comment:金额" json:"amount"`
}

func (OrderServer) TableName() string {
	return "workflow_order_servers"
}

type OrderServerDetail struct {
	Order     `gorm:"-"`
	Content   string         `gorm:"type:text;column:content;comment:工单内容" json:"content"`
	Business  string         `gorm:"column:business;varchar(255);comment:业务"  json:"business" `
	ModelList datatypes.JSON `gorm:"column:model_list;comment:配置清单" json:"model_list"`
	ServerIDs datatypes.JSON `gorm:"column:server_ids;comment:关联服务器id" json:"server_ids"`
	Amount    float64        `gorm:"type:decimal(16,3);column:amount;comment:金额" json:"amount"`
}

// 设置表名
func (OrderServerDetail) TableName() string {
	return "workflow_order_servers"
}

func GetOrderServerDetail(o Order) (detail OrderServerDetail, err error) {
	detail = OrderServerDetail{}
	err = app.DB().Where("sn = ?", o.SN).Take(&detail).Error
	if err != nil {
		return
	}
	detail.Order = o
	return
}

type OrderServerForm struct {
	OrderType      string    `json:"order_type" binding:"required,max=255"`
	Content        string    `json:"content"`
	ModelList      any       `json:"model_list"`
	Business       string    `json:"business" binding:"required,max=255" `
	Amount         float64   `json:"amount"`
	Orders         []string  `json:"orders"`
	Env            asset.ENV `json:"env"`
	Critical       bool      `json:"critical"`
	CloudPlatforms []string  `json:"cloud_platforms"`
}

func (form OrderServerForm) check() (err error) {
	return
}

func (form OrderServerForm) GenProcesses(sn string, a Applicant, admin *auth.User) (processes []Process, err error) {
	var flow Flow
	err = app.DB().Where("order_type = ?", ServerOrderType).Take(&flow).Error
	if err != nil {
		return
	}
	flow.Nodes, err = flow.GetFlowNodes()
	if err != nil {
		return
	}
	processes, err = flow.genProcesses(sn, a, admin)
	return
}

func (form OrderServerForm) Create(a Applicant, flow Flow, admin *auth.User) (err error) {
	err = form.check()
	if err != nil {
		return
	}
	sn := genSN("server")
	processes, err := flow.genProcesses(sn, a, admin)
	if err != nil || len(processes) == 0 {
		return
	}
	status := ApprovingStatus
	var index uint
	if len(processes) == 0 {
		err = errors.New("生成流程失败")
		return
	} else if len(processes) <= 1 {
		// 如果流程没有配置就直接结束流程
		status = CompletedStatus
	} else {
		// 如果有流程就获取下一个节点的索引
		index = processes[1].ProcessIndex
		processes[1].Status = ApprovingProcessStatus
	}
	planTime := time.Now().AddDate(0, 0, 1)
	snsByte, _ := json.Marshal(&form.Orders)
	platformsByte, _ := json.Marshal(&form.CloudPlatforms)
	order := Order{
		Title:          " 申请 " + form.OrderType + " - " + a.Name,
		OrderType:      form.OrderType,
		Env:            form.Env,
		ExtType:        ServerExtType,
		SN:             sn,
		Critical:       form.Critical,
		ApplicantID:    a.ID,
		Status:         status,
		PlanTime:       &planTime,
		ProcessIndex:   index,
		Orders:         snsByte,
		CloudPlatforms: platformsByte,
	}
	modeListByte, _ := json.Marshal(&form.ModelList)
	extOrder := OrderServer{
		SN:        sn,
		Amount:    form.Amount,
		ModelList: modeListByte,
		Content:   form.Content,
		Business:  form.Business,
	}
	dbop := app.DB().Begin()
	err = dbop.Create(&order).Error
	if err != nil {
		dbop.Rollback()
		return
	}
	err = dbop.Create(&extOrder).Error
	if err != nil {
		dbop.Rollback()
		return
	}
	err = dbop.Create(&processes).Error
	if err != nil {
		dbop.Rollback()
		return
	}
	err = dbop.Commit().Error
	if err != nil {
		dbop.Rollback()
	}
	go order.notifyNextProcess()
	return
}

func (form OrderServerForm) Update(o Order) (err error) {
	extOrder := OrderServer{}
	err = app.DB().Where("sn = ?", o.SN).Take(&extOrder).Error
	if err != nil {
		return
	}
	updateMap := map[string]any{}
	if form.Content != extOrder.Content {
		updateMap["content"] = form.Content
	}
	if form.Amount != extOrder.Amount {
		updateMap["amount"] = form.Amount
	}
	if form.Business != extOrder.Business {
		updateMap["business"] = form.Business
	}
	byteModelList, _ := json.Marshal(&form.ModelList)
	if !bytes.Equal(byteModelList, extOrder.ModelList) {
		updateMap["model_list"] = byteModelList
	}
	if len(updateMap) > 0 {
		err = app.DB().Model(&extOrder).Updates(updateMap).Error
	}
	return
}
