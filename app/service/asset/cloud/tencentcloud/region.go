package tencentcloud

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/asset/cloud"
	"errors"

	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	cvm "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/cvm/v20170312"
	"gorm.io/gorm"
)

func (a Account) syncRegions() (results cloud.SyncResults, err error) {
	credential := common.NewCredential(
		"SecretId",
		"SecretKey",
	)
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = "cvm.tencentcloudapi.com"
	client, err := cvm.NewClient(credential, "", cpf)
	if err != nil {
		return
	}
	request := cvm.NewDescribeRegionsRequest()
	response, err := client.DescribeRegions(request)
	if err != nil {
		return
	}
	regions := response.Response.RegionSet
	for i := range regions {
		exist := asset.Datacenter{}
		err1 := app.DB().Where("cloud_type = ? AND code = ?", asset.TencentCloudType, regions[i].Region).Take(&exist).Error
		if errors.Is(err1, gorm.ErrRecordNotFound) {
			err1 = app.DB().Create(&asset.Datacenter{
				Name:      *regions[i].RegionName,
				Code:      *regions[i].Region,
				CloudType: asset.TencentCloudType,
			}).Error
			if err1 != nil {
				results = append(results, cloud.SyncResult{Key: *regions[i].RegionName, Result: "创建失败：" + err1.Error()})
			}
		} else if err1 == nil {
			updateMap := map[string]any{}
			if exist.Name != *regions[i].RegionName {
				updateMap["name"] = regions[i].RegionName
			}
			if len(updateMap) > 0 {
				err1 = app.DB().Model(&exist).Updates(updateMap).Error
				if err1 != nil {
					results = append(results, cloud.SyncResult{Key: *regions[i].RegionName, Result: "更新失败：" + err1.Error()})
				}
			}
		} else {
			err = err1

		}
		results = append(results, cloud.SyncResult{Key: *regions[i].RegionName, Result: "同步成功"})
	}
	return
}
