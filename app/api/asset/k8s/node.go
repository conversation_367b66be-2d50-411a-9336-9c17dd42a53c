package k8s

import (
	"cmdb/app"
	"cmdb/app/service/asset/k8s"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

type NodeRow struct {
	k8s.Node
	ClusterName   string `json:"cluster_name"`
	RequestCPU    int64  `json:"request_cpu"`
	RequestMemory int64  `json:"request_memory"`
	LimitCPU      int64  `json:"limit_cpu"`
	LimitMemory   int64  `json:"limit_memory"`
}

func GetNodes(c *gin.Context) {
	// 获取查询参数
	var keyword *string
	if val := strings.TrimSpace(c.Query("keyword")); val != "" {
		keyword = &val
	}
	// 获取分页参数
	page, _ := strconv.Atoi(c.<PERSON>("page", "1"))
	limit, _ := strconv.Atoi(c.<PERSON>("limit", "10"))
	// 计算偏移量
	offset := (page - 1) * limit
	clusterIDStr := c.Query("cluster_id")
	var clusterID *int
	if val, err1 := strconv.Atoi(clusterIDStr); err1 == nil && val > 0 {
		clusterID = &val
	}
	count, data, err := k8s.GetNodes(offset, limit, clusterID, keyword)
	if err != nil {
		app.Log().Error("获取数据失败", "err", err)
		app.FailedResponseMsg(c, "获取数据失败")
		return
	}
	newData := []NodeRow{}
	if len(data) > 0 {
		tmpStore := map[uint]string{}
		for _, v := range data {
			v.Labels, _ = v.GetLabels()
			resource, _ := v.GetNodeResources()
			if val, ok := tmpStore[v.ClusterID]; ok {
				newData = append(newData, NodeRow{Node: v, ClusterName: val, RequestCPU: resource.RequestCPU, RequestMemory: resource.RequestMemory, LimitCPU: resource.LimitCPU, LimitMemory: resource.LimitMemory})
			} else {
				if name, err := k8s.GetClusterName(v.ClusterID); err == nil {
					tmpStore[v.ClusterID] = name
					newData = append(newData, NodeRow{Node: v, ClusterName: name, RequestCPU: resource.RequestCPU, RequestMemory: resource.RequestMemory, LimitCPU: resource.LimitCPU, LimitMemory: resource.LimitMemory})
				} else {
					newData = append(newData, NodeRow{Node: v, ClusterName: "", RequestCPU: resource.RequestCPU, RequestMemory: resource.RequestMemory, LimitCPU: resource.LimitCPU, LimitMemory: resource.LimitMemory})
				}
			}
		}
	}
	app.SuccessResponseDataCount(c, newData, count)
}
