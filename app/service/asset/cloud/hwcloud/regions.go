package hwcloud

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/asset/cloud"
	"cmdb/pkg/utils"
	"errors"

	"github.com/huaweicloud/huaweicloud-sdk-go-v3/core/auth/global"
	rms "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/rms/v1"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/rms/v1/model"
	region "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/rms/v1/region"
	"gorm.io/gorm"
)

func (a Account) syncRegions() (results cloud.SyncResults, err error) {
	ak := a.AccessKey
	sk, err := utils.Base64Decode(a.AccessSecret)
	if err != nil {
		return
	}
	auth, err := global.NewCredentialsBuilder().
		WithAk(ak).
		WithSk(sk).
		SafeBuild()
	if err != nil {
		return
	}
	r, err := region.SafeValueOf("cn-north-4")
	if err != nil {
		return
	}
	c, err := rms.RmsClientBuilder().WithRegion(r).WithCredential(auth).SafeBuild()
	if err != nil {
		return
	}
	client := rms.NewRmsClient(c)
	request := &model.ListRegionsRequest{}
	response, err := client.ListRegions(request)
	if err != nil {
		return
	}
	regions := *response.Value
	for i := range regions {
		exist := asset.Datacenter{}
		err1 := app.DB().Where("cloud_type = ? AND code = ?", asset.HuaweiCloudType, regions[i].RegionId).Take(&exist).Error
		if errors.Is(err1, gorm.ErrRecordNotFound) {
			err1 = app.DB().Create(&asset.Datacenter{
				Name:      *regions[i].DisplayName,
				Code:      *regions[i].RegionId,
				CloudType: asset.HuaweiCloudType,
			}).Error
			if err1 != nil {
				results = append(results, cloud.SyncResult{Key: *regions[i].DisplayName, Result: "创建失败：" + err1.Error()})
			}
			continue
		}
		if err1 != nil {
			results = append(results, cloud.SyncResult{Key: *regions[i].DisplayName, Result: "查询失败：" + err1.Error()})
			continue
		}
		updateMap := map[string]any{}
		if exist.Name != *regions[i].DisplayName {
			updateMap["name"] = regions[i].DisplayName
		}
		if len(updateMap) > 0 {
			err1 = app.DB().Model(&exist).Updates(updateMap).Error
			if err1 != nil {
				results = append(results, cloud.SyncResult{Key: *regions[i].DisplayName, Result: "更新失败：" + err1.Error()})
				continue
			}
		}
		results = append(results, cloud.SyncResult{Key: *regions[i].DisplayName, Result: "同步成功"})
	}
	return
}
