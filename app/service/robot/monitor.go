package robot

import (
	"cmdb/app/service/monitor"
	"cmdb/app/service/monitor/n9e"
	"fmt"
	"strconv"
	"strings"
)

func switchMonitor(usage string, params ...string) (msg HTTPBodyMarkdown) {
	msg.MsgType = "markdown"
	msg.Markdown.Title = "监控管理"
	msg.Markdown.Text = usage
	if len(params) < 2 {
		return
	}
	op, ob := params[0], params[1]
	hosts := strings.Split(ob, ",")
	results := []string{}
	switch op {
	case "on", "开启":
		r1 := n9e.RemoteSwitchCatgraf("on", hosts...)
		results = append(results, r1)
		err1 := monitor.BatSwitchHostPingmonitor(hosts, true)
		if err1 != nil {
			results = append(results, "开启ping监控失败"+err1.Error())
		} else {
			results = append(results, "开启ping监控成功")
		}
	case "off", "关闭":
		r2 := n9e.RemoteSwitchCatgraf("off", hosts...)
		results = append(results, r2)
		r1, err := n9e.DeleteN9ETargets(hosts...)
		if err != nil {
			results = append(results, "删除夜莺主机失败"+err.Error())
		} else {
			results = append(results, "删除夜莺主机成功"+r1)
		}
		err1 := monitor.BatSwitchHostPingmonitor(hosts, false)
		if err1 != nil {
			results = append(results, "关闭ping监控失败"+err1.Error())
		} else {
			results = append(results, "关闭ping监控成功")
		}

	case "抑制":
		duarationHour := 2
		if len(params) > 2 {
			duarationHourStr := params[2]
			var err error
			duarationHour, err = strconv.Atoi(duarationHourStr)
			if err != nil {
				results = append(results, "抑制监控存在非法参数:"+duarationHourStr+"，使用默认2小时")
				duarationHour = 2
			} else {
				results = append(results, "抑制监控持续时间为"+duarationHourStr+"小时")
			}
		} else {
			results = append(results, "抑制监控持续时间为2小时")
		}
		r1, err := n9e.N9eAlertMetu(duarationHour, "instance", hosts...)
		if err != nil {
			results = append(results, "抑制instance监控失败"+err.Error()+r1)
		} else {
			results = append(results, "抑制instance监控成功"+r1)
		}
		r1, err = n9e.N9eAlertMetu(duarationHour, "ident", hosts...)
		if err != nil {
			results = append(results, "抑制ident监控失败"+err.Error()+r1)
		} else {
			results = append(results, "抑制ident监控成功"+r1)
		}
	}
	msg.Markdown.Text = fmt.Sprintf("## 监控 %s %s\n", op, ob) + strings.Join(results, "\n>")
	return
}
