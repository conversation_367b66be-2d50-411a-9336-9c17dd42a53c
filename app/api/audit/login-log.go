package audit

import (
	"cmdb/app"
	"cmdb/app/service/audit"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// 分页获取登录日志
func GetLoginLogs(c *gin.Context) {
	user := strings.TrimSpace(c.Query("user"))
	ip := strings.TrimSpace(c.Query("ip"))
	page, _ := strconv.Atoi(c.<PERSON>("page", "1"))
	limit, _ := strconv.Atoi(c<PERSON>("limit", "10"))
	var startTime, endTime *time.Time
	startTimeStr := strings.TrimSpace(c.Query("start_time"))
	if value, err1 := time.Parse(time.RFC3339, startTimeStr); err1 == nil {
		startTime = &value
	}
	endTimeStr := strings.TrimSpace(c.Query("end_time"))
	if value, err2 := time.Parse(time.RFC3339, endTimeStr); err2 == nil {
		endTime = &value
	}
	offset := (page - 1) * limit
	count, data, err := audit.GetLoginLogs(offset, limit, ip, user, startTime, endTime)
	if err != nil {
		app.Log().Error("获取登录记录数据失败 ", "err", err)
		c.JSON(http.StatusOK, gin.H{"code": 1, "msg": "获取失败"})
		return
	}
	c.JSON(http.StatusOK, gin.H{"success": true, "count": count, "data": data})
}
