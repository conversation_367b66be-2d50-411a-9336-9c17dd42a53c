package assets

import (
	"cmdb/app"
	"cmdb/app/service/assets"
	"cmdb/app/service/audit"
	"strconv"

	"github.com/gin-gonic/gin"
)

// 获取节点列表
func GetAssetSystemNodes(c *gin.Context) {
	id, err := strconv.Atoi(c.<PERSON>m("id"))
	if err != nil || id == 0 {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	system, err := assets.GetAssetSystemByID(id)
	if err != nil {
		app.Log().Error("获取节点列表失败", "err", err)
		app.FailedResponseMsg(c, "获取节点列表失败")
		return
	}
	nodes, err := system.GetNodes()
	if err != nil {
		app.Log().Error("获取节点列表失败", "err", err)
		app.FailedResponseMsg(c, "获取节点列表失败")
		return
	}
	app.SuccessResponseData(c, nodes)
}

// 创建节点
func CreateAssetNode(c *gin.Context) {
	var form assets.AssetNodeForm
	if err := c.ShouldBindJSON(&form); err != nil {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	err := form.Create()
	if err != nil {
		app.Log().Error("创建节点失败", "err", err)
		app.FailedResponseMsg(c, "创建节点失败")
		return
	}
	audit.LogAPIOP(c, "资产管理", "创建节点："+form.Name)
	app.SuccessResponseMsg(c, "创建成功")
}

// 更新节点
func UpdateAssetNode(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	node, err := assets.GetAssetNodeByID(uint(id))
	if err != nil {
		app.Log().Error("更新节点失败", "err", err)
		app.FailedResponseMsg(c, "更新节点失败")
		return
	}
	var form assets.AssetNodeForm
	if err := c.ShouldBindJSON(&form); err != nil {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	err = node.Update(form)
	if err != nil {
		app.Log().Error("更新节点失败", "err", err)
		app.FailedResponseMsg(c, "更新节点失败")
		return
	}
	audit.LogAPIOP(c, "资产管理", "更新节点："+form.Name)
	app.SuccessResponseMsg(c, "更新成功")
}

// 删除节点
func DeleteAssetNode(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	node, err := assets.GetAssetNodeByID(uint(id))
	if err != nil {
		app.Log().Error("删除节点失败", "err", err)
		app.FailedResponseMsg(c, "删除节点失败")
		return
	}
	err = node.Delete()
	if err != nil {
		app.Log().Error("删除节点失败", "err", err)
		app.FailedResponseMsg(c, "删除节点失败")
		return
	}
	app.SuccessResponseMsg(c, "删除成功")
}

// 获取节点关联的资产
func GetAssetNodeAssets(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	node, err := assets.GetAssetNodeByID(uint(id))
	if err != nil {
		app.Log().Error("获取节点关联的资产失败", "err", err)
		app.FailedResponseMsg(c, "获取节点关联的资产失败")
		return
	}
	assets, err := node.GetNodeAssets()
	if err != nil {
		app.Log().Error("获取节点关联的资产失败", "err", err)
		app.FailedResponseMsg(c, "获取节点关联的资产失败")
		return
	}
	app.SuccessResponseData(c, assets)
}
