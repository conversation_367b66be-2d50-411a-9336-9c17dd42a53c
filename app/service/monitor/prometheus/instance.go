package prometheus

import (
	"cmdb/app"
	"cmdb/pkg/db"
	"errors"
	"io"
	"net/http"
	"time"

	"gorm.io/gorm"
)

var (
	ErrInstanceExist = errors.New("实例已经存在")
)

type InstanceType string

const (
	InstanceTypeFping      InstanceType = "fping"
	InstanceTypeKubernetes InstanceType = "kubernetes"
	InstanceTypeTiDB       InstanceType = "tidb"
	InstanceTypeN9e        InstanceType = "n9e"
	InstanceTypeOther      InstanceType = "other"
)

type Instance struct {
	ID           uint           `gorm:"column:id;primaryKey;comment:id" json:"id"`
	Name         string         `gorm:"column:name;index;comment:实例名称" json:"name"`
	InstanceType InstanceType   `gorm:"column:instance_type;index;comment:实例类型" json:"instance_type"`
	Datasource   string         `gorm:"column:datasource;comment:实例地址" json:"datasource"`
	CreatedAt    time.Time      `gorm:"column:created_at;index;comment:创建时间" json:"created_at"`
	UpdatedAt    time.Time      `gorm:"column:updated_at;comment:更新时间" json:"updated_at"`
	DeletedAt    gorm.DeletedAt `gorm:"column:deleted_at;index;comment:软删除" json:"-"`
}

func (Instance) TableName() string {
	return "monitor_prometheus_instances"
}

type InstanceForm struct {
	Name         string       `json:"name" binding:"required,max=255"`
	InstanceType InstanceType `json:"instance_type" binding:"required,max=125"`
	Datasource   string       `json:"datasource" binding:"required,max=255"`
}

func (form InstanceForm) Create() (err error) {
	// 判断是否存在
	err = app.DB().Where("name = ?", form.Name).Take(&Instance{}).Error
	if err == nil {
		return ErrInstanceExist
	} else if errors.Is(err, gorm.ErrRecordNotFound) {
		err = app.DB().Create(&Instance{
			Name:         form.Name,
			Datasource:   form.Datasource,
			InstanceType: form.InstanceType,
		}).Error
	}
	return
}

func GetInstance(id int) (instance Instance, err error) {
	err = app.DB().Where("id = ?", id).Take(&instance).Error
	return
}

func GetInstances(offset, limit int, instanceType *InstanceType, keyword *string) (count int64, instances []Instance, err error) {
	dbop := app.DB()
	if keyword != nil {
		dbop = db.MLike(dbop, *keyword, "name", "datasource")
	}
	if instanceType != nil {
		dbop = dbop.Where("instance_type = ?", *instanceType)
	}
	err = dbop.Model(&Instance{}).Count(&count).
		Order("created_at DESC").Offset(offset).Limit(limit).Find(&instances).Error
	return
}

func (instance Instance) Update(form InstanceForm) (err error) {
	if instance.Name != form.Name {
		exist := Instance{}
		err = app.DB().Where("name = ?", form.Name).Take(&exist).Error
		if err == nil && exist.ID != instance.ID {
			err = ErrInstanceExist
			return
		} else if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
	}
	updateMap := map[string]any{}
	if instance.Name != form.Name {
		updateMap["name"] = form.Name
	}
	if instance.Datasource != form.Datasource {
		updateMap["datasource"] = form.Datasource
	}
	if instance.InstanceType != form.InstanceType {
		updateMap["instance_type"] = form.InstanceType
	}
	if len(updateMap) > 0 {
		err = app.DB().Model(&instance).Updates(updateMap).Error
	}
	return
}

func (instance Instance) Delete() (err error) {
	err = app.DB().Delete(&instance).Error
	return
}

func (instance Instance) TestConnect() (result string, responseCode int, err error) {
	client := http.Client{
		Timeout: 10 * time.Second,
	}
	request, err := http.NewRequest("GET", instance.Datasource, nil)
	if err != nil {
		return
	}
	response, err := client.Do(request)
	if err != nil {
		return
	}
	responseCode = response.StatusCode
	body, err := io.ReadAll(response.Body)
	if err != nil {
		return
	}
	result = string(body)
	return
}

func GetAllInstances() (instances []Instance, err error) {
	err = app.DB().Find(&instances).Error
	return
}
