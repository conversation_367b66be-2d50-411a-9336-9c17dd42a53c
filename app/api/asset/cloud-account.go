package asset

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/asset/cloud/aliyun"
	"cmdb/app/service/asset/cloud/hwcloud"
	"cmdb/app/service/asset/cloud/tencentcloud"
	"cmdb/app/service/audit"
	"errors"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

type CloudAccountRow struct {
	asset.CloudAccount
	HostTotal int64 `json:"host_total"`
}

// 获取云账户列表
func GetCloudAccounts(c *gin.Context) {
	// 定义参数结构体
	var params asset.GetAccountsParmas
	// 从查询参数中获取关键字
	params.Keyword = c.Query("keyword")
	// 从查询参数中获取云类型，默认为0
	params.CloudType, _ = strconv.Atoi(c.DefaultQuery("cloud_type", "0"))
	// 从查询参数中获取页码，默认为1
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	// 从查询参数中获取每页显示数量，默认为10
	params.Limit, _ = strconv.Atoi(c.<PERSON>fault<PERSON>uery("limit", "10"))
	// 计算偏移量
	params.Offset = (page - 1) * params.Limit
	// 调用获取云账户列表的函数
	count, data, err := asset.GetCloudAccounts(params)
	// 如果出现错误，记录日志并返回错误信息
	if err != nil {
		app.Log().Error("查询云账户列表时，查询数据失败", "err", err)
		app.FailedResponseMsg(c, "获取数据失败")
		return
	}
	newData := make([]CloudAccountRow, len(data))
	if len(data) > 0 {
		for i, v := range data {
			count, _ := v.CountHost()
			newData[i] = CloudAccountRow{
				CloudAccount: v,
				HostTotal:    count,
			}
		}
	}
	// 返回查询结果
	app.SuccessResponseDataCount(c, newData, count)
}

// 添加云账户
func AddCloudAccount(c *gin.Context) {
	// 定义表单结构体
	var form asset.CloudAccountForm
	// 绑定表单数据
	err := c.ShouldBind(&form)
	// 如果绑定失败，记录日志并返回错误信息
	if err != nil {
		app.Log().Debug("添加云账户时，验证表单失败", "err", err)
		app.FailedResponseMsg(c, "验证表单失败")
		return
	}
	// 调用添加云账户的函数
	err = form.Create()
	// 如果出现错误，记录日志并返回错误信息
	if err != nil {
		msg := "添加失败"
		// 如果错误是云账户已存在，则返回错误信息
		if errors.Is(err, asset.ErrCloudAccountExist) {
			msg = err.Error()
		} else {
			app.Log().Error("添加云账户失败", "err", err)
		}
		app.FailedResponseMsg(c, msg)
		return
	}
	// 记录操作日志
	audit.LogAPIOP(c, "资产管理", "添加云账户："+form.Name)
	// 返回成功信息
	app.SuccessResponseMsg(c, "添加成功")
}

// 更新云账户
func UpdateCloudAccount(c *gin.Context) {
	// 从路径参数中获取id
	id, err := strconv.Atoi(c.Param("id"))
	// 如果id非法，记录日志并返回错误信息
	if err != nil {
		app.Log().Debug("更新云账户时，存在非法参数id：" + c.Param("id"))
		app.FailedResponseMsg(c, "非法参数")
		return
	}
	// 定义表单结构体
	var form asset.CloudAccountForm
	// 绑定表单数据
	err = c.ShouldBind(&form)
	// 如果绑定失败，记录日志并返回错误信息
	if err != nil {
		app.Log().Debug("更新云账户时，验证表单失败", "err", err)
		app.FailedResponseMsg(c, "验证表单失败")
		return
	}
	// 调用获取云账户的函数
	account, err := asset.GetCloudAccountByID(id)
	// 如果出现错误，记录日志并返回错误信息
	if err != nil {
		app.Log().Error("更新云账户时，获取账户失败", "err", err)
		app.FailedResponseMsg(c, "获取账户失败")
		return
	}
	// 调用更新云账户的函数
	err = account.Update(form)
	// 如果出现错误，记录日志并返回错误信息
	if err != nil {
		msg := "更新失败"
		// 如果错误是云账户已存在，则返回错误信息
		if errors.Is(err, asset.ErrCloudAccountExist) {
			msg = err.Error()
		} else {
			app.Log().Error("更新云账户失败", "err", err)
		}
		app.FailedResponseMsg(c, msg)
		return
	}
	// 记录操作日志
	audit.LogAPIOP(c, "资产管理", "更新云账户："+form.Name)
	// 返回成功信息
	app.SuccessResponseMsg(c, "更新成功")
}

// 删除云账户
func DeleteCloudAccount(c *gin.Context) {
	// 将参数id转换为int类型
	id, err := strconv.Atoi(c.Param("id"))
	// 如果转换失败，记录日志并返回错误信息
	if err != nil {
		app.Log().Debug("删除云账户时，存在非法参数id：" + c.Param("id"))
		app.FailedResponseMsg(c, "存在非法参数id")
		return
	}
	// 根据id获取云账户
	account, err := asset.GetCloudAccountByID(id)
	// 如果获取失败，记录日志并返回错误信息
	if err != nil {
		app.Log().Error("删除云账户时，获取账户失败", "err", err)
		app.FailedResponseMsg(c, "获取账户失败")
		return
	}
	// 删除云账户
	err = account.Delete()
	// 如果删除失败，记录日志并返回错误信息
	if err != nil {
		app.Log().Error("删除云账户失败", "err", err)
		app.FailedResponseMsg(c, "删除失败")
		return
	}
	// 记录操作日志
	audit.LogAPIOP(c, "资产管理", "删除云账户："+account.Name)
	// 返回成功信息
	app.SuccessResponseMsg(c, "删除成功")
}

func SyncCloudAccountAsset(c *gin.Context) {
	// 将参数id转换为int类型
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		// 记录日志
		app.Log().Debug("同步云账户时，存在非法参数id：" + c.Param("id"))
		// 返回错误信息
		app.FailedResponseMsg(c, "存在非法参数id")
		return
	}
	assetType := c.Param("asset")
	// 根据id获取云账户
	account, err := asset.GetCloudAccountByID(id)
	if err != nil {
		// 记录日志
		app.Log().Error("同步云账户时，获取账户失败", "err", err)
		// 返回错误信息
		app.FailedResponseMsg(c, "获取账户失败")
		return
	}
	// 创建一个done通道
	done := make(chan struct{}, 1)
	// 启动一个goroutine进行同步
	go func() {
		// 记录日志
		audit.LogAPIOP(c, "资产管理", "提交同步 "+assetType+"，账户："+account.Name)
		// 根据云账户类型进行同步
		switch account.CloudType {
		case asset.AliyunCloudType:
			err = aliyun.CloudAccount(account).Sync(assetType)
			if err != nil {
				app.Log().Error("同步 "+assetType+" 失败", "err", err)
			}
		case asset.HuaweiCloudType:
			err = hwcloud.CloudAccount(account).Sync(assetType)
			if err != nil {
				app.Log().Error("同步 "+assetType+" 失败", "err", err)
			}
		case asset.TencentCloudType:
			err = tencentcloud.CloudAccount(account).Sync(assetType)
			if err != nil {
				app.Log().Error("同步 "+assetType+" 失败", "err", err)
			}
		default:
			// 返回错误信息
			err = errors.New("不支持同步")
		}
		// 发送完成信号
		done <- struct{}{}
	}()
	ticker := time.NewTicker(500 * time.Millisecond)
	select {
	case <-done:
		if err != nil {
			app.FailedResponseMsg(c, err.Error())
		} else {
			app.SuccessResponseMsg(c, "同步成功")
		}
		break
	case <-ticker.C:
		app.SuccessResponseMsg(c, "任务提交成功")
		break
	}
}

func GetAllCloudAccounts(c *gin.Context) {
	data, err := asset.GetAllCloudAccounts()
	if err != nil {
		app.Log().Error("获取云账户失败", "err", err)
		app.FailedResponseMsg(c, err.Error())
		return
	}
	app.SuccessResponseData(c, data)
}
func SyncMonthlyBills(c *gin.Context) {
	// 从查询参数中获取账单周期，如果没有则默认为当前月份
	var billCycle string
	if val := strings.TrimSpace(c.Query("bill_cycle")); val != "" {
		billCycle = val
	} else {
		billCycle = time.Now().Format("2006-01")
	}

	// 从请求参数中获取云账户ID，并转换为整数
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		// 记录错误日志并返回错误信息
		app.Log().Debug("参数异常", "err", err)
		app.FailedResponseMsg(c, "参数异常")
		return
	}

	// 根据ID获取云账户信息
	account, err := asset.GetCloudAccountByID(id)
	if err != nil {
		// 记录错误日志并返回错误信息
		app.Log().Error("获取云账户异常", "err", err)
		app.FailedResponseMsg(c, "获取云账户异常")
		return
	}

	// 创建一个done通道，用于接收异步任务完成的信号
	done := make(chan struct{}, 1)
	go func() {
		// 记录API操作日志
		audit.LogAPIOP(c, "资产管理", "提交同步账单，账户："+account.Name)
		// 根据云账户类型调用相应的同步账单方法
		switch account.CloudType {
		case asset.AliyunCloudType:
			err = aliyun.CloudAccount(account).SyncMonthlyBills(billCycle)
			if err != nil {
				app.Log().Error("同步账单失败", "err", err)
			}
		case asset.HuaweiCloudType:
			err = hwcloud.CloudAccount(account).SyncMonthlyBills(billCycle)
			if err != nil {
				app.Log().Error("同步账单失败", "err", err)
			}
		case asset.TencentCloudType:
			err = tencentcloud.CloudAccount(account).SyncMonthlyBills(billCycle)
			if err != nil {
				app.Log().Error("同步账单失败", "err", err)
			}
		}
		// 发送完成信号
		done <- struct{}{}
	}()

	// 创建一个定时器，用于检查异步任务是否超时
	ticker := time.NewTicker(500 * time.Millisecond)
	select {
	case <-done:
		// 如果任务完成，根据是否有错误返回相应的响应信息
		if err != nil {
			app.FailedResponseMsg(c, err.Error())
		} else {
			app.SuccessResponseMsg(c, "同步成功")
		}
		break
	case <-ticker.C:
		// 如果超时，返回任务提交成功的信息
		app.SuccessResponseMsg(c, "任务提交成功")
		break
	}
}
