package k8s

import (
	"time"

	"gorm.io/gorm"
)

type Container struct {
	ID            int            `gorm:"column:id;primary_key;auto_increment;comment:ID" json:"id"`
	ClusterID     uint           `gorm:"column:cluster_id;index;comment:集群ID" json:"cluster_id"`
	Name          string         `gorm:"column:name;index;comment:容器名称" json:"name"`
	Pod           string         `gorm:"column:pod;index;comment:Pod" json:"pod"`
	Namespace     string         `gorm:"column:namespace;index;comment:命名空间" json:"namespace"`
	Image         string         `gorm:"column:image;comment:镜像" json:"image"`
	Status        string         `gorm:"column:status;comment:状态" json:"status"`
	Command       string         `gorm:"column:command;comment:命令" json:"command"`
	Args          string         `gorm:"column:args;comment:参数" json:"args"`
	WorkingDir    string         `gorm:"column:working_dir;comment:工作目录" json:"working_dir"`
	RestartPolicy string         `gorm:"column:restart_policy;comment:重启策略" json:"restart_policy"`
	Envs          string         `gorm:"column:envs;comment:环境变量" json:"envs"`
	RequestCPU    int64          `gorm:"column:request_cpu;comment:请求CPU" json:"request_cpu"`
	RequestMemory int64          `gorm:"column:request_memory;comment:请求内存" json:"request_memory"`
	LimitCPU      int64          `gorm:"column:limit_cpu;comment:限制CPU" json:"limit_cpu"`
	LimitMemory   int64          `gorm:"column:limit_memory;comment:限制内存" json:"limit_memory"`
	SyncTime      time.Time      `gorm:"column:sync_time;comment:同步时间" json:"sync_time"`
	DeletedAt     gorm.DeletedAt `gorm:"column:deleted_at;comment:删除时间" json:"deleted_at"`
}

func (Container) TableName() string {
	return "asset_k8s_containers"
}
