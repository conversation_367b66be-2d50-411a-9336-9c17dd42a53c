package db

import (
	"context"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
)

// RedisConnParams Redis 连接参数
type RedisConnParams struct {
	Host     string
	Port     int
	User     string
	Password string
	DB       int // 数据库编号
	PoolSize int // 连接池大小
	MinIdle  int // 最小空闲连接数
	MaxConn  int // 最大连接数
}

// RedisClient Redis 客户端接口
type RedisClient interface {
	redis.Cmdable
	Close() error
}

// NewRedisClient 创建新的 Redis 连接
func NewRedisClient(params RedisConnParams) (RedisClient, error) {
	addr := fmt.Sprintf("%s:%d", params.Host, params.Port)

	// 设置默认值
	if params.PoolSize == 0 {
		params.PoolSize = 10
	}
	if params.MinIdle == 0 {
		params.MinIdle = 5
	}
	if params.MaxConn == 0 {
		params.MaxConn = 100
	}

	client := redis.NewClient(&redis.Options{
		Addr:         addr,
		Username:     params.User,
		Password:     params.Password,
		DB:           params.DB,
		PoolSize:     params.PoolSize,
		MinIdleConns: params.MinIdle,
		MaxRetries:   3,
		OnConnect: func(ctx context.Context, cn *redis.Conn) error {
			// 连接建立时的回调
			return nil
		},
	})

	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 测试连接
	if err := client.Ping(ctx).Err(); err != nil {
		client.Close()
		return nil, fmt.Errorf("连接 Redis 失败 [%s]: %v", addr, err)
	}

	return client, nil
}

// CloseRedisClient 安全关闭 Redis 客户端
func CloseRedisClient(client RedisClient) error {
	if client == nil {
		return nil
	}
	return client.Close()
}

// IsRedisError 检查是否是 Redis 错误
func IsRedisError(err error) bool {
	if err == nil {
		return false
	}
	return redis.Nil == err || redis.ErrClosed == err
}
