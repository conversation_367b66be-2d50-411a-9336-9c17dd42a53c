package auth

import (
	"cmdb/app"
	"cmdb/app/service/auth"
	"encoding/json"
	"log/slog"
	"net/http"

	"github.com/gin-gonic/gin"
)

// GetCurrentUser 获取当前用户
func GetCurrentUser(c *gin.Context) {
	u, err := auth.ParseToken(app.GetToken(c))
	if err != nil {
		app.Log().Error("获取当前用户失败", slog.Any("err", err))
		c.<PERSON>(http.StatusOK, gin.H{"code": 1, "msg": "获取当前用户失败"})
		return
	}
	roles := []string{}
	json.Unmarshal(u.Roles, &roles)
	if u.IsAdmin {
		roles = append(roles, "admin")
	}
	roles = append(roles, "common")
	app.SuccessResponseData(c, gin.H{
		"userId":   u.ID,
		"userName": u.Username,
		"name":     u.Name,
		"email":    u.<PERSON><PERSON>,
		"roles":    roles,
		"buttons":  []string{}},
	)
}

func GetAllUsers(c *gin.Context) {
	filterDisabled := c.<PERSON>fault<PERSON>("filter_disabled", "true")
	data, err := auth.GetAllUsers(filterDisabled == "true")
	if err != nil {
		app.Log().Error("获取所有用户失败", "err", err)
		app.FailedResponseMsg(c, "获取所有用户失败")
		return
	}
	app.SuccessResponseData(c, data)
}
