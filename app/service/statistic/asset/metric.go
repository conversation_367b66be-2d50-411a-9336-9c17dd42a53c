package asset

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"errors"
	"fmt"
	"time"

	"github.com/jinzhu/now"
	"gorm.io/gorm"
)

// MetricItem 指标数据项
type MetricItem struct {
	Date  string  `json:"date"`  // 日期，格式为 YYYY-MM-DD
	Value float64 `json:"value"` // 指标值
}

// ResourceType 资源类型
type ResourceType string

const (
	ResourceTypeComputer    ResourceType = "computer"    // 计算资源总计
	ResourceTypeCloud       ResourceType = "cloud"       // 云计算资源
	ResourceTypeRegion      ResourceType = "region"      // 区域计算资源
	ResourceTypeData        ResourceType = "data"        // 数据资源
	ResourceTypeNetwork     ResourceType = "network"     // 网络资源
	ResourceTypeOptimizable ResourceType = "optimizable" // 可优化资源
)

// MetricType 指标类型
type MetricType string

const (
	// 计算资源指标类型
	MetricTypeHostTotal   MetricType = "host_total"      // 主机总数
	MetricTypeCPUTotal    MetricType = "cpu_total"       // CPU总数
	MetricTypeMemoryTotal MetricType = "memory_mb_total" // 内存总数(MB)
	MetricTypeGPUTotal    MetricType = "gpu_total"       // GPU总数
	MetricTypeDLICUTotal  MetricType = "dli_cu_total"    // DLI CU总数

	// 数据资源指标类型
	MetricTypeMySQLStorage      MetricType = "mysql_storage_total"      // MySQL存储总量
	MetricTypeOSSStorage        MetricType = "oss_storage_total"        // OSS存储总量
	MetricTypeOBSStorage        MetricType = "obs_storage_total"        // OBS存储总量
	MetricTypeSFSStorage        MetricType = "sfs_storage_total"        // SFS存储总量
	MetricTypeNASStorage        MetricType = "nas_storage_total"        // NAS存储总量
	MetricTypeMongoDBStorage    MetricType = "mongodb_storage_total"    // MongoDB存储总量
	MetricTypeStarrocksStorage  MetricType = "starrocks_storage_total"  // Starrocks存储总量
	MetricTypeClickhouseStorage MetricType = "clickhouse_storage_total" // Clickhouse存储总量
	MetricTypeTiDBStorage       MetricType = "tidb_storage_total"       // TiDB存储总量
	MetricTypeDLIStorage        MetricType = "dli_storage_total"        // DLI存储总量

	// 网络资源指标类型
	MetricTypeInBps  MetricType = "in_bps"  // 入带宽
	MetricTypeOutBps MetricType = "out_bps" // 出带宽

	// 可优化资源指标类型
	MetricTypeAMDPercent  MetricType = "amd_percent"  // AMD百分比
	MetricTypeCPUUsage    MetricType = "cpu_usage"    // CPU使用率
	MetricTypeMemoryUsage MetricType = "memory_usage" // 内存使用率
)

// GetResourceMetrics 获取资源指标历史趋势
// 参数：
// - resourceType: 资源类型，可选值为 "computer", "cloud", "region", "data", "network", "optimizable"
// - metricType: 指标类型
//   - 计算资源: "host_total", "cpu_total", "memory_mb_total", "gpu_total", "dli_cu_total"
//   - 数据资源: "mysql_storage_total", "oss_storage_total", "obs_storage_total", "sfs_storage_total", "nas_storage_total",
//     "mongodb_storage_total", "starrocks_storage_total", "clickhouse_storage_total", "tidb_storage_total", "dli_storage_total"
//   - 网络资源: "in_bps", "out_bps"
//   - 可优化资源: "amd_percent", "cpu_usage", "memory_usage"
//
// - name: 资源名称，当resourceType为"cloud"时表示云类型名称，当resourceType为"region"时表示区域名称，当resourceType为"network"时表示域名
// - startDate: 开始日期，格式为 YYYY-MM-DD
// - endDate: 结束日期，格式为 YYYY-MM-DD
// 返回值：
// - []MetricItem: 指标数据列表
// - error: 错误信息
func GetResourceMetrics(resourceType ResourceType, metricType MetricType, name string, startDate, endDate string) ([]MetricItem, error) {
	// 验证参数
	if resourceType != ResourceTypeComputer && resourceType != ResourceTypeCloud &&
		resourceType != ResourceTypeRegion && resourceType != ResourceTypeData &&
		resourceType != ResourceTypeNetwork && resourceType != ResourceTypeOptimizable {
		return nil, errors.New("不支持的资源类型")
	}

	// 验证指标类型
	validMetricType := false
	switch resourceType {
	case ResourceTypeComputer, ResourceTypeCloud, ResourceTypeRegion:
		// 计算资源指标类型
		if metricType == MetricTypeHostTotal || metricType == MetricTypeCPUTotal ||
			metricType == MetricTypeMemoryTotal || metricType == MetricTypeGPUTotal ||
			metricType == MetricTypeDLICUTotal {
			validMetricType = true
		}
	case ResourceTypeData:
		// 数据资源指标类型
		if metricType == MetricTypeMySQLStorage || metricType == MetricTypeOSSStorage ||
			metricType == MetricTypeOBSStorage || metricType == MetricTypeSFSStorage ||
			metricType == MetricTypeNASStorage || metricType == MetricTypeMongoDBStorage ||
			metricType == MetricTypeStarrocksStorage || metricType == MetricTypeClickhouseStorage ||
			metricType == MetricTypeTiDBStorage || metricType == MetricTypeDLIStorage {
			validMetricType = true
		}
	case ResourceTypeNetwork:
		// 网络资源指标类型
		if metricType == MetricTypeInBps || metricType == MetricTypeOutBps {
			validMetricType = true
		}
	case ResourceTypeOptimizable:
		// 可优化资源指标类型
		if metricType == MetricTypeAMDPercent || metricType == MetricTypeCPUUsage ||
			metricType == MetricTypeMemoryUsage {
			validMetricType = true
		}
	}

	if !validMetricType {
		return nil, errors.New("不支持的指标类型")
	}

	// 解析日期
	start, err := time.Parse("2006-01-02", startDate)
	if err != nil {
		return nil, errors.New("开始日期格式错误，正确格式为：YYYY-MM-DD")
	}

	end, err := time.Parse("2006-01-02", endDate)
	if err != nil {
		return nil, errors.New("结束日期格式错误，正确格式为：YYYY-MM-DD")
	}

	// 确保开始日期不晚于结束日期
	if start.After(end) {
		return nil, errors.New("开始日期不能晚于结束日期")
	}

	// 根据资源类型获取指标数据
	switch resourceType {
	case ResourceTypeComputer:
		return getComputerResourceMetrics(string(metricType), start, end)
	case ResourceTypeCloud:
		return getCloudResourceMetrics(string(metricType), name, start, end)
	case ResourceTypeRegion:
		return getRegionResourceMetrics(string(metricType), name, start, end)
	case ResourceTypeData:
		return getDataResourceMetrics(string(metricType), start, end)
	case ResourceTypeNetwork:
		return getNetworkResourceMetrics(string(metricType), name, start, end)
	case ResourceTypeOptimizable:
		return getOptimizableResourceMetrics(string(metricType), start, end)
	default:
		return nil, errors.New("不支持的资源类型")
	}
}

// getComputerResourceMetrics 获取计算资源总计指标历史趋势
func getComputerResourceMetrics(metricType string, startDate, endDate time.Time) ([]MetricItem, error) {
	var results []struct {
		StatDate time.Time `gorm:"column:stat_date"`
		Value    float64   `gorm:"column:value"`
	}

	// 构建SQL查询
	query := app.DB().Model(&ComputerResource{}).
		Select(fmt.Sprintf("stat_date, %s as value", metricType)).
		Where("stat_date >= ? AND stat_date <= ?", startDate, endDate).
		Order("stat_date")

	// 执行查询
	err := query.Find(&results).Error
	if err != nil {
		app.Log().Error("获取计算资源指标历史趋势失败", "err", err, "metric_type", metricType)
		return nil, err
	}

	// 转换结果
	metrics := make([]MetricItem, len(results))
	for i, result := range results {
		metrics[i] = MetricItem{
			Date:  result.StatDate.Format("2006-01-02"),
			Value: result.Value,
		}
	}

	return metrics, nil
}

// getCloudResourceMetrics 获取云计算资源指标历史趋势
func getCloudResourceMetrics(metricType, cloudType string, startDate, endDate time.Time) ([]MetricItem, error) {
	var results []struct {
		StatDate time.Time `gorm:"column:stat_date"`
		Value    float64   `gorm:"column:value"`
	}

	// 构建SQL查询
	query := app.DB().Model(&CloudComputerResource{}).
		Select(fmt.Sprintf("stat_date, %s as value", metricType)).
		Where("stat_date >= ? AND stat_date <= ? AND cloud_type_name = ?", startDate, endDate, cloudType).
		Order("stat_date")

	// 执行查询
	err := query.Find(&results).Error
	if err != nil {
		app.Log().Error("获取云计算资源指标历史趋势失败", "err", err, "metric_type", metricType, "cloud_type", cloudType)
		return nil, err
	}

	// 转换结果
	metrics := make([]MetricItem, len(results))
	for i, result := range results {
		metrics[i] = MetricItem{
			Date:  result.StatDate.Format("2006-01-02"),
			Value: result.Value,
		}
	}

	return metrics, nil
}

// getRegionResourceMetrics 获取区域计算资源指标历史趋势
func getRegionResourceMetrics(metricType, regionName string, startDate, endDate time.Time) ([]MetricItem, error) {
	var results []struct {
		StatDate time.Time `gorm:"column:stat_date"`
		Value    float64   `gorm:"column:value"`
	}

	// 构建SQL查询
	query := app.DB().Model(&RegionComputerResource{}).
		Select(fmt.Sprintf("stat_date, %s as value", metricType)).
		Where("stat_date >= ? AND stat_date <= ? AND region_name = ?", startDate, endDate, regionName).
		Order("stat_date")

	// 执行查询
	err := query.Find(&results).Error
	if err != nil {
		app.Log().Error("获取区域计算资源指标历史趋势失败", "err", err, "metric_type", metricType, "region_name", regionName)
		return nil, err
	}

	// 转换结果
	metrics := make([]MetricItem, len(results))
	for i, result := range results {
		metrics[i] = MetricItem{
			Date:  result.StatDate.Format("2006-01-02"),
			Value: result.Value,
		}
	}

	return metrics, nil
}

// getDataResourceMetrics 获取数据资源指标历史趋势
func getDataResourceMetrics(metricType string, startDate, endDate time.Time) ([]MetricItem, error) {
	var results []struct {
		StatDate time.Time `gorm:"column:stat_date"`
		Value    float64   `gorm:"column:value"`
	}

	// 构建SQL查询
	query := app.DB().Model(&DataResource{}).
		Select(fmt.Sprintf("stat_date, %s as value", metricType)).
		Where("stat_date >= ? AND stat_date <= ?", startDate, endDate).
		Order("stat_date")

	// 执行查询
	err := query.Find(&results).Error
	if err != nil {
		app.Log().Error("获取数据资源指标历史趋势失败", "err", err, "metric_type", metricType)
		return nil, err
	}

	// 转换结果
	metrics := make([]MetricItem, len(results))
	for i, result := range results {
		metrics[i] = MetricItem{
			Date:  result.StatDate.Format("2006-01-02"),
			Value: result.Value,
		}
	}

	return metrics, nil
}

// getNetworkResourceMetrics 获取网络资源指标历史趋势
func getNetworkResourceMetrics(metricType, domain string, startDate, endDate time.Time) ([]MetricItem, error) {
	var results []struct {
		StatDate time.Time `gorm:"column:stat_date"`
		Value    float64   `gorm:"column:value"`
	}

	// 使用 asset.CloudDDosDomainDailyBps 表获取网络域名 bps 统计数据
	query := app.DB().Model(&asset.CloudDDosDomainDailyBps{}).
		Select(fmt.Sprintf("stat_date, %s as value", metricType)).
		Where("stat_date >= ? AND stat_date <= ? AND domain = ?", startDate, endDate, domain).
		Order("stat_date")

	// 执行查询
	err := query.Find(&results).Error
	if err != nil {
		app.Log().Error("获取网络资源指标历史趋势失败", "err", err, "metric_type", metricType, "domain", domain)
		return nil, err
	}

	// 如果没有数据，尝试使用旧表查询
	if len(results) == 0 {
		app.Log().Info("从 CloudDDosDomainDailyBps 表未找到数据，尝试使用 statistic_daily_domain_bps 表", "domain", domain)
		tableName := "statistic_daily_domain_bps"
		query := app.DB().Table(tableName).
			Select(fmt.Sprintf("stat_date, %s as value", metricType)).
			Where("stat_date >= ? AND stat_date <= ? AND domain = ?", startDate, endDate, domain).
			Order("stat_date")

		// 执行查询
		err := query.Find(&results).Error
		if err != nil {
			app.Log().Error("从 statistic_daily_domain_bps 表获取网络资源指标历史趋势失败", "err", err, "metric_type", metricType, "domain", domain)
			// 不返回错误，使用空结果继续
		}
	}

	// 转换结果
	metrics := make([]MetricItem, len(results))
	for i, result := range results {
		metrics[i] = MetricItem{
			Date:  result.StatDate.Format("2006-01-02"),
			Value: result.Value,
		}
	}

	return metrics, nil
}

// getOptimizableResourceMetrics 获取可优化资源指标历史趋势
func getOptimizableResourceMetrics(metricType string, startDate, endDate time.Time) ([]MetricItem, error) {
	var results []struct {
		StatDate time.Time `gorm:"column:stat_date"`
		Value    float64   `gorm:"column:value"`
	}

	// 构建SQL查询
	query := app.DB().Model(&OptimizableStat{}).
		Select(fmt.Sprintf("stat_date, %s as value", metricType)).
		Where("stat_date >= ? AND stat_date <= ?", startDate, endDate).
		Order("stat_date")

	// 执行查询
	err := query.Find(&results).Error
	if err != nil {
		app.Log().Error("获取可优化资源指标历史趋势失败", "err", err, "metric_type", metricType)
		return nil, err
	}

	// 转换结果
	metrics := make([]MetricItem, len(results))
	for i, result := range results {
		metrics[i] = MetricItem{
			Date:  result.StatDate.Format("2006-01-02"),
			Value: result.Value,
		}
	}

	return metrics, nil
}

// GetLatestResourceMetric 获取最新的资源指标值
// 参数：
// - resourceType: 资源类型，可选值为 "computer", "cloud", "region", "data", "network", "optimizable"
// - metricType: 指标类型
//   - 计算资源: "host_total", "cpu_total", "memory_mb_total", "gpu_total", "dli_cu_total"
//   - 数据资源: "mysql_storage_total", "oss_storage_total", "obs_storage_total", "sfs_storage_total", "nas_storage_total",
//     "mongodb_storage_total", "starrocks_storage_total", "clickhouse_storage_total", "tidb_storage_total", "dli_storage_total"
//   - 网络资源: "in_bps", "out_bps"
//   - 可优化资源: "amd_percent", "cpu_usage", "memory_usage"
//
// - name: 资源名称，当resourceType为"cloud"时表示云类型名称，当resourceType为"region"时表示区域名称，当resourceType为"network"时表示域名
// 返回值：
// - float64: 指标值
// - error: 错误信息
func GetLatestResourceMetric(resourceType ResourceType, metricType MetricType, name string) (float64, error) {
	// 验证参数
	if resourceType != ResourceTypeComputer && resourceType != ResourceTypeCloud &&
		resourceType != ResourceTypeRegion && resourceType != ResourceTypeData &&
		resourceType != ResourceTypeNetwork && resourceType != ResourceTypeOptimizable {
		return 0, errors.New("不支持的资源类型")
	}

	// 验证指标类型
	validMetricType := false
	switch resourceType {
	case ResourceTypeComputer, ResourceTypeCloud, ResourceTypeRegion:
		// 计算资源指标类型
		if metricType == MetricTypeHostTotal || metricType == MetricTypeCPUTotal ||
			metricType == MetricTypeMemoryTotal || metricType == MetricTypeGPUTotal ||
			metricType == MetricTypeDLICUTotal {
			validMetricType = true
		}
	case ResourceTypeData:
		// 数据资源指标类型
		if metricType == MetricTypeMySQLStorage || metricType == MetricTypeOSSStorage ||
			metricType == MetricTypeOBSStorage || metricType == MetricTypeSFSStorage ||
			metricType == MetricTypeNASStorage || metricType == MetricTypeMongoDBStorage ||
			metricType == MetricTypeStarrocksStorage || metricType == MetricTypeClickhouseStorage ||
			metricType == MetricTypeTiDBStorage || metricType == MetricTypeDLIStorage {
			validMetricType = true
		}
	case ResourceTypeNetwork:
		// 网络资源指标类型
		if metricType == MetricTypeInBps || metricType == MetricTypeOutBps {
			validMetricType = true
		}
	case ResourceTypeOptimizable:
		// 可优化资源指标类型
		if metricType == MetricTypeAMDPercent || metricType == MetricTypeCPUUsage ||
			metricType == MetricTypeMemoryUsage {
			validMetricType = true
		}
	}

	if !validMetricType {
		return 0, errors.New("不支持的指标类型")
	}

	// 根据资源类型获取最新指标值
	switch resourceType {
	case ResourceTypeComputer:
		return getLatestComputerResourceMetric(string(metricType))
	case ResourceTypeCloud:
		return getLatestCloudResourceMetric(string(metricType), name)
	case ResourceTypeRegion:
		return getLatestRegionResourceMetric(string(metricType), name)
	case ResourceTypeData:
		return getLatestDataResourceMetric(string(metricType))
	case ResourceTypeNetwork:
		return getLatestNetworkResourceMetric(string(metricType), name)
	case ResourceTypeOptimizable:
		return getLatestOptimizableResourceMetric(string(metricType))
	default:
		return 0, errors.New("不支持的资源类型")
	}
}

// getLatestComputerResourceMetric 获取最新的计算资源总计指标值
func getLatestComputerResourceMetric(metricType string) (float64, error) {
	var result struct {
		Value float64 `gorm:"column:value"`
	}

	// 构建SQL查询
	query := app.DB().Model(&ComputerResource{}).
		Select(fmt.Sprintf("%s as value", metricType)).
		Order("stat_date DESC").
		Limit(1)

	// 执行查询
	err := query.Take(&result).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return 0, errors.New("未找到计算资源指标数据")
		}
		app.Log().Error("获取最新计算资源指标值失败", "err", err, "metric_type", metricType)
		return 0, err
	}

	return result.Value, nil
}

// getLatestCloudResourceMetric 获取最新的云计算资源指标值
func getLatestCloudResourceMetric(metricType, cloudType string) (float64, error) {
	var result struct {
		Value float64 `gorm:"column:value"`
	}

	// 构建SQL查询
	query := app.DB().Model(&CloudComputerResource{}).
		Select(fmt.Sprintf("%s as value", metricType)).
		Where("cloud_type_name = ?", cloudType).
		Order("stat_date DESC").
		Limit(1)

	// 执行查询
	err := query.Take(&result).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return 0, errors.New("未找到云计算资源指标数据")
		}
		app.Log().Error("获取最新云计算资源指标值失败", "err", err, "metric_type", metricType, "cloud_type", cloudType)
		return 0, err
	}

	return result.Value, nil
}

// getLatestRegionResourceMetric 获取最新的区域计算资源指标值
func getLatestRegionResourceMetric(metricType, regionName string) (float64, error) {
	var result struct {
		Value float64 `gorm:"column:value"`
	}

	// 构建SQL查询
	query := app.DB().Model(&RegionComputerResource{}).
		Select(fmt.Sprintf("%s as value", metricType)).
		Where("region_name = ?", regionName).
		Order("stat_date DESC").
		Limit(1)

	// 执行查询
	err := query.Take(&result).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return 0, errors.New("未找到区域计算资源指标数据")
		}
		app.Log().Error("获取最新区域计算资源指标值失败", "err", err, "metric_type", metricType, "region_name", regionName)
		return 0, err
	}

	return result.Value, nil
}

// getLatestDataResourceMetric 获取最新的数据资源指标值
func getLatestDataResourceMetric(metricType string) (float64, error) {
	var result struct {
		Value float64 `gorm:"column:value"`
	}

	// 构建SQL查询
	query := app.DB().Model(&DataResource{}).
		Select(fmt.Sprintf("%s as value", metricType)).
		Order("stat_date DESC").
		Limit(1)

	// 执行查询
	err := query.Take(&result).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return 0, errors.New("未找到数据资源指标数据")
		}
		app.Log().Error("获取最新数据资源指标值失败", "err", err, "metric_type", metricType)
		return 0, err
	}

	return result.Value, nil
}

// getLatestNetworkResourceMetric 获取最新的网络资源指标值
func getLatestNetworkResourceMetric(metricType, domain string) (float64, error) {
	var result struct {
		Value float64 `gorm:"column:value"`
	}

	// 使用 asset.CloudDDosDomainDailyBps 表获取最新的网络域名 bps 统计数据
	query := app.DB().Model(&asset.CloudDDosDomainDailyBps{}).
		Select(fmt.Sprintf("%s as value", metricType)).
		Where("domain = ?", domain).
		Order("stat_date DESC").
		Limit(1)

	// 执行查询
	err := query.Take(&result).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 如果没有找到数据，尝试使用旧表查询
			app.Log().Info("从 CloudDDosDomainDailyBps 表未找到数据，尝试使用 statistic_daily_domain_bps 表", "domain", domain)
			tableName := "statistic_daily_domain_bps"
			query := app.DB().Table(tableName).
				Select(fmt.Sprintf("%s as value", metricType)).
				Where("domain = ?", domain).
				Order("stat_date DESC").
				Limit(1)

			// 执行查询
			err := query.Take(&result).Error
			if err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					return 0, errors.New("未找到网络资源指标数据")
				}
				app.Log().Error("从 statistic_daily_domain_bps 表获取最新网络资源指标值失败", "err", err, "metric_type", metricType, "domain", domain)
				return 0, err
			}
		} else {
			app.Log().Error("从 CloudDDosDomainDailyBps 表获取最新网络资源指标值失败", "err", err, "metric_type", metricType, "domain", domain)
			return 0, err
		}
	}

	return result.Value, nil
}

// getLatestOptimizableResourceMetric 获取最新的可优化资源指标值
func getLatestOptimizableResourceMetric(metricType string) (float64, error) {
	var result struct {
		Value float64 `gorm:"column:value"`
	}

	// 构建SQL查询
	query := app.DB().Model(&OptimizableStat{}).
		Select(fmt.Sprintf("%s as value", metricType)).
		Order("stat_date DESC").
		Limit(1)

	// 执行查询
	err := query.Take(&result).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return 0, errors.New("未找到可优化资源指标数据")
		}
		app.Log().Error("获取最新可优化资源指标值失败", "err", err, "metric_type", metricType)
		return 0, err
	}

	return result.Value, nil
}

// GetResourceMetricsByDateRange 获取指定日期范围内的资源指标历史趋势
// 参数：
// - resourceType: 资源类型，可选值为 "computer", "cloud", "region", "data", "network", "optimizable"
// - metricType: 指标类型
//   - 计算资源: "host_total", "cpu_total", "memory_mb_total", "gpu_total", "dli_cu_total"
//   - 数据资源: "mysql_storage_total", "oss_storage_total", "obs_storage_total", "sfs_storage_total", "nas_storage_total",
//     "mongodb_storage_total", "starrocks_storage_total", "clickhouse_storage_total", "tidb_storage_total", "dli_storage_total"
//   - 网络资源: "in_bps", "out_bps"
//   - 可优化资源: "amd_percent", "cpu_usage", "memory_usage"
//
// - name: 资源名称，当resourceType为"cloud"时表示云类型名称，当resourceType为"region"时表示区域名称，当resourceType为"network"时表示域名
// - days: 天数，表示获取最近多少天的数据
// 返回值：
// - []MetricItem: 指标数据列表
// - error: 错误信息
func GetResourceMetricsByDateRange(resourceType ResourceType, metricType MetricType, name string, days int) ([]MetricItem, error) {
	// 验证参数
	if days <= 0 {
		return nil, errors.New("天数必须大于0")
	}

	// 计算日期范围
	endDate := now.BeginningOfDay()
	startDate := endDate.AddDate(0, 0, -days+1)

	// 调用GetResourceMetrics函数
	return GetResourceMetrics(resourceType, metricType, name, startDate.Format("2006-01-02"), endDate.Format("2006-01-02"))
}

// GetAllCloudTypes 获取所有云类型
// 返回值：
// - []string: 云类型列表
// - error: 错误信息
func GetAllCloudTypes() ([]string, error) {
	var cloudTypes []string

	// 构建SQL查询
	query := app.DB().Model(&CloudComputerResource{}).
		Distinct("cloud_type_name").
		Order("cloud_type_name")

	// 执行查询
	err := query.Pluck("cloud_type_name", &cloudTypes).Error
	if err != nil {
		app.Log().Error("获取所有云类型失败", "err", err)
		return nil, err
	}

	return cloudTypes, nil
}

// GetAllRegionNames 获取所有区域名称
// 返回值：
// - []string: 区域名称列表
// - error: 错误信息
func GetAllRegionNames() ([]string, error) {
	var regionNames []string

	// 构建SQL查询
	query := app.DB().Model(&RegionComputerResource{}).
		Distinct("region_name").
		Order("region_name")

	// 执行查询
	err := query.Pluck("region_name", &regionNames).Error
	if err != nil {
		app.Log().Error("获取所有区域名称失败", "err", err)
		return nil, err
	}

	return regionNames, nil
}

// GetAllDomainNames 获取所有域名
// 返回值：
// - []string: 域名列表
// - error: 错误信息
func GetAllDomainNames() ([]string, error) {
	var domainNames []string

	// 使用 asset.CloudDDosDomainDailyBps 表获取所有域名
	query := app.DB().Model(&asset.CloudDDosDomainDailyBps{}).
		Distinct("domain").
		Order("domain")

	// 执行查询
	err := query.Pluck("domain", &domainNames).Error
	if err != nil {
		app.Log().Error("从 CloudDDosDomainDailyBps 表获取所有域名失败", "err", err)
		// 尝试使用旧表查询
		tableName := "statistic_daily_domain_bps"
		query := app.DB().Table(tableName).
			Distinct("domain").
			Order("domain")

		// 执行查询
		err := query.Pluck("domain", &domainNames).Error
		if err != nil {
			app.Log().Error("从 statistic_daily_domain_bps 表获取所有域名失败", "err", err)
			return nil, err
		}
	}

	// 如果没有找到域名，尝试从 CloudDDosDomain 表获取
	if len(domainNames) == 0 {
		app.Log().Info("从 CloudDDosDomainDailyBps 和 statistic_daily_domain_bps 表未找到域名，尝试从 CloudDDosDomain 表获取")
		err := app.DB().Model(&asset.CloudDDosDomain{}).
			Distinct("domain").
			Order("domain").
			Pluck("domain", &domainNames).Error
		if err != nil {
			app.Log().Error("从 CloudDDosDomain 表获取所有域名失败", "err", err)
			return nil, err
		}
	}

	return domainNames, nil
}

// GetAllDataResourceTypes 获取所有数据资源类型
// 返回值：
// - []string: 数据资源类型列表
// - error: 错误信息
func GetAllDataResourceTypes() ([]MetricType, error) {
	// 返回所有数据资源指标类型
	return []MetricType{
		MetricTypeMySQLStorage,
		MetricTypeOSSStorage,
		MetricTypeOBSStorage,
		MetricTypeSFSStorage,
		MetricTypeNASStorage,
		MetricTypeMongoDBStorage,
		MetricTypeStarrocksStorage,
		MetricTypeClickhouseStorage,
		MetricTypeTiDBStorage,
		MetricTypeDLIStorage,
	}, nil
}

// GetAllOptimizableResourceTypes 获取所有可优化资源类型
// 返回值：
// - []string: 可优化资源类型列表
// - error: 错误信息
func GetAllOptimizableResourceTypes() ([]MetricType, error) {
	// 返回所有可优化资源指标类型
	return []MetricType{
		MetricTypeAMDPercent,
		MetricTypeCPUUsage,
		MetricTypeMemoryUsage,
	}, nil
}
