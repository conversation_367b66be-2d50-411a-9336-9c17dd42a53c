#!/bin/bash

# 夜莺(N9E)告警统计API使用示例
# 使用前请确保：
# 1. 服务已启动
# 2. 已获取有效的访问令牌
# 3. 具有管理员权限

# 配置
BASE_URL="http://localhost:8080"
TOKEN="YOUR_ACCESS_TOKEN_HERE"

# 时间参数（示例：查询最近24小时）
START_TIME=$(date -d "1 day ago" "+%Y-%m-%d %H:%M:%S")
END_TIME=$(date "+%Y-%m-%d %H:%M:%S")

echo "=== 夜莺(N9E)告警统计API测试 ==="
echo "查询时间范围: $START_TIME 到 $END_TIME"
echo ""

# 1. 获取告警统计数据
echo "1. 获取告警统计数据"
echo "请求URL: $BASE_URL/api/v1/statistic/monitor/n9e/alerts"
curl -s -X GET "$BASE_URL/api/v1/statistic/monitor/n9e/alerts?start_time=$(echo $START_TIME | sed 's/ /%20/g')&end_time=$(echo $END_TIME | sed 's/ /%20/g')" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" | jq '.'
echo ""

# 2. 按实例维度获取告警统计
echo "2. 按实例维度获取告警统计（前10条）"
echo "请求URL: $BASE_URL/api/v1/statistic/monitor/n9e/alerts/by-instance"
curl -s -X GET "$BASE_URL/api/v1/statistic/monitor/n9e/alerts/by-instance?start_time=$(echo $START_TIME | sed 's/ /%20/g')&end_time=$(echo $END_TIME | sed 's/ /%20/g')&page=1&limit=10" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" | jq '.'
echo ""

# 3. 按告警规则维度获取告警统计
echo "3. 按告警规则维度获取告警统计（前10条）"
echo "请求URL: $BASE_URL/api/v1/statistic/monitor/n9e/alerts/by-rule"
curl -s -X GET "$BASE_URL/api/v1/statistic/monitor/n9e/alerts/by-rule?start_time=$(echo $START_TIME | sed 's/ /%20/g')&end_time=$(echo $END_TIME | sed 's/ /%20/g')&page=1&limit=10" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" | jq '.'
echo ""

# 4. 按集群维度获取告警统计
echo "4. 按集群维度获取告警统计"
echo "请求URL: $BASE_URL/api/v1/statistic/monitor/n9e/alerts/by-cluster"
curl -s -X GET "$BASE_URL/api/v1/statistic/monitor/n9e/alerts/by-cluster?start_time=$(echo $START_TIME | sed 's/ /%20/g')&end_time=$(echo $END_TIME | sed 's/ /%20/g')&page=1&limit=10" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" | jq '.'
echo ""

# 5. 获取当前活跃的告警数据
echo "5. 获取当前活跃的告警数据（所有告警）"
echo "请求URL: $BASE_URL/api/v1/statistic/monitor/n9e/alerts/current"
curl -s -X GET "$BASE_URL/api/v1/statistic/monitor/n9e/alerts/current?page=1&limit=10" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" | jq '.'
echo ""

# 6. 获取当前活跃的告警数据（按严重程度过滤）
echo "6. 获取当前活跃的告警数据（仅紧急告警）"
echo "请求URL: $BASE_URL/api/v1/statistic/monitor/n9e/alerts/current?severity=0"
curl -s -X GET "$BASE_URL/api/v1/statistic/monitor/n9e/alerts/current?severity=0&page=1&limit=5" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" | jq '.'
echo ""

# 7. 获取当前活跃的告警数据（按集群过滤）
echo "7. 获取当前活跃的告警数据（按集群过滤）"
echo "请求URL: $BASE_URL/api/v1/statistic/monitor/n9e/alerts/current?cluster=production"
curl -s -X GET "$BASE_URL/api/v1/statistic/monitor/n9e/alerts/current?cluster=production&page=1&limit=5" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" | jq '.'
echo ""

# 8. 获取当前活跃的告警数据（时间范围过滤）
echo "8. 获取当前活跃的告警数据（最近1小时）"
HOUR_AGO=$(date -d "1 hour ago" "+%Y-%m-%d %H:%M:%S")
echo "请求URL: $BASE_URL/api/v1/statistic/monitor/n9e/alerts/current?start_time=$HOUR_AGO"
curl -s -X GET "$BASE_URL/api/v1/statistic/monitor/n9e/alerts/current?start_time=$(echo $HOUR_AGO | sed 's/ /%20/g')&page=1&limit=5" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" | jq '.'
echo ""

echo "=== 测试完成 ==="

# 使用说明：
# 1. 将 YOUR_ACCESS_TOKEN_HERE 替换为实际的访问令牌
# 2. 根据需要修改 BASE_URL
# 3. 确保安装了 jq 工具用于格式化JSON输出
# 4. 给脚本添加执行权限：chmod +x example.sh
# 5. 运行脚本：./example.sh
