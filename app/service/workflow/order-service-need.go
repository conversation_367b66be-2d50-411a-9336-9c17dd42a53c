package workflow

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/auth"
	"encoding/json"
	"errors"
	"time"
)

type OrderServiceNeed struct {
	ID                  uint   `gorm:"column:id;primarykey;comment:id" json:"id"`
	SN                  string `gorm:"type:varchar(128);unique;column:sn;comment:工单单号" json:"sn"`
	Content             string `gorm:"type:text;column:content;comment:工单内容" json:"content"`
	IterationVersion    string `gorm:"column:iteration_version;default:'';comment:迭代版本" json:"iteration_version"`
	NeedDecryptStatInfo bool   `gorm:"column:need_decrypt_stat_info;default:false;comment:是否需要解密状态信息" json:"need_decrypt_stat_info"`
}

func (OrderServiceNeed) TableName() string {
	return "workflow_order_service_needs"
}

type OrderServiceNeedDetail struct {
	Order               `gorm:"-"`
	Content             string `gorm:"type:text;column:content;comment:工单内容" json:"content"`
	IterationVersion    string `gorm:"column:iteration_version;comment:迭代版本" json:"iteration_version"`
	NeedDecryptStatInfo bool   `gorm:"column:need_decrypt_stat_info;default:false;comment:是否需要解密状态信息" json:"need_decrypt_stat_info"`
}

// 设置表名
func (OrderServiceNeedDetail) TableName() string {
	return "workflow_order_service_needs"
}

func GetOrderServiceNeedDetail(o Order) (detail OrderServiceNeedDetail, err error) {
	detail = OrderServiceNeedDetail{}
	err = app.DB().Where("sn = ?", o.SN).Take(&detail).Error
	if err != nil {
		return
	}
	detail.Order = o
	return
}

type OrderServiceNeedForm struct {
	Title               string    `json:"title" binding:"required,max=255"`
	OrderType           string    `json:"order_type" binding:"required,max=255"`
	Content             string    `json:"content" binding:"required"`
	PlanTime            time.Time `json:"plan_time" binding:"required"`
	Orders              []string  `json:"orders"`
	Env                 asset.ENV `json:"env" binding:"required"`
	Critical            bool      `json:"critical"`
	IterationVersion    string    `json:"iteration_version"`
	CloudPlatforms      []string  `json:"cloud_platforms"`
	NeedDecryptStatInfo bool      `json:"need_decrypt_stat_info"`
}

func (form OrderServiceNeedForm) GenProcesses(sn string, a Applicant, admin *auth.User) (processes []Process, err error) {
	var flow Flow
	err = app.DB().Where("order_type = ?", ServiceNeedOrderType).Take(&flow).Error
	if err != nil {
		return
	}
	flow.Nodes, err = flow.GetFlowNodes()
	if err != nil {
		return
	}
	processes, err = flow.genProcesses(sn, a, admin)
	return
}

func (form OrderServiceNeedForm) check() (err error) {
	return
}

func (form OrderServiceNeedForm) Create(a Applicant, flow Flow, admin *auth.User) (err error) {
	err = form.check()
	if err != nil {
		return
	}
	sn := genSN("service-need")
	processes, err := flow.genProcesses(sn, a, admin)
	if err != nil || len(processes) == 0 {
		return
	}
	status := ApprovingStatus
	var index uint
	if len(processes) == 0 {
		err = errors.New("生成流程失败")
		return
	} else if len(processes) <= 1 {
		// 如果流程没有配置就直接结束流程
		status = CompletedStatus
	} else {
		// 如果有流程就获取下一个节点的索引
		index = processes[1].ProcessIndex
		processes[1].Status = ApprovingProcessStatus
	}
	planTime := time.Now().AddDate(0, 0, 1)
	platformsBytes, _ := json.Marshal(&form.CloudPlatforms)
	order := Order{
		Title:          form.Title + " - " + a.Name,
		OrderType:      form.OrderType,
		Env:            form.Env,
		ExtType:        ServiceNeedExtType,
		SN:             sn,
		Critical:       form.Critical,
		ApplicantID:    a.ID,
		Status:         status,
		PlanTime:       &planTime,
		ProcessIndex:   index,
		CloudPlatforms: platformsBytes,
	}
	extOrder := OrderServiceNeed{
		SN:                  sn,
		Content:             form.Content,
		IterationVersion:    form.IterationVersion,
		NeedDecryptStatInfo: form.NeedDecryptStatInfo,
	}
	dbop := app.DB().Begin()
	err = dbop.Create(&order).Error
	if err != nil {
		dbop.Rollback()
		return
	}
	err = dbop.Create(&extOrder).Error
	if err != nil {
		dbop.Rollback()
		return
	}
	err = dbop.Create(&processes).Error
	if err != nil {
		dbop.Rollback()
		return
	}
	err = dbop.Commit().Error
	if err != nil {
		dbop.Rollback()
	}
	if admin != nil {
		go order.AutoCompleteOrder(*admin)
	} else {
		go order.notifyNextProcess()
	}
	return
}

func (form OrderServiceNeedForm) Update(o Order) (err error) {
	extOrder := OrderServiceNeed{}
	err = app.DB().Where("sn = ?", o.SN).Take(&extOrder).Error
	if err != nil {
		return
	}
	updateMap0 := map[string]any{}
	updateMap := map[string]any{}
	if form.Content != extOrder.Content {
		updateMap["content"] = form.Content
	}
	if form.IterationVersion != extOrder.IterationVersion {
		updateMap["iteration_version"] = form.IterationVersion
	}
	if form.OrderType != o.OrderType {
		updateMap0["order_type"] = form.OrderType
	}
	if form.Critical != o.Critical {
		updateMap0["critical"] = form.Critical
	}
	if form.Env != o.Env {
		updateMap0["env"] = form.Env
	}
	if form.Title != o.Title {
		updateMap0["title"] = form.Title
	}
	if o.PlanTime == nil || o.PlanTime != nil && form.PlanTime.Equal(*o.PlanTime) {
		updateMap0["plan_time"] = form.PlanTime
	}
	if form.NeedDecryptStatInfo != extOrder.NeedDecryptStatInfo {
		updateMap["need_decrypt_stat_info"] = form.NeedDecryptStatInfo
	}
	dbop := app.DB().Begin()
	if len(updateMap) > 0 {
		err = dbop.Model(&extOrder).Updates(updateMap).Error
		if err != nil {
			dbop.Rollback()
			return
		}
	}
	if len(updateMap0) > 0 {
		err = dbop.Model(&o).Updates(updateMap0).Error
		if err != nil {
			dbop.Rollback()
			return
		}
	}
	err = dbop.Commit().Error
	if err != nil {
		dbop.Rollback()
	}
	return
}
