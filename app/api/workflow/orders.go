package workflow

import (
	"cmdb/app"
	"cmdb/app/service/workflow"
	"strconv"
	"sync"

	"github.com/gin-gonic/gin"
)

type OrderList struct {
	workflow.Order
	Applicant workflow.Applicant `json:"applicant"`
}

func GetOrders(c *gin.Context) {
	page, _ := strconv.Atoi(c<PERSON>("page", "1"))
	limit, _ := strconv.Atoi(c<PERSON>("limit", "10"))
	offset := (page - 1) * limit
	var keyword *string
	if val := c.<PERSON>ry("keyword"); val != "" {
		keyword = &val
	}
	var extType *workflow.OrderExtType
	if val, err := strconv.Atoi(c.Que<PERSON>("ext_type")); err == nil {
		if et, err := workflow.GetExtTypeByID(val); err == nil {
			extType = &et
		}
	}

	var applicantID *int
	if val, err := strconv.Atoi(c<PERSON><PERSON>("applicant_id")); err == nil {
		applicantID = &val
	}
	var status *workflow.OrderStatus
	if val, err := strconv.Atoi(c.Query("status")); err == nil {
		a := workflow.GetOrderStatusByID(val)
		status = &a
	}
	count, data, err := workflow.GetOrders(offset, limit, keyword, extType, status, applicantID)
	if err != nil {
		app.Log().Error("获取用户工单失败", "err", err)
		app.FailedResponseMsg(c, "获取用户工单失败")
		return
	}
	var newData []OrderList
	if len(data) > 0 {
		userMap := sync.Map{}
		for _, v := range data {
			if val, ok := userMap.Load(v.ApplicantID); ok {
				if applicant, ok1 := val.(workflow.Applicant); ok1 {
					newData = append(newData, OrderList{
						Order:     v,
						Applicant: applicant,
					})
				}
			} else {
				if applicant, err := workflow.GetApplicantByID(v.ApplicantID); err == nil {
					userMap.Store(v.ApplicantID, applicant)
					newData = append(newData, OrderList{
						Order:     v,
						Applicant: applicant,
					})
				}
			}
		}
	}
	app.SuccessResponseDataCount(c, newData, count)
}
