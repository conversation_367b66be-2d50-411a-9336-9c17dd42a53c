package slowlog

import (
	"time"
)

type ProjectStatistics struct {
	Name     string `gorm:"column:name;not null;default:'';comment:'数据库实例企业项目名称'" json:"name"`
	Total    int64  `gorm:"column:total;not null;default:0;comment:'慢日志数量'" json:"total"`
	StatTime string `gorm:"column:stat_time;not null;default:'';comment:'统计时间'" json:"stat_time"`
}

// GetMySQLProjectStatistics 获取MySQL慢日志统计
func (s SlowlogStore) GetMySQLProjectStatistics(startTime, endTime time.Time) (data []ProjectStatistics, err error) {
	dbop, err := s.NewConnect()
	if err != nil {
		return
	}
	defer func() {
		d, err1 := dbop.DB()
		if err1 == nil {
			d.Close()
		}
	}()
	err = dbop.Model(&MySQLSlowLogStatistics{}).Select("db_project_name AS name, date_format(create_time, '%Y-%m-%d') AS stat_time, sum(total_count) AS total").Group("db_project_name, stat_time").Order("stat_time,name").Where("create_time BETWEEN ? AND ?", startTime, endTime).Find(&data).Error
	return
}

// GetRedisProjectStatistics 获取Redis慢日志统计
func (s SlowlogStore) GetRedisProjectStatistics(startTime, endTime time.Time) (data []ProjectStatistics, err error) {
	dbop, err := s.NewConnect()
	if err != nil {
		return
	}
	defer func() {
		d, err1 := dbop.DB()
		if err1 == nil {
			d.Close()
		}
	}()
	err = dbop.Model(&RedisBigKeysStringStatistic{}).Select("db_project_name AS name, date_format(create_time, '%Y-%m-%d') AS stat_time, sum(bigkeys_string_count) AS total").Group("db_project_name, stat_time").Order("stat_time,name").Where("create_time BETWEEN ? AND ?", startTime, endTime).Find(&data).Error
	return
}

// GetMongoDBProjectStatistics 获取MongoDB慢日志统计
func (s SlowlogStore) GetMongoDBProjectStatistics(startTime, endTime time.Time) (data []ProjectStatistics, err error) {
	dbop, err := s.NewConnect()
	if err != nil {
		return
	}
	defer func() {
		d, err1 := dbop.DB()
		if err1 == nil {
			d.Close()
		}
	}()
	err = dbop.Model(&MongoDBSlowLogStatistics{}).Select("db_project_name AS name, date_format(create_time, '%Y-%m-%d') AS stat_time, sum(total_count) AS total").Group("db_project_name, stat_time").Order("stat_time,name").Where("create_time BETWEEN ? AND ?", startTime, endTime).Find(&data).Error
	return
}
