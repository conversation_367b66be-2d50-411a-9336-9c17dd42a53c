package asset

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"strconv"
	"strings"
	"sync"

	"github.com/gin-gonic/gin"
)

type SubetNetListItem struct {
	asset.SubNet
	Account           string `json:"account"`
	Datacenter        string `json:"datacenter"`
	ResourceGroupName string `json:"resource_group_name"`
}

func GetSubNets(c *gin.Context) {
	// 获取分页参数
	page, _ := strconv.Atoi(c.<PERSON>("page", "1"))
	limit, _ := strconv.Atoi(c.<PERSON>("limit", "10"))
	// 计算偏移量
	offset := (page - 1) * limit
	//  获取查询参数
	var keyword, ip, cidr *string
	if val := strings.TrimSpace(c.Query("keyword")); val != "" {
		keyword = &val
	}
	if val := strings.TrimSpace(c.Query("ip")); val != "" {
		ip = &val
	}
	if val := strings.TrimSpace(c.Query("cidr")); val != "" {
		cidr = &val
	}
	var accountID, datacenterID *int
	if val := strings.TrimSpace(c.Query("account_id")); val != "" {
		if val1, err := strconv.Atoi(val); err == nil {
			accountID = &val1
		}
	}
	if val := strings.TrimSpace(c.Query("datacenter_id")); val != "" {
		if val1, err := strconv.Atoi(val); err == nil {
			datacenterID = &val1
		}
	}
	count, subNets, err := asset.GetSubNets(offset, limit, accountID, datacenterID, ip, cidr, keyword)
	if err != nil {
		app.Log().Error("获取子网列表失败", "err", err)
		app.FailedResponseMsg(c, "获取子网列表失败")
		return
	}
	// 将数据转换为新的结构体
	newData := make([]SubetNetListItem, len(subNets))
	if len(subNets) > 0 {
		tmpCloudAccountStore := sync.Map{}
		tmpDatacenterStore := sync.Map{}
		tmpResourceGroupStore := sync.Map{}
		wg := sync.WaitGroup{}
		wg.Add(len(subNets))
		for i := range subNets {
			go func() {
				newData[i].SubNet = subNets[i]
				// 获取云账户名称
				if val, ok := tmpCloudAccountStore.Load(subNets[i].AccountID); ok {
					newData[i].Account, _ = val.(string)
				} else {
					account, err1 := asset.GetAccountNameByID(int(subNets[i].AccountID))
					if err1 == nil {
						newData[i].Account = account
						tmpCloudAccountStore.Store(subNets[i].AccountID, account)
					}
				}
				// 获取数据中心名称
				if val, ok := tmpDatacenterStore.Load(subNets[i].DatacenterID); ok {
					newData[i].Datacenter, _ = val.(string)
				} else {
					datacenter, err1 := asset.GetDatacenterNameByID(int(subNets[i].DatacenterID))
					if err1 == nil {
						newData[i].Datacenter = datacenter
						tmpDatacenterStore.Store(subNets[i].DatacenterID, datacenter)
					}
				}
				// 获取资源组名称
				if val, ok := tmpResourceGroupStore.Load(subNets[i].ResourceGroupID); ok {
					newData[i].ResourceGroupName, _ = val.(string)
				} else {
					resourceGroupName, err1 := asset.GetResourceGroupNameByID(subNets[i].ResourceGroupID)
					if err1 == nil {
						newData[i].ResourceGroupName = resourceGroupName
						tmpResourceGroupStore.Store(subNets[i].ResourceGroupID, resourceGroupName)
					}
				}
				wg.Done()
			}()
		}
		wg.Wait()
	}
	app.SuccessResponseDataCount(c, newData, count)
}
