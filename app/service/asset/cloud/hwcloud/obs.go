package hwcloud

import (
	"cmdb/app"
	"cmdb/pkg/utils"
	"time"

	obs "github.com/huaweicloud/huaweicloud-sdk-go-obs/obs"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/core/auth/basic"
	ces "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/ces/v1"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/ces/v1/model"
	region "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/ces/v1/region"
)

func StatAllAccountsOBSStorage() (storageTotal int64, err error) {
	storageTotal = 0
	accounts, err := GetAllAccounts()
	if err != nil {
		app.Log().Error("列举华为云账号异常", "err", err.Error())
		return
	}
	for _, a := range accounts {
		total, err := a.getOBSStorageTotal()
		if err != nil {
			app.Log().Error("获取华为云OBS存储容量异常", "account", a.Name, "err", err.Error())
			continue
		}
		storageTotal += total
	}
	return
}

func (a Account) getAllOBSBuckets() (buckets []string, err error) {
	secret, err := utils.Base64Decode(a.AccessSecret)
	if err != nil {
		app.Log().Error("解密华为云账号密钥异常", "account", a.Name, "err", err.Error())
		return
	}
	endPoint := "https://obs.cn-north-4.myhuaweicloud.com"
	obsClient, err := obs.New(a.AccessKey, secret, endPoint)
	if err != nil {
		app.Log().Error("创建OBS客户端失败", "account", a.Name, "err", err.Error())
		return
	}
	result, err := obsClient.ListBuckets(&obs.ListBucketsInput{
		QueryLocation: true,
		BucketType:    obs.OBJECT,
	})
	if err != nil {
		app.Log().Error("列举OBS桶失败", "account", a.Name, "err", err.Error())
		return
	}

	for _, bucket := range result.Buckets {
		buckets = append(buckets, bucket.Name)
	}
	return
}

func (a Account) getOssStorageBucket(bucketName, marker string) (storage int64, err error) {
	secret, err := utils.Base64Decode(a.AccessSecret)
	if err != nil {
		app.Log().Error("解密华为云账号密钥异常", "account", a.Name, "err", err.Error())
		return
	}
	endPoint := "https://obs.cn-north-4.myhuaweicloud.com"
	obsClient, err := obs.New(a.AccessKey, secret, endPoint)
	if err != nil {
		app.Log().Error("创建OBS客户端失败", "account", a.Name, "err", err.Error())
		return
	}
	response, err := obsClient.ListObjects(&obs.ListObjectsInput{
		Bucket: bucketName,
		Marker: marker,
	})
	if err != nil {
		app.Log().Error("列举OBS桶失败", "account", a.Name, "err", err.Error())
		return
	}
	var totalSize int64
	for _, object := range response.Contents {
		totalSize += object.Size
	}
	storage = totalSize
	return
}

func (a Account) getOBSStorageTotal() (storageTotal int64, err error) {
	sk, err := utils.Base64Decode(a.AccessSecret)
	if err != nil {
		app.Log().Error("解密华为云账号密钥异常", "account", a.Name, "err", err.Error())
		return
	}
	ak := a.AccessKey
	auth, err := basic.NewCredentialsBuilder().
		WithAk(ak).
		WithSk(sk).
		SafeBuild()
	if err != nil {
		return
	}
	regionID, err := region.SafeValueOf("cn-north-4")
	if err != nil {
		return
	}
	clientBuild, err := ces.CesClientBuilder().
		WithRegion(regionID).
		WithCredential(auth).
		SafeBuild()
	if err != nil {
		return
	}
	client := ces.NewCesClient(clientBuild)

	request := &model.ShowMetricDataRequest{}
	request.Namespace = "SYS.OBS"
	request.MetricName = "capacity_total"
	request.Dim0 = "tenant_id,0ba4edf37a000fac0f3bc0114415fe80"
	request.Filter = model.GetShowMetricDataRequestFilterEnum().MAX
	request.Period = int32(300)
	// 当前时间 timestamp毫秒
	toTimeStamp := time.Now().UnixMilli()
	// 一个小时前
	fromTimeStamp := toTimeStamp - 3600000
	request.To = toTimeStamp
	request.From = fromTimeStamp
	response, err := client.ShowMetricData(request)
	if err != nil {
		return
	}
	if len(*response.Datapoints) == 0 {
		return
	}
	var max float64
	for _, point := range *response.Datapoints {
		if point.Max != nil && *point.Max > max {
			max = *point.Max
		}
	}
	storageTotal = int64(max)
	return
}
