package mysql

import (
	"cmdb/app"
	"cmdb/pkg/db"
	"cmdb/pkg/utils"
	"strings"
	"sync"

	"gorm.io/gorm"
)

// UserPrivDBTable 用户有权限的表
type UserPrivDBTable struct {
	DBs     []DBTable `json:"dbs"`
	Project string    `json:"project"`
}

// DBTable 一个数据库多个表
type DBTable struct {
	DB     string   `json:"db"`
	Tables []string `json:"tables"`
}

// PrivTable 权限表
type PrivTable struct {
	DB    string `gorm:"column:db" json:"db"`
	Table string `gorm:"column:table_name" json:"table"`
}

// GetUserPrivDBTableList 获取用户有权限的表
func GetUserPrivDBTableList(username string) (userPrivDBTables map[string]UserPrivDBTable, err error) {
	instances := []Instance{}
	err = app.DB().Select(
		"id", "name", "host", "port", "username", "password", "ci_address",
	).Where("ci_address != ?", "").Find(&instances).Error
	if err != nil {
		return
	}
	syncMap := sync.Map{}
	userPrivDBTables = map[string]UserPrivDBTable{}
	var wg sync.WaitGroup
	for i := range instances {
		wg.Add(1)
		go func(i int) {
			defer wg.Done()
			dbTables := map[string][]string{}
			password, _ := utils.Base64Decode(instances[i].Password)
			var op *gorm.DB
			var err error
			var sr bool
			if strings.Contains(instances[i].Name, "starrocks") {
				sr = true
				op, err = db.NewMySQLDBConnect(db.MysqlConnParmas{
					Host:     instances[i].Host,
					Port:     instances[i].Port,
					User:     instances[i].Username,
					Password: password,
					DB:       "information_schema",
				}, nil)
			} else {
				op, err = db.NewMySQLDBConnect(db.MysqlConnParmas{
					Host:     instances[i].Host,
					Port:     instances[i].Port,
					User:     instances[i].Username,
					Password: password,
					DB:       "information_schema",
				}, nil)
			}
			if err != nil {
				app.Log().Error("连接数据库失败", "host", instances[i].Host, "port", instances[i].Port, "err", err)
				return
			} else {
				app.Log().Info("连接数据库成功", "host", instances[i].Host, "port", instances[i].Port)
			}
			if sr {
				shemaTables := []SchemaTable{}
				err = op.Table("information_schema.TABLES").Select("TABLE_SCHEMA", "TABLE_NAME").Where("TABLE_SCHEMA NOT IN (?)", []string{"sys", "_statistics_", "performance_schema", "mysql", "information_schema"}).Find(&shemaTables).Error
				if err != nil {
					return
				}
				for i := range shemaTables {
					if schemas, ok := dbTables[shemaTables[i].Schema]; ok {
						dbTables[shemaTables[i].Schema] = append(schemas, shemaTables[i].Name)
					} else {
						dbTables[shemaTables[i].Schema] = []string{shemaTables[i].Name}
					}
				}
			} else {
				op1 := op.Table("mysql.db").Select("distinct(replace(db,'\\\\','')) as db").Where("user = ?", username)
				shemaTables := []SchemaTable{}
				err1 := op.Table("information_schema.TABLES").Select([]string{"table_name as TABLE_NAME", "table_schema as TABLE_SCHEMA"}).Where("table_schema in (?) and TABLE_TYPE = ?", op1, "BASE TABLE").Find(&shemaTables).Error
				if err1 == nil {
					for i := range shemaTables {
						if schemas, ok := dbTables[shemaTables[i].Schema]; ok {
							dbTables[shemaTables[i].Schema] = append(schemas, shemaTables[i].Name)
						} else {
							dbTables[shemaTables[i].Schema] = []string{shemaTables[i].Name}
						}
					}
				} else {
					app.Log().Error("数据库查询失败", "host", instances[i].Host, "port", instances[i].Port, "err", err1)
					return
				}
				privTables := []PrivTable{}
				err1 = op.Table("mysql.tables_priv").Select("table_name", "db").Where("user = ?", username).Find(&privTables).Error
				if err1 == nil {
					for i := range privTables {
						if dbs, ok := dbTables[privTables[i].DB]; ok {
							dbTables[privTables[i].DB] = append(dbs, privTables[i].Table)
						} else {
							dbTables[privTables[i].DB] = []string{privTables[i].Table}
						}
					}
				} else {
					app.Log().Error("数据库查询失败", "host", instances[i].Host, "port", instances[i].Port, "err", err1)
					return
				}
			}
			if len(dbTables) > 0 {
				DBs := []DBTable{}
				for k, v := range dbTables {
					DBs = append(DBs, DBTable{
						DB:     k,
						Tables: v,
					})
				}
				syncMap.Store(instances[i].CiAddress, UserPrivDBTable{
					DBs:     DBs,
					Project: instances[i].Name,
				})
			}
		}(i)
	}
	wg.Wait()
	syncMap.Range(func(k, v interface{}) bool {
		if keyName, ok := k.(string); ok {
			if keyValue, ok := v.(UserPrivDBTable); ok {
				userPrivDBTables[keyName] = keyValue
			}
		}
		// 出错不终止遍历
		return true
	})

	return
}

// TableColumn 表字段
type TableColumn struct {
	ColumnName    string `gorm:"column:COLUMN_NAME" json:"column_name"`
	ColumnType    string `gorm:"column:COLUMN_TYPE" json:"column_type"`
	ColumnComment string `gorm:"column:COLUMN_COMMENT" json:"column_comment"`
}

// GetDBTableColumns 获取数据库表字段类型
func GetDBTableColumns(ciAdress, dbName, table string) (columns map[string]interface{}, err error) {
	instance := Instance{}
	err = app.DB().Select(
		"id", "name", "host", "port", "username", "password", "ci_address",
	).Where("ci_address = ?", ciAdress).Take(&instance).Error
	if err != nil {
		return
	}
	columns = map[string]interface{}{}
	password, _ := utils.Base64Decode(instance.Password)

	var op *gorm.DB
	if strings.Contains(instance.Name, "starrocks") {
		op, err = db.NewMySQLDBConnect(db.MysqlConnParmas{
			Host:     instance.Host,
			Port:     instance.Port,
			User:     instance.Username,
			Password: password,
			DB:       "information_schema",
		}, nil)
	} else {
		op, err = db.NewMySQLDBConnect(db.MysqlConnParmas{
			Host:     instance.Host,
			Port:     instance.Port,
			User:     instance.Username,
			Password: password,
			DB:       "information_schema",
		}, nil)
	}
	if err != nil {
		return
	}
	tableColumns := []TableColumn{}
	err = op.Table("information_schema.COLUMNS").Select("COLUMN_NAME", "COLUMN_TYPE", "COLUMN_COMMENT").
		Where("TABLE_SCHEMA = ? and TABLE_NAME = ?", dbName, table).Find(&tableColumns).Error
	if err != nil {
		return
	}
	for i := range tableColumns {
		columns[tableColumns[i].ColumnName] = tableColumns[i].ColumnType + " | " + tableColumns[i].ColumnComment
	}
	return
}

// DBTableComment 表备注
type DBTableComment struct {
	Schema  string `gorm:"column:TABLE_SCHEMA" json:"schema"`
	Name    string `gorm:"column:TABLE_NAME" json:"name"`
	Comment string `gorm:"column:TABLE_COMMENT" json:"comment"`
}

// 获取数据库表备注
func GetDBTableComments(ciAdress, dbName, table string) (dbTables []DBTableComment, err error) {
	instance := Instance{}
	err = app.DB().Select(
		"id", "name", "host", "port", "username", "password", "ci_address",
	).Where("ci_address = ?", ciAdress).Take(&instance).Error
	if err != nil {
		return
	}
	password, _ := utils.Base64Decode(instance.Password)
	var op *gorm.DB
	if strings.Contains(instance.Name, "starrocks") {
		op, err = db.NewMySQLDBConnect(db.MysqlConnParmas{
			Host:     instance.Host,
			Port:     instance.Port,
			User:     instance.Username,
			Password: password,
			DB:       "information_schema",
		}, nil)
	} else {
		op, err = db.NewMySQLDBConnect(db.MysqlConnParmas{
			Host:     instance.Host,
			Port:     instance.Port,
			User:     instance.Username,
			Password: password,
			DB:       "information_schema",
		}, nil)
	}
	if err != nil {
		return
	}
	err = op.Table("information_schema.TABLES").Select("COLUMN_NAME", "COLUMN_TYPE", "COLUMN_COMMENT").Where(
		"TABLE_SCHEMA = ? AND TABLE_NAME = ? AND TABLE_SCHEMA not in (?) AND TABLE_TYPE = ?",
		dbName, table, []string{"sys", "_statistics_", "performance_schema", "mysql", "information_schema"}, "BASE TABLE",
	).Find(&dbTables).Error
	return
}

// Database 数据库信息
type Database struct {
	Name string `gorm:"column:name" json:"database_name"`
}

// InstanceDatabase 实例库名信息
type InstanceDatabase struct {
	Name      string     `gorm:"column:name" json:"instance_name"`
	Host      string     `gorm:"column:host" json:"host"`
	Port      uint       `gorm:"column:port" json:"port"`
	Databases []Database `gorm:"-" json:"databases"`
}

// 获取用户有权限的表
func GetCIInstancesDatabases() (instanceDatabases []InstanceDatabase, err error) {
	instances := []Instance{}
	err = app.DB().Select(
		"id", "name", "host", "port", "username", "password", "ci_address",
	).Where("ci_address != ?", "").Find(&instances).Error
	if err != nil {
		return
	}
	var wg sync.WaitGroup
	for i := range instances {
		wg.Add(1)
		go func(i int) {
			defer wg.Done()
			password, _ := utils.Base64Decode(instances[i].Password)
			var op *gorm.DB
			var err error
			if strings.Contains(instances[i].Name, "starrocks") {
				op, err = db.NewMySQLDBConnect(db.MysqlConnParmas{
					Host:     instances[i].Host,
					Port:     instances[i].Port,
					User:     instances[i].Username,
					Password: password,
					DB:       "information_schema",
				}, nil)
			} else {
				op, err = db.NewMySQLDBConnect(db.MysqlConnParmas{
					Host:     instances[i].Host,
					Port:     instances[i].Port,
					User:     instances[i].Username,
					Password: password,
					DB:       "information_schema",
				}, nil)
			}
			// op, err := db.NewDB(instances[i].Host, strconv.Itoa(int(instances[i].Port)), instances[i].AdminUser, password, "mysql", true)
			if err != nil {
				app.Log().Error("连接数据库失败", "host", instances[i].Host, "port", instances[i].Port, "err", err)
				return
			}
			defer func() {
				newDBOP, err1 := op.DB()
				if err1 == nil {
					newDBOP.Close()
				}
			}()
			databases := []Database{}
			err = op.Table("information_schema.SCHEMATA").Select("distinct(replace(SCHEMA_NAME,'\\\\','')) as name").
				Where("SCHEMA_NAME NOT IN (?)", []string{"sys", "mysql", "information_schema", "performance_schema"}).Find(&databases).Error
			if err == nil {
				instanceDatabases = append(instanceDatabases, InstanceDatabase{
					Name: instances[i].Name, Host: instances[i].Host, Port: instances[i].Port, Databases: databases,
				})
			}
		}(i)
	}
	wg.Wait()
	return
}

// Table 表
type TableColumns struct {
	Name    string   `gorm:"column:TABLE_NAME" json:"table_name"`
	Comment string   `gorm:"column:TABLE_COMMENT" json:"table_comment"`
	Columns []Column `gorm:"-" json:"fields"`
}

// 获取用户有权限的表
func GetCIInstanceDatabaseTables(host string, port uint, dbname string) (tables []TableColumns, err error) {
	instance := Instance{}
	err = app.DB().Select(
		"id", "name", "host", "port", "username", "password", "ci_address",
	).Where("ci_address != ? AND host = ? AND port = ?", "", host, port).Take(&instance).Error
	if err != nil {
		return
	}
	password, _ := utils.Base64Decode(instance.Password)
	var op *gorm.DB
	if strings.Contains(instance.Name, "starrocks") {
		op, err = db.NewMySQLDBConnect(db.MysqlConnParmas{
			Host:     instance.Host,
			Port:     instance.Port,
			User:     instance.Username,
			Password: password,
			DB:       "information_schema",
		}, nil)
	} else {
		op, err = db.NewMySQLDBConnect(db.MysqlConnParmas{
			Host:     instance.Host,
			Port:     instance.Port,
			User:     instance.Username,
			Password: password,
			DB:       "information_schema",
		}, nil)
	}
	if err != nil {
		app.Log().Error("连接数据库失败", "host", instance.Host, "port", instance.Port, "err", err)
		return
	}
	defer func() {
		newDBOP, err1 := op.DB()
		if err1 == nil {
			newDBOP.Close()
		}
	}()
	err = op.Table("information_schema.TABLES").Select(
		[]string{"replace(TABLE_NAME,'\\\\','') as TABLE_NAME", "TABLE_COMMENT"},
	).Where("TABLE_SCHEMA = ? AND TABLE_TYPE = ?", dbname, "BASE TABLE").Find(&tables).Error
	if err != nil {
		return
	}
	for i := range tables {
		err = op.Table("information_schema.COLUMNS").Select(
			"COLUMN_NAME", "DATA_TYPE", "COLUMN_COMMENT",
		).Where("TABLE_SCHEMA = ? AND TABLE_NAME = ?", dbname, tables[i].Name).Find(&tables[i].Columns).Error
		if err != nil {
			return
		}
	}
	return
}
