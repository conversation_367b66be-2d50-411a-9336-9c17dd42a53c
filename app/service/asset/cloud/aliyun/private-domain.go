package aliyun

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/asset/cloud"
	"cmdb/pkg/utils"
	"errors"
	"fmt"
	"time"

	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/requests"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/pvtz"
	"gorm.io/gorm"
)

func (a Account) syncPrivateDomains() (result cloud.SyncResults, err error) {
	r, err := a.syncPagePrivateDomains(1)
	sr := cloud.SyncResult{
		Key: "账号：" + a.Name,
	}
	if err == nil {
		sr.Result = "同步成功"
		// 回收过期主机
		err = a.ExpiredPrivateDomains(time.Now().Add(-10 * time.Minute))
		if err != nil {
			sr.Result = "同步成功，但是回收 内网域名 失败" + err.Error()
		}
	} else {
		sr.Result = r + " " + err.Error()
	}
	result = append(result, sr)
	return
}

func (a Account) syncPagePrivateDomains(startPage int) (result string, err error) {
	accessSecret, _ := utils.Base64Decode(a.AccessSecret)
	client, err := pvtz.NewClientWithAccessKey("cn-beijing", a.AccessKey, accessSecret)
	if err != nil {
		result = "创建客户端失败"
		return
	}
	request := pvtz.CreateDescribeZonesRequest()
	request.Scheme = "https"
	request.PageSize = requests.NewInteger(100)
	if startPage < 1 {
		startPage = 1
	}
	request.PageNumber = requests.NewInteger(startPage)
	response, err := client.DescribeZones(request)
	if err != nil {
		result = fmt.Sprintf("请求失败 状态码：%d", response.GetHttpStatus())
		return
	}
	// 保存
	now := time.Now()
	for _, zone := range response.Zones.Zone {
		exist := asset.PrivateDomain{}
		err = app.DB().Where("domain_id = ? AND account_id = ?", zone.ZoneId, a.ID).Take(&exist).Error
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = app.DB().Create(&asset.PrivateDomain{
				AccountID:       a.ID,
				Name:            zone.ZoneName,
				DomainID:        zone.ZoneId,
				CloudType:       a.CloudType,
				RecordCount:     int64(zone.RecordCount),
				ResourceGroupID: zone.ResourceGroupId,
				CreatedAt:       time.UnixMilli(zone.CreateTimestamp),
				UpdatedAt:       time.UnixMilli(zone.UpdateTimestamp),
				SyncTime:        now,
				Remark:          zone.Remark,
			}).Error
		} else if err == nil {
			updateMap := map[string]any{
				"sync_time": now,
			}
			if exist.Name != zone.ZoneName {
				updateMap["name"] = zone.ZoneName
			}
			if exist.Remark != zone.Remark {
				updateMap["remark"] = zone.Remark
			}
			if exist.RecordCount != int64(zone.RecordCount) {
				updateMap["record_count"] = int64(zone.RecordCount)
			}
			if exist.ResourceGroupID != zone.ResourceGroupId {
				updateMap["resource_group_id"] = zone.ResourceGroupId
			}
			if exist.UpdatedAt != time.UnixMilli(zone.UpdateTimestamp) {
				updateMap["updated_at"] = time.UnixMilli(zone.UpdateTimestamp)
			}
			if exist.CreatedAt != time.UnixMilli(zone.CreateTimestamp) {
				updateMap["created_at"] = time.UnixMilli(zone.CreateTimestamp)
			}

			err = app.DB().Model(&exist).Updates(updateMap).Error
		}
		if err != nil {
			return
		}
	}
	// 分页查询
	if response.TotalItems > (startPage+1)*100 {
		result, err = a.syncPagePrivateDomains(startPage + 1)
	}
	return
}
