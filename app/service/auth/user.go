package auth

import (
	"bytes"
	"cmdb/app"
	"cmdb/pkg/db"
	"cmdb/pkg/utils"
	"encoding/json"
	"errors"
	"time"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// User 系统用户
type User struct {
	ID             uint           `gorm:"column:id;primaryKey;comment:主键" json:"id"`
	Email          string         `gorm:"type:varchar(255);uniqueIndex;column:email;comment:邮箱" json:"email"`
	Username       string         `gorm:"type:varchar(255);column:username;index;comment:用户名" json:"username"`
	Name           string         `gorm:"type:varchar(255);column:name;comment:姓名" json:"name"`
	Password       string         `gorm:"type:varchar(255);column:password;comment:密码" json:"-"`
	SN             string         `gorm:"type:varchar(255);column:sn;comment:SN" json:"sn"`
	Phone          string         `gorm:"type:varchar(255);column:phone" json:"phone"`
	LeaderID       uint           `gorm:"column:leader_id;comment:领导ID" json:"leader_id"`
	ManualLeaderID uint           `grom:"column:manual_leader_id;comment:手动指定的领导ID" json:"manual_leader_id"`
	IsDisabled     bool           `gorm:"column:is_disabled;comment:是否禁用：0 表示启用，其他禁用" json:"is_disabled"`
	IsAdmin        bool           `gorm:"column:is_admin;index;comment:是否管理员 " json:"is_admin"`
	Remark         string         `gorm:"type:varchar(255);column:remark;comment:备注" json:"remark"`
	CreatedAt      time.Time      `gorm:"column:created_at;index;comment:创建时间" json:"created_at"`
	SyncedAt       *time.Time     `gorm:"column:synced_at;index;comment:同步时间" json:"synced_at"`
	LastLoginTime  *time.Time     `gorm:"column:last_login_time;index;comment:最后登录时间" json:"last_login_time"`
	Departments    []Department   `gorm:"many2many:auth_user_departments" json:"departments"`
	Groups         []Group        `gorm:"many2many:auth_user_groups" json:"groups"`
	Roles          datatypes.JSON `gorm:"column:roles;comment:角色" json:"roles"`
	Leader         *User          `gorm:"-" json:"leader,omitempty"`
}

// TableName 表名
func (User) TableName() string {
	return "auth_users"
}

// GetUsersParams 分页获取用户列表参数
type GetUsersParams struct {
	Offset     int
	Limit      int
	Keyword    string
	IsAdmin    *bool
	IsDisabled *bool
	GroupID    *int
}

// GetUsers 分页获取用户列表
func GetUsers(parmas GetUsersParams) (count int64, users []User, err error) {
	dbop := app.DB() // 使用数据库操作对象
	// 根据是否是管理员进行查询过滤
	if parmas.IsAdmin != nil {
		dbop = dbop.Where("is_admin = ?", parmas.IsAdmin)
	}
	// 根据是否禁用进行查询过滤
	if parmas.IsDisabled != nil {
		dbop = dbop.Where("is_disabled = ?", parmas.IsDisabled)
	}
	// 根据关键字进行查询过滤，关键字可以匹配用户名、备注和电子邮件
	if parmas.Keyword != "" {
		dbop = db.MLike(dbop, parmas.Keyword, "name", "remark", "email")
	}
	if parmas.GroupID != nil {
		dbop = dbop.Where("id IN (SELECT user_id FROM auth_user_groups WHERE group_id = ?)", parmas.GroupID)
	}
	// 执行查询操作，计算符合条件的用户总数，并按指定的排序、偏移和限制条件获取用户列表
	err = dbop.Model(&User{}).Count(&count).
		Order("last_login_time DESC ,id ").Offset(parmas.Offset).Limit(parmas.Limit).Find(&users).Error
	// 如果查询到用户，为每个用户加载其直接上级、所属组和部门信息
	if err != nil {
		return
	}
	if len(users) > 0 {
		for i := range users {
			if u, err1 := users[i].GetLeader(); err1 == nil {
				users[i].Leader = &u
			}
			users[i].Groups, _ = users[i].GetGroups()
			users[i].Departments, _ = users[i].GetDepartments()
		}
	}
	return
}

// GetAllUsers 获取所有用户,filterDisabled为true时，未禁用的用户返回所有用户，为false时，返回所有用户
func GetAllUsers(filterDisabled bool) (users []User, err error) {
	if filterDisabled {
		err = app.DB().Where("is_disabled = ?", false).Order("username").Find(&users).Error
	} else {
		err = app.DB().Order("username").Find(&users).Error
	}
	return
}

// Auth 函数用于进行用户邮箱和密码的认证。
func Auth(email, password string) (u User, err error) {
	// 在数据库中查询指定邮箱的用户信息
	err = app.DB().Where("email = ? ", email).Take(&u).Error
	if err != nil {
		return
	}

	// 对比密码，如果密码不匹配，则认证失败
	if !utils.ComparePassword(password, u.Password) {
		// 校验密码失败
		err = ErrAuthFail
		return
	}

	// 检查用户是否被禁用，如果被禁用，则返回错误信息
	if u.IsDisabled {
		// 用户被禁用
		err = ErrUserDisabled
	}
	return
}

// UpdateLoginTime 更新用户登陆时间
func (u User) UpdateLoginTime() (err error) {
	err = app.DB().Model(&u).Update("last_login_time", time.Now()).Error
	return
}

// GetUserByUsername 通过邮件获取用户
func GetUserByUsername(username string) (u User, err error) {
	// 通过邮件地址从数据库中查询用户信息
	err = app.DB().Where("username = ?", username).Take(&u).Error
	return
}

// GetUserByID 通过用户ID获取用户信息
func GetUserByID(id int) (u User, err error) {
	// 通过ID查询用户信息并保存到u变量中
	err = app.DB().Where("id = ?", id).Take(&u).Error
	return
}

// GetUsersByIDs 通过提供的用户ID列表获取相应的用户信息。
func GetUsersByIDs(ids ...uint) (us []User, err error) {
	// 当提供至少一个ID时，尝试从数据库中检索对应的用户信息
	if len(ids) > 0 {
		err = app.DB().Where("id IN (?)", ids).Find(&us).Error
	}
	return
}

func GetUserNameByID(id int) (name string, err error) {
	err = app.DB().Model(&User{}).Select("name").Where("id = ?", id).Limit(1).Scan(&name).Error
	return
}

func GetUserNamesByIDs(ids ...uint) (name []string, err error) {
	name = make([]string, 0)
	if len(ids) > 0 {
		err = app.DB().Model(&User{}).Select("name").Where("id IN ?", ids).Scan(&name).Error
	}
	return
}

// GetLeader 获取领导
func (u User) GetLeader() (leader User, err error) {
	if u.ManualLeaderID > 0 {
		leader, err = GetUserByID(int(u.ManualLeaderID))
		if err != nil && err != gorm.ErrRecordNotFound {
			// 查询错误
			return
		} else if err == nil {
			return
		} else {
			err = nil
		}
	}
	if u.LeaderID > 0 {
		leader, err = GetUserByID(int(u.LeaderID))
		if err == nil {
			return
		}
	}
	if u.LeaderID == 0 {
		err = errors.New("没有上级领导")
	}
	return
}
func (u User) GetGroups() (groups []Group, err error) {
	err = app.DB().Model(&u).Association("Groups").Find(&groups)
	return
}
func (u User) GetDepartments() (deparments []Department, err error) {
	err = app.DB().Model(&u).Where("family = ?", "team").Association("Departments").Find(&deparments)
	return
}

// GetLeader 获取领导
func (u User) OASetLeader(leader User) (err error) {
	exist, err1 := GetUserByUsername(leader.Username)
	if errors.Is(err1, gorm.ErrRecordNotFound) {
		err1 = app.DB().Create(&leader).Error
		if err1 != nil {
			err = err1
			return
		}
		exist, _ = GetUserByUsername(leader.Username)
	}
	err = app.DB().Model(&u).Update("leader_id", exist.ID).Error
	return
}

// OASetDeparment 设置用户部门
func (u User) OASetDeparment(ds ...Department) (err error) {
	// 先查询记录
	for i := range ds {
		ds[i].Gen()
	}
	// 替换关联关系
	err = app.DB().Model(&u).Association("Departments").Replace(&ds)
	return
}

// 定义更新用户表单结构体
type UpdateUserForm struct {
	ManualLeaderID uint   `json:"manual_leader_id"`
	Roles          []Role `json:"roles"`
	Phone          string `json:"phone" binding:"max=255"`
	IsAdmin        bool   `json:"is_admin"`
	IsDisabled     bool   `json:"is_disabled"`
}

// 更新用户信息
func (u User) Update(form UpdateUserForm) error {
	updateMap := map[string]any{}
	if form.Phone != u.Phone {
		updateMap["phone"] = form.Phone
	}
	if form.IsAdmin != u.IsAdmin {
		updateMap["is_admin"] = form.IsAdmin
	}
	if form.IsDisabled != u.IsDisabled {
		updateMap["is_disabled"] = form.IsDisabled
	}
	if form.ManualLeaderID != u.ManualLeaderID {
		updateMap["manual_leader_id"] = form.ManualLeaderID
	}
	rolesBytes, _ := json.Marshal(form.Roles)
	if !bytes.Equal(u.Roles, rolesBytes) {
		updateMap["roles"] = rolesBytes
	}
	if len(updateMap) == 0 {
		return nil
	}
	// 使用数据库模型更新用户信息
	return app.DB().Model(&u).Updates(updateMap).Error
}

type BatGroupUsersFrom struct {
	UsersIDs  []uint `json:"users_ids"`
	GroupsIDs []uint `json:"groups_ids"`
	OP        string `json:"op"`
}

func (form BatGroupUsersFrom) Do() (err error) {
	switch form.OP {
	case "append":
		var users []User
		users, err = GetUsersByIDs(form.UsersIDs...)
		if err != nil {
			return err
		}
		var groups []Group
		groups, err = GetGroupsByIDs(form.GroupsIDs...)
		if err != nil {
			return err
		}
		for i := range users {
			err = app.DB().Model(&users[i]).Association("Groups").Append(&groups)
			if err != nil {
				return
			}
		}
	case "delete":
		var users []User
		users, err = GetUsersByIDs(form.UsersIDs...)
		if err != nil {
			return err
		}
		var groups []Group
		groups, err = GetGroupsByIDs(form.GroupsIDs...)
		if err != nil {
			return err
		}
		for i := range users {
			err = app.DB().Model(&users[i]).Association("Groups").Delete(&groups)
			if err != nil {
				return
			}
		}
	case "replace":
		var users []User
		users, err = GetUsersByIDs(form.UsersIDs...)
		if err != nil {
			return err
		}
		var groups []Group
		groups, err = GetGroupsByIDs(form.GroupsIDs...)
		if err != nil {
			return err
		}
		for i := range users {
			err = app.DB().Model(&users[i]).Association("Groups").Replace(&groups)
			if err != nil {
				return
			}
		}
	case "clear":
		var users []User
		users, err = GetUsersByIDs(form.UsersIDs...)
		if err != nil {
			return err
		}
		for i := range users {
			err = app.DB().Model(&users[i]).Association("Groups").Clear()
			if err != nil {
				return
			}
		}
	default:
		return errors.New("op is not support")
	}
	return
}

type UpdatePersonalForm struct {
	Phone string `json:"phone" binding:"max=255"`
}

func (u User) UpdateUserPhone(phone string) error {
	// 更新用户的电话信息
	return app.DB().Model(&u).Update("phone", phone).Error
}

type UpdatePersonalPasswordFrom struct {
	OldPassword string `json:"old_password" binding:"required"`
	Password    string `json:"password" binding:"required"`
}

func (u User) UpdatePersonalPassword(form UpdatePersonalPasswordFrom) error {
	if !utils.ComparePassword(form.OldPassword, u.Password) {
		return errors.New("旧密码不正确")
	}
	u.Password = form.Password
	return app.DB().Model(&u).Update("password", u.Password).Error
}
