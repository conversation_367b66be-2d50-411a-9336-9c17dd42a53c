package n9e

import (
	"database/sql"
	"fmt"
	"strings"
	"time"
)

// AlertHisEvent 告警历史事件表结构
type AlertHisEvent struct {
	ID               uint64  `gorm:"column:id;primaryKey;autoIncrement;comment:主键ID" json:"id"`
	IsRecovered      bool    `gorm:"column:is_recovered;comment:是否已恢复" json:"is_recovered"`
	Cate             string  `gorm:"column:cate;type:varchar(128);not null;comment:分类" json:"cate"`
	DatasourceID     int64   `gorm:"column:datasource_id;not null;default:0;comment:数据源ID" json:"datasource_id"`
	Cluster          string  `gorm:"column:cluster;type:varchar(128);not null;comment:集群" json:"cluster"`
	GroupID          uint64  `gorm:"column:group_id;not null;comment:业务组ID" json:"group_id"`
	GroupName        string  `gorm:"column:group_name;type:varchar(255);not null;default:'';comment:业务组名称" json:"group_name"`
	Hash             string  `gorm:"column:hash;type:varchar(64);not null;comment:规则ID+向量主键" json:"hash"`
	RuleID           uint64  `gorm:"column:rule_id;not null;comment:规则ID" json:"rule_id"`
	RuleName         string  `gorm:"column:rule_name;type:varchar(255);not null;comment:规则名称" json:"rule_name"`
	RuleNote         string  `gorm:"column:rule_note;type:varchar(2048);not null;default:'alert rule note';comment:规则备注" json:"rule_note"`
	RuleProd         string  `gorm:"column:rule_prod;type:varchar(255);not null;default:'';comment:规则产品" json:"rule_prod"`
	RuleAlgo         string  `gorm:"column:rule_algo;type:varchar(255);not null;default:'';comment:规则算法" json:"rule_algo"`
	Severity         int8    `gorm:"column:severity;not null;comment:严重程度:0:Emergency 1:Warning 2:Notice" json:"severity"`
	PromForDuration  int     `gorm:"column:prom_for_duration;not null;comment:prometheus for持续时间,单位:s" json:"prom_for_duration"`
	PromQL           string  `gorm:"column:prom_ql;type:varchar(8192);not null;comment:promql查询语句" json:"prom_ql"`
	PromEvalInterval int     `gorm:"column:prom_eval_interval;not null;comment:评估间隔" json:"prom_eval_interval"`
	Callbacks        string  `gorm:"column:callbacks;type:varchar(255);not null;default:'';comment:回调地址" json:"callbacks"`
	RunbookURL       *string `gorm:"column:runbook_url;type:varchar(255);comment:运行手册URL" json:"runbook_url"`
	NotifyRecovered  bool    `gorm:"column:notify_recovered;not null;comment:是否通知恢复" json:"notify_recovered"`
	NotifyChannels   string  `gorm:"column:notify_channels;type:varchar(255);not null;default:'';comment:通知渠道" json:"notify_channels"`
	NotifyGroups     string  `gorm:"column:notify_groups;type:varchar(255);not null;default:'';comment:通知组" json:"notify_groups"`
	NotifyCurNumber  int     `gorm:"column:notify_cur_number;not null;default:0;comment:当前通知次数" json:"notify_cur_number"`
	TargetIdent      string  `gorm:"column:target_ident;type:varchar(191);not null;default:'';comment:目标标识" json:"target_ident"`
	TargetNote       string  `gorm:"column:target_note;type:varchar(191);not null;default:'';comment:目标备注" json:"target_note"`
	FirstTriggerTime *int64  `gorm:"column:first_trigger_time;comment:首次触发时间" json:"first_trigger_time"`
	TriggerTime      int64   `gorm:"column:trigger_time;not null;comment:触发时间" json:"trigger_time"`
	TriggerValue     string  `gorm:"column:trigger_value;type:varchar(255);not null;comment:触发值" json:"trigger_value"`
	RecoverTime      int64   `gorm:"column:recover_time;not null;default:0;comment:恢复时间" json:"recover_time"`
	LastEvalTime     int64   `gorm:"column:last_eval_time;not null;default:0;comment:最后评估时间" json:"last_eval_time"`
	Tags             string  `gorm:"column:tags;type:varchar(1024);not null;default:'';comment:标签" json:"tags"`
	Annotations      string  `gorm:"column:annotations;type:text;not null;comment:注释" json:"annotations"`
	RuleConfig       string  `gorm:"column:rule_config;type:text;not null;comment:规则配置" json:"rule_config"`
	OriginalTags     *string `gorm:"column:original_tags;type:text;comment:原始标签" json:"original_tags"`
}

// TableName 返回表名
func (AlertHisEvent) TableName() string {
	return "n9e.alert_his_event"
}

// AlertCurEvent 当前告警事件表结构
type AlertCurEvent struct {
	ID               uint64  `gorm:"column:id;primaryKey;comment:使用alert_his_event.id" json:"id"`
	Cate             string  `gorm:"column:cate;type:varchar(128);not null;comment:分类" json:"cate"`
	DatasourceID     int64   `gorm:"column:datasource_id;not null;default:0;comment:数据源ID" json:"datasource_id"`
	Cluster          string  `gorm:"column:cluster;type:varchar(128);not null;comment:集群" json:"cluster"`
	GroupID          uint64  `gorm:"column:group_id;not null;comment:业务组ID" json:"group_id"`
	GroupName        string  `gorm:"column:group_name;type:varchar(255);not null;default:'';comment:业务组名称" json:"group_name"`
	Hash             string  `gorm:"column:hash;type:varchar(64);not null;comment:规则ID+向量主键" json:"hash"`
	RuleID           uint64  `gorm:"column:rule_id;not null;comment:规则ID" json:"rule_id"`
	RuleName         string  `gorm:"column:rule_name;type:varchar(255);not null;comment:规则名称" json:"rule_name"`
	RuleNote         string  `gorm:"column:rule_note;type:varchar(2048);not null;default:'alert rule note';comment:规则备注" json:"rule_note"`
	RuleProd         string  `gorm:"column:rule_prod;type:varchar(255);not null;default:'';comment:规则产品" json:"rule_prod"`
	RuleAlgo         string  `gorm:"column:rule_algo;type:varchar(255);not null;default:'';comment:规则算法" json:"rule_algo"`
	Severity         int8    `gorm:"column:severity;not null;comment:严重程度:0:Emergency 1:Warning 2:Notice" json:"severity"`
	PromForDuration  int     `gorm:"column:prom_for_duration;not null;comment:prometheus for持续时间,单位:s" json:"prom_for_duration"`
	PromQL           string  `gorm:"column:prom_ql;type:varchar(8192);not null;comment:promql查询语句" json:"prom_ql"`
	PromEvalInterval int     `gorm:"column:prom_eval_interval;not null;comment:评估间隔" json:"prom_eval_interval"`
	Callbacks        string  `gorm:"column:callbacks;type:varchar(255);not null;default:'';comment:回调地址" json:"callbacks"`
	RunbookURL       *string `gorm:"column:runbook_url;type:varchar(255);comment:运行手册URL" json:"runbook_url"`
	NotifyRecovered  bool    `gorm:"column:notify_recovered;not null;comment:是否通知恢复" json:"notify_recovered"`
	NotifyChannels   string  `gorm:"column:notify_channels;type:varchar(255);not null;default:'';comment:通知渠道" json:"notify_channels"`
	NotifyGroups     string  `gorm:"column:notify_groups;type:varchar(255);not null;default:'';comment:通知组" json:"notify_groups"`
	NotifyRepeatNext int64   `gorm:"column:notify_repeat_next;not null;default:0;comment:下次通知时间戳" json:"notify_repeat_next"`
	NotifyCurNumber  int     `gorm:"column:notify_cur_number;not null;default:0;comment:当前通知次数" json:"notify_cur_number"`
	TargetIdent      string  `gorm:"column:target_ident;type:varchar(191);not null;default:'';comment:目标标识" json:"target_ident"`
	TargetNote       string  `gorm:"column:target_note;type:varchar(191);not null;default:'';comment:目标备注" json:"target_note"`
	FirstTriggerTime *int64  `gorm:"column:first_trigger_time;comment:首次触发时间" json:"first_trigger_time"`
	TriggerTime      int64   `gorm:"column:trigger_time;not null;comment:触发时间" json:"trigger_time"`
	TriggerValue     string  `gorm:"column:trigger_value;type:varchar(255);not null;comment:触发值" json:"trigger_value"`
	Annotations      string  `gorm:"column:annotations;type:text;not null;comment:注释" json:"annotations"`
	RuleConfig       string  `gorm:"column:rule_config;type:text;not null;comment:规则配置" json:"rule_config"`
	Tags             string  `gorm:"column:tags;type:varchar(1024);not null;default:'';comment:标签" json:"tags"`
	OriginalTags     *string `gorm:"column:original_tags;type:text;comment:原始标签" json:"original_tags"`
}

// TableName 返回表名
func (AlertCurEvent) TableName() string {
	return "n9e.alert_cur_event"
}

// AlertStatistics 告警统计结果
type AlertStatistics struct {
	TotalCount     int64   `json:"total_count"`     // 告警总数
	EmergencyCount int64   `json:"emergency_count"` // 紧急告警数量
	WarningCount   int64   `json:"warning_count"`   // 警告告警数量
	NoticeCount    int64   `json:"notice_count"`    // 通知告警数量
	RecoveredCount int64   `json:"recovered_count"` // 已恢复告警数量
	RecoveryRate   float64 `json:"recovery_rate"`   // 恢复率
	AvgDuration    float64 `json:"avg_duration"`    // 平均持续时间(秒)
}

// InstanceAlertStatistics 实例维度告警统计
type InstanceAlertStatistics struct {
	TargetIdent    string  `json:"target_ident"`    // 实例标识
	TargetNote     string  `json:"target_note"`     // 实例备注
	TotalCount     int64   `json:"total_count"`     // 告警总数
	EmergencyCount int64   `json:"emergency_count"` // 紧急告警数量
	WarningCount   int64   `json:"warning_count"`   // 警告告警数量
	NoticeCount    int64   `json:"notice_count"`    // 通知告警数量
	RecoveredCount int64   `json:"recovered_count"` // 已恢复告警数量
	RecoveryRate   float64 `json:"recovery_rate"`   // 恢复率
	AvgDuration    float64 `json:"avg_duration"`    // 平均持续时间(秒)
}

// RuleAlertStatistics 规则维度告警统计
type RuleAlertStatistics struct {
	RuleID            uint64  `json:"rule_id"`            // 规则ID
	RuleName          string  `json:"rule_name"`          // 规则名称
	RuleNote          string  `json:"rule_note"`          // 规则备注
	TriggerCount      int64   `json:"trigger_count"`      // 触发次数
	AffectedInstances int64   `json:"affected_instances"` // 影响的实例数
	EmergencyCount    int64   `json:"emergency_count"`    // 紧急告警数量
	WarningCount      int64   `json:"warning_count"`      // 警告告警数量
	NoticeCount       int64   `json:"notice_count"`       // 通知告警数量
	RecoveredCount    int64   `json:"recovered_count"`    // 已恢复告警数量
	RecoveryRate      float64 `json:"recovery_rate"`      // 恢复率
	AvgDuration       float64 `json:"avg_duration"`       // 平均持续时间(秒)
}

// ClusterAlertStatistics 集群维度告警统计
type ClusterAlertStatistics struct {
	Cluster           string  `json:"cluster"`            // 集群名称
	TotalCount        int64   `json:"total_count"`        // 告警总数
	EmergencyCount    int64   `json:"emergency_count"`    // 紧急告警数量
	WarningCount      int64   `json:"warning_count"`      // 警告告警数量
	NoticeCount       int64   `json:"notice_count"`       // 通知告警数量
	RecoveredCount    int64   `json:"recovered_count"`    // 已恢复告警数量
	RecoveryRate      float64 `json:"recovery_rate"`      // 恢复率
	AvgDuration       float64 `json:"avg_duration"`       // 平均持续时间(秒)
	AffectedInstances int64   `json:"affected_instances"` // 影响的实例数
}

// CurrentAlert 当前告警信息
type CurrentAlert struct {
	ID               uint64 `json:"id"`                 // 告警ID
	RuleID           uint64 `json:"rule_id"`            // 规则ID
	RuleName         string `json:"rule_name"`          // 规则名称
	RuleNote         string `json:"rule_note"`          // 规则备注
	Severity         int8   `json:"severity"`           // 严重程度
	SeverityText     string `json:"severity_text"`      // 严重程度文本
	TargetIdent      string `json:"target_ident"`       // 目标实例标识
	TargetNote       string `json:"target_note"`        // 目标实例备注
	TriggerTime      int64  `json:"trigger_time"`       // 触发时间
	TriggerTimeText  string `json:"trigger_time_text"`  // 触发时间文本
	TriggerValue     string `json:"trigger_value"`      // 触发值
	FirstTriggerTime *int64 `json:"first_trigger_time"` // 首次触发时间
	Cluster          string `json:"cluster"`            // 集群
	GroupID          uint64 `json:"group_id"`           // 业务组ID
	GroupName        string `json:"group_name"`         // 业务组名称
	Annotations      string `json:"annotations"`        // 告警描述
	Tags             string `json:"tags"`               // 标签
	NotifyCurNumber  int    `json:"notify_cur_number"`  // 当前通知次数
	Duration         int64  `json:"duration"`           // 持续时间(秒)
	DurationText     string `json:"duration_text"`      // 持续时间文本
}

// SeverityType 告警严重程度类型
type SeverityType int8

const (
	SeverityEmergency SeverityType = 0 // 紧急
	SeverityWarning   SeverityType = 1 // 警告
	SeverityNotice    SeverityType = 2 // 通知
)

// String 返回严重程度的字符串表示
func (s SeverityType) String() string {
	switch s {
	case SeverityEmergency:
		return "Emergency"
	case SeverityWarning:
		return "Warning"
	case SeverityNotice:
		return "Notice"
	default:
		return "Unknown"
	}
}

// GetGroupNameWithDefault 获取资源组名称，如果为空则返回默认值
func GetGroupNameWithDefault(groupName string) string {
	if groupName == "" {
		return "(未设置资源组)"
	}
	return groupName
}

// CalculateRecoveryRate 计算恢复率
func CalculateRecoveryRate(recoveredCount, totalCount int64) float64 {
	if totalCount == 0 {
		return 0.0
	}
	return float64(recoveredCount) / float64(totalCount) * 100.0
}

// CalculateAvgDuration 计算平均持续时间
// 参数：
//   - totalDuration: 数据库查询返回的平均持续时间（可能为NULL）
//   - count: 用于计算的记录数（这里实际上不需要再次除法，因为数据库已经计算了平均值）
//
// 返回：
//   - 平均持续时间（秒），如果无效则返回0.0
func CalculateAvgDuration(avgDuration sql.NullFloat64, count int64) float64 {
	// 检查数据库返回的平均值是否有效
	if !avgDuration.Valid {
		return 0.0
	}

	// 检查平均值是否为负数（异常情况）
	if avgDuration.Float64 < 0 {
		return 0.0
	}

	// 数据库已经计算了平均值，直接返回
	return avgDuration.Float64
}

// CalculateTotalAvgDuration 计算总的平均持续时间（当需要手动计算时使用）
// 参数：
//   - totalDuration: 总持续时间
//   - count: 记录数
//
// 返回：
//   - 平均持续时间（秒）
func CalculateTotalAvgDuration(totalDuration sql.NullFloat64, count int64) float64 {
	if !totalDuration.Valid || count == 0 {
		return 0.0
	}

	// 检查总持续时间是否为负数
	if totalDuration.Float64 < 0 {
		return 0.0
	}

	return totalDuration.Float64 / float64(count)
}

// FormatUnixTime 格式化Unix时间戳为可读字符串
func FormatUnixTime(timestamp int64) string {
	if timestamp == 0 {
		return ""
	}
	return time.Unix(timestamp, 0).Format("2006-01-02 15:04:05")
}

// FormatDuration 格式化持续时间为可读字符串
func FormatDuration(seconds int64) string {
	if seconds <= 0 {
		return "0秒"
	}

	duration := time.Duration(seconds) * time.Second

	days := int(duration.Hours()) / 24
	hours := int(duration.Hours()) % 24
	minutes := int(duration.Minutes()) % 60
	secs := int(duration.Seconds()) % 60

	var parts []string
	if days > 0 {
		parts = append(parts, fmt.Sprintf("%d天", days))
	}
	if hours > 0 {
		parts = append(parts, fmt.Sprintf("%d小时", hours))
	}
	if minutes > 0 {
		parts = append(parts, fmt.Sprintf("%d分钟", minutes))
	}
	if secs > 0 || len(parts) == 0 {
		parts = append(parts, fmt.Sprintf("%d秒", secs))
	}

	return strings.Join(parts, "")
}

// CalculateCurrentDuration 计算当前告警的持续时间
func CalculateCurrentDuration(triggerTime int64) int64 {
	if triggerTime <= 0 {
		return 0
	}
	now := time.Now().Unix()
	if now <= triggerTime {
		return 0
	}
	return now - triggerTime
}
