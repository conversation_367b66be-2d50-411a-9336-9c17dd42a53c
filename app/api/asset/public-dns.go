package asset

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"strconv"
	"strings"
	"sync"

	"github.com/gin-gonic/gin"
)

type PublicDomainRow struct {
	asset.PublicDomain
	Account string `json:"account"`
}

func GetPublicDomains(c *gin.Context) {
	page, _ := strconv.Atoi(c<PERSON>("page", "1"))
	limit, _ := strconv.Atoi(c<PERSON>("limit", "10"))
	offset := (page - 1) * limit
	var keyword, ip *string
	if k := strings.TrimSpace(c.Query("keyword")); k != "" {
		keyword = &k
	}
	if k := strings.TrimSpace(c.Query("ip")); k != "" {
		ip = &k
	}
	var accountID *int
	if aid, err1 := strconv.Atoi(c.Query("account_id")); err1 == nil {
		accountID = &aid
	}
	count, data, err := asset.GetPublicDomains(offset, limit, accountID, keyword, ip)
	if err != nil {
		app.Log().Error("获取私有域名列表失败", "err", err)
		app.FailedResponseMsg(c, "获取私有域名列表失败")
		return
	}
	// 将数据转换为新的结构体
	newData := make([]PublicDomainRow, len(data))
	if len(data) > 0 {
		tmpCloudAccountStore := sync.Map{}
		wg := sync.WaitGroup{}
		wg.Add(len(data))
		for i := range data {
			go func() {
				newData[i].PublicDomain = data[i]
				// 获取云账户名称
				if val, ok := tmpCloudAccountStore.Load(data[i].AccountID); ok {
					newData[i].Account, _ = val.(string)
				} else {
					account, err1 := asset.GetAccountNameByID(int(data[i].AccountID))
					if err1 == nil {
						newData[i].Account = account
						tmpCloudAccountStore.Store(data[i].AccountID, account)
					}
				}
				wg.Done()
			}()
		}
		wg.Wait()
	}
	app.SuccessResponseDataCount(c, newData, count)
}

func GetPublicDomainRecords(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	offset := (page - 1) * limit
	var keyword *string
	if k := strings.TrimSpace(c.Query("keyword")); k != "" {
		keyword = &k
	}
	domainID := strings.TrimSpace(c.Param("domain_id"))
	domain, err := asset.GetPublicDomainByDomainID(domainID)
	if err != nil {
		app.Log().Error("获取私有域名记录失败", "err", err)
		app.FailedResponseMsg(c, "获取私有域名记录失败")
		return
	}
	count, data, err := domain.GetRecords(offset, limit, keyword)
	if err != nil {
		app.Log().Error("获取私有域名记录失败", "err", err)
		app.FailedResponseMsg(c, "获取私有域名记录失败")
		return
	}
	app.SuccessResponseDataCount(c, data, count)

}
