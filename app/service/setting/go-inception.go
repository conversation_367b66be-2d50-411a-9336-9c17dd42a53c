package setting

import "strconv"

type GoInception struct {
	Host string
	Port int
	User string
}

func GetGoInceptionSetting() (goInception GoInception, err error) {
	goInception = GoInception{
		Host: GetSettingValue("goInception", "host"),
		User: GetSettingValue("goInception", "user"),
	}
	port, err := strconv.Atoi(GetSettingValue("goInception", "port"))
	if err != nil {
		port = 4000
	}
	goInception.Port = port
	return
}
