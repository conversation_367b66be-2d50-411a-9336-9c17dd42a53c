# 运维资产管理平台（cmdb）

`cmdb` 是一个功能完善的运维资产管理平台，用于管理和监控各种IT资源。通过cmdb，您可以轻松管理包括主机、网络设备、数据库、容器、云资源等在内的基础设施。

## 项目概述

cmdb 提供以下核心功能：

- 资产管理：全面管理物理和虚拟资产
- 监控集成：集成多种监控系统和数据源
- 工作流管理：流程化处理运维申请
- 自动化操作：支持批量任务和自动化运维
- 数据统计：多维度统计和分析功能

## 目录结构

```conf
.
├── app/              # API服务代码
├── cmd/              # 主程序入口
├── configs/          # 配置文件
├── docs/             # 文档
├── scripts/          # 脚本工具
└── test/             # 测试用例
```

## 安装部署

### 环境依赖

- Golang 1.24+
- MySQL 8.0+ 或 TiDB
- Prometheus (可选)
- Elasticsearch (可选)

### 快速安装

```bash
# 克隆仓库
git clone https://github.com/your-repo/cmdb.git
cd cmdb

# 安装依赖
go mod tidy

# 编译
go build -o cmdb .
```

## 配置

### 生成默认配置

```bash
./cmdb -default-config | tee app.ini
```

### 配置文件说明

```ini
[database]
host = "localhost"
port = 3306
user = "cmdb"
password = "cmdb"
database = "cmdb"
charset = "utf8mb4"

[server]
port = 8080
```

## 同步数据库

```bash
./cmdb -syncdb
```

## 启动服务

```bash
# 启动API服务
./cmdb

# 启动异步任务
./cmdb -async-task
```

## 使用文档

### API使用

#### API请求示例

```bash
curl -X POST http://localhost:8080/api/v1/assets \
  -H "Content-Type: application/json" \
  -d '{"name":"my-host","ip":"***********"}'
```

### 工作流管理

支持多种工作流类型：

- 主机变更
- 权限申请
- 数据库变更
- 网络变更

### 统计分析

提供多种统计视图：

- 资产统计
- 网络流量
- 资源使用率
- 故障统计

## 贡献指南

欢迎贡献代码！请遵循以下步骤：

1. Fork仓库
2. 创建功能分支
3. 提交代码
4. 创建Pull Request

## 联系方式

- 邮箱：<<EMAIL>>
- 项目地址：<https://gitlab.meiyou.com/yunwei/cmdb>
