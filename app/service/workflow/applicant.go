package workflow

import (
	"cmdb/app"
	"cmdb/app/service/auth"
	"cmdb/pkg/db"
)

// 定义申请人结构体
type Applicant struct {
	auth.User
}

// 获取申请人表名
func (a Applicant) TableName() string {
	return a.User.TableName()
}

// 根据ID获取申请人
func GetApplicantByID(id uint) (a Applicant, err error) {
	err = app.DB().Where("id = ?", id).Take(&a).Error
	return
}

// 创建申请人
func NewApplicant(u auth.User) (a Applicant, err error) {
	err = app.DB().Where("id = ?", u.ID).Take(&u).Error
	a = Applicant{
		User: u,
	}
	return
}

// 定义申请人表单接口
type ApplicantForm interface {
	Create(a Applicant, flow Flow, admin *auth.User) error
	Update(o Order) error
	check() error
	GenProcesses(sn string, a Applicant, admin *auth.User) (ps []Process, err error)
}

// 获取我的订单
func (a Applicant) GetMyOrders(offset, limit int, keyword *string, extType *OrderExtType, status *OrderStatus) (count int64, orders []Order, err error) {
	// 获取数据库操作对象
	dbop := app.DB()

	// 如果 extType 不为空，则添加 ext_type 条件
	if extType != nil {
		dbop = dbop.Where("ext_type = ?", *extType)
	}

	// 如果 status 不为空，则根据 status 的值添加条件
	if status != nil {
		switch *status {
		case 100:
			// 如果 status 为 100，添加特殊条件
			dbop = dbop.Where("status = ? AND NOT EXISTS (SELECT 1 FROM workflow_order_evaluations WHERE workflow_order_evaluations.sn = workflow_orders.sn)", CompletedStatus)
		default:
			// 其他情况，添加 status 条件
			dbop = dbop.Where("status = ?", *status)
		}
	}

	// 如果 keyword 不为空，则添加 keyword 条件
	if keyword != nil {
		dbop = db.MLike(dbop, *keyword, "title")
	}

	// 执行数据库查询操作，获取订单数量和订单列表
	err = dbop.Model(&Order{}).Where("applicant_id = ?", a.ID).Count(&count).
		Order("created_at DESC").Offset(offset).Limit(limit).Find(&orders).Error
	return
}

// 获取我的所有工单
func (a Applicant) GetMyAllOrders(extType *OrderExtType) (orders []Order, err error) {
	err = app.DB().Where("applicant_id = ?", a.ID).Where("ext_type = ?", extType).Find(&orders).Error
	return
}
