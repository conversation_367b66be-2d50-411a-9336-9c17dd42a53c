package auth

import (
	"cmdb/app"
	"cmdb/app/service/audit"
	"cmdb/app/service/auth"
	"encoding/json"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// X-Pomerium-Claim-Email
// X-Pomerium-Claim-Sub
// X-Pomerium-Claim-Name
//
//	type OAGatewayInfo struct {
//		// Aud          string `json:"aud"`
//		// Exp          int64  `json:"exp"`
//		// Iat          int64  `json:"iat"`
//		// Iss          string `json:"iss"`
//		// Jti          string `json:"jti"`
//		// Nbf          int64  `json:"nbf"`
//		// Programmatic bool   `json:"programmatic"`
//		Sub string `json:"sub"`
//		// Ver string `json:"ver"`
//	}
type OAGatewayRequest struct {
	OAGatway string `json:"oa_gateway"`
}

func OAGatewayLogin(c *gin.Context) {
	if c.Client<PERSON>() != "10.0.3.157" && c.Client<PERSON>() != "10.0.11.168" {
		app.FailedResponseMsg(c, "非法请求")
		return
	}
	// headerEmail := c.GetHeader("X-Pomerium-Claim-Email")
	headerSub := c.GetHeader("X-Pomerium-Claim-Sub")
	// headerName := c.GetHeader("X-Pomerium-Claim-Name")
	headerToken := c.GetHeader("X-Pomerium-Jwt-Assertion")
	if headerToken == "" {
		app.Log().Error("X-Pomerium-Jwt-Assertion", "请求参数错误", headerToken)
		app.FailedResponseMsg(c, "请求参数错误")
		return
	}
	// oaGatewayTokenFeilds := strings.Split(headerToken, ".")
	// if len(oaGatewayTokenFeilds) < 2 {
	// 	app.Log().Error("oa-gateway-fileds", "请求参数错误", headerToken)
	// 	app.FailedResponseMsg(c, "请求参数错误")
	// 	return
	// }
	// ogWayUserinfo, err := base64.RawStdEncoding.DecodeString(oaGatewayTokenFeilds[1])
	// if err != nil {
	// 	app.Log().Error("oa-gateway-base64-decode", "请求参数错误", err.Error(), "ogWayUserinfo-base64", oaGatewayTokenFeilds[1], "oaGatewayTokenFeilds", headerToken)
	// 	app.FailedResponseMsg(c, "请求参数错误")
	// 	return
	// }
	// oaGatewayInfo := OAGatewayInfo{}
	// if err := json.Unmarshal(ogWayUserinfo, &oaGatewayInfo); err != nil {
	// 	app.Log().Error("oa-gateway-unmarshal", "请求参数错误", err.Error())
	// 	app.FailedResponseMsg(c, "请求参数错误")
	// 	return
	// }
	// if headerSub != oaGatewayInfo.Sub {
	// 	app.Log().Error("oa-gateway-sub", "请求参数错误 header:", headerSub, "oaGatewayInfo:", oaGatewayInfo.Sub)
	// 	app.FailedResponseMsg(c, "请求参数错误")
	// 	return
	// }
	sn := ""
	if len(strings.Split(headerSub, "/")) > 1 {
		sn = strings.Split(headerSub, "/")[1]
	}
	u, err := auth.OAGatewayAuth(sn)
	if err != nil {
		app.Log().Error("oa-gateway-user", "获取用户信息失败", err.Error())
		app.FailedResponseMsg(c, "获取用户信息失败")
		return
	}
	// 如果不存在就需要创建用户，请求oa-service
	token, err := u.GenerateToken(time.Hour * 24)
	if err != nil {
		app.Log().Error("登陆，生成token失败", "err", err)
		c.JSON(http.StatusOK, LoginResponseData{
			Success: false,
			Msg:     "登陆失败",
		})
		return
	}
	refreshToken, err := u.GenerateToken(time.Hour * 24)
	if err != nil {
		app.Log().Error("登陆，生成refresh token失败", "err", err)
		c.JSON(http.StatusOK, LoginResponseData{
			Success: false,
			Msg:     "登陆失败",
		})
		return
	}
	roles := []string{}
	json.Unmarshal(u.Roles, &roles)
	if u.IsAdmin {
		roles = append(roles, "admin")
	}
	roles = append(roles, "common")
	go u.UpdateLoginTime()
	clientIP := c.GetHeader("X-Forwarded-For")
	if clientIP == "" {
		clientIP = c.ClientIP()
	}
	audit.LogLogin(u.Name, u.Username, clientIP, c.Request.UserAgent(), "登陆成功", "IAM登陆")
	c.JSON(http.StatusOK, LoginResponseData{
		Success: true,
		Data: UserData{
			AccessToken:  token,
			Username:     u.Username,
			Nickname:     u.Name,
			RefreshToken: refreshToken,
			Expires:      time.Now().Add(time.Hour * 24).Format("2006/01/02 15:04:05"),
			Roles:        roles,
		},
	})

}
