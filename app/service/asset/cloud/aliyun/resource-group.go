package aliyun

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/pkg/utils"
	"errors"

	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/requests"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/resourcemanager"
	"gorm.io/gorm"
)

func (a Account) syncResourceGroups() (result string, err error) {
	result, err = a.syncPageResourceGroups(1)
	return
}

// SyncResourceGroups 同步资源组信息到数据库
func (a Account) syncPageResourceGroups(page int) (result string, err error) {
	// 解码访问密钥
	accessSecret, _ := utils.Base64Decode(a.AccessSecret)
	// 创建阿里云客户端
	client, err := resourcemanager.NewClientWithAccessKey("cn-beijing", a.AccessKey, accessSecret)
	if err != nil {
		result = "创建阿里云客户端失败"
		return
	}

	// 创建请求以获取资源组列表
	request := resourcemanager.CreateListResourceGroupsRequest()
	request.Scheme = "https"
	request.PageNumber = requests.NewInteger(page)
	request.PageSize = requests.NewInteger(100)
	response, err := client.ListResourceGroups(request)
	if err != nil {
		result = "获取资源组列表失败"
		return
	}
	err = a.saveResourceGroup(response.ResourceGroups.ResourceGroup...)
	if err != nil {
		return
	}
	page++
	if response.TotalCount > page*100 {
		r1, err1 := a.syncPageResourceGroups(page)
		if err1 != nil {
			result += r1 + ":" + err1.Error()
			err = err1
			return
		}
	}
	return
}

func (a Account) saveResourceGroup(groups ...resourcemanager.ResourceGroup) (err error) {
	// 遍历获取到的资源组
	for _, group := range groups {
		exist := asset.ResourceGroup{}
		// 检查数据库中是否已存在该资源组
		err = app.DB().Where("group_id = ? AND account_id = ?", group.Id, a.ID).Take(&exist).Error
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 如果不存在，则创建新的资源组记录
			err = app.DB().Create(&asset.ResourceGroup{AccountID: a.ID, GroupID: group.Id, GroupName: group.Name, GroupDisplayName: exist.GroupDisplayName}).Error
		} else if err == nil {
			updateMap := map[string]any{}
			if exist.GroupName != group.Name {
				updateMap["group_name"] = group.Name
			}
			if exist.GroupDisplayName != group.DisplayName {
				updateMap["group_display_name"] = group.DisplayName
			}
			if len(updateMap) > 0 {
				err = app.DB().Model(&exist).Updates(updateMap).Error
			}
		}
		if err != nil {
			return err
		}
	}
	return
}
