package workflow

import (
	"bytes"
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/auth"
	"encoding/json"
	"errors"
	"time"

	"gorm.io/datatypes"
)

// 权限申请
type OrderPermission struct {
	ID          uint           `gorm:"column:id;primarykey;comment:id" json:"id"`
	SN          string         `gorm:"type:varchar(128);unique;column:sn;comment:工单单号" json:"sn"`
	Content     string         `gorm:"type:text;column:content;comment:工单内容" json:"content"`
	Bucket      string         `gorm:"type:varchar(255);column:bucket;comment:OSS工单的bucket" json:"bucket"`
	Hosts       datatypes.JSON `gorm:"column:hosts;comment:主机列表，字符串数组" json:"hosts"`
	OS          datatypes.JSON `gorm:"column:os;comment:VPN申请时对应的操作系统，字符串数组" json:"os"`
	IsSensitive bool           `gorm:"column:is_sensitive;comment:是否是敏感" json:"is_sensitive"`
}

// 设置表名
func (OrderPermission) TableName() string {
	return "workflow_order_permissions"
}

type OrderPermissionDetail struct {
	Order       `gorm:"-"`
	Content     string         `gorm:"type:text;column:content;comment:工单内容" json:"content"`
	Bucket      string         `gorm:"type:varchar(255);column:bucket;comment:OSS工单的bucket" json:"bucket"`
	Hosts       datatypes.JSON `gorm:"column:hosts;comment:主机列表，字符串数组" json:"hosts"`
	OS          datatypes.JSON `gorm:"column:os;comment:VPN申请时对应的操作系统，字符串数组" json:"os"`
	IsSensitive bool           `gorm:"column:is_sensitive;comment:是否是敏感" json:"is_sensitive"`
}

// 设置表名
func (OrderPermissionDetail) TableName() string {
	return "workflow_order_permissions"
}

func GetOrderPermissionDetail(o Order) (detail OrderPermissionDetail, err error) {
	detail = OrderPermissionDetail{}
	err = app.DB().Where("sn = ?", o.SN).Take(&detail).Error
	if err != nil {
		return
	}
	detail.Order = o
	return
}

type OrderPermissionForm struct {
	OrderType   string    `json:"order_type" binding:"required,max=255"`
	Bucket      string    `json:"bucket" binding:"max=255"`
	Content     string    `json:"content"`
	Hosts       []string  `json:"hosts"`
	OS          []string  `json:"os"`
	Orders      []string  `json:"orders"`
	Env         asset.ENV `json:"env"`
	IsSensitive bool      `json:"is_sensitive"`
	Critical    bool      `json:"critical"`
}

func (form OrderPermissionForm) GenProcesses(sn string, a Applicant, admin *auth.User) (processes []Process, err error) {
	var flow Flow
	err = app.DB().Where("order_type = ?", PermissionOrderType).Take(&flow).Error
	if err != nil {
		return
	}
	flow.Nodes, err = flow.GetFlowNodes()
	if err != nil {
		return
	}
	processes, err = flow.genProcesses(sn, a, admin)
	return
}

func (form OrderPermissionForm) Create(a Applicant, flow Flow, admin *auth.User) (err error) {
	err = form.check()
	if err != nil {
		return
	}
	if form.IsSensitive {
		flow, err = GetFlowByCode(PermissionDGCSensitiveOrderType.Code())
		if err != nil {
			return
		}
	}
	sn := genSN("permission")
	processes, err := flow.genProcesses(sn, a, admin)
	if err != nil {
		return
	}
	status := ApprovingStatus
	var index uint
	if len(processes) == 0 {
		err = errors.New("生成流程失败")
		return
	} else if len(processes) <= 1 {
		// 如果流程没有配置就直接结束流程
		status = CompletedStatus
	} else {
		// 如果有流程就获取下一个节点的索引
		index = processes[1].ProcessIndex
		processes[1].Status = ApprovingProcessStatus
	}
	planTime := time.Now().AddDate(0, 0, 1)
	snsByte, _ := json.Marshal(&form.Orders)
	order := Order{
		Title:        form.OrderType + " - " + a.Name,
		OrderType:    form.OrderType,
		Env:          form.Env,
		ExtType:      PermissionExtType,
		SN:           sn,
		Critical:     form.Critical,
		ApplicantID:  a.ID,
		Status:       status,
		PlanTime:     &planTime,
		ProcessIndex: index,
		Orders:       snsByte,
	}
	byteOS, _ := json.Marshal(&form.OS)
	byteHosts, _ := json.Marshal(&form.Hosts)
	extOrder := OrderPermission{
		SN:          sn,
		Content:     form.Content,
		Bucket:      form.Bucket,
		Hosts:       byteHosts,
		OS:          byteOS,
		IsSensitive: form.IsSensitive,
	}
	dbop := app.DB().Begin()
	err = dbop.Create(&order).Error
	if err != nil {
		dbop.Rollback()
		return
	}
	err = dbop.Create(&extOrder).Error
	if err != nil {
		dbop.Rollback()
		return
	}
	err = dbop.Create(&processes).Error
	if err != nil {
		dbop.Rollback()
		return
	}
	err = dbop.Commit().Error
	if err != nil {
		dbop.Rollback()
	}
	go order.notifyNextProcess()
	return
}

func (form OrderPermissionForm) Update(o Order) (err error) {
	extOrder := OrderPermission{}
	err = app.DB().Where("sn = ?", o.SN).Take(&extOrder).Error
	if err != nil {
		return
	}
	updateMap := map[string]any{}
	if form.Content != extOrder.Content {
		updateMap["content"] = form.Content
	}
	if form.Bucket != extOrder.Bucket {
		updateMap["bucket"] = form.Bucket
	}
	byteOS, _ := json.Marshal(&form.OS)
	if !bytes.Equal(extOrder.OS, byteOS) {
		updateMap["os"] = byteOS
	}
	byteHosts, _ := json.Marshal(&form.Hosts)
	if !bytes.Equal(extOrder.Hosts, byteHosts) {
		updateMap["hosts"] = byteHosts
	}
	if len(updateMap) > 0 {
		err = app.DB().Model(&extOrder).Updates(updateMap).Error
	}
	return
}
func (form OrderPermissionForm) check() (err error) {
	return
}
