package asset

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/audit"
	"cmdb/app/service/auth"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

func GetHostChangeLogs(c *gin.Context) {
	var hostID int
	var err error
	if hostID, err = strconv.Atoi(c.<PERSON>m("id")); err != nil {
		app.Log().Error("参数错误", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	host, err := asset.GetHostByID(hostID)
	if err != nil {
		app.Log().Error("获取主机失败", "err", err)
		app.FailedResponseMsg(c, "主机不存在")
		return
	}
	var startTime, endTime *time.Time
	startTimeStr := strings.TrimSpace(c.Query("start_time"))
	if value, err1 := time.ParseInLocation(time.DateTime, startTimeStr, time.Local); err1 == nil {
		startTime = &value
	}
	endTimeStr := strings.TrimSpace(c.Query("end_time"))
	if value, err2 := time.ParseInLocation(time.DateTime, endTimeStr, time.Local); err2 == nil {
		endTime = &value
	}
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	offset := (page - 1) * limit
	count, data, err := host.GetChangeLogs(offset, limit, startTime, endTime)
	if err != nil {
		app.Log().Error("获取主机变更日志失败", "err", err)
		app.FailedResponseMsg(c, "获取主机变更日志失败")
		return
	}
	c.JSON(http.StatusOK, gin.H{"success": true, "count": count, "data": data})
}

func AddHostChangeLog(c *gin.Context) {
	var form asset.HostChangeLogForm
	if err := c.ShouldBind(&form); err != nil {
		app.Log().Error("存在非法字段", "err", err)
		app.FailedResponseMsg(c, "存在非法字段")
		return
	}
	var hostID int
	var err error
	if hostID, err = strconv.Atoi(c.Param("id")); err != nil {
		app.Log().Error("参数错误", "err", err)
		app.FailedResponseMsg(c, "参数错误")
		return
	}
	host, err := asset.GetHostByID(hostID)
	if err != nil {
		app.Log().Error("获取主机失败", "err", err)
		app.FailedResponseMsg(c, "主机不存在")
		return
	}
	loginUser, err := auth.GetLoginUser(c)
	if err != nil {
		app.Log().Error("获取用户信息失败", "err", err)
		app.FailedResponseMsg(c, "获取用户信息失败")
		return
	}
	form.Operator = loginUser.Name + "(" + loginUser.Username + ")"
	err = host.LogChanges(form.Operator, form.Content)
	if err != nil {
		app.Log().Error("添加主机变更日志失败", "err", err)
		app.FailedResponseMsg(c, "添加主机变更日志失败")
		return
	}
	audit.LogAPIOP(c, "资产管理", "添加主机变更日志")
	app.SuccessResponseMsg(c, "添加成功")
}
