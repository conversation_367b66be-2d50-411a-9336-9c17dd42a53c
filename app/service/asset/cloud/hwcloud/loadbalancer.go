package hwcloud

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/asset/cloud"
	"cmdb/pkg/utils"
	"errors"
	"time"

	"github.com/huaweicloud/huaweicloud-sdk-go-v3/core/auth/basic"
	elb "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/elb/v2"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/elb/v2/model"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/elb/v2/region"
	"gorm.io/gorm"
)

func (a Account) syncLoadbalancers() (result cloud.SyncResults, err error) {
	dcs, err := asset.GetDatacentersByCloudType(asset.HuaweiCloudType)
	if err != nil {
		return
	}
	for _, dc := range dcs {
		r, err := a.syncRegionELBs(dc, nil)
		sr := cloud.SyncResult{
			Key: dc.Name,
		}
		if err == nil {
			sr.Result = "同步成功"
			// 回收过期ELB
			err = a.expiredLoadbanlancers(time.Now().Add(-10*time.Minute), dc, "elb")
			if err != nil {
				sr.Result = "同步成功，但是回收ELB失败" + err.Error()
			}
		} else {
			sr.Result = r + " " + err.Error()
		}
		result = append(result, sr)
	}
	return
}

func (a Account) GetLoadbalancersByIP(dc asset.Datacenter, ip string, marker *string) (lbs []asset.Loadbalancer, err error) {
	secret, _ := utils.Base64Decode(a.AccessSecret)
	auth, err := basic.NewCredentialsBuilder().WithAk(a.AccessKey).WithSk(secret).SafeBuild()
	if err != nil {
		err = errors.New("构建认证失败")
		return
	}
	regionID, err := region.SafeValueOf(dc.Code)
	if err != nil {
		err = errors.New("区域编码错误")
		return
	}
	clientBuild, err := elb.ElbClientBuilder().WithRegion(regionID).WithCredential(auth).SafeBuild()
	if err != nil {
		err = errors.New("构建客户端失败")
		return
	}
	client := elb.NewElbClient(clientBuild)
	var limit int32 = 10000
	response, err := client.ListLoadbalancers(&model.ListLoadbalancersRequest{
		Limit: &limit, Marker: marker, MemberAddress: &ip,
	})
	if err != nil {
		err = errors.New("获取ELB列表失败")
		return
	}
	dataLength := len(*response.Loadbalancers)
	if dataLength > 0 {
		for _, lb := range *response.Loadbalancers {
			lbs = append(lbs, asset.Loadbalancer{
				DatacenterID:     dc.ID,
				LoadbalancerID:   lb.Id,
				AccountID:        a.ID,
				Name:             lb.Name,
				LoadbalancerType: "elb",
				Status:           lb.OperatingStatus.Value(),
				SyncTime:         time.Now(),
				ResourceGroupID:  lb.EnterpriseProjectId,
				Host:             lb.VipAddress,
			})
		}
		var sublbs []asset.Loadbalancer
		sublbs, err = a.GetLoadbalancersByIP(dc, ip, &(*response.Loadbalancers)[dataLength-1].Id)
		if err != nil {
			return
		}
		lbs = append(lbs, sublbs...)
	}
	return
}

func (a Account) syncRegionELBs(dc asset.Datacenter, marker *string) (result string, err error) {
	secret, _ := utils.Base64Decode(a.AccessSecret)
	auth, err := basic.NewCredentialsBuilder().WithAk(a.AccessKey).WithSk(secret).SafeBuild()
	if err != nil {
		result = "构建认证失败"
		return
	}
	regionID, err := region.SafeValueOf(dc.Code)
	if err != nil {
		result = "区域编码错误"
		return
	}
	clientBuild, err := elb.ElbClientBuilder().WithRegion(regionID).WithCredential(auth).SafeBuild()
	if err != nil {
		result = "构建客户端失败"
		return
	}
	client := elb.NewElbClient(clientBuild)
	var limit int32 = 10000
	response, err := client.ListLoadbalancers(&model.ListLoadbalancersRequest{
		Limit: &limit, Marker: marker,
	})
	if err != nil {
		result = "获取ELB列表失败"
		return
	}
	dataLength := len(*response.Loadbalancers)
	if dataLength > 0 {
		err = a.saveLoadbalancer(dc, *response.Loadbalancers)
		if err != nil {
			return
		}
		result, err = a.syncRegionELBs(dc, &(*response.Loadbalancers)[dataLength-1].Id)
	}
	return
}

func (a Account) saveLoadbalancer(dc asset.Datacenter, loadbalancer []model.LoadbalancerResp) (err error) {
	now := time.Now()
	for _, lb := range loadbalancer {
		exist := asset.Loadbalancer{}
		var err1 error
		var createTime time.Time
		createTime, err1 = time.ParseInLocation("2006-01-02T15:04:05", lb.CreatedAt, time.Local)
		if err1 != nil {
			createTime = now
		}
		host := lb.VipAddress
		if host == "" {
			if len(lb.Publicips) > 0 {
				host = lb.Publicips[0].PublicipAddress
			}
		}
		err = app.DB().Where("datacenter_id = ? and loadbalancer_id = ? and account_id = ?", dc.ID, lb.Id, a.ID).First(&exist).Error
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = app.DB().Create(&asset.Loadbalancer{
				DatacenterID:     dc.ID,
				LoadbalancerID:   lb.Id,
				AccountID:        a.ID,
				Name:             lb.Name,
				LoadbalancerType: "elb",
				Status:           lb.OperatingStatus.Value(),
				SyncTime:         now,
				ResourceGroupID:  lb.EnterpriseProjectId,
				Host:             host,
				CreatedAt:        createTime,
			}).Error
		} else if err == nil {
			updateMap := map[string]any{
				"sync_time": now,
			}
			if exist.Name != lb.Name {
				updateMap["name"] = lb.Name
			}
			if exist.Status != lb.OperatingStatus.Value() {
				updateMap["status"] = lb.OperatingStatus.Value()
			}
			if exist.ResourceGroupID != lb.EnterpriseProjectId {
				updateMap["resource_group_id"] = lb.EnterpriseProjectId
			}
			if exist.Host != host {
				updateMap["host"] = host
			}
			if exist.CreatedAt != createTime {
				updateMap["created_at"] = createTime
			}
			err = app.DB().Model(&exist).Updates(updateMap).Error
			if err != nil {
				return
			}
		} else {
			return
		}
	}
	return
}

func (a Account) expiredLoadbanlancers(expiredTime time.Time, dc asset.Datacenter, loadbalancerType string) (err error) {
	lbs := []asset.Loadbalancer{}
	err = app.DB().Where("account_id = ? and sync_time < ? and datacenter_id = ? AND loadbalancer_type = ?", a.ID, expiredTime, dc.ID, loadbalancerType).Delete(&lbs).Error
	return
}
