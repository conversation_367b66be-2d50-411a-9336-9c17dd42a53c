package asset

import (
	"cmdb/app"
	"testing"

	"github.com/jinzhu/now"
)

func TestGetResourceMetrics(t *testing.T) {
	// 初始化应用和数据库连接
	err := app.NewApp("../../../../app.ini")
	if err != nil {
		t.Fatal(err)
	}
	err = app.ConnectDB()
	if err != nil {
		t.<PERSON><PERSON>(err)
	}
	defer func() {
		dbop, err := app.DB().DB()
		if err != nil {
			t.Fatal(err)
		}
		err = dbop.Close()
		if err != nil {
			t.Fatal(err)
		}
	}()

	// 计算日期范围
	endDate := now.BeginningOfDay()
	startDate := endDate.AddDate(0, -1, 0) // 获取最近一个月的数据

	// 测试获取计算资源总计指标历史趋势
	t.Run("GetComputerResourceMetrics", func(t *testing.T) {
		metrics, err := GetResourceMetrics(ResourceTypeComputer, MetricTypeHostTotal, "", startDate.Format("2006-01-02"), endDate.Format("2006-01-02"))
		if err != nil {
			t.Logf("获取计算资源总计指标历史趋势失败: %v", err)
			return
		}
		t.Logf("获取到 %d 条计算资源总计指标历史趋势数据", len(metrics))
		for _, metric := range metrics {
			t.Logf("日期: %s, 值: %f", metric.Date, metric.Value)
		}
	})

	// 测试获取数据资源指标历史趋势
	t.Run("GetDataResourceMetrics", func(t *testing.T) {
		metrics, err := GetResourceMetrics(ResourceTypeData, MetricTypeMySQLStorage, "", startDate.Format("2006-01-02"), endDate.Format("2006-01-02"))
		if err != nil {
			t.Logf("获取数据资源指标历史趋势失败: %v", err)
			return
		}
		t.Logf("获取到 %d 条数据资源指标历史趋势数据", len(metrics))
		for _, metric := range metrics {
			t.Logf("日期: %s, 值: %f", metric.Date, metric.Value)
		}
	})

	// 测试获取可优化资源指标历史趋势
	t.Run("GetOptimizableResourceMetrics", func(t *testing.T) {
		metrics, err := GetResourceMetrics(ResourceTypeOptimizable, MetricTypeAMDPercent, "", startDate.Format("2006-01-02"), endDate.Format("2006-01-02"))
		if err != nil {
			t.Logf("获取可优化资源指标历史趋势失败: %v", err)
			return
		}
		t.Logf("获取到 %d 条可优化资源指标历史趋势数据", len(metrics))
		for _, metric := range metrics {
			t.Logf("日期: %s, 值: %f", metric.Date, metric.Value)
		}
	})

	// 获取所有云类型
	cloudTypes, err := GetAllCloudTypes()
	if err != nil {
		t.Logf("获取所有云类型失败: %v", err)
	} else if len(cloudTypes) > 0 {
		// 测试获取云计算资源指标历史趋势
		t.Run("GetCloudResourceMetrics", func(t *testing.T) {
			cloudType := cloudTypes[0]
			metrics, err := GetResourceMetrics(ResourceTypeCloud, MetricTypeCPUTotal, cloudType, startDate.Format("2006-01-02"), endDate.Format("2006-01-02"))
			if err != nil {
				t.Logf("获取云计算资源指标历史趋势失败: %v", err)
				return
			}
			t.Logf("获取到 %d 条云计算资源指标历史趋势数据，云类型: %s", len(metrics), cloudType)
			for _, metric := range metrics {
				t.Logf("日期: %s, 值: %f", metric.Date, metric.Value)
			}
		})
	}

	// 获取所有区域名称
	regionNames, err := GetAllRegionNames()
	if err != nil {
		t.Logf("获取所有区域名称失败: %v", err)
	} else if len(regionNames) > 0 {
		// 测试获取区域计算资源指标历史趋势
		t.Run("GetRegionResourceMetrics", func(t *testing.T) {
			regionName := regionNames[0]
			metrics, err := GetResourceMetrics(ResourceTypeRegion, MetricTypeMemoryTotal, regionName, startDate.Format("2006-01-02"), endDate.Format("2006-01-02"))
			if err != nil {
				t.Logf("获取区域计算资源指标历史趋势失败: %v", err)
				return
			}
			t.Logf("获取到 %d 条区域计算资源指标历史趋势数据，区域名称: %s", len(metrics), regionName)
			for _, metric := range metrics {
				t.Logf("日期: %s, 值: %f", metric.Date, metric.Value)
			}
		})
	}
}

func TestGetLatestResourceMetric(t *testing.T) {
	// 初始化应用和数据库连接
	err := app.NewApp("../../../../app.ini")
	if err != nil {
		t.Fatal(err)
	}
	err = app.ConnectDB()
	if err != nil {
		t.Fatal(err)
	}
	defer func() {
		dbop, err := app.DB().DB()
		if err != nil {
			t.Fatal(err)
		}
		err = dbop.Close()
		if err != nil {
			t.Fatal(err)
		}
	}()

	// 测试获取最新的计算资源总计指标值
	t.Run("GetLatestComputerResourceMetric", func(t *testing.T) {
		value, err := GetLatestResourceMetric(ResourceTypeComputer, MetricTypeHostTotal, "")
		if err != nil {
			t.Logf("获取最新的计算资源总计指标值失败: %v", err)
			return
		}
		t.Logf("最新的计算资源总计指标值: %f", value)
	})

	// 测试获取最新的数据资源指标值
	t.Run("GetLatestDataResourceMetric", func(t *testing.T) {
		value, err := GetLatestResourceMetric(ResourceTypeData, MetricTypeMySQLStorage, "")
		if err != nil {
			t.Logf("获取最新的数据资源指标值失败: %v", err)
			return
		}
		t.Logf("最新的数据资源指标值: %f", value)
	})

	// 测试获取最新的可优化资源指标值
	t.Run("GetLatestOptimizableResourceMetric", func(t *testing.T) {
		value, err := GetLatestResourceMetric(ResourceTypeOptimizable, MetricTypeAMDPercent, "")
		if err != nil {
			t.Logf("获取最新的可优化资源指标值失败: %v", err)
			return
		}
		t.Logf("最新的可优化资源指标值: %f", value)
	})

	// 获取所有云类型
	cloudTypes, err := GetAllCloudTypes()
	if err != nil {
		t.Logf("获取所有云类型失败: %v", err)
	} else if len(cloudTypes) > 0 {
		// 测试获取最新的云计算资源指标值
		t.Run("GetLatestCloudResourceMetric", func(t *testing.T) {
			cloudType := cloudTypes[0]
			value, err := GetLatestResourceMetric(ResourceTypeCloud, MetricTypeCPUTotal, cloudType)
			if err != nil {
				t.Logf("获取最新的云计算资源指标值失败: %v", err)
				return
			}
			t.Logf("最新的云计算资源指标值: %f，云类型: %s", value, cloudType)
		})
	}

	// 获取所有区域名称
	regionNames, err := GetAllRegionNames()
	if err != nil {
		t.Logf("获取所有区域名称失败: %v", err)
	} else if len(regionNames) > 0 {
		// 测试获取最新的区域计算资源指标值
		t.Run("GetLatestRegionResourceMetric", func(t *testing.T) {
			regionName := regionNames[0]
			value, err := GetLatestResourceMetric(ResourceTypeRegion, MetricTypeMemoryTotal, regionName)
			if err != nil {
				t.Logf("获取最新的区域计算资源指标值失败: %v", err)
				return
			}
			t.Logf("最新的区域计算资源指标值: %f，区域名称: %s", value, regionName)
		})
	}
}

func TestGetResourceMetricsByDateRange(t *testing.T) {
	// 初始化应用和数据库连接
	err := app.NewApp("../../../../app.ini")
	if err != nil {
		t.Fatal(err)
	}
	err = app.ConnectDB()
	if err != nil {
		t.Fatal(err)
	}
	defer func() {
		dbop, err := app.DB().DB()
		if err != nil {
			t.Fatal(err)
		}
		err = dbop.Close()
		if err != nil {
			t.Fatal(err)
		}
	}()

	// 测试获取指定日期范围内的计算资源总计指标历史趋势
	t.Run("GetComputerResourceMetricsByDateRange", func(t *testing.T) {
		metrics, err := GetResourceMetricsByDateRange(ResourceTypeComputer, MetricTypeHostTotal, "", 30)
		if err != nil {
			t.Logf("获取指定日期范围内的计算资源总计指标历史趋势失败: %v", err)
			return
		}
		t.Logf("获取到 %d 条指定日期范围内的计算资源总计指标历史趋势数据", len(metrics))
		for _, metric := range metrics {
			t.Logf("日期: %s, 值: %f", metric.Date, metric.Value)
		}
	})
}

func TestGetAllCloudTypes(t *testing.T) {
	// 初始化应用和数据库连接
	err := app.NewApp("../../../../app.ini")
	if err != nil {
		t.Fatal(err)
	}
	err = app.ConnectDB()
	if err != nil {
		t.Fatal(err)
	}
	defer func() {
		dbop, err := app.DB().DB()
		if err != nil {
			t.Fatal(err)
		}
		err = dbop.Close()
		if err != nil {
			t.Fatal(err)
		}
	}()

	// 测试获取所有云类型
	cloudTypes, err := GetAllCloudTypes()
	if err != nil {
		t.Fatalf("获取所有云类型失败: %v", err)
	}
	t.Logf("获取到 %d 种云类型", len(cloudTypes))
	for _, cloudType := range cloudTypes {
		t.Logf("云类型: %s", cloudType)
	}
}

func TestGetAllRegionNames(t *testing.T) {
	// 初始化应用和数据库连接
	err := app.NewApp("../../../../app.ini")
	if err != nil {
		t.Fatal(err)
	}
	err = app.ConnectDB()
	if err != nil {
		t.Fatal(err)
	}
	defer func() {
		dbop, err := app.DB().DB()
		if err != nil {
			t.Fatal(err)
		}
		err = dbop.Close()
		if err != nil {
			t.Fatal(err)
		}
	}()

	// 测试获取所有区域名称
	regionNames, err := GetAllRegionNames()
	if err != nil {
		t.Fatalf("获取所有区域名称失败: %v", err)
	}
	t.Logf("获取到 %d 个区域", len(regionNames))
	for _, regionName := range regionNames {
		t.Logf("区域名称: %s", regionName)
	}
}

func TestGetAllDomainNames(t *testing.T) {
	// 初始化应用和数据库连接
	err := app.NewApp("../../../../app.ini")
	if err != nil {
		t.Fatal(err)
	}
	err = app.ConnectDB()
	if err != nil {
		t.Fatal(err)
	}
	defer func() {
		dbop, err := app.DB().DB()
		if err != nil {
			t.Fatal(err)
		}
		err = dbop.Close()
		if err != nil {
			t.Fatal(err)
		}
	}()

	// 测试获取所有域名
	domainNames, err := GetAllDomainNames()
	if err != nil {
		t.Fatalf("获取所有域名失败: %v", err)
	}
	t.Logf("获取到 %d 个域名", len(domainNames))
	for i, domainName := range domainNames {
		if i < 10 { // 只打印前10个，避免输出过多
			t.Logf("域名: %s", domainName)
		}
	}
}

func TestGetAllDataResourceTypes(t *testing.T) {
	// 测试获取所有数据资源类型
	dataResourceTypes, err := GetAllDataResourceTypes()
	if err != nil {
		t.Fatalf("获取所有数据资源类型失败: %v", err)
	}
	t.Logf("获取到 %d 种数据资源类型", len(dataResourceTypes))
	for _, dataResourceType := range dataResourceTypes {
		t.Logf("数据资源类型: %s", dataResourceType)
	}
}

func TestGetAllOptimizableResourceTypes(t *testing.T) {
	// 测试获取所有可优化资源类型
	optimizableResourceTypes, err := GetAllOptimizableResourceTypes()
	if err != nil {
		t.Fatalf("获取所有可优化资源类型失败: %v", err)
	}
	t.Logf("获取到 %d 种可优化资源类型", len(optimizableResourceTypes))
	for _, optimizableResourceType := range optimizableResourceTypes {
		t.Logf("可优化资源类型: %s", optimizableResourceType)
	}
}
