package monitor

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/app/service/monitor/prometheus"
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"
)

type MonitorHost struct {
	ID          uint           `gorm:"column:id;primaryKey;comment:主键" json:"id"`
	AccountID   int            `gorm:"column:account_id;index;comment:账号ID" json:"account_id"`
	SN          string         `gorm:"column:sn;type:varchar(255);index;comment:主机序列号/ECS的实例ID" json:"sn"`
	Name        string         `gorm:"column:name;type:varchar(255);index" json:"name"`
	IP          string         `gorm:"column:ip;type:varchar(255);index" json:"ip"`
	PingMonitor bool           `gorm:"column:ping_monitor;index;comment:监控状态" json:"ping_monitor"`
	PublicIP    string         `gorm:"column:public_ip;type:varchar(255);index" json:"public_ip"`
	IDCName     string         `gorm:"-" json:"idc_name"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`
}

func (MonitorHost) TableName() string {
	return "asset_hosts"
}

func GetMonitorHosts(accountID int, filterIDCNames ...string) (hosts []MonitorHost, err error) {
	if len(filterIDCNames) == 0 {
		err = app.DB().Model(&MonitorHost{}).Where("ping_monitor =?", true).Find(&hosts).Error
		return
	}
	idcs := []asset.Datacenter{}
	err = app.DB().Select("id").Where("name IN (?) OR code IN (?)", filterIDCNames, filterIDCNames).Find(&idcs).Error
	if err != nil {
		return
	}
	if len(idcs) == 0 {
		return
	}
	ids := []uint{}
	for _, idc := range idcs {
		ids = append(ids, idc.ID)
	}
	if accountID == 0 {
		err = app.DB().Model(&MonitorHost{}).Where("datacenter_id IN (?) AND ping_monitor =?", ids, true).Find(&hosts).Error
	} else {
		err = app.DB().Model(&MonitorHost{}).Where("datacenter_id IN (?) AND ping_monitor = ? AND account_id = ?", ids, true, accountID).Find(&hosts).Error
	}
	return
}

func SwitchHostPingmonitor(hostID uint, pingMonitor bool) (err error) {
	err = app.DB().Model(&MonitorHost{}).Where("id =?", hostID).Update("ping_monitor", pingMonitor).Error
	return
}

func BatSwitchHostPingmonitor(hosts []string, pingMonitor bool) (err error) {
	if len(hosts) == 0 {
		return
	}
	err = app.DB().Model(&MonitorHost{}).Where("ip IN (?) OR public_ip IN (?)", hosts, hosts).Update("ping_monitor", pingMonitor).Error
	return
}

func StatsHostDiskDataXUsage(hosts ...string) (storageTotal int64, err error) {
	if len(hosts) == 0 {
		return
	}
	storageTotalFloat := 0.0
	cnN9eMonitorHosts := []string{}
	cnDatasource := "http://**********:8428"
	intlDatasource := "http://*************:8428"
	intlN9eMonitorHosts := []string{}
	for _, host := range hosts {
		if strings.HasPrefix(host, "10.2.") || strings.HasPrefix(host, "10.15.") || strings.HasPrefix(host, "10.16.") {
			intlN9eMonitorHosts = append(intlN9eMonitorHosts, host)
		} else {
			cnN9eMonitorHosts = append(cnN9eMonitorHosts, host)
		}
	}
	nowTime := time.Now()
	duration := time.Minute
	if len(cnN9eMonitorHosts) > 0 {
		cnQL := fmt.Sprintf("sum(disk_used{ident=~'%s',path=~'/data.*'})", strings.Join(cnN9eMonitorHosts, "|"))
		result, err := prometheus.QueryInstant(cnDatasource, cnQL, &nowTime, &duration)
		if err == nil {
			v, err := result.GetVectorValue()
			if err == nil {
				storageTotalFloat += v
			}
		}
	}
	if len(intlN9eMonitorHosts) > 0 {
		intlQL := fmt.Sprintf("sum(disk_used{ident=~'%s',path=~'/data.*'})", strings.Join(intlN9eMonitorHosts, "|"))
		result, err := prometheus.QueryInstant(intlDatasource, intlQL, &nowTime, &duration)
		if err == nil {
			v, err := result.GetVectorValue()
			if err == nil {
				storageTotalFloat += v
			}
		}
	}

	storageTotal = int64(storageTotalFloat)
	return
}
