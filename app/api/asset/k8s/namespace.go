package k8s

import (
	"cmdb/app"
	"cmdb/app/service/asset/k8s"
	"strconv"

	"github.com/gin-gonic/gin"
)

func GetClusterNamespaces(c *gin.Context) {
	clusterID, _ := strconv.Atoi(c<PERSON><PERSON>("id"))
	cluster, err := k8s.GetClusterByID(clusterID)
	if err != nil {
		app.Log().Error("获取集群失败", "err", err)
		app.FailedResponseMsg(c, "获取集群失败")
		return
	}
	namespaces, err := cluster.GetAllNamespaces()
	if err != nil {
		app.Log().Error("获取命名空间失败", "err", err)
		app.FailedResponseMsg(c, "获取命名空间失败")
		return
	}
	app.SuccessResponseData(c, namespaces)
}
