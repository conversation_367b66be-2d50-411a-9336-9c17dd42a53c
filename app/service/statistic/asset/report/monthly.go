package report

import (
	"bytes"
	"cmdb/app"
	"cmdb/app/service/asset"
	statisticAsset "cmdb/app/service/statistic/asset"
	"encoding/json"
	"errors"
	"time"

	"github.com/jinzhu/now"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// DomainBpsMonthly 域名带宽月度统计
type DomainBpsMonthly struct {
	Domain       string `gorm:"column:domain;comment:域名" json:"domain"`
	InBps        int64  `gorm:"column:in_bps;comment:入流量" json:"in_bps"`
	InBpsChange  *int64 `gorm:"column:in_bps_change;comment:入流量变化" json:"in_bps_change"`
	OutBps       int64  `gorm:"column:out_bps;comment:出流量" json:"out_bps"`
	OutBpsChange *int64 `gorm:"column:out_bps_change;comment:出流量变化" json:"out_bps_change"`
}

// MonthlyReport 月度资源统计报告
type MonthlyReport struct {
	ID                    uint           `gorm:"column:id;primaryKey;comment:主键id" json:"id"`
	Month                 string         `gorm:"column:month;type:varchar(7);index;comment:统计月份(YYYY-MM)" json:"month"`
	ComputerResourceStats datatypes.JSON `gorm:"column:computer_resource_stats;type:JSON;comment:计算机资源统计" json:"computer_resource_stats"`
	DataResourceStats     datatypes.JSON `gorm:"column:data_resource_stats;type:JSON;comment:数据资源统计" json:"data_resource" `
	OptimizableAssetStats datatypes.JSON `gorm:"column:optimizable_asset_stats;type:JSON;comment:可优化资源统计" json:"optimizable_asset_stats"`
	DomainBpsStats        datatypes.JSON `gorm:"column:domain_bps_stats;type:JSON;comment:域名带宽统计" json:"domain_bps_stats"`
}

func (MonthlyReport) TableName() string {
	return "statistic_monthly_reports"
}

// genMonthlyReport 生成月度报告，每月1号生成，并与上个月的数据进行对比
func genMonthlyReport(reportMonth time.Time) (err error) {
	// 确保是本月月初第一天
	reportMonth = time.Date(reportMonth.Year(), reportMonth.Month(), 1, 0, 0, 0, 0, reportMonth.Location())

	// 获取上个月的日期
	lastMonth := reportMonth.AddDate(0, -1, 0)
	monthStr := lastMonth.Format("2006-01")
	app.Log().Info("开始生成月度报告", "month", monthStr)

	// 记录上个月的字符串格式，用于日志
	app.Log().Info("对比上个月数据", "last_month", lastMonth.Format("2006-01"))
	//  0. 获取当月计算机资源汇总
	var computerResource ComputerResource
	err = app.DB().Model(&statisticAsset.ComputerResource{}).
		Select("host_total, cpu_total, memory_mb_total, gpu_total, dli_cu_total").
		Where("stat_date = ?", reportMonth.Format("2006-01-02")).
		Take(&computerResource).Error
	if err != nil {
		app.Log().Error("获取当月计算机资源汇总失败", "err", err)
		return
	}
	// 1. 获取当月云计算资源统计数据
	var cloudResources []CloudComputerResource
	err = app.DB().Model(&statisticAsset.CloudComputerResource{}).
		Select("cloud_type_name as cloud_type, host_total, cpu_total, memory_mb_total as memory_mb_total, gpu_total").
		Where("stat_date = ?", reportMonth.Format("2006-01-02")).
		Find(&cloudResources).Error
	if err != nil {
		app.Log().Error("获取当月云计算资源统计数据失败", "err", err)
		return
	}

	if len(cloudResources) == 0 {
		app.Log().Warn("当月没有云计算资源统计数据", "month", monthStr)
		// 继续处理其他数据
	}

	// 获取上个月的云计算资源统计数据，计算变化量
	for i := range cloudResources {
		var lastMonthData statisticAsset.CloudComputerResource
		err = app.DB().Model(&statisticAsset.CloudComputerResource{}).
			Where("stat_date = ? AND cloud_type_name = ?", lastMonth.Format("2006-01-02"), cloudResources[i].CloudType).
			Take(&lastMonthData).Error
		if err == nil {
			// 计算变化量
			hostTotalChange := int64(cloudResources[i].HostTotal) - int64(lastMonthData.HostTotal)
			cpuTotalChange := int64(cloudResources[i].CPUTotal) - int64(lastMonthData.CPUTotal)
			memoryMBChange := int64(cloudResources[i].MemoryMBTotal) - int64(lastMonthData.MemoryMBTotal)
			gpuTotalChange := int64(cloudResources[i].GPUTotal) - int64(lastMonthData.GPUTotal)

			// 如果没有变化，则设置为0，否则设置为变化量
			if hostTotalChange != 0 {
				cloudResources[i].HostTotalChange = &hostTotalChange
			} else {
				zero := int64(0)
				cloudResources[i].HostTotalChange = &zero
			}

			if cpuTotalChange != 0 {
				cloudResources[i].CPUTotalChange = &cpuTotalChange
			} else {
				zero := int64(0)
				cloudResources[i].CPUTotalChange = &zero
			}

			if memoryMBChange != 0 {
				cloudResources[i].MemoryMBTotalChange = &memoryMBChange
			} else {
				zero := int64(0)
				cloudResources[i].MemoryMBTotalChange = &zero
			}

			if gpuTotalChange != 0 {
				cloudResources[i].GPUTotalChange = &gpuTotalChange
			} else {
				zero := int64(0)
				cloudResources[i].GPUTotalChange = &zero
			}
		} else if errors.Is(err, gorm.ErrRecordNotFound) {
			// 如果上个月没有数据，则变化量为nil
			app.Log().Info("上个月没有云计算资源统计数据，变化量设为nil", "cloud_type", cloudResources[i].CloudType)
			// 变化量已经初始化为nil，不需要额外设置
		} else {
			app.Log().Error("获取上个月云计算资源统计数据失败", "err", err)
			return
		}
	}

	// 2. 获取当月区域计算资源统计数据
	var regionResources []RegionComputerResource
	err = app.DB().Model(&statisticAsset.RegionComputerResource{}).
		Select("region_name, host_total, cpu_total, memory_mb_total as memory_mb, gpu_total").
		Where("stat_date = ?", reportMonth.Format("2006-01-02")).
		Find(&regionResources).Error
	if err != nil {
		app.Log().Error("获取当月区域计算资源统计数据失败", "err", err)
		return
	}

	if len(regionResources) == 0 {
		app.Log().Warn("当月没有区域计算资源统计数据", "month", monthStr)
		// 继续处理其他数据
	}

	// 获取上个月的区域计算资源统计数据，计算变化量
	for i := range regionResources {
		var lastMonthData statisticAsset.RegionComputerResource
		err = app.DB().Model(&statisticAsset.RegionComputerResource{}).
			Where("stat_date = ? AND region_name = ?", lastMonth.Format("2006-01-02"), regionResources[i].RegionName).
			Take(&lastMonthData).Error
		if err == nil {
			// 计算变化量
			hostTotalChange := int64(regionResources[i].HostTotal) - int64(lastMonthData.HostTotal)
			cpuTotalChange := int64(regionResources[i].CPUTotal) - int64(lastMonthData.CPUTotal)
			memoryMBChange := int64(regionResources[i].MemoryMBTotal) - int64(lastMonthData.MemoryMBTotal)
			gpuTotalChange := int64(regionResources[i].GPUTotal) - int64(lastMonthData.GPUTotal)

			// 如果没有变化，则设置为0，否则设置为变化量
			if hostTotalChange != 0 {
				regionResources[i].HostTotalChange = &hostTotalChange
			} else {
				zero := int64(0)
				regionResources[i].HostTotalChange = &zero
			}

			if cpuTotalChange != 0 {
				regionResources[i].CPUTotalChange = &cpuTotalChange
			} else {
				zero := int64(0)
				regionResources[i].CPUTotalChange = &zero
			}

			if memoryMBChange != 0 {
				regionResources[i].MemoryMBTotalChange = &memoryMBChange
			} else {
				zero := int64(0)
				regionResources[i].MemoryMBTotalChange = &zero
			}

			if gpuTotalChange != 0 {
				regionResources[i].GPUTotalChange = &gpuTotalChange
			} else {
				zero := int64(0)
				regionResources[i].GPUTotalChange = &zero
			}
		} else if errors.Is(err, gorm.ErrRecordNotFound) {
			// 如果上个月没有数据，则变化量为nil
			app.Log().Info("上个月没有区域计算资源统计数据，变化量设为nil", "region_name", regionResources[i].RegionName)
			// 变化量已经初始化为nil，不需要额外设置
		} else {
			app.Log().Error("获取上个月区域计算资源统计数据失败", "err", err)
			return
		}
	}

	// 3. 获取当月数据资源统计数据
	var dataResource DataResource
	err = app.DB().Model(&statisticAsset.DataResource{}).
		Where("stat_date = ?", reportMonth.Format("2006-01-02")).
		Select("mysql_storage_total, oss_storage_total, obs_storage_total, sfs_storage_total, nas_storage_total, mongodb_storage_total, starrocks_storage_total, clickhouse_storage_total, tidb_storage_total, dli_storage_total").
		Take(&dataResource).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			app.Log().Warn("当月没有数据资源统计数据，将使用默认值", "month", monthStr)
			// 使用默认值（全部为0）继续处理
		} else {
			app.Log().Error("获取当月数据资源统计数据失败", "err", err)
			return
		}
	}

	// 获取上个月的数据资源统计数据，计算变化量
	var lastMonthDataResource statisticAsset.DataResource
	err = app.DB().Model(&statisticAsset.DataResource{}).
		Where("stat_date = ?", lastMonth.Format("2006-01-02")).
		Take(&lastMonthDataResource).Error
	if err == nil {
		// 计算变化量
		mysqlStorageChange := dataResource.MySQLStorageTotal - lastMonthDataResource.MySQLStorageTotal
		ossStorageChange := dataResource.OSSStorageTotal - lastMonthDataResource.OSSStorageTotal
		obsStorageChange := dataResource.OBSStorageTotal - lastMonthDataResource.OBSStorageTotal
		sfsStorageChange := dataResource.SFSStorageTotal - lastMonthDataResource.SFSStorageTotal
		nasStorageChange := dataResource.NASStorageTotal - lastMonthDataResource.NASStorageTotal
		mongodbStorageChange := dataResource.MongodbStorageTotal - lastMonthDataResource.MongodbStorageTotal
		starrocksStorageChange := dataResource.StarrocksStorageTotal - lastMonthDataResource.StarrocksStorageTotal
		clickhouseStorageChange := dataResource.ClickhouseStorageTotal - lastMonthDataResource.ClickhouseStorageTotal
		tidbStorageChange := dataResource.TidbStorageTotal - lastMonthDataResource.TidbStorageTotal
		dliStorageChange := dataResource.DliStorageTotal - lastMonthDataResource.DliStorageTotal

		// 如果没有变化，则设置为0，否则设置为变化量
		if mysqlStorageChange != 0 {
			dataResource.MySQLStorageTotalChange = &mysqlStorageChange
		} else {
			zero := int64(0)
			dataResource.MySQLStorageTotalChange = &zero
		}

		if ossStorageChange != 0 {
			dataResource.OSSStorageTotalChange = &ossStorageChange
		} else {
			zero := int64(0)
			dataResource.OSSStorageTotalChange = &zero
		}

		if obsStorageChange != 0 {
			dataResource.OBSStorageTotalChange = &obsStorageChange
		} else {
			zero := int64(0)
			dataResource.OBSStorageTotalChange = &zero
		}

		if sfsStorageChange != 0 {
			dataResource.SFSStorageTotalChange = &sfsStorageChange
		} else {
			zero := int64(0)
			dataResource.SFSStorageTotalChange = &zero
		}

		if nasStorageChange != 0 {
			dataResource.NASStorageTotalChange = &nasStorageChange
		} else {
			zero := int64(0)
			dataResource.NASStorageTotalChange = &zero
		}

		if mongodbStorageChange != 0 {
			dataResource.MongodbStorageTotalChange = &mongodbStorageChange
		} else {
			zero := int64(0)
			dataResource.MongodbStorageTotalChange = &zero
		}

		if starrocksStorageChange != 0 {
			dataResource.StarrocksStorageTotalChange = &starrocksStorageChange
		} else {
			zero := int64(0)
			dataResource.StarrocksStorageTotalChange = &zero
		}

		if clickhouseStorageChange != 0 {
			dataResource.ClickhouseStorageTotalChange = &clickhouseStorageChange
		} else {
			zero := int64(0)
			dataResource.ClickhouseStorageTotalChange = &zero
		}

		if tidbStorageChange != 0 {
			dataResource.TidbStorageTotalChange = &tidbStorageChange
		} else {
			zero := int64(0)
			dataResource.TidbStorageTotalChange = &zero
		}

		if dliStorageChange != 0 {
			dataResource.DliStorageTotalChange = &dliStorageChange
		} else {
			zero := int64(0)
			dataResource.DliStorageTotalChange = &zero
		}
	} else if errors.Is(err, gorm.ErrRecordNotFound) {
		// 如果上个月没有数据，则变化量为nil
		app.Log().Info("上个月没有数据资源统计数据，变化量设为nil")
		// 变化量已经初始化为nil，不需要额外设置
	} else {
		app.Log().Error("获取上个月数据资源统计数据失败", "err", err)
		return
	}

	// 4. 获取当月可优化资源统计数据
	var optimizableAsset OptimizableAsset
	err = app.DB().Model(&statisticAsset.OptimizableStat{}).
		Where("stat_date = ?", reportMonth.Format("2006-01-02")).
		Select("amd_percent, cpu_usage, memory_usage").
		Take(&optimizableAsset).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			app.Log().Warn("当月没有可优化资源统计数据，将使用默认值", "month", monthStr)
			// 使用默认值（全部为0）继续处理
		} else {
			app.Log().Error("获取当月可优化资源统计数据失败", "err", err)
			return
		}
	}

	// 获取上个月的可优化资源统计数据，计算变化量
	var lastMonthOptimizableAsset statisticAsset.OptimizableStat
	err = app.DB().Model(&statisticAsset.OptimizableStat{}).
		Where("stat_date = ?", lastMonth.Format("2006-01-02")).
		Take(&lastMonthOptimizableAsset).Error
	if err == nil {
		// 计算变化量
		amdPercentChange := optimizableAsset.AMDPercent - lastMonthOptimizableAsset.AMDPercent
		cpuUsageChange := optimizableAsset.CPUUsage - lastMonthOptimizableAsset.CPUUsage
		memoryUsageChange := optimizableAsset.MemoryUsage - lastMonthOptimizableAsset.MemoryUsage

		// 如果没有变化，则设置为0，否则设置为变化量
		if amdPercentChange != 0 {
			optimizableAsset.AMDPercentChange = &amdPercentChange
		} else {
			zero := float64(0)
			optimizableAsset.AMDPercentChange = &zero
		}

		if cpuUsageChange != 0 {
			optimizableAsset.CPUUsageChange = &cpuUsageChange
		} else {
			zero := float64(0)
			optimizableAsset.CPUUsageChange = &zero
		}

		if memoryUsageChange != 0 {
			optimizableAsset.MemoryUsageChange = &memoryUsageChange
		} else {
			zero := float64(0)
			optimizableAsset.MemoryUsageChange = &zero
		}
	} else if errors.Is(err, gorm.ErrRecordNotFound) {
		// 如果上个月没有数据，则变化量为nil
		app.Log().Info("上个月没有可优化资源统计数据，变化量设为nil")
		// 变化量已经初始化为nil，不需要额外设置
	} else {
		app.Log().Error("获取上个月可优化资源统计数据失败", "err", err)
		return
	}

	// 5. 获取当月域名带宽统计数据
	// 计算当月每个域名的平均带宽
	var domainBpsStats []DomainBpsMonthly

	// 获取当月所有域名
	var domains []string
	err = app.DB().Model(&asset.CloudDDosDomainDailyBps{}).
		Where("stat_date >= ? AND stat_date < ?",
			reportMonth.Format("2006-01-02"),
			reportMonth.AddDate(0, 1, 0).Format("2006-01-02")).
		Distinct("domain").
		Pluck("domain", &domains).Error
	if err != nil {
		app.Log().Error("获取当月域名列表失败", "err", err)
		return
	}

	// 对每个域名计算当月平均带宽
	for _, domain := range domains {
		var avgBps struct {
			AvgInBps  float64
			AvgOutBps float64
		}

		err = app.DB().Model(&asset.CloudDDosDomainDailyBps{}).
			Select("AVG(in_bps) as avg_in_bps, AVG(out_bps) as avg_out_bps").
			Where("domain = ? AND stat_date >= ? AND stat_date < ?",
				domain,
				reportMonth.Format("2006-01-02"),
				reportMonth.AddDate(0, 1, 0).Format("2006-01-02")).
			Take(&avgBps).Error
		if err != nil {
			app.Log().Error("计算当月域名平均带宽失败", "domain", domain, "err", err)
			continue
		}

		// 获取上个月的平均带宽
		var lastMonthAvgBps struct {
			AvgInBps  float64
			AvgOutBps float64
		}

		err = app.DB().Model(&asset.CloudDDosDomainDailyBps{}).
			Select("AVG(in_bps) as avg_in_bps, AVG(out_bps) as avg_out_bps").
			Where("domain = ? AND stat_date >= ? AND stat_date < ?",
				domain,
				lastMonth.Format("2006-01-02"),
				lastMonth.AddDate(0, 1, 0).Format("2006-01-02")).
			Take(&lastMonthAvgBps).Error

		domainBps := DomainBpsMonthly{
			Domain: domain,
			InBps:  int64(avgBps.AvgInBps),
			OutBps: int64(avgBps.AvgOutBps),
		}

		if err == nil {
			// 计算变化量
			inBpsChangef := avgBps.AvgInBps - lastMonthAvgBps.AvgInBps
			outBpsChangef := avgBps.AvgOutBps - lastMonthAvgBps.AvgOutBps

			// 转换为int64
			inBpsChange := int64(inBpsChangef)
			outBpsChange := int64(outBpsChangef)

			// 如果没有变化，则设置为0，否则设置为变化量
			if inBpsChange != 0 {
				domainBps.InBpsChange = &inBpsChange
			} else {
				zero := int64(0)
				domainBps.InBpsChange = &zero
			}

			if outBpsChange != 0 {
				domainBps.OutBpsChange = &outBpsChange
			} else {
				zero := int64(0)
				domainBps.OutBpsChange = &zero
			}
		} else {
			// 如果上个月没有数据，则变化量为nil
			app.Log().Info("上个月没有域名带宽统计数据，变化量设为nil", "domain", domain)
			// 变化量已经初始化为nil，不需要额外设置
		}

		domainBpsStats = append(domainBpsStats, domainBps)
	}

	// 6. 组合成MonthlyReport对象
	computerResource.CloudResources = cloudResources
	computerResource.RegionResources = regionResources

	byteComputerResource, err := json.Marshal(computerResource)
	if err != nil {
		app.Log().Error("序列化计算机资源统计数据失败", "err", err)
		return
	}
	byteDataResource, err := json.Marshal(dataResource)
	if err != nil {
		app.Log().Error("序列化数据资源统计数据失败", "err", err)
		return
	}
	byteOptimizableAsset, err := json.Marshal(optimizableAsset)
	if err != nil {
		app.Log().Error("序列化可优化资源统计数据失败", "err", err)
		return
	}
	byteDomainBpsStats, err := json.Marshal(domainBpsStats)
	if err != nil {
		app.Log().Error("序列化域名带宽统计数据失败", "err", err)
		return
	}

	monthlyReport := MonthlyReport{
		Month:                 monthStr,
		ComputerResourceStats: byteComputerResource,
		DataResourceStats:     byteDataResource,
		OptimizableAssetStats: byteOptimizableAsset,
		DomainBpsStats:        byteDomainBpsStats,
	}

	// 7. 检查数据库中是否已存在相同月份的报告
	var existReport MonthlyReport
	err = app.DB().Where("month = ?", monthStr).Take(&existReport).Error
	if err == nil {
		// 已存在，检查是否有变化再更新
		updateMap := map[string]any{}
		if !bytes.Equal(existReport.ComputerResourceStats, byteComputerResource) {
			updateMap["computer_resource_stats"] = monthlyReport.ComputerResourceStats
		}
		if !bytes.Equal(existReport.DataResourceStats, byteDataResource) {
			updateMap["data_resource_stats"] = monthlyReport.DataResourceStats
		}
		if !bytes.Equal(existReport.OptimizableAssetStats, byteOptimizableAsset) {
			updateMap["optimizable_asset_stats"] = monthlyReport.OptimizableAssetStats
		}
		if !bytes.Equal(existReport.DomainBpsStats, byteDomainBpsStats) {
			updateMap["domain_bps_stats"] = monthlyReport.DomainBpsStats
		}

		// 只有在有变化时才进行更新
		if len(updateMap) > 0 {
			err = app.DB().Model(&existReport).Updates(updateMap).Error
			if err != nil {
				app.Log().Error("更新月度报告失败", "err", err)
				return
			}
			app.Log().Info("更新月度报告成功", "month", monthStr, "updated_fields", len(updateMap))
		} else {
			app.Log().Info("月度报告无变化，无需更新", "month", monthStr)
		}
	} else if errors.Is(err, gorm.ErrRecordNotFound) {
		// 不存在，创建
		err = app.DB().Create(&monthlyReport).Error
		if err != nil {
			app.Log().Error("创建月度报告失败", "err", err)
			return
		}
		app.Log().Info("创建月度报告成功", "month", monthStr)
	} else {
		// 其他错误
		app.Log().Error("查询月度报告失败", "err", err)
		return
	}

	return nil
}

// GenLastMonthReport 生成上月月度报告，每月1号生成，并与上上个月的数据进行对比
func GenLastMonthReport() (err error) {
	// 调用 GenMonthlyReport 生成上个月的月度报告
	return genMonthlyReport(now.With(now.BeginningOfDay()).BeginningOfMonth())
}
