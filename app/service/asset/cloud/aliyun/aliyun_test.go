package aliyun

import (
	"cmdb/app"
	"testing"
)

func TestSync(t *testing.T) {
	err := app.NewApp("../../../../../app.ini")
	if err != nil {
		t.<PERSON>al(err)
	}
	err = app.ConnectDB()
	if err != nil {
		t.<PERSON>al(err)
	}
	defer func() {
		dbop, err := app.DB().DB()
		if err != nil {
			t.<PERSON>al(err)
		}
		err = dbop.Close()
		if err != nil {
			t.Fatal(err)
		}
	}()
	storage, err := StatsAllAccountsNasStorage()
	if err != nil {
		t.<PERSON>al(err)
	}
	app.Log().Info("统计nas存储容量", "容量", storage)
	// ossSotrage, err := StatsAllAccountsOssStorage()
	// if err != nil {
	// 	t.Fatal(err)
	// }
	// app.Log().Info("统计oss存储容量", "容量", ossSotrage)
}
