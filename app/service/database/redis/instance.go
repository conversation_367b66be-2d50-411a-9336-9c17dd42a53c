package redis

import (
	"cmdb/app"
	"cmdb/app/service/asset"
	"cmdb/pkg/db"
	"cmdb/pkg/utils"
	"context"
	"errors"
	"time"

	"gorm.io/gorm"
)

var (
	ErrInstanceExist = errors.New("Redis实例已存在")
)

// Instance 表示一个 Redis 实例
type Instance struct {
	ID        uint           `gorm:"column:id;primaryKey;comment:主键" json:"id"`
	Host      string         `gorm:"column:host;type:varchar(255);not null;comment:主机地址" json:"host"`
	Port      int            `gorm:"column:port;type:int;not null;comment:端口" json:"port"`
	User      string         `gorm:"column:user;type:varchar(255);comment:用户名" json:"user"`
	Password  string         `gorm:"column:password;type:varchar(255);comment:密码" json:"-"`
	Remark    string         `gorm:"column:remark;type:varchar(255);comment:备注" json:"remark"`
	CreatedAt time.Time      `gorm:"column:created_at;index" json:"created_at"`
	UpdatedAt time.Time      `gorm:"column:updated_at;index" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at;index" json:"-"`
	Tags      []asset.Tag    `gorm:"many2many:asset_db_redis_instance_tags;joinForeignKey:instance_id;joinReferences:tag_id" json:"tags"`
}

// TableName 指定表名
func (Instance) TableName() string {
	return "asset_db_redis_instances"
}

// InstanceForm 创建实例的表单
type InstanceForm struct {
	Host     string `json:"host" binding:"required"`
	Port     int    `json:"port" binding:"required,min=1,max=65535"`
	User     string `json:"user" binding:"max=255"`
	Password string `json:"password"`
	Remark   string `json:"remark"`
	TagIDs   []uint `json:"tag_ids"`
}

// Create 创建 Redis 实例
func (form InstanceForm) Create() (err error) {
	// 检查是否已存在相同的主机和端口
	var count int64
	if err := app.DB().Model(&Instance{}).Where("host = ? AND port = ?", form.Host, form.Port).Count(&count).Error; err != nil {
		return err
	}
	if count > 0 {
		err = ErrInstanceExist
		return
	}

	instance := &Instance{
		Host:     form.Host,
		Port:     form.Port,
		User:     form.User,
		Password: utils.Base64Encode(form.Password),
		Remark:   form.Remark,
	}

	// 开启事务
	tx := app.DB().Begin()
	if err = tx.Create(instance).Error; err != nil {
		tx.Rollback()
		return
	}

	// 如果提供了标签ID，添加标签关联
	if len(form.TagIDs) > 0 {
		var tags []asset.Tag
		if err = tx.Find(&tags, form.TagIDs).Error; err != nil {
			tx.Rollback()
			return
		}
		if err = tx.Model(instance).Association("Tags").Replace(tags); err != nil {
			tx.Rollback()
			return
		}
	}

	if err = tx.Commit().Error; err != nil {
		return
	}

	return
}

// GetInstanceByID 获取单个 Redis 实例
func GetInstanceByID(id int) (instance *Instance, err error) {
	err = app.DB().Where("id = ?", id).Take(&instance).Error
	return
}

// GetInstances 获取 Redis 实例列表
func GetInstances(offset, limit int, keyword *string, tagIDs []int) (count int64, instances []Instance, err error) {

	query := app.DB().Model(&Instance{})

	// 如果有关键字，进行搜索
	if keyword != nil && *keyword != "" {
		query = db.MLike(query, *keyword, "host", "remark")
	}
	if len(tagIDs) > 0 {
		query = query.Joins("JOIN asset_db_redis_instance_tags ON asset_db_redis_instances.id = asset_db_redis_instance_tags.instance_id").
			Where("asset_db_redis_instance_tags.tag_id IN (?)", tagIDs)
	}
	// 获取总数
	if err = query.Count(&count).Error; err != nil {
		return
	}

	// 分页查询
	if err = query.Order("created_at DESC").Offset(offset).Limit(limit).Find(&instances).Error; err != nil {
		return
	}

	for i := range instances {
		// 获取标签
		if err1 := app.DB().Model(&instances[i]).Association("Tags").Find(&instances[i].Tags); err1 != nil {
			app.Log().Error("分页获取redis实例，查询标签出错", "err", err1)
		}
	}

	return
}

// Update 更新 Redis 实例
func (instance Instance) Update(form InstanceForm) (err error) {

	// 更新基本信息
	updates := map[string]interface{}{}
	if form.Host != instance.Host {
		updates["host"] = form.Host
	}
	if form.Port != instance.Port {
		updates["port"] = form.Port
	}
	if form.User != instance.User {
		updates["user"] = form.User
	}
	if form.Password != "" && utils.Base64Encode(form.Password) != instance.Password {
		updates["password"] = utils.Base64Encode(form.Password)
	}
	if form.Remark != instance.Remark {
		updates["remark"] = form.Remark
	}

	// 开启事务
	tx := app.DB().Begin()
	if len(updates) > 0 {
		if err = tx.Model(instance).Updates(updates).Error; err != nil {
			tx.Rollback()
			return
		}
	}

	// 如果提供了标签ID，更新标签关联
	if len(form.TagIDs) > 0 {
		var tags []asset.Tag
		if err = tx.Find(&tags, form.TagIDs).Error; err != nil {
			tx.Rollback()
			return
		}
		if err = tx.Model(&instance).Association("Tags").Replace(&tags); err != nil {
			tx.Rollback()
			return
		}
	} else {
		// 如果没有提供标签ID，清空标签关联
		if err = tx.Model(&instance).Association("Tags").Clear(); err != nil {
			tx.Rollback()
			return
		}
	}

	if err = tx.Commit().Error; err != nil {
		return
	}

	return
}

// Delete 删除 Redis 实例
func Delete(id uint) error {
	return app.DB().Transaction(func(tx *gorm.DB) error {
		// 先删除标签关联
		if err := tx.Model(&Instance{ID: id}).Association("Tags").Clear(); err != nil {
			return err
		}
		// 删除实例
		return tx.Delete(&Instance{}, id).Error
	})
}

// TestConnect 测试 Redis 连接
func (form InstanceForm) TestConnect() error {
	client, err := db.NewRedisClient(db.RedisConnParams{
		Host:     form.Host,
		Port:     form.Port,
		User:     form.User,
		Password: form.Password,
	})
	if err != nil {
		return err
	}
	defer client.Close()

	// 测试连接
	ctx := context.Background()
	_, err = client.Ping(ctx).Result()
	return err
}

// NewConnect 创建新的 Redis 连接
func (instance *Instance) NewConnect() (db.RedisClient, error) {
	password, _ := utils.Base64Decode(instance.Password)
	return db.NewRedisClient(db.RedisConnParams{
		Host:     instance.Host,
		Port:     instance.Port,
		User:     instance.User,
		Password: password,
	})
}
