package asset

import (
	"cmdb/app"
	"encoding/json"
	"time"

	"github.com/jinzhu/now"
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type CloudDDosProtection struct {
	ID         uint           `gorm:"column:id;primaryKey;comment:主键" json:"id"`
	AccountID  uint           `gorm:"column:account_id;index;comment:账户ID" json:"account_id"`
	InstanceID string         `gorm:"type:varchar(255);column:instance_id;index:idx_instance_id;comment:实例ID" json:"instance_id"`
	Status     int            `gorm:"column:status;comment:状态" json:"status"`
	Enabled    bool           `gorm:"column:enabled;comment:是否启用" json:"enabled"`
	IP         string         `gorm:"type:varchar(255);column:ip;comment:IP" json:"ip"`
	IPVersion  string         `gorm:"type:varchar(255);column:ip_version;comment:IP版本" json:"ip_version"`
	IPMode     string         `gorm:"type:varchar(255);column:ip_mode;comment:IP模式" json:"ip_mode"`
	ExpireTime *time.Time     `gorm:"column:expire_time;comment:过期时间" json:"expire_time"`
	Remark     string         `gorm:"type:varchar(255);column:remark;comment:备注" json:"remark"`
	CreateTime time.Time      `gorm:"column:create_time;comment:创建时间" json:"create_time"`
	SyncTime   time.Time      `gorm:"column:sync_time;index;comment:同步时间" json:"sync_time"`
	DeletedAt  gorm.DeletedAt `gorm:"column:deleted_at;index" json:"-"`
}

func (CloudDDosProtection) TableName() string {
	return "asset_cloud_ddos_protections"
}

func GetCloudDDosProtections(offset, limit int, accountID *int, keyword *string) (count int64, data []CloudDDosProtection, err error) {
	dbop := app.DB().Model(&CloudDDosProtection{})
	if accountID != nil {
		dbop = dbop.Where("account_id = ?", *accountID)
	}
	if keyword != nil {
		dbop = dbop.Where("remark LIKE ? OR ip LIKE ? OR instance_id LIKE ?", "%"+*keyword+"%", "%"+*keyword+"%", "%"+*keyword+"%")
	}
	err = dbop.Count(&count).Order("create_time DESC").Offset(offset).Limit(limit).Find(&data).Error
	return
}

type CloudDDosDomain struct {
	ID          uint           `gorm:"column:id;primaryKey;comment:主键" json:"id"`
	AccountID   uint           `gorm:"column:account_id;index;comment:账户ID" json:"account_id"`
	Domain      string         `gorm:"type:varchar(255);column:domain;index:idx_domain;comment:域名" json:"domain"`
	Cname       string         `gorm:"type:varchar(255);column:cname;index:idx_cname;comment:CNAME" json:"cname"`
	Http2Enable bool           `gorm:"column:http2_enable;index:idx_http2_enable;comment:HTTP/2 启用" json:"http2_enable"`
	ProxyTypes  string         `gorm:"type:varchar(255);column:proxy_types;comment:代理类型" json:"proxy_types"`
	RealServers datatypes.JSON `gorm:"column:real_servers;comment:真实服务器" json:"real_servers"`
	Hosts       []Host         `gorm:"-" json:"hosts"`
	CreatedAt   time.Time      `gorm:"column:created_at;comment:创建时间" json:"created_at"`
	SyncTime    time.Time      `gorm:"column:sync_time;index;comment:同步时间" json:"sync_time"`
	DeletedAt   gorm.DeletedAt `gorm:"column:deleted_at;index" json:"-"`
}

func (CloudDDosDomain) TableName() string {
	return "asset_cloud_ddos_domains"
}

func (cdd CloudDDosDomain) GetHosts() (hosts []Host, err error) {
	ips := []string{}
	hosts = []Host{}
	err = json.Unmarshal(cdd.RealServers, &ips)
	if err != nil {
		return
	}
	if len(ips) == 0 {
		return
	}
	app.DB().Model(&Host{}).Where("ip IN(?) OR public_ip IN(?)", ips, ips).Find(&hosts)
	return
}

func GetCloudDDosDomains(offset, limit int, accountID *int, keyword *string) (count int64, data []CloudDDosDomain, err error) {
	dbop := app.DB().Model(&CloudDDosDomain{})
	if accountID != nil {
		dbop = dbop.Where("account_id = ?", *accountID)
	}
	if keyword != nil {
		dbop = dbop.Where("id IN(?) OR domain LIKE ? OR proxy_types LIKE ? OR cname LIKE ? ", app.DB().Select("id").Model(&CloudDDosDomain{}).Clauses(clause.Expr{
			SQL:  "JSON_CONTAINS(real_servers,JSON_ARRAY(?))",
			Vars: []interface{}{keyword},
		}), "%"+*keyword+"%", "%"+*keyword+"%", "%"+*keyword+"%")
	}
	err = dbop.Count(&count).Order("domain").Offset(offset).Limit(limit).Find(&data).Error
	if err != nil {
		return
	}
	for i := range data {
		data[i].Hosts, _ = data[i].GetHosts()
	}
	return
}

func GetCloudDDosDomainByID(id int) (data CloudDDosDomain, err error) {
	err = app.DB().Model(&CloudDDosDomain{}).Where("id = ?", id).Take(&data).Error
	return
}

type CloudDDosDomainDailyBps struct {
	ID        uint      `gorm:"column:id;primaryKey;comment:主键" json:"id"`
	AccountID uint      `gorm:"column:account_id;index;comment:账户ID" json:"account_id"`
	Domain    string    `gorm:"type:varchar(255);column:domain;index:idx_domain;comment:域名" json:"domain"`
	StatDate  time.Time `gorm:"type:date;column:stat_date;index:idx_stat_date;comment:日期" json:"stat_date"`
	InBps     int64     `gorm:"column:in_bps;comment:入流量" json:"in_bps"`
	OutBps    int64     `gorm:"column:out_bps;comment:出流量" json:"out_bps"`
	CreatedAt time.Time `gorm:"column:created_at;comment:创建时间" json:"created_at"`
}

func (CloudDDosDomainDailyBps) TableName() string {
	return "asset_cloud_ddos_domain_daily_bps"
}

func GetCloudDDosDomainDailyBpsByID(id int) (data CloudDDosDomainDailyBps, err error) {
	err = app.DB().Model(&CloudDDosDomainDailyBps{}).Where("id = ?", id).Take(&data).Error
	return
}

func (domain CloudDDosDomain) GetDailyBpsList(startDate, endDate time.Time) (data []CloudDDosDomainDailyBps, err error) {
	err = app.DB().Model(&CloudDDosDomainDailyBps{}).Where("domain = ? AND account_id = ? AND stat_date >= ? AND stat_date <= ?", domain.Domain, domain.AccountID, startDate.Format("2006-01-02"), endDate.Format("2006-01-02")).Order("stat_date ASC").Find(&data).Error
	return
}

func GetCloudDDosDomainDailyBpsList(startDate, endDate time.Time) (data []CloudDDosDomainDailyBps, err error) {
	err = app.DB().Model(&CloudDDosDomainDailyBps{}).Select(" stat_date, sum(in_bps) as in_bps, sum(out_bps) as out_bps").Where("stat_date >= ? AND stat_date <= ?", startDate.Format("2006-01-02"), endDate.Format("2006-01-02")).Group("stat_date").Order("stat_date ASC").Find(&data).Error
	return
}

type MonthMaxBps struct {
	Domain string  `gorm:"type:varchar(255);column:domain;index:idx_domain;comment:域名" json:"domain"`
	InBps  float64 `gorm:"column:in_bps;comment:入流量" json:"in_bps"`
	OutBps float64 `gorm:"column:out_bps;comment:出流量" json:"out_bps"`
}

func GetCloudDDosDomainMonthAvgBps(month time.Time) (data []MonthMaxBps, err error) {
	startTime := now.With(month).BeginningOfMonth()
	endTime := startTime.AddDate(0, 1, 0)
	err = app.DB().Model(&CloudDDosDomainDailyBps{}).Select(" domain, avg(in_bps) as in_bps, avg(out_bps) as out_bps").Where("stat_date >= ? AND stat_date <= ?", startTime.Format("2006-01-02"), endTime.Format("2006-01-02")).Group("domain").Order("in_bps ASC").Find(&data).Error
	return
}

func GetCloudDDosDomainByIP(ip string) (data []CloudDDosDomain, err error) {
	err = app.DB().Model(&CloudDDosDomain{}).Clauses(clause.Expr{
		SQL:  "JSON_CONTAINS(real_servers,JSON_ARRAY(?))",
		Vars: []interface{}{ip},
	}).Find(&data).Error
	return
}
